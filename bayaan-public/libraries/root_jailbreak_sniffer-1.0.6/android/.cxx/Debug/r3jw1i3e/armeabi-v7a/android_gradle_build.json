{"buildFiles": ["/Users/<USER>/.pub-cache/hosted/pub.dev/root_jailbreak_sniffer-1.0.6/android/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.18.1/bin/ninja", "-C", "/Users/<USER>/.pub-cache/hosted/pub.dev/root_jailbreak_sniffer-1.0.6/android/.cxx/Debug/r3jw1i3e/armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.18.1/bin/ninja", "-C", "/Users/<USER>/.pub-cache/hosted/pub.dev/root_jailbreak_sniffer-1.0.6/android/.cxx/Debug/r3jw1i3e/armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"native-lib::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "native-lib", "output": "/Users/<USER>/Documents/GitHub/scad-mobile/build/root_jailbreak_sniffer/intermediates/cxx/Debug/r3jw1i3e/obj/armeabi-v7a/libnative-lib.so", "runtimeFiles": []}}, "toolchains": {"toolchain": {"cCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang.lld", "cppCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++.lld"}}, "cFileExtensions": ["c"], "cppFileExtensions": []}