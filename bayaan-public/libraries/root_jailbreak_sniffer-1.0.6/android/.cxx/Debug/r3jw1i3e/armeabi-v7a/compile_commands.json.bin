C/C++ Build Metadata                A           c         
                                c/Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang   '--target=armv7-none-linux-androideabi16   i--gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/toolchains/llvm/prebuilt/darwin-x86_64   k--sysroot=/Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/toolchains/llvm/prebuilt/darwin-x86_64/sysroot   -Dnative_lib_EXPORTS   -g   	-DANDROID   -fdata-sections   -ffunction-sections   -funwind-tables   -fstack-protector-strong   -no-canonical-prefixes   -D_FORTIFY_SOURCE=2   -march=armv7-a   -mthumb   -Wformat   -Werror=format-security   -fno-limit-debug-info   -fPIC   l/Users/<USER>/.pub-cache/hosted/pub.dev/root_jailbreak_sniffer-1.0.6/android/.cxx/Debug/r3jw1i3e/armeabi-v7a   
native-lib   d/Users/<USER>/.pub-cache/hosted/pub.dev/root_jailbreak_sniffer-1.0.6/android/src/main/c/native-lib.c   3CMakeFiles/native-lib.dir/src/main/c/native-lib.c.o                              	   
         
                  