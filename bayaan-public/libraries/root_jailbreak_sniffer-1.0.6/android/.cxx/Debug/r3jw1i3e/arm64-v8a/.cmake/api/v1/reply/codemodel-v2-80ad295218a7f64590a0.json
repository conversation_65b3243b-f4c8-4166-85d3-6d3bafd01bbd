{"configurations": [{"directories": [{"build": ".", "minimumCMakeVersion": {"string": "3.4.1"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Project", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "native-lib::@6890427a1f51a3e7e1df", "jsonFile": "target-native-lib-Debug-112172620ce8b421a56b.json", "name": "native-lib", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/.pub-cache/hosted/pub.dev/root_jailbreak_sniffer-1.0.6/android/.cxx/Debug/r3jw1i3e/arm64-v8a", "source": "/Users/<USER>/.pub-cache/hosted/pub.dev/root_jailbreak_sniffer-1.0.6/android"}, "version": {"major": 2, "minor": 1}}