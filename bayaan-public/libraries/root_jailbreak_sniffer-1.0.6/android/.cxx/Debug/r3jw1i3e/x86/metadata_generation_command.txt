                    -H/Users/<USER>/.pub-cache/hosted/pub.dev/root_jailbreak_sniffer-1.0.6/android
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=16
-DAN<PERSON>OID_PLATFORM=android-16
-DANDROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/23.1.7779620
-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/23.1.7779620
-DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.18.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Documents/GitHub/scad-mobile/build/root_jailbreak_sniffer/intermediates/cxx/Debug/r3jw1i3e/obj/x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Documents/GitHub/scad-mobile/build/root_jailbreak_sniffer/intermediates/cxx/Debug/r3jw1i3e/obj/x86
-DCMAKE_BUILD_TYPE=Debug
-B/Users/<USER>/.pub-cache/hosted/pub.dev/root_jailbreak_sniffer-1.0.6/android/.cxx/Debug/r3jw1i3e/x86
-GNinja
                    Build command args: []
                    Version: 2