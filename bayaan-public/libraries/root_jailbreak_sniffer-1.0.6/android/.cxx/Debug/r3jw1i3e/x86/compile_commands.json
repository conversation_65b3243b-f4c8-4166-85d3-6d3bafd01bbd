[{"directory": "/Users/<USER>/.pub-cache/hosted/pub.dev/root_jailbreak_sniffer-1.0.6/android/.cxx/Debug/r3jw1i3e/x86", "command": "/Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang --target=i686-none-linux-android16 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dnative_lib_EXPORTS -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -mstackrealign -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles/native-lib.dir/src/main/c/native-lib.c.o -c /Users/<USER>/.pub-cache/hosted/pub.dev/root_jailbreak_sniffer-1.0.6/android/src/main/c/native-lib.c", "file": "/Users/<USER>/.pub-cache/hosted/pub.dev/root_jailbreak_sniffer-1.0.6/android/src/main/c/native-lib.c"}]