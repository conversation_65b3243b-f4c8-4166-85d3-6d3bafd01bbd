{"configurations": [{"directories": [{"build": ".", "minimumCMakeVersion": {"string": "3.4.1"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Project", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "native-lib::@6890427a1f51a3e7e1df", "jsonFile": "target-native-lib-Debug-c868cbe6b6d165bd557d.json", "name": "native-lib", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/Documents/GitHub/scad-mobile-public/libraries/root_jailbreak_sniffer-1.0.6/android/.cxx/Debug/6xy396b2/x86_64", "source": "/Users/<USER>/Documents/GitHub/scad-mobile-public/libraries/root_jailbreak_sniffer-1.0.6/android"}, "version": {"major": 2, "minor": 1}}