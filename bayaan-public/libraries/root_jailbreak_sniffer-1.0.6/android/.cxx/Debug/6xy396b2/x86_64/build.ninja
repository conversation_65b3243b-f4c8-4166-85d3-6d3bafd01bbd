# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.18

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: Project
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/GitHub/scad-mobile-public/libraries/root_jailbreak_sniffer-1.0.6/android/.cxx/Debug/6xy396b2/x86_64 && /Users/<USER>/Library/Android/sdk/cmake/3.18.1/bin/cmake --regenerate-during-build -S/Users/<USER>/Documents/GitHub/scad-mobile-public/libraries/root_jailbreak_sniffer-1.0.6/android -B/Users/<USER>/Documents/GitHub/scad-mobile-public/libraries/root_jailbreak_sniffer-1.0.6/android/.cxx/Debug/6xy396b2/x86_64
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/GitHub/scad-mobile-public/libraries/root_jailbreak_sniffer-1.0.6/android/.cxx/Debug/6xy396b2/x86_64 && /Users/<USER>/Library/Android/sdk/cmake/3.18.1/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util

# =============================================================================
# Object build statements for SHARED_LIBRARY target native-lib


#############################################
# Order-only phony target for native-lib

build cmake_object_order_depends_target_native-lib: phony || CMakeFiles/native-lib.dir

build CMakeFiles/native-lib.dir/src/main/c/native-lib.c.o: C_COMPILER__native-lib_Debug ../../../../src/main/c/native-lib.c || cmake_object_order_depends_target_native-lib
  DEFINES = -Dnative_lib_EXPORTS
  DEP_FILE = CMakeFiles/native-lib.dir/src/main/c/native-lib.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  OBJECT_DIR = CMakeFiles/native-lib.dir
  OBJECT_FILE_DIR = CMakeFiles/native-lib.dir/src/main/c


# =============================================================================
# Link build statements for SHARED_LIBRARY target native-lib


#############################################
# Link the shared library /Users/<USER>/Documents/GitHub/scad-mobile-public/build/root_jailbreak_sniffer/intermediates/cxx/Debug/6xy396b2/obj/x86_64/libnative-lib.so

build /Users/<USER>/Documents/GitHub/scad-mobile-public/build/root_jailbreak_sniffer/intermediates/cxx/Debug/6xy396b2/obj/x86_64/libnative-lib.so: C_SHARED_LIBRARY_LINKER__native-lib_Debug CMakeFiles/native-lib.dir/src/main/c/native-lib.c.o | /Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/x86_64-linux-android/21/liblog.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info
  LINK_FLAGS = -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = -llog  -latomic -lm
  OBJECT_DIR = CMakeFiles/native-lib.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libnative-lib.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = /Users/<USER>/Documents/GitHub/scad-mobile-public/build/root_jailbreak_sniffer/intermediates/cxx/Debug/6xy396b2/obj/x86_64/libnative-lib.so
  TARGET_PDB = native-lib.so.dbg

# =============================================================================
# Target aliases.

build libnative-lib.so: phony /Users/<USER>/Documents/GitHub/scad-mobile-public/build/root_jailbreak_sniffer/intermediates/cxx/Debug/6xy396b2/obj/x86_64/libnative-lib.so

build native-lib: phony /Users/<USER>/Documents/GitHub/scad-mobile-public/build/root_jailbreak_sniffer/intermediates/cxx/Debug/6xy396b2/obj/x86_64/libnative-lib.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /Users/<USER>/Documents/GitHub/scad-mobile-public/libraries/root_jailbreak_sniffer-1.0.6/android/.cxx/Debug/6xy396b2/x86_64

build all: phony /Users/<USER>/Documents/GitHub/scad-mobile-public/build/root_jailbreak_sniffer/intermediates/cxx/Debug/6xy396b2/obj/x86_64/libnative-lib.so

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | ../../../../CMakeLists.txt /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCCompiler.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCCompilerABI.c /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCXXCompiler.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCXXCompilerABI.cpp /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCXXInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCommonLanguageInclude.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeDetermineCCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeDetermineCXXCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeDetermineCompileFeatures.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeDetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeDetermineCompilerABI.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeDetermineSystem.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeFindBinUtils.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeGenericSystem.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeInitializeConfigs.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeLanguageInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeParseImplicitIncludeInfo.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeParseImplicitLinkInfo.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeSystem.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeSystemSpecificInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeSystemSpecificInitialize.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeTestCCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeTestCXXCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeTestCompilerCommon.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Compiler/CMakeCommonCompilerMacros.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Compiler/Clang-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Compiler/Clang-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Compiler/Clang-FindBinUtils.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Compiler/Clang.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Compiler/GNU.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Internal/CMakeCheckCompilerFlag.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Internal/FeatureTesting.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Clang-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Clang-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Clang.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Determine-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Determine-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Determine.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Initialize.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android/Determine-Compiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Linux.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/UnixPaths.cmake /Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/build/cmake/android-legacy.toolchain.cmake /Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/build/cmake/android.toolchain.cmake /Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/build/cmake/compiler_id.cmake /Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/build/cmake/platforms.cmake CMakeCache.txt CMakeFiles/3.18.1-g262b901/CMakeCCompiler.cmake CMakeFiles/3.18.1-g262b901/CMakeCXXCompiler.cmake CMakeFiles/3.18.1-g262b901/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build ../../../../CMakeLists.txt /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCCompiler.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCCompilerABI.c /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCXXCompiler.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCXXCompilerABI.cpp /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCXXInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCommonLanguageInclude.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeDetermineCCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeDetermineCXXCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeDetermineCompileFeatures.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeDetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeDetermineCompilerABI.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeDetermineSystem.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeFindBinUtils.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeGenericSystem.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeInitializeConfigs.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeLanguageInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeParseImplicitIncludeInfo.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeParseImplicitLinkInfo.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeSystem.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeSystemSpecificInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeSystemSpecificInitialize.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeTestCCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeTestCXXCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeTestCompilerCommon.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Compiler/CMakeCommonCompilerMacros.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Compiler/Clang-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Compiler/Clang-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Compiler/Clang-FindBinUtils.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Compiler/Clang.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Compiler/GNU.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Internal/CMakeCheckCompilerFlag.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Internal/FeatureTesting.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Clang-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Clang-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Clang.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Determine-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Determine-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Determine.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Initialize.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android/Determine-Compiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Linux.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/UnixPaths.cmake /Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/build/cmake/android-legacy.toolchain.cmake /Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/build/cmake/android.toolchain.cmake /Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/build/cmake/compiler_id.cmake /Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/build/cmake/platforms.cmake CMakeCache.txt CMakeFiles/3.18.1-g262b901/CMakeCCompiler.cmake CMakeFiles/3.18.1-g262b901/CMakeCXXCompiler.cmake CMakeFiles/3.18.1-g262b901/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
