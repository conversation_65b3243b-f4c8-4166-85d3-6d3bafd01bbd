{"buildFiles": ["/Users/<USER>/Documents/GitHub/scad-mobile-public/libraries/root_jailbreak_sniffer-1.0.6/android/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.18.1/bin/ninja", "-C", "/Users/<USER>/Documents/GitHub/scad-mobile-public/libraries/root_jailbreak_sniffer-1.0.6/android/.cxx/Debug/6xy396b2/x86", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.18.1/bin/ninja", "-C", "/Users/<USER>/Documents/GitHub/scad-mobile-public/libraries/root_jailbreak_sniffer-1.0.6/android/.cxx/Debug/6xy396b2/x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"native-lib::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "x86", "artifactName": "native-lib", "output": "/Users/<USER>/Documents/GitHub/scad-mobile-public/build/root_jailbreak_sniffer/intermediates/cxx/Debug/6xy396b2/obj/x86/libnative-lib.so", "runtimeFiles": []}}, "toolchains": {"toolchain": {"cCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang.lld", "cppCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++.lld"}}, "cFileExtensions": ["c"], "cppFileExtensions": []}