C/C++ Build Metadata                A           c                                         c/Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang   "--target=i686-none-linux-android16   i--gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/toolchains/llvm/prebuilt/darwin-x86_64   k--sysroot=/Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/toolchains/llvm/prebuilt/darwin-x86_64/sysroot   -Dnative_lib_EXPORTS   -g   	-DANDROID   -fdata-sections   -ffunction-sections   -funwind-tables   -fstack-protector-strong   -no-canonical-prefixes   -mstackrealign   -D_FORTIFY_SOURCE=2   -Wformat   -Werror=format-security   -fno-limit-debug-info   -fPIC   x/Users/<USER>/Documents/GitHub/scad-mobile-public/libraries/root_jailbreak_sniffer-1.0.6/android/.cxx/Debug/6xy396b2/x86   
native-lib   x/Users/<USER>/Documents/GitHub/scad-mobile-public/libraries/root_jailbreak_sniffer-1.0.6/android/src/main/c/native-lib.c   3CMakeFiles/native-lib.dir/src/main/c/native-lib.c.o                              	   
         
               