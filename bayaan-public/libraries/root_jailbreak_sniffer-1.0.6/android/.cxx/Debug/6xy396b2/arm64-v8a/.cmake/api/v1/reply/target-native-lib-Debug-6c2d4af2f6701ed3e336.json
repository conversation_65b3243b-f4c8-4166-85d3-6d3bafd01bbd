{"artifacts": [{"path": "/Users/<USER>/Documents/GitHub/scad-mobile-public/build/root_jailbreak_sniffer/intermediates/cxx/Debug/6xy396b2/obj/arm64-v8a/libnative-lib.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 23, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC"}], "defines": [{"define": "native_lib_EXPORTS"}], "language": "C", "sourceIndexes": [0], "sysroot": {"path": "/Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/toolchains/llvm/prebuilt/darwin-x86_64/sysroot"}}], "id": "native-lib::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments", "role": "flags"}, {"fragment": "-llog", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "C", "sysroot": {"path": "/Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/toolchains/llvm/prebuilt/darwin-x86_64/sysroot"}}, "name": "native-lib", "nameOnDisk": "libnative-lib.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/main/c/native-lib.c", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}