part of 'route_imports.dart';

@AutoRouterConfig(replaceInRouteName: 'Route')
class AppRouter extends $AppRouter {
  RouteType get defaultType => const RouteType.cupertino();

  @override
  List<CupertinoRoute> get routes => [
        CupertinoRoute(
          page: SplashPageRoute.page,
          initial: true,
        ),
        CupertinoRoute(
          page: HomeNavigationRoute.page,
          // initial: true,
        ),
        CupertinoRoute(
          page: HomePageRoute.page,
          // initial: true,
        ),
        CupertinoRoute(page: SettingsRoute.page),
        CupertinoRoute(page: SearchScreenRoute.page),
        CupertinoRoute(
          page: DetailsPageRoute.page,
        ),
        CupertinoRoute(
          page: CompareIndicatorsPageRoute.page,
        ),
        CupertinoRoute(page: ComputePageRoute.page),
        CupertinoRoute(page: AboutThisAppScreenRoute.page),
        CupertinoRoute(page: TermsAndConditionsScreenRoute.page),
        CupertinoRoute(page: DomainsPageRoute.page),
        CupertinoRoute(page: ThemesPageRoute.page),
        CupertinoRoute(page: ThemeIndicatorsScreenRoute.page),
        CupertinoRoute(page: ProductsRoute.page),
        CupertinoRoute(page: DashboardWebViewPageRoute.page),
        CupertinoRoute(page: OnboardingScreenRoute.page),
        CupertinoRoute(page: GlossaryScreenRoute.page),
        CupertinoRoute(page: UserGuideScreenRoute.page),
        CupertinoRoute(page: WhatIfDetailsPageRoute.page),
        CupertinoRoute(
          page: ChangeDriversFullScreenDialogRoute.page,
          fullscreenDialog: true,
        ),
        CupertinoRoute(page: ReportWebViewPageRoute.page),
        CupertinoRoute(page: FaqPageRoute.page),
      ];
}
