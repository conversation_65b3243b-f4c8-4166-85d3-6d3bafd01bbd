// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auto_route/auto_route.dart' as _i23;
import 'package:flutter/cupertino.dart' as _i25;
import 'package:flutter/material.dart' as _i27;
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_response_helper.dart'
    as _i24;
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart'
    as _i26;
import 'package:scad_mobile/src/features/authentication/presentation/pages/splash/splash.dart'
    as _i17;
import 'package:scad_mobile/src/features/chat_with_sme/presentation/pages/ask_us_page.dart'
    as _i8;
import 'package:scad_mobile/src/features/details/presentation/pages/compare_indicators_page.dart'
    as _i3;
import 'package:scad_mobile/src/features/details/presentation/pages/compute_page.dart'
    as _i4;
import 'package:scad_mobile/src/features/details/presentation/pages/details_page.dart'
    as _i6;
import 'package:scad_mobile/src/features/details/presentation/pages/what_if_details_page.dart'
    as _i22;
import 'package:scad_mobile/src/features/details/presentation/widgets/bottom_sheets/change_driver_bottom_sheet/change_drivers_full_screen_dialog.dart'
    as _i2;
import 'package:scad_mobile/src/features/domains/data/models/domain_classification_model/domain_classification_model.dart'
    as _i29;
import 'package:scad_mobile/src/features/domains/data/models/domain_model/domain_model.dart'
    as _i28;
import 'package:scad_mobile/src/features/domains/data/models/theme_subtheme_response.dart'
    as _i30;
import 'package:scad_mobile/src/features/domains/presentation/pages/domains_page.dart'
    as _i7;
import 'package:scad_mobile/src/features/domains/presentation/pages/theme_indicators_screen.dart'
    as _i19;
import 'package:scad_mobile/src/features/domains/presentation/pages/themes_page.dart'
    as _i20;
import 'package:scad_mobile/src/features/drawer_items/about_this_app/presentation/pages/about_this_app_screen.dart'
    as _i1;
import 'package:scad_mobile/src/features/drawer_items/glossary/presentation/pages/glossary_screen.dart'
    as _i9;
import 'package:scad_mobile/src/features/drawer_items/terms_and_conditions/presentation/pages/terms_and_conditions_screen.dart'
    as _i18;
import 'package:scad_mobile/src/features/drawer_items/user_guide/user_guide_screen.dart'
    as _i21;
import 'package:scad_mobile/src/features/home/<USER>/pages/home/<USER>'
    as _i11;
import 'package:scad_mobile/src/features/home/<USER>/pages/home_navigation.dart'
    as _i10;
import 'package:scad_mobile/src/features/onboarding/presentation/pages/onboarding_main_screen.dart'
    as _i12;
import 'package:scad_mobile/src/features/products/presentation/pages/dashboard_webpage_screen.dart'
    as _i5;
import 'package:scad_mobile/src/features/products/presentation/pages/products_screen.dart'
    as _i13;
import 'package:scad_mobile/src/features/products/presentation/pages/ReportWebViewPage.dart'
    as _i14;
import 'package:scad_mobile/src/features/search/presentation/pages/search/search_screen.dart'
    as _i15;
import 'package:scad_mobile/src/features/settings/presentation/pages/settings_page.dart'
    as _i16;

abstract class $AppRouter extends _i23.RootStackRouter {
  $AppRouter({super.navigatorKey});

  @override
  final Map<String, _i23.PageFactory> pagesMap = {
    AboutThisAppScreenRoute.name: (routeData) {
      return _i23.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i1.AboutThisAppScreen(),
      );
    },
    ChangeDriversFullScreenDialogRoute.name: (routeData) {
      final args = routeData.argsAs<ChangeDriversFullScreenDialogRouteArgs>();
      return _i23.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i2.ChangeDriversFullScreenDialog(
          indicatorDetails: args.indicatorDetails,
          key: args.key,
          index: args.index,
          isWhatIfDetails: args.isWhatIfDetails,
          scadProjection: args.scadProjection,
          contentType: args.contentType,
        ),
      );
    },
    CompareIndicatorsPageRoute.name: (routeData) {
      final args = routeData.argsAs<CompareIndicatorsPageRouteArgs>();
      return _i23.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i3.CompareIndicatorsPage(
          domainId: args.domainId,
          domainName: args.domainName,
          contentClassificationKey: args.contentClassificationKey,
          indicatorId: args.indicatorId,
          indicatorDetails: args.indicatorDetails,
          key: args.key,
        ),
      );
    },
    ComputePageRoute.name: (routeData) {
      final args = routeData.argsAs<ComputePageRouteArgs>();
      return _i23.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i4.ComputePage(
          title: args.title,
          indicatorId: args.indicatorId,
          firstIndicatorList: args.firstIndicatorList,
          secondIndicatorList: args.secondIndicatorList,
          key: args.key,
        ),
      );
    },
    DashboardWebViewPageRoute.name: (routeData) {
      final args = routeData.argsAs<DashboardWebViewPageRouteArgs>();
      return _i23.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i5.DashboardWebViewPage(
          title: args.title,
          uuid: args.uuid,
          url: args.url,
          urlAr: args.urlAr,
          urlDark: args.urlDark,
          urlArDark: args.urlArDark,
          key: args.key,
        ),
      );
    },
    DetailsPageRoute.name: (routeData) {
      final args = routeData.argsAs<DetailsPageRouteArgs>(
          orElse: () => const DetailsPageRouteArgs());
      return _i23.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i6.DetailsPage(
          value: args.value,
          unit: args.unit,
          numberUnit: args.numberUnit,
          domain: args.domain,
          title: args.title,
          indicatorDetails: args.indicatorDetails,
          key: args.key,
          negativeArrow: args.negativeArrow,
          contentType: args.contentType,
          originalIndicatorForFilter: args.originalIndicatorForFilter,
          id: args.id,
          fromNotification: args.fromNotification,
          comparedIndicatorName: args.comparedIndicatorName,
        ),
      );
    },
    DomainsPageRoute.name: (routeData) {
      return _i23.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i7.DomainsPage(),
      );
    },
    FaqPageRoute.name: (routeData) {
      return _i23.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i8.FaqPage(),
      );
    },
    GlossaryScreenRoute.name: (routeData) {
      final args = routeData.argsAs<GlossaryScreenRouteArgs>(
          orElse: () => const GlossaryScreenRouteArgs());
      return _i23.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i9.GlossaryScreen(
          initialSearchTerm: args.initialSearchTerm,
          key: args.key,
        ),
      );
    },
    HomeNavigationRoute.name: (routeData) {
      final args = routeData.argsAs<HomeNavigationRouteArgs>(
          orElse: () => const HomeNavigationRouteArgs());
      return _i23.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i10.HomeNavigation(
          key: args.key,
          screenTabIndex: args.screenTabIndex,
        ),
      );
    },
    HomePageRoute.name: (routeData) {
      final args = routeData.argsAs<HomePageRouteArgs>();
      return _i23.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i11.HomePage(
          navToDomainsPage: args.navToDomainsPage,
          navToProductsPage: args.navToProductsPage,
          key: args.key,
        ),
      );
    },
    OnboardingScreenRoute.name: (routeData) {
      return _i23.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i12.OnboardingScreen(),
      );
    },
    ProductsRoute.name: (routeData) {
      final args = routeData.argsAs<ProductsRouteArgs>(
          orElse: () => const ProductsRouteArgs());
      return _i23.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i13.Products(
          key: args.key,
          isFromDetailsScreen: args.isFromDetailsScreen,
        ),
      );
    },
    ReportWebViewPageRoute.name: (routeData) {
      final args = routeData.argsAs<ReportWebViewPageRouteArgs>();
      return _i23.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i14.ReportWebViewPage(
          title: args.title,
          url: args.url,
          key: args.key,
        ),
      );
    },
    SearchScreenRoute.name: (routeData) {
      final args = routeData.argsAs<SearchScreenRouteArgs>(
          orElse: () => const SearchScreenRouteArgs());
      return _i23.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i15.SearchScreen(
          key: args.key,
          type: args.type,
          contentType: args.contentType,
          initialNodeIdForComparison: args.initialNodeIdForComparison,
        ),
      );
    },
    SettingsRoute.name: (routeData) {
      return _i23.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i16.Settings(),
      );
    },
    SplashPageRoute.name: (routeData) {
      final args = routeData.argsAs<SplashPageRouteArgs>(
          orElse: () => const SplashPageRouteArgs());
      return _i23.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i17.SplashPage(
          toAnimateLogo: args.toAnimateLogo,
          key: args.key,
        ),
      );
    },
    TermsAndConditionsScreenRoute.name: (routeData) {
      return _i23.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i18.TermsAndConditionsScreen(),
      );
    },
    ThemeIndicatorsScreenRoute.name: (routeData) {
      final args = routeData.argsAs<ThemeIndicatorsScreenRouteArgs>();
      return _i23.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i19.ThemeIndicatorsScreen(
          title: args.title,
          domain: args.domain,
          classification: args.classification,
          subTheme: args.subTheme,
          subDomain: args.subDomain,
          key: args.key,
          screenerConfiguration: args.screenerConfiguration,
          isFromMainScreen: args.isFromMainScreen,
        ),
      );
    },
    ThemesPageRoute.name: (routeData) {
      final args = routeData.argsAs<ThemesPageRouteArgs>(
          orElse: () => const ThemesPageRouteArgs());
      return _i23.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i20.ThemesPage(
          domainId: args.domainId,
          key: args.key,
        ),
      );
    },
    UserGuideScreenRoute.name: (routeData) {
      return _i23.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i21.UserGuideScreen(),
      );
    },
    WhatIfDetailsPageRoute.name: (routeData) {
      final args = routeData.argsAs<WhatIfDetailsPageRouteArgs>();
      return _i23.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i22.WhatIfDetailsPage(
          nodeId: args.nodeId,
          key: args.key,
          indicatorDetails: args.indicatorDetails,
          originalIndicatorData: args.originalIndicatorData,
        ),
      );
    },
  };
}

/// generated route for
/// [_i1.AboutThisAppScreen]
class AboutThisAppScreenRoute extends _i23.PageRouteInfo<void> {
  const AboutThisAppScreenRoute({List<_i23.PageRouteInfo>? children})
      : super(
          AboutThisAppScreenRoute.name,
          initialChildren: children,
        );

  static const String name = 'AboutThisAppScreenRoute';

  static const _i23.PageInfo<void> page = _i23.PageInfo<void>(name);
}

/// generated route for
/// [_i2.ChangeDriversFullScreenDialog]
class ChangeDriversFullScreenDialogRoute
    extends _i23.PageRouteInfo<ChangeDriversFullScreenDialogRouteArgs> {
  ChangeDriversFullScreenDialogRoute({
    required _i24.IndicatorDetailsResponseHelper indicatorDetails,
    _i25.Key? key,
    int index = 0,
    bool isWhatIfDetails = false,
    bool? scadProjection,
    String? contentType,
    List<_i23.PageRouteInfo>? children,
  }) : super(
          ChangeDriversFullScreenDialogRoute.name,
          args: ChangeDriversFullScreenDialogRouteArgs(
            indicatorDetails: indicatorDetails,
            key: key,
            index: index,
            isWhatIfDetails: isWhatIfDetails,
            scadProjection: scadProjection,
            contentType: contentType,
          ),
          initialChildren: children,
        );

  static const String name = 'ChangeDriversFullScreenDialogRoute';

  static const _i23.PageInfo<ChangeDriversFullScreenDialogRouteArgs> page =
      _i23.PageInfo<ChangeDriversFullScreenDialogRouteArgs>(name);
}

class ChangeDriversFullScreenDialogRouteArgs {
  const ChangeDriversFullScreenDialogRouteArgs({
    required this.indicatorDetails,
    this.key,
    this.index = 0,
    this.isWhatIfDetails = false,
    this.scadProjection,
    this.contentType,
  });

  final _i24.IndicatorDetailsResponseHelper indicatorDetails;

  final _i25.Key? key;

  final int index;

  final bool isWhatIfDetails;

  final bool? scadProjection;

  final String? contentType;

  @override
  String toString() {
    return 'ChangeDriversFullScreenDialogRouteArgs{indicatorDetails: $indicatorDetails, key: $key, index: $index, isWhatIfDetails: $isWhatIfDetails, scadProjection: $scadProjection, contentType: $contentType}';
  }
}

/// generated route for
/// [_i3.CompareIndicatorsPage]
class CompareIndicatorsPageRoute
    extends _i23.PageRouteInfo<CompareIndicatorsPageRouteArgs> {
  CompareIndicatorsPageRoute({
    required String domainId,
    required String domainName,
    required String? contentClassificationKey,
    required String? indicatorId,
    required _i24.IndicatorDetailsResponseHelper? indicatorDetails,
    _i25.Key? key,
    List<_i23.PageRouteInfo>? children,
  }) : super(
          CompareIndicatorsPageRoute.name,
          args: CompareIndicatorsPageRouteArgs(
            domainId: domainId,
            domainName: domainName,
            contentClassificationKey: contentClassificationKey,
            indicatorId: indicatorId,
            indicatorDetails: indicatorDetails,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'CompareIndicatorsPageRoute';

  static const _i23.PageInfo<CompareIndicatorsPageRouteArgs> page =
      _i23.PageInfo<CompareIndicatorsPageRouteArgs>(name);
}

class CompareIndicatorsPageRouteArgs {
  const CompareIndicatorsPageRouteArgs({
    required this.domainId,
    required this.domainName,
    required this.contentClassificationKey,
    required this.indicatorId,
    required this.indicatorDetails,
    this.key,
  });

  final String domainId;

  final String domainName;

  final String? contentClassificationKey;

  final String? indicatorId;

  final _i24.IndicatorDetailsResponseHelper? indicatorDetails;

  final _i25.Key? key;

  @override
  String toString() {
    return 'CompareIndicatorsPageRouteArgs{domainId: $domainId, domainName: $domainName, contentClassificationKey: $contentClassificationKey, indicatorId: $indicatorId, indicatorDetails: $indicatorDetails, key: $key}';
  }
}

/// generated route for
/// [_i4.ComputePage]
class ComputePageRoute extends _i23.PageRouteInfo<ComputePageRouteArgs> {
  ComputePageRoute({
    required String title,
    required String indicatorId,
    List<_i26.Properties>? firstIndicatorList,
    List<_i26.Properties>? secondIndicatorList,
    _i27.Key? key,
    List<_i23.PageRouteInfo>? children,
  }) : super(
          ComputePageRoute.name,
          args: ComputePageRouteArgs(
            title: title,
            indicatorId: indicatorId,
            firstIndicatorList: firstIndicatorList,
            secondIndicatorList: secondIndicatorList,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'ComputePageRoute';

  static const _i23.PageInfo<ComputePageRouteArgs> page =
      _i23.PageInfo<ComputePageRouteArgs>(name);
}

class ComputePageRouteArgs {
  const ComputePageRouteArgs({
    required this.title,
    required this.indicatorId,
    this.firstIndicatorList,
    this.secondIndicatorList,
    this.key,
  });

  final String title;

  final String indicatorId;

  final List<_i26.Properties>? firstIndicatorList;

  final List<_i26.Properties>? secondIndicatorList;

  final _i27.Key? key;

  @override
  String toString() {
    return 'ComputePageRouteArgs{title: $title, indicatorId: $indicatorId, firstIndicatorList: $firstIndicatorList, secondIndicatorList: $secondIndicatorList, key: $key}';
  }
}

/// generated route for
/// [_i5.DashboardWebViewPage]
class DashboardWebViewPageRoute
    extends _i23.PageRouteInfo<DashboardWebViewPageRouteArgs> {
  DashboardWebViewPageRoute({
    required String title,
    required String uuid,
    required String url,
    required String urlAr,
    required String urlDark,
    required String urlArDark,
    _i27.Key? key,
    List<_i23.PageRouteInfo>? children,
  }) : super(
          DashboardWebViewPageRoute.name,
          args: DashboardWebViewPageRouteArgs(
            title: title,
            uuid: uuid,
            url: url,
            urlAr: urlAr,
            urlDark: urlDark,
            urlArDark: urlArDark,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'DashboardWebViewPageRoute';

  static const _i23.PageInfo<DashboardWebViewPageRouteArgs> page =
      _i23.PageInfo<DashboardWebViewPageRouteArgs>(name);
}

class DashboardWebViewPageRouteArgs {
  const DashboardWebViewPageRouteArgs({
    required this.title,
    required this.uuid,
    required this.url,
    required this.urlAr,
    required this.urlDark,
    required this.urlArDark,
    this.key,
  });

  final String title;

  final String uuid;

  final String url;

  final String urlAr;

  final String urlDark;

  final String urlArDark;

  final _i27.Key? key;

  @override
  String toString() {
    return 'DashboardWebViewPageRouteArgs{title: $title, uuid: $uuid, url: $url, urlAr: $urlAr, urlDark: $urlDark, urlArDark: $urlArDark, key: $key}';
  }
}

/// generated route for
/// [_i6.DetailsPage]
class DetailsPageRoute extends _i23.PageRouteInfo<DetailsPageRouteArgs> {
  DetailsPageRoute({
    String value = '',
    String unit = '',
    String numberUnit = '',
    String domain = '',
    String title = '',
    _i24.IndicatorDetailsResponseHelper? indicatorDetails,
    _i27.Key? key,
    bool negativeArrow = false,
    String? contentType,
    _i26.IndicatorDetailsResponse? originalIndicatorForFilter,
    String? id,
    bool fromNotification = false,
    String? comparedIndicatorName,
    List<_i23.PageRouteInfo>? children,
  }) : super(
          DetailsPageRoute.name,
          args: DetailsPageRouteArgs(
            value: value,
            unit: unit,
            numberUnit: numberUnit,
            domain: domain,
            title: title,
            indicatorDetails: indicatorDetails,
            key: key,
            negativeArrow: negativeArrow,
            contentType: contentType,
            originalIndicatorForFilter: originalIndicatorForFilter,
            id: id,
            fromNotification: fromNotification,
            comparedIndicatorName: comparedIndicatorName,
          ),
          initialChildren: children,
        );

  static const String name = 'DetailsPageRoute';

  static const _i23.PageInfo<DetailsPageRouteArgs> page =
      _i23.PageInfo<DetailsPageRouteArgs>(name);
}

class DetailsPageRouteArgs {
  const DetailsPageRouteArgs({
    this.value = '',
    this.unit = '',
    this.numberUnit = '',
    this.domain = '',
    this.title = '',
    this.indicatorDetails,
    this.key,
    this.negativeArrow = false,
    this.contentType,
    this.originalIndicatorForFilter,
    this.id,
    this.fromNotification = false,
    this.comparedIndicatorName,
  });

  final String value;

  final String unit;

  final String numberUnit;

  final String domain;

  final String title;

  final _i24.IndicatorDetailsResponseHelper? indicatorDetails;

  final _i27.Key? key;

  final bool negativeArrow;

  final String? contentType;

  final _i26.IndicatorDetailsResponse? originalIndicatorForFilter;

  final String? id;

  final bool fromNotification;

  final String? comparedIndicatorName;

  @override
  String toString() {
    return 'DetailsPageRouteArgs{value: $value, unit: $unit, numberUnit: $numberUnit, domain: $domain, title: $title, indicatorDetails: $indicatorDetails, key: $key, negativeArrow: $negativeArrow, contentType: $contentType, originalIndicatorForFilter: $originalIndicatorForFilter, id: $id, fromNotification: $fromNotification, comparedIndicatorName: $comparedIndicatorName}';
  }
}

/// generated route for
/// [_i7.DomainsPage]
class DomainsPageRoute extends _i23.PageRouteInfo<void> {
  const DomainsPageRoute({List<_i23.PageRouteInfo>? children})
      : super(
          DomainsPageRoute.name,
          initialChildren: children,
        );

  static const String name = 'DomainsPageRoute';

  static const _i23.PageInfo<void> page = _i23.PageInfo<void>(name);
}

/// generated route for
/// [_i8.FaqPage]
class FaqPageRoute extends _i23.PageRouteInfo<void> {
  const FaqPageRoute({List<_i23.PageRouteInfo>? children})
      : super(
          FaqPageRoute.name,
          initialChildren: children,
        );

  static const String name = 'FaqPageRoute';

  static const _i23.PageInfo<void> page = _i23.PageInfo<void>(name);
}

/// generated route for
/// [_i9.GlossaryScreen]
class GlossaryScreenRoute extends _i23.PageRouteInfo<GlossaryScreenRouteArgs> {
  GlossaryScreenRoute({
    String initialSearchTerm = '',
    _i27.Key? key,
    List<_i23.PageRouteInfo>? children,
  }) : super(
          GlossaryScreenRoute.name,
          args: GlossaryScreenRouteArgs(
            initialSearchTerm: initialSearchTerm,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'GlossaryScreenRoute';

  static const _i23.PageInfo<GlossaryScreenRouteArgs> page =
      _i23.PageInfo<GlossaryScreenRouteArgs>(name);
}

class GlossaryScreenRouteArgs {
  const GlossaryScreenRouteArgs({
    this.initialSearchTerm = '',
    this.key,
  });

  final String initialSearchTerm;

  final _i27.Key? key;

  @override
  String toString() {
    return 'GlossaryScreenRouteArgs{initialSearchTerm: $initialSearchTerm, key: $key}';
  }
}

/// generated route for
/// [_i10.HomeNavigation]
class HomeNavigationRoute extends _i23.PageRouteInfo<HomeNavigationRouteArgs> {
  HomeNavigationRoute({
    _i27.Key? key,
    int? screenTabIndex,
    List<_i23.PageRouteInfo>? children,
  }) : super(
          HomeNavigationRoute.name,
          args: HomeNavigationRouteArgs(
            key: key,
            screenTabIndex: screenTabIndex,
          ),
          initialChildren: children,
        );

  static const String name = 'HomeNavigationRoute';

  static const _i23.PageInfo<HomeNavigationRouteArgs> page =
      _i23.PageInfo<HomeNavigationRouteArgs>(name);
}

class HomeNavigationRouteArgs {
  const HomeNavigationRouteArgs({
    this.key,
    this.screenTabIndex,
  });

  final _i27.Key? key;

  final int? screenTabIndex;

  @override
  String toString() {
    return 'HomeNavigationRouteArgs{key: $key, screenTabIndex: $screenTabIndex}';
  }
}

/// generated route for
/// [_i11.HomePage]
class HomePageRoute extends _i23.PageRouteInfo<HomePageRouteArgs> {
  HomePageRoute({
    required void Function() navToDomainsPage,
    required void Function(String) navToProductsPage,
    _i27.Key? key,
    List<_i23.PageRouteInfo>? children,
  }) : super(
          HomePageRoute.name,
          args: HomePageRouteArgs(
            navToDomainsPage: navToDomainsPage,
            navToProductsPage: navToProductsPage,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'HomePageRoute';

  static const _i23.PageInfo<HomePageRouteArgs> page =
      _i23.PageInfo<HomePageRouteArgs>(name);
}

class HomePageRouteArgs {
  const HomePageRouteArgs({
    required this.navToDomainsPage,
    required this.navToProductsPage,
    this.key,
  });

  final void Function() navToDomainsPage;

  final void Function(String) navToProductsPage;

  final _i27.Key? key;

  @override
  String toString() {
    return 'HomePageRouteArgs{navToDomainsPage: $navToDomainsPage, navToProductsPage: $navToProductsPage, key: $key}';
  }
}

/// generated route for
/// [_i12.OnboardingScreen]
class OnboardingScreenRoute extends _i23.PageRouteInfo<void> {
  const OnboardingScreenRoute({List<_i23.PageRouteInfo>? children})
      : super(
          OnboardingScreenRoute.name,
          initialChildren: children,
        );

  static const String name = 'OnboardingScreenRoute';

  static const _i23.PageInfo<void> page = _i23.PageInfo<void>(name);
}

/// generated route for
/// [_i13.Products]
class ProductsRoute extends _i23.PageRouteInfo<ProductsRouteArgs> {
  ProductsRoute({
    _i27.Key? key,
    bool isFromDetailsScreen = false,
    List<_i23.PageRouteInfo>? children,
  }) : super(
          ProductsRoute.name,
          args: ProductsRouteArgs(
            key: key,
            isFromDetailsScreen: isFromDetailsScreen,
          ),
          initialChildren: children,
        );

  static const String name = 'ProductsRoute';

  static const _i23.PageInfo<ProductsRouteArgs> page =
      _i23.PageInfo<ProductsRouteArgs>(name);
}

class ProductsRouteArgs {
  const ProductsRouteArgs({
    this.key,
    this.isFromDetailsScreen = false,
  });

  final _i27.Key? key;

  final bool isFromDetailsScreen;

  @override
  String toString() {
    return 'ProductsRouteArgs{key: $key, isFromDetailsScreen: $isFromDetailsScreen}';
  }
}

/// generated route for
/// [_i14.ReportWebViewPage]
class ReportWebViewPageRoute
    extends _i23.PageRouteInfo<ReportWebViewPageRouteArgs> {
  ReportWebViewPageRoute({
    required String title,
    required String url,
    _i27.Key? key,
    List<_i23.PageRouteInfo>? children,
  }) : super(
          ReportWebViewPageRoute.name,
          args: ReportWebViewPageRouteArgs(
            title: title,
            url: url,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'ReportWebViewPageRoute';

  static const _i23.PageInfo<ReportWebViewPageRouteArgs> page =
      _i23.PageInfo<ReportWebViewPageRouteArgs>(name);
}

class ReportWebViewPageRouteArgs {
  const ReportWebViewPageRouteArgs({
    required this.title,
    required this.url,
    this.key,
  });

  final String title;

  final String url;

  final _i27.Key? key;

  @override
  String toString() {
    return 'ReportWebViewPageRouteArgs{title: $title, url: $url, key: $key}';
  }
}

/// generated route for
/// [_i15.SearchScreen]
class SearchScreenRoute extends _i23.PageRouteInfo<SearchScreenRouteArgs> {
  SearchScreenRoute({
    _i27.Key? key,
    _i15.SearchTypes? type,
    String? contentType,
    String? initialNodeIdForComparison,
    List<_i23.PageRouteInfo>? children,
  }) : super(
          SearchScreenRoute.name,
          args: SearchScreenRouteArgs(
            key: key,
            type: type,
            contentType: contentType,
            initialNodeIdForComparison: initialNodeIdForComparison,
          ),
          initialChildren: children,
        );

  static const String name = 'SearchScreenRoute';

  static const _i23.PageInfo<SearchScreenRouteArgs> page =
      _i23.PageInfo<SearchScreenRouteArgs>(name);
}

class SearchScreenRouteArgs {
  const SearchScreenRouteArgs({
    this.key,
    this.type,
    this.contentType,
    this.initialNodeIdForComparison,
  });

  final _i27.Key? key;

  final _i15.SearchTypes? type;

  final String? contentType;

  final String? initialNodeIdForComparison;

  @override
  String toString() {
    return 'SearchScreenRouteArgs{key: $key, type: $type, contentType: $contentType, initialNodeIdForComparison: $initialNodeIdForComparison}';
  }
}

/// generated route for
/// [_i16.Settings]
class SettingsRoute extends _i23.PageRouteInfo<void> {
  const SettingsRoute({List<_i23.PageRouteInfo>? children})
      : super(
          SettingsRoute.name,
          initialChildren: children,
        );

  static const String name = 'SettingsRoute';

  static const _i23.PageInfo<void> page = _i23.PageInfo<void>(name);
}

/// generated route for
/// [_i17.SplashPage]
class SplashPageRoute extends _i23.PageRouteInfo<SplashPageRouteArgs> {
  SplashPageRoute({
    bool toAnimateLogo = true,
    _i27.Key? key,
    List<_i23.PageRouteInfo>? children,
  }) : super(
          SplashPageRoute.name,
          args: SplashPageRouteArgs(
            toAnimateLogo: toAnimateLogo,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'SplashPageRoute';

  static const _i23.PageInfo<SplashPageRouteArgs> page =
      _i23.PageInfo<SplashPageRouteArgs>(name);
}

class SplashPageRouteArgs {
  const SplashPageRouteArgs({
    this.toAnimateLogo = true,
    this.key,
  });

  final bool toAnimateLogo;

  final _i27.Key? key;

  @override
  String toString() {
    return 'SplashPageRouteArgs{toAnimateLogo: $toAnimateLogo, key: $key}';
  }
}

/// generated route for
/// [_i18.TermsAndConditionsScreen]
class TermsAndConditionsScreenRoute extends _i23.PageRouteInfo<void> {
  const TermsAndConditionsScreenRoute({List<_i23.PageRouteInfo>? children})
      : super(
          TermsAndConditionsScreenRoute.name,
          initialChildren: children,
        );

  static const String name = 'TermsAndConditionsScreenRoute';

  static const _i23.PageInfo<void> page = _i23.PageInfo<void>(name);
}

/// generated route for
/// [_i19.ThemeIndicatorsScreen]
class ThemeIndicatorsScreenRoute
    extends _i23.PageRouteInfo<ThemeIndicatorsScreenRouteArgs> {
  ThemeIndicatorsScreenRoute({
    required String title,
    required _i28.DomainModel domain,
    required _i29.DomainClassificationModel classification,
    required _i30.Subthemes subTheme,
    required _i30.ThemeSubThemeResponse subDomain,
    _i27.Key? key,
    _i30.ScreenerConfiguration? screenerConfiguration,
    bool isFromMainScreen = false,
    List<_i23.PageRouteInfo>? children,
  }) : super(
          ThemeIndicatorsScreenRoute.name,
          args: ThemeIndicatorsScreenRouteArgs(
            title: title,
            domain: domain,
            classification: classification,
            subTheme: subTheme,
            subDomain: subDomain,
            key: key,
            screenerConfiguration: screenerConfiguration,
            isFromMainScreen: isFromMainScreen,
          ),
          initialChildren: children,
        );

  static const String name = 'ThemeIndicatorsScreenRoute';

  static const _i23.PageInfo<ThemeIndicatorsScreenRouteArgs> page =
      _i23.PageInfo<ThemeIndicatorsScreenRouteArgs>(name);
}

class ThemeIndicatorsScreenRouteArgs {
  const ThemeIndicatorsScreenRouteArgs({
    required this.title,
    required this.domain,
    required this.classification,
    required this.subTheme,
    required this.subDomain,
    this.key,
    this.screenerConfiguration,
    this.isFromMainScreen = false,
  });

  final String title;

  final _i28.DomainModel domain;

  final _i29.DomainClassificationModel classification;

  final _i30.Subthemes subTheme;

  final _i30.ThemeSubThemeResponse subDomain;

  final _i27.Key? key;

  final _i30.ScreenerConfiguration? screenerConfiguration;

  final bool isFromMainScreen;

  @override
  String toString() {
    return 'ThemeIndicatorsScreenRouteArgs{title: $title, domain: $domain, classification: $classification, subTheme: $subTheme, subDomain: $subDomain, key: $key, screenerConfiguration: $screenerConfiguration, isFromMainScreen: $isFromMainScreen}';
  }
}

/// generated route for
/// [_i20.ThemesPage]
class ThemesPageRoute extends _i23.PageRouteInfo<ThemesPageRouteArgs> {
  ThemesPageRoute({
    String? domainId,
    _i27.Key? key,
    List<_i23.PageRouteInfo>? children,
  }) : super(
          ThemesPageRoute.name,
          args: ThemesPageRouteArgs(
            domainId: domainId,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'ThemesPageRoute';

  static const _i23.PageInfo<ThemesPageRouteArgs> page =
      _i23.PageInfo<ThemesPageRouteArgs>(name);
}

class ThemesPageRouteArgs {
  const ThemesPageRouteArgs({
    this.domainId,
    this.key,
  });

  final String? domainId;

  final _i27.Key? key;

  @override
  String toString() {
    return 'ThemesPageRouteArgs{domainId: $domainId, key: $key}';
  }
}

/// generated route for
/// [_i21.UserGuideScreen]
class UserGuideScreenRoute extends _i23.PageRouteInfo<void> {
  const UserGuideScreenRoute({List<_i23.PageRouteInfo>? children})
      : super(
          UserGuideScreenRoute.name,
          initialChildren: children,
        );

  static const String name = 'UserGuideScreenRoute';

  static const _i23.PageInfo<void> page = _i23.PageInfo<void>(name);
}

/// generated route for
/// [_i22.WhatIfDetailsPage]
class WhatIfDetailsPageRoute
    extends _i23.PageRouteInfo<WhatIfDetailsPageRouteArgs> {
  WhatIfDetailsPageRoute({
    required String nodeId,
    _i27.Key? key,
    _i24.IndicatorDetailsResponseHelper? indicatorDetails,
    _i26.IndicatorDetailsResponse? originalIndicatorData,
    List<_i23.PageRouteInfo>? children,
  }) : super(
          WhatIfDetailsPageRoute.name,
          args: WhatIfDetailsPageRouteArgs(
            nodeId: nodeId,
            key: key,
            indicatorDetails: indicatorDetails,
            originalIndicatorData: originalIndicatorData,
          ),
          initialChildren: children,
        );

  static const String name = 'WhatIfDetailsPageRoute';

  static const _i23.PageInfo<WhatIfDetailsPageRouteArgs> page =
      _i23.PageInfo<WhatIfDetailsPageRouteArgs>(name);
}

class WhatIfDetailsPageRouteArgs {
  const WhatIfDetailsPageRouteArgs({
    required this.nodeId,
    this.key,
    this.indicatorDetails,
    this.originalIndicatorData,
  });

  final String nodeId;

  final _i27.Key? key;

  final _i24.IndicatorDetailsResponseHelper? indicatorDetails;

  final _i26.IndicatorDetailsResponse? originalIndicatorData;

  @override
  String toString() {
    return 'WhatIfDetailsPageRouteArgs{nodeId: $nodeId, key: $key, indicatorDetails: $indicatorDetails, originalIndicatorData: $originalIndicatorData}';
  }
}
