import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/extentions/string_extentions.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

class CheckBoxTextRow extends StatelessWidget {
  const CheckBoxTextRow({
    required this.title,
    this.titleColor,
    required this.isSelected,
    required this.onChanged,
    this.isRadioButton = false,
    super.key,
    this.isDefaultValue = false,
    this.isDisable = false,
    this.padding = EdgeInsets.zero,
  });
  final EdgeInsets padding;
  final String title;
  final Color? titleColor;
  final bool isSelected;
  final Function onChanged;
  final bool isRadioButton;
  final bool isDefaultValue;
  final bool isDisable;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Opacity(
      opacity: isDisable ? 0.5 : 1,
      child: AbsorbPointer(
        absorbing: isDefaultValue || isDisable ? true : false,
        child: InkWell(
          onTap: () => onChanged(),
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          child: Padding(
            padding: padding,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (!isRadioButton) ...[
                  SvgPicture.asset(
                    isDefaultValue
                        ? AppImages.icCheckBoxTickGrey
                        : isSelected
                            ? isLightMode
                                ? AppImages.icCheckBoxTick
                                : 'assets/images/checkbox-tick-dark.svg'
                            : AppImages.icCheckBoxUnTick,
                    width: 19,
                    height: 19,
                  ),
                ] else ...[
                  SvgPicture.asset(
                    isSelected
                        ? isLightMode
                            ? AppImages.icRadioSelect
                            : 'assets/images/radio-select-dart.svg'
                        : AppImages.icRadioUnSelect,
                    width: 19,
                    height: 19,
                  ),
                ],
                const SizedBox(width: 10),
                Flexible(
                  child: Text(
                    title.capitalize(),
                    style: TextStyle(
                      color: titleColor ??
                          (isLightMode
                              ? AppColors.blackTextTile
                              : AppColors.white),
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      height: 1.2,
                    ),
                    textScaler: TextScaler.linear(textScaleFactor.value),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
