import 'package:json_annotation/json_annotation.dart';

part 'common_api_response.g.dart';

@JsonSerializable()
class CommonApiResponse {
  String? status;
  String? message;

  CommonApiResponse(this.status, this.message);

  factory CommonApiResponse.fromJson(Map<String, dynamic> json) =>
      _$CommonApiResponseFromJson(json);

  Map<String, dynamic> toJson() => _$CommonApiResponseToJson(this);
}
