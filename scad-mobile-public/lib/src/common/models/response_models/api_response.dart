class ApiResponse1<T> {
  void success(T res) {
    isSuccess = true;
    message = 'success';
    response = res;
  }

  void error(String msg) {
    isSuccess = false;
    response = null;
    message = msg;
  }

  late bool isSuccess;
  late String message;
  late T? response;
}
class ApiResponse {
  ApiResponse.success(this.response) {
    isSuccess = true;
    message = '';
  }

  ApiResponse.error(this.message) {
    isSuccess = false;
    response = {};
  }

  late bool isSuccess;
  late String message;
  late Map<String, dynamic> response;
}
