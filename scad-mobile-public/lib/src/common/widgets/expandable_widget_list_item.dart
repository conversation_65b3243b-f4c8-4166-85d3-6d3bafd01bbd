import 'package:flutter/material.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

class ExpandableWidgetListItem extends StatelessWidget {
  const ExpandableWidgetListItem(
      {required this.title,
      required this.onTap,
      super.key,
      this.leadingIcon,
      this.trailingIcon,});

  final String title;
  final Widget? trailingIcon;
  final Widget? leadingIcon;
  final void Function() onTap;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Material(
      color: isLightMode ? AppColors.whiteShade8 : AppColors.blueShade36,
      borderRadius: BorderRadius.circular(10),
      child: InkWell(
        borderRadius: BorderRadius.circular(10),
        onTap: onTap,
        child: Container(
          clipBehavior: Clip.antiAlias,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
          ),
          padding: const EdgeInsets.only(
            left: 14,
            top: 5,
            bottom: 5,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              leadingIcon ?? const SizedBox(),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Text(
                    title,
                    maxLines: 3,
                    style: TextStyle(
                      color: isLightMode
                          ? AppColors.blueGreyShade1
                          : AppColors.white,
                      fontWeight: FontWeight.w500,
                    ),
                    textScaler: TextScaler.linear(textScaleFactor.value),
                  ),
                ),
              ),
              trailingIcon ?? const SizedBox(),
            ],
          ),
        ),
      ),
    );
  }
}
