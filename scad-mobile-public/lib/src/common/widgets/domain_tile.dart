import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/widgets/tile_custom_clipper.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';

import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

class DomainTile extends StatelessWidget {
  const DomainTile({
    required this.domain,
    required this.icon,
    required this.onTap,
    this.isSelected,
    super.key,
  });

  final String? domain;
  final String? icon;
  final bool? isSelected;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    final rtl = DeviceType.isDirectionRTL(context);
    return GestureDetector(
      onTap: onTap,
      child: Stack(
        children: [
          Positioned(
            right: rtl ? null : 0,
            left: rtl ? 0 : null,
            top: 0,
            height: 30,
            width: 30,
            child: Container(
              decoration: ShapeDecoration(
                gradient: LinearGradient(
                  begin: const Alignment(-0.65, -0.76),
                  end: const Alignment(0.65, 0.76),
                  colors: [AppColors.blueShade11, AppColors.blueShade12],
                ),
                shape: const OvalBorder(),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(100),
                  onTap: onTap,
                  child: Padding(
                    padding: const EdgeInsets.all(5),
                    child: RotatedBox(
                      quarterTurns: DeviceType.isDirectionRTL(context) ? -1 : 0,
                      child: Icon(
                        Icons.arrow_outward_rounded,
                        color: AppColors.white,
                        size: 18,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
          Container(
            decoration: const BoxDecoration(
              boxShadow: [
                BoxShadow(
                  color: Color(0x0A4E4F51),
                  blurRadius: 5,
                  offset: Offset(4, 1),
                ),
              ],
            ),
            child: ClipPath(
              clipper: TileCustomClipper(isRtl: rtl),
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(14),
                decoration: BoxDecoration(
                  color: isLightMode
                      ? const Color(0xFFF9F9F9)
                      : AppColors.inidcatorCardDark,
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 50,
                      alignment: Alignment.center,
                      child: SvgPicture.network(
                        icon ?? '',
                        width: 50,
                        height: 50,
                      ),
                    ),
                    if (!rtl) const SizedBox(width: 10),
                    Flexible(
                      child: Text(
                        domain ?? '-',
                        style: TextStyle(
                          color: isLightMode
                              ? AppColors.blackShade1
                              : AppColors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                        textScaler: TextScaler.linear(textScaleFactor.value),
                      ),
                    ),
                    const SizedBox(width: 40),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
