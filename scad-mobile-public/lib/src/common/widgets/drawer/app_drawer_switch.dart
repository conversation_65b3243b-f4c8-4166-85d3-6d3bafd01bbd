import 'package:flutter/material.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';

class AppDrawerSwitch extends StatefulWidget {
  const AppDrawerSwitch({
    required this.onTap,
    required this.value,
    required this.textOff,
    required this.textOn,
    required this.iconOff,
    required this.iconOn,
    super.key,
  });

  final bool value;
  final void Function(bool isOn) onTap;
  final String textOff;
  final String textOn;
  final Widget iconOff;
  final Widget iconOn;

  @override
  _AppDrawerSwitchState createState() => _AppDrawerSwitchState();
}

class _AppDrawerSwitchState extends State<AppDrawerSwitch>
    with SingleTickerProviderStateMixin {
  late AnimationController animationController;
  late Animation<double> animation;
  late bool turnState;
  late double value;

  final double buttonSize = 36;
  final double width = 121;

  @override
  void dispose() {
    animationController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    value = widget.value ? 1 : 0;
    animationController = AnimationController(
      vsync: this,
      lowerBound: 0.0,
      upperBound: 1.0,
      duration: const Duration(milliseconds: 300),
    );
    animation =
        CurvedAnimation(parent: animationController, curve: Curves.easeInOut);
    animationController.addListener(() {
      setState(() {
        value = animation.value;
      });
    });
    turnState = widget.value;

    // Executes a function only one time after the layout is completed.
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        if (turnState) {
          value = widget.value ? 1 : 0;
          // animationController.forward();
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return GestureDetector(
      onTap: () {
        _action();
      },
      child: Container(
        width: width,
        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2.5),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(50),
          color: isLightMode ? AppColors.white : const Color(0xFF3F5D72),
        ),
        child: Stack(
          children: <Widget>[
            Transform.translate(
              offset: DeviceType.isDirectionRTL(context)
                  ? Offset(-10 * value, 0)
                  : Offset(10 * value, 0), //original
              child: Opacity(
                opacity: (1 - value).clamp(0.0, 1.0),
                child: Container(
                  padding: const EdgeInsets.fromLTRB(16, 0, 20, 0),
                  alignment: DeviceType.isDirectionRTL(context)
                      ? Alignment.centerLeft
                      : Alignment.centerRight,
                  height: buttonSize,
                  child: Text(
                    widget.textOff,
                    style: AppTextStyles.s16w4cBlueGreyShade1.copyWith(
                      color: isLightMode ? AppColors.black : AppColors.white,
                    ),
                    textScaler: TextScaler.linear(textScaleFactor.value),
                  ),
                ),
              ),
            ),
            Transform.translate(
              offset: DeviceType.isDirectionRTL(context)
                  ? Offset(-10 * (1 - value), 0)
                  : Offset(10 * (1 - value), 0), //original
              child: Opacity(
                opacity: value.clamp(0.0, 1.0),
                child: Container(
                  // padding: DeviceType.isDirectionRTL(context)
                  //     ? EdgeInsets.only(right: 5)
                  //     : EdgeInsets.only(left: 5),
                  padding: const EdgeInsets.fromLTRB(12, 0, 16, 0),
                  alignment: DeviceType.isDirectionRTL(context)
                      ? Alignment.centerRight
                      : Alignment.centerLeft,
                  height: buttonSize,
                  child: Text(
                    widget.textOn,
                    style: AppTextStyles.s16w4cBlueGreyShade1.copyWith(
                      color: isLightMode ? AppColors.black : AppColors.white,
                    ),
                    textScaler: TextScaler.linear(textScaleFactor.value),
                  ),
                ),
              ),
            ),
            Transform.translate(
              offset: DeviceType.isDirectionRTL(context)
                  ? Offset((-width + buttonSize + 8) * value, 0)
                  : Offset((width - (buttonSize + 8)) * value, 0),
              child: Transform.rotate(
                angle: 0,
                child: Container(
                  height: buttonSize,
                  width: buttonSize,
                  alignment: Alignment.center,
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white,
                  ),
                  child: Stack(
                    children: <Widget>[
                      Center(
                        child: Opacity(
                          opacity: (1 - value).clamp(0.0, 1.0),
                          child: widget.iconOff,
                        ),
                      ),
                      Center(
                        child: Opacity(
                          opacity: value.clamp(0.0, 1.0),
                          child: widget.iconOn,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  _action() {
    _determine(changeState: true);
  }

  _determine({bool changeState = false}) {
    setState(() {
      if (changeState) turnState = !turnState;
      (turnState)
          ? animationController.forward()
          : animationController.reverse();
      Future.delayed(const Duration(milliseconds: 300), () {
        widget.onTap(turnState);
      });
    });
  }
}
