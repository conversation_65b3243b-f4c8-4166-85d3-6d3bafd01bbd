part of '../app_drawer_part.dart';

final appDrawerController = AppDrawerController();

class AppDrawer extends StatefulWidget {
  const AppDrawer({
    required this.child,
    this.initSetting = false,
    super.key,
    this.disabledGestures = true,
  });

  final Widget child;
  final bool initSetting;
  final bool disabledGestures;

  @override
  AppDrawerState createState() => AppDrawerState();
}

class AppDrawerState extends State<AppDrawer> with TickerProviderStateMixin {
  double openRatio = 0.68;
  double openScale = 0.68;
  Duration animationDuration = const Duration(milliseconds: 300);
  bool rtlOpening = false;

  AnimationController? _animationController;

  late Animation<double> _drawerScaleAnimation;
  late Animation<Offset> _childSlideAnimation;
  late Animation<double> _childScaleAnimation;
  late Animation<Decoration> _childDecorationAnimation;

  late double _offsetValue;
  late Offset _freshPosition;

  bool _captured = false;
  Offset? _startPosition;

  ValueNotifier<bool> isEnglish = ValueNotifier(true);
  final List<Locale> languages = [
    const Locale('en', ''),
    const Locale('ar', ''),
  ];

  @override
  void initState() {
    super.initState();

    _initControllers();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        if (widget.initSetting) {
          context.read<SettingBloc>().add(
                LanguageUpdateEvent(
                  languageUpdateRequest: LanguageUpdateRequest(
                    name: HiveUtilsSettings.getAppLanguage(),
                    deviceRegId: HiveUtilsPersistent.getDeviceToken(),
                  ),
                ),
              );
          context.read<SettingBloc>().add(const DefaultSettingLoadEvent());

          final int selected = languages.indexWhere(
            (element) => element.languageCode == context.locale.languageCode,
          );
          isEnglish.value = selected == 0;
        } else {
          isEnglish.value = HiveUtilsSettings.getAppLanguage() == 'en';
        }
      }
    });
  }

  @override
  void dispose() {
    _animationController?.dispose();
    _animationController = null;
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant AppDrawer oldWidget) {
    _initControllers();

    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    rtlOpening = DeviceType.isDirectionRTL(context);
    return Material(
      color: isLightMode ? AppColors.blueShade13 : AppColors.drawerDark,
      child: Container(
        decoration: BoxDecoration(
          gradient: isLightMode
              ? LinearGradient(
                  colors: [
                    AppColors.blueGradientShade3,
                    AppColors.blueGradientShade4,
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                )
              : LinearGradient(
                  colors: [
                    AppColors.blueGradientShade3,
                    AppColors.blueGradientShade4,
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
        ),
        child: GestureDetector(
          onHorizontalDragStart:
              widget.disabledGestures ? null : _handleDragStart,
          onHorizontalDragUpdate:
              widget.disabledGestures ? null : _handleDragUpdate,
          onHorizontalDragEnd: widget.disabledGestures ? null : _handleDragEnd,
          onHorizontalDragCancel:
              widget.disabledGestures ? null : _handleDragCancel,
          child: Stack(
            children: [
              // widget.backdrop,
              Positioned(
                right: 23,
                top: 87,
                child: Material(
                  color: Colors.transparent,
                  borderRadius: BorderRadius.circular(30),
                  child: InkWell(
                    borderRadius: BorderRadius.circular(30),
                    onTap: appDrawerController.hideDrawer,
                    child: Container(
                      width: 30,
                      height: 30,
                      margin: const EdgeInsets.all(8),
                      padding: const EdgeInsets.all(7),
                      decoration: BoxDecoration(
                        color: isLightMode
                            ? AppColors.white
                            : AppColors.blueShade12,
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: SvgPicture.asset(
                        colorFilter: isLightMode
                            ? null
                            : ColorFilter.mode(
                                AppColors.white, BlendMode.srcIn,),
                        AppImages.icArrowsLeft,
                      ),
                    ),
                  ),
                ),
              ),
              Align(
                alignment:
                    rtlOpening ? Alignment.centerRight : Alignment.centerLeft,
                child: SafeArea(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: FractionallySizedBox(
                          widthFactor: openRatio + .1,
                          child: ScaleTransition(
                            scale: _drawerScaleAnimation,
                            alignment: rtlOpening
                                ? Alignment.centerLeft
                                : Alignment.centerRight,
                            child: RepaintBoundary(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.stretch,
                                children: [
                                  Expanded(
                                    child: Padding(
                                      padding: const EdgeInsets.all(8),
                                      child: Align(
                                        alignment: Alignment.topLeft,
                                        // child: widget.drawer,
                                        child: ListView(
                                          padding: const EdgeInsets.only(top: 60),
                                          shrinkWrap: true,
                                          children: [
                                            _drawerItem(
                                              AppImages.icHome2,
                                              LocaleKeys.home.tr(),
                                              isLightMode,
                                              onTap: () {
                                                _removeFragmentRoutes();
                                                AutoRouter.of(context)
                                                    .replaceAll([
                                                  HomeNavigationRoute(),
                                                ]).then((value) {
                                                  context
                                                      .read<HomeBloc>()
                                                      .add(NavToHomeEvent());
                                                });
                                              },
                                            ),
                                            // if (!isDemoMode)
                                            //   _drawerItem(
                                            //     AppImages.icGlossary,
                                            //     LocaleKeys.glossary.tr(),
                                            //     isLightMode,
                                            //     onTap: () {
                                            //       _removeFragmentRoutes();
                                            //       AutoRouter.of(context).push(
                                            //         GlossaryScreenRoute(),
                                            //       );
                                            //     },
                                            //   ),
                                              _drawerItem(
                                                AppImages.icUserGuide,
                                                LocaleKeys.userGuide.tr(),
                                                isLightMode,
                                                onTap: () {
                                                  _removeFragmentRoutes();
                                                  AutoRouter.of(context).push(
                                                    const UserGuideScreenRoute(),
                                                  );
                                                },
                                              ),
                                            _drawerItem(
                                              AppImages.icAskUsMenu,
                                              LocaleKeys.faq.tr(),
                                              isLightMode,
                                              onTap: () {
                                                _removeFragmentRoutes();
                                                AutoRouter.of(context).push(
                                                  const FaqPageRoute(),
                                                );
                                              },
                                            ),
                                            _drawerItem(
                                              AppImages.icTerms,
                                              LocaleKeys.termsAndConditions
                                                  .tr(),
                                              isLightMode,
                                              onTap: () {
                                                _removeFragmentRoutes();
                                                AutoRouter.of(context).push(
                                                  const TermsAndConditionsScreenRoute(),
                                                );
                                              },
                                            ),
                                            _drawerItem(
                                              AppImages.icDevice,
                                              LocaleKeys.aboutThisApp.tr(),
                                              isLightMode,
                                              onTap: () {
                                                _removeFragmentRoutes();
                                                AutoRouter.of(context).push(
                                                  const AboutThisAppScreenRoute(),
                                                );
                                              },
                                            ),
                                            const SizedBox(height: 12),
                                            themeSwitch(),
                                            const SizedBox(height: 20),
                                            languageSwitch(context),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                  Align(
                                    alignment: !rtlOpening
                                        ? Alignment.centerLeft
                                        : Alignment.centerRight,
                                    child: FittedBox(
                                      child: Padding(
                                        padding: const EdgeInsets.only(
                                          bottom: 20,
                                          left: 24,
                                          right: 24,
                                        ),
                                        child: Material(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          color: Colors.transparent,
                                          child: InkWell(
                                            borderRadius:
                                                BorderRadius.circular(8),
                                            onTap: () async {
                                              appDrawerController.hideDrawer();
                                              await Future.delayed(
                                                  const Duration(
                                                    milliseconds: 350,
                                                  ), () {
                                                _removeFragmentRoutes();
                                                AutoRouter.of(
                                                  context,
                                                ).push(
                                                  const SettingsRoute(),
                                                );
                                              });
                                            },
                                            child: Padding(
                                              padding: const EdgeInsets.all(12),
                                              child: Row(
                                                children: [
                                                  SvgPicture.asset(
                                                    AppImages.icSettings,
                                                    width: 30,
                                                    height: 30,
                                                    colorFilter:
                                                        ColorFilter.mode(
                                                      AppColors.white,
                                                      BlendMode.srcIn,
                                                    ),
                                                  ),
                                                  const SizedBox(
                                                    width: 10,
                                                  ),
                                                  Text(
                                                    LocaleKeys.settings.tr(),
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                    style: TextStyle(
                                                      color: AppColors.white,
                                                      fontSize: 16,
                                                      fontWeight:
                                                          FontWeight.w400,
                                                      height: 0,
                                                    ),
                                                    textScaler:
                                                        TextScaler.linear(
                                                      textScaleFactor.value,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              SlideTransition(
                position: _childSlideAnimation,
                textDirection:
                    rtlOpening ? TextDirection.rtl : TextDirection.ltr,
                child: ScaleTransition(
                  scale: _childScaleAnimation,
                  child: Builder(
                    builder: (_) {
                      final childStack = Stack(
                        children: [
                          RepaintBoundary(
                            child: widget.child,
                          ),
                          ValueListenableBuilder<DrawerState>(
                            valueListenable: appDrawerController,
                            builder: (_, value, __) {
                              if (!value.visible) {
                                return const SizedBox();
                              }

                              return Material(
                                color: Colors.transparent,
                                child: InkWell(
                                  onTap: appDrawerController.hideDrawer,
                                  highlightColor: Colors.transparent,
                                  child: Container(),
                                ),
                              );
                            },
                          ),
                        ],
                      );

                      return AnimatedBuilder(
                        animation: _childDecorationAnimation,
                        builder: (_, child) {
                          return Container(
                            clipBehavior: Clip.antiAlias,
                            decoration: _childDecorationAnimation.value,
                            child: child,
                          );
                        },
                        child: childStack,
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget themeSwitch() {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 26),
      child: Row(
        children: [
          InkWell(
            borderRadius: BorderRadius.circular(50),
            onTap: () async {
              await HiveUtilsSettings.setThemeMode(
                HiveUtilsSettings.getThemeMode() == ThemeMode.dark
                    ? ThemeMode.light
                    : ThemeMode.dark,
              );

              await HiveUtilsApiCache.clear();

              final BuildContext ctx1 = servicelocator<AppRouter>().navigatorKey.currentContext!;
              unawaited(AutoRouter.of(ctx1).replaceAll([HomeNavigationRoute()]));
            },
            child: Container(
              width: 121,
              height: 41,
              clipBehavior: Clip.antiAlias,
              decoration: ShapeDecoration(
                color: isLightMode ? AppColors.white : AppColors.blueTitleText,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(50),
                ),
                shadows: const [
                  BoxShadow(
                    color: Color(0x146F7A9C),
                    blurRadius: 50,
                    offset: Offset(0, 35),
                    spreadRadius: -23,
                  ),
                ],
              ),
              child: Stack(
                children: [
                  Positioned(
                    left: 5,
                    top: 3,
                    child: Container(
                      width: 56,
                      height: 35,
                      decoration: ShapeDecoration(
                        color: isLightMode
                            ? AppColors.blueLight
                            : Colors.transparent,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30),
                        ),
                      ),
                      child: Center(
                        child: FittedBox(
                          child: SizedBox(
                            height: 22,
                            width: 22,
                            child: SvgPicture.asset(
                              AppImages.icLightOutline,
                              colorFilter: ColorFilter.mode(
                                AppColors.white,
                                BlendMode.srcIn,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  Positioned(
                    left: 61,
                    top: 2,
                    child: Container(
                      width: 58,
                      height: 37,
                      decoration: ShapeDecoration(
                        color: isLightMode
                            ? Colors.transparent
                            : const Color(0xFF2587FC),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30),
                        ),
                      ),
                      child: Center(
                        child: FittedBox(
                          child: SizedBox(
                            height: 22,
                            width: 22,
                            child: SvgPicture.asset(
                              AppImages.icDarkOutline,
                              colorFilter: ColorFilter.mode(
                                (isLightMode
                                    ? AppColors.grey
                                    : AppColors.white),
                                BlendMode.srcIn,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
    // return Row(
    //   children: [
    //     Padding(
    //       padding: const EdgeInsets.all(26),
    //       child: AppDrawerSwitch(
    //         value: HiveUtilsSettings.getThemeMode() == ThemeMode.light,
    //         textOn: LocaleKeys.light.tr(),
    //         textOff: LocaleKeys.dark.tr(),
    //         iconOn: Container(
    //           width: 36,
    //           height: 36,
    //           padding: const EdgeInsets.all(9),
    //           decoration: const ShapeDecoration(
    //             color: Color(0xFF2587FC),
    //             shape: OvalBorder(),
    //           ),
    //           child: RotatedBox(
    //             quarterTurns: 2,
    //             child: SvgPicture.asset(
    //               AppImages.icThemeContrast,
    //             ),
    //           ),
    //         ),
    //         iconOff: Container(
    //           width: 36,
    //           height: 36,
    //           padding: const EdgeInsets.all(9),
    //           decoration: const ShapeDecoration(
    //             color: Color(0xFF2587FC),
    //             shape: OvalBorder(),
    //           ),
    //           child: SvgPicture.asset(
    //             AppImages.icThemeContrast,
    //           ),
    //         ),
    //         onTap: (bool b) {
    //           HiveUtilsSettings.setThemeMode(
    //             HiveUtilsSettings.getThemeMode() == ThemeMode.dark
    //                 ? ThemeMode.light
    //                 : ThemeMode.dark,
    //           );
    //         },
    //       ),
    //     ),
    //   ],
    // );
  }

  Widget languageSwitch(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;

    return ValueListenableBuilder(
      valueListenable: isEnglish,
      builder: (context, b, w) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 26),
          child: Row(
            children: [
              InkWell(
                borderRadius: BorderRadius.circular(50),
                onTap: () async {
                  final BuildContext ctx =
                      servicelocator<AppRouter>().navigatorKey.currentContext!;
                  try {
                    isEnglish.value = !isEnglish.value;
                    //     await ctx.setLocale(languages[isEnglish.value ? 0 : 1]);
                    //     await HiveUtilsSettings.setAppLanguage(
                    //         isEnglish.value ? 'en' : 'ar');
                    //     // appDrawerController.hideDrawer();
                    //       print('++++++++++++++${DateTime.now()} ++++++++++++++++');

                    //   print('stack1:${AutoRouter.of(ctx).stack}');

                    // AutoRouter.of(ctx).popUntil((route) => true);

                    //     // AutoRouter.of(context).removeWhere((route) => route.name == HomeNavigationRoute.name);

                    //   print('stack2:${AutoRouter.of(ctx).stack}');

                    //     // AutoRouter.of(context).push(HomeNavigationRoute());
                    //     AutoRouter.of(ctx).replaceAll([HomeNavigationRoute()]);

                    ctx.read<SettingBloc>().add(
                          LanguageUpdateEvent(
                            languageUpdateRequest: LanguageUpdateRequest(
                              name: languages[isEnglish.value ? 0 : 1]
                                  .languageCode,
                              deviceRegId: HiveUtilsPersistent.getDeviceToken(),
                            ),
                          ),
                        );
                    ctx.read<DomainsBloc>().add(const DomainsInitEvent());
                  } catch (e, s) {
                    Completer<dynamic>().completeError(e, s);
                  }
                },
                child: Container(
                  width: 121,
                  height: 41,
                  clipBehavior: Clip.antiAlias,
                  decoration: ShapeDecoration(
                    color:
                        isLightMode ? AppColors.white : AppColors.blueTitleText,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(50),
                    ),
                    shadows: const [
                      BoxShadow(
                        color: Color(0x146F7A9C),
                        blurRadius: 50,
                        offset: Offset(0, 35),
                        spreadRadius: -23,
                      ),
                    ],
                  ),
                  child: Stack(
                    children: [
                      Positioned(
                        left: 5,
                        top: 3,
                        child: Container(
                          width: 56,
                          height: 35,
                          decoration: ShapeDecoration(
                            color: isEnglish.value
                                ? isLightMode
                                    ? AppColors.blueLight
                                    : const Color(0xFF2587FC)
                                : Colors.transparent,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(30),
                            ),
                          ),
                          child: Center(
                            child: FittedBox(
                              child: SizedBox(
                                height: 12,
                                width: 18,
                                child: SvgPicture.asset(
                                  AppImages.icEnAlt,
                                  colorFilter: ColorFilter.mode(
                                    isEnglish.value
                                        ? AppColors.white
                                        : (isLightMode
                                            ? AppColors.grey
                                            : AppColors.white),
                                    BlendMode.srcIn,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                      Positioned(
                        left: 61,
                        top: 2,
                        child: Container(
                          width: 58,
                          height: 37,
                          decoration: ShapeDecoration(
                            color: isEnglish.value
                                ? Colors.transparent
                                : isLightMode
                                    ? AppColors.blueLight
                                    : const Color(0xFF2587FC),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(30),
                            ),
                          ),
                          child: Center(
                            child: FittedBox(
                              child: SizedBox(
                                height: 22,
                                width: 14,
                                child: SvgPicture.asset(
                                  AppImages.icArAlt,
                                  colorFilter: ColorFilter.mode(
                                    isEnglish.value
                                        ? (isLightMode
                                            ? AppColors.grey
                                            : AppColors.white)
                                        : AppColors.white,
                                    BlendMode.srcIn,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );

    // return Row(
    //   children: [
    //     Padding(
    //       padding: const EdgeInsets.all(26),
    //       child: AppDrawerSwitch(
    //         value: HiveUtilsSettings.getAppLanguage() == 'en',
    //         textOn: '',
    //         textOff: '',
    //         iconOn: Container(
    //           width: 60,
    //           height: 36,
    //           padding: const EdgeInsets.all(9),
    //           decoration: const ShapeDecoration(
    //             color: Color(0xFF2587FC),
    //             shape: OvalBorder(),
    //           ),
    //           child: SvgPicture.asset(
    //             AppImages.icEn,
    //             colorFilter: ColorFilter.mode(
    //               AppColors.white,
    //               BlendMode.srcIn,
    //             ),
    //           ),
    //         ),
    //         iconOff: Container(
    //           width: 60,
    //           height: 36,
    //           padding: const EdgeInsets.all(9),
    //           decoration: const ShapeDecoration(
    //             color: Color(0xFF2587FC),
    //             shape: OvalBorder(),
    //           ),
    //           child: SvgPicture.asset(
    //             AppImages.icAr,
    //             colorFilter: ColorFilter.mode(
    //               AppColors.white,
    //               BlendMode.srcIn,
    //             ),
    //           ),
    //         ),
    //         onTap: (bool isEnglish) async {
    //           if (mounted) {
    //             await context.setLocale(languages[isEnglish ? 0 : 1]);
    //             // appDrawerController.hideDrawer();
    //             context.read<SettingBloc>().add(
    //                   LanguageUpdateEvent(
    //                     languageUpdateRequest: LanguageUpdateRequest(
    //                       name: languages[isEnglish ? 0 : 1].languageCode,
    //                       deviceRegId: HiveUtilsPersistent.getDeviceToken(),
    //                     ),
    //                   ),
    //                 );
    //             context.read<DomainsBloc>().add(const DomainsInitEvent());
    //           }
    //         },
    //       ),
    //     ),
    //   ],
    // );
  }

  void _initControllers() {
    appDrawerController
      ..removeListener(_handleControllerChanged)
      ..addListener(_handleControllerChanged);
    try {
      _animationController = AnimationController(
        vsync: this,
        value: appDrawerController.value.visible ? 1 : 0,
      );

      _animationController?.reverseDuration =
          _animationController?.duration = animationDuration;
    } catch (e) {
      // error
    }

    final parentAnimation = CurvedAnimation(
      curve: Curves.easeInOut,
      parent: _animationController!,
    );

    _drawerScaleAnimation = Tween<double>(
      begin: 0.75,
      end: 1,
    ).animate(parentAnimation);

    _childSlideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: Offset(openRatio, 0),
    ).animate(parentAnimation);

    _childScaleAnimation = Tween<double>(
      begin: 1,
      end: openScale,
    ).animate(parentAnimation);

    _childDecorationAnimation = DecorationTween(
      begin: const BoxDecoration(),
      end: const BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(16)),
        //   boxShadow: [
        //   BoxShadow(
        //     color: Colors.red.withOpacity(0.01),
        //     spreadRadius: 1,
        //     blurRadius: 0.1,
        //     offset: Offset(-20, 10), // changes position of shadow
        //   ),
        // ],
      ),
    ).animate(parentAnimation);
  }

  void _handleControllerChanged() {
    if (_animationController == null) return;
    appDrawerController.value.visible
        ? _animationController?.forward()
        : _animationController?.reverse();
  }

  void _handleDragStart(DragStartDetails details) {
    _captured = true;
    _startPosition = details.globalPosition;
    _offsetValue = _animationController!.value;
  }

  void _handleDragUpdate(DragUpdateDetails details) {
    if (!_captured) return;

    final screenSize = MediaQuery.of(context).size;

    _freshPosition = details.globalPosition;

    final diff = (_freshPosition - _startPosition!).dx;

    _animationController!.value = _offsetValue +
        (diff / (screenSize.width * openRatio)) * (rtlOpening ? -1 : 1);
  }

  void _handleDragEnd(DragEndDetails details) {
    if (!_captured) return;

    _captured = false;

    if (_animationController!.value >= 0.5) {
      if (appDrawerController.value.visible) {
        _animationController!.forward();
      } else {
        appDrawerController.showDrawer();
      }
    } else {
      if (!appDrawerController.value.visible) {
        _animationController!.reverse();
      } else {
        appDrawerController.hideDrawer();
      }
    }
  }

  void _handleDragCancel() {
    _captured = false;
  }

  Widget _drawerItem(
    String icon,
    String title,
    bool isLightMode, {
    VoidCallback? onTap,
  }) {
    return InkWell(
      borderRadius: BorderRadius.circular(8),
      onTap: () async {
        appDrawerController.hideDrawer();
        await Future.delayed(
          const Duration(
            milliseconds: 350,
          ),
          onTap,
        );
      },
      child: Container(
        margin: const EdgeInsets.all(24),
        width: double.maxFinite,
        height: 25,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(
              icon,
              width: 25,
              height: 25,
              colorFilter: ColorFilter.mode(
                AppColors.white,
                BlendMode.srcIn,
              ),
            ),
            const SizedBox(width: 10),
            Flexible(
              child: Text(
                title,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  color: isLightMode ? AppColors.white : AppColors.greyShade8,
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                  height: 0,
                ),
                textScaler: TextScaler.linear(textScaleFactor.value),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _removeFragmentRoutes() {
    AutoRouter.of(context).removeWhere(
      (route) =>
          route.name == GlossaryScreenRoute.name ||
          route.name == UserGuideScreenRoute.name ||
          route.name == TermsAndConditionsScreenRoute.name ||
          route.name == AboutThisAppScreenRoute.name ||
          route.name == SettingsRoute.name ||
          route.name == FaqPageRoute.name,
    );
  }
}
