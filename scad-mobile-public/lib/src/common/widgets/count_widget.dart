import 'package:flutter/material.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

class CountWidget extends StatelessWidget {
  const CountWidget({
    required this.count,
    super.key,
  });

  final int count;

  @override
  Widget build(BuildContext context) {
    final bool isLightTheme =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Container(
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: isLightTheme ? AppColors.blueGreyShade2 : AppColors.blueGreyShade2,
        shape: BoxShape.circle,
      ),
      width: textScaleFactor.value* 20,
      height:textScaleFactor.value*  20,
      //padding: const EdgeInsets.all(6),
      child: Text(
        count.toString(),
        style: TextStyle(
          color: AppColors.white,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
        textScaler: TextScaler.linear(textScaleFactor.value),
      ),
    );
  }
}
