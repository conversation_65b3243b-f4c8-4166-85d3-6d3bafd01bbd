// import 'package:auto_route/auto_route.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:scad_mobile/main.dart';
// import 'package:scad_mobile/route_manager/route_imports.gr.dart';
// import 'package:scad_mobile/src/common/widgets/card_custom_clipper.dart';
// import 'package:scad_mobile/src/common/widgets/indicator_value.dart';
// import 'package:scad_mobile/src/features/my_apps/presentation/pages/add_to_myapps.dart';
// import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
// import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
//
// class IndicatorCard extends StatelessWidget {
//   const IndicatorCard({
//     required this.title,
//     required this.domain,
//     required this.child,
//     required this.value,
//     required this.unit,
//     required this.numberUnit,
//     required this.series,
//     super.key,
//     // this.onTap,
//     this.utilities,
//     this.openButton,
//   });
//
//   final String domain;
//   final String title;
//   final Widget? child;
//
//   // final VoidCallback? onTap;
//   final Widget? utilities;
//   final Widget? openButton;
//
//   final String value;
//   final String unit;
//   final String numberUnit;
//
//   final List<Map<String, dynamic>> series;
//
//   @override
//   Widget build(BuildContext context) {
//     return SizedBox(
//       width: double.infinity,
//       // height: child == null ? 260 : null,
//       child: Stack(
//         children: [
//           Positioned(
//             right: 0,
//             top: 0,
//             height: 40,
//             width: 40,
//             child: openButton ??
//                 ElevatedButton(
//                   style: ElevatedButton.styleFrom(
//                     backgroundColor: AppColors.blueDark,
//                     elevation: 0,
//                     shape: const CircleBorder(),
//                     padding: const EdgeInsets.all(5),
//                   ),
//                   onPressed: () {},
//                   child: Icon(
//                     Icons.arrow_outward_rounded,
//                     color: AppColors.white,
//                   ),
//                 ),
//           ),
//           ClipPath(
//             clipper: CardCustomClipper(),
//             child: SizedBox(
//               width: double.infinity,
//               child: Card(
//                 shape: RoundedRectangleBorder(
//                   borderRadius: BorderRadius.circular(20),
//                 ),
//                 margin: EdgeInsets.zero,
//                 color: AppColors.white,
//                 elevation: 0,
//                 child: Padding(
//                   padding: const EdgeInsets.all(15),
//                   child: SizedBox(
//                     height: 300,
//                     child: Column(
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         SizedBox(
//                           width: MediaQuery.of(context).size.width * .68,
//                           child: Row(
//                             children: [
//                               Container(
//                                 padding: const EdgeInsets.all(8),
//                                 decoration: BoxDecoration(
//                                   color: AppColors.green,
//                                   borderRadius: BorderRadius.circular(100),
//                                 ),
//                                 child: SvgPicture.asset(
//                                   'assets/images/domain_economy.svg',
//                                   width: 10,
//                                   colorFilter: const ColorFilter.mode(
//                                     Colors.white,
//                                     BlendMode.srcIn,
//                                   ),
//                                 ),
//                               ),
//                               const SizedBox(width: 10),
//                               Expanded(
//                                   child: Text(
//                                 domain,
//                                 maxLines: 1,
//                                 overflow: TextOverflow.ellipsis,
//                                 style: TextStyle(
//                                     color: AppColors.grey,
//                                     fontSize: 14,
//                                     fontWeight: FontWeight.w400),
//                                 textScaler:
//                                     TextScaler.linear(textScaleFactor.value),
//                               )),
//                               const SizedBox(width: 10),
//                               utilities ??
//                                   Row(
//                                     children: [
//                                       // InkWell(
//                                       //   onTap: () {},
//                                       //   child: SvgPicture.asset(
//                                       //       AppImages.icDownload),
//                                       // ),
//                                       // const SizedBox(
//                                       //   width: 12,
//                                       // ),
//                                       InkWell(
//                                         onTap: () {},
//                                         child: SvgPicture.asset(
//                                           AppImages.icBell,
//                                         ),
//                                       ),
//                                       const SizedBox(width: 12),
//                                       InkWell(
//                                         onTap: () {
//                                           // showModalBottomSheet<void>(
//                                           //   isScrollControlled: true,
//                                           //   constraints: BoxConstraints(
//                                           //     minHeight: 100,
//                                           //     maxHeight:
//                                           //         MediaQuery.sizeOf(context)
//                                           //                 .height *
//                                           //             .90,
//                                           //   ),
//                                           //   shape: const RoundedRectangleBorder(
//                                           //     borderRadius: BorderRadius.only(
//                                           //       topLeft: Radius.circular(20),
//                                           //       topRight: Radius.circular(20),
//                                           //     ),
//                                           //   ),
//                                           //   backgroundColor: AppColors.white,
//                                           //   context: context,
//                                           //   builder: (context) {
//                                           //     return AddToMyApps(
//                                           //       title: title,
//                                           //       domainName: domain,
//                                           //     );
//                                           //   },
//                                           // );
//                                         },
//                                         child: SvgPicture.asset(
//                                           AppImages.icAddToMyAppsOff,
//                                         ),
//                                       ),
//                                     ],
//                                   ),
//                             ],
//                           ),
//                         ),
//                         const SizedBox(height: 10),
//                         Row(
//                           mainAxisSize: MainAxisSize.min,
//                           children: [
//                             Flexible(
//                               child: Text(
//                                 title,
//                                 style: TextStyle(
//                                   color: AppColors.black,
//                                   fontSize: 14,
//                                   fontWeight: FontWeight.w500,
//                                 ),
//                                 // maxLines: 2,
//                                 overflow: TextOverflow.ellipsis,
//                                 textScaler:
//                                     TextScaler.linear(textScaleFactor.value),
//                               ),
//                             ),
//                             const SizedBox(width: 8),
//                             SvgPicture.asset(AppImages.icOfficialActive),
//                           ],
//                         ),
//                         const SizedBox(
//                           height: 5,
//                         ),
//                         IndicatorValue(
//                           negativeArrow: num.parse(value) < 0,
//                           value: value,
//                           unit: unit,
//                           numberUnit: numberUnit,
//                         ),
//                         const SizedBox(
//                           height: 5,
//                         ),
//                         child ?? SizedBox(),
//                       ],
//                     ),
//                   ),
//                 ),
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
