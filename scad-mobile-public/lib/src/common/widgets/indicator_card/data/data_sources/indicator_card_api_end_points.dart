import 'package:scad_mobile/src/config/app_config/api_config.dart';

class IndicatorCardApiEndPoints extends ApiConfig {
  static String ifpPath = ApiConfig.ifpApiPath;
  static String appPath = ApiConfig.appApiPath;

  static String indicatorDetails(
          {required String contentType, required String id}) =>
      '$ifpPath/content-type/$contentType/$id';// removed param ?overview=true

  static String indicatorDetailsInnovativeInsights(
          {required String contentType, required String id}) =>
      '$ifpPath/content-type/$contentType/$id';

  static String indicatorDetailsAnalyticalApps(
          {required String contentType, required String id}) =>
      '$ifpPath/content-type/$contentType/$id';

  // for statistical insights only
  static String indicatorOverview =
      '$ifpPath/content-type/statistics-insights/overview';

}
