import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:scad_mobile/src/common/models/response_models/api_response.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_card_api_end_points.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/domain/repositories/indicator_card_repository_imports.dart';
import 'package:scad_mobile/src/services/http_service_impl.dart';
import 'package:scad_mobile/src/services/http_services.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class IndicatorCardImpl implements IndicatorCardRepository {
  IndicatorCardImpl() {
    _httpService = HttpServiceRequests();
  }

  late HttpService _httpService;

  @override
  Future<RepoResponse<IndicatorDetailsResponse>> indicatorDetails({
    required String contentType,
    required String id,
    Map<String, String> payload = const {},
  }) async {
    try {
      ApiResponse response;
      if (contentType == 'analytical-apps' ||
          contentType ==
              'analytical_apps' /*||contentType=='statistics-insights'*/) {
        final String endpoint =
            IndicatorCardApiEndPoints.indicatorDetailsAnalyticalApps(
          contentType: 'analytical-apps',
          // contentType,
          id: id,
        );

        response = await _httpService.postJson(
          endpoint,
          server: ApiServer.ifp,
          jsonPayloadMap: {'indicatorDrivers': payload},
        );
      } else {
        String cType = contentType;

        const Map<String, String> map = {
          'scad_official_indicator': 'statistics-insights',
          'analytical-apps': 'analytical-apps',
          'analytical_apps': 'analytical_apps',
          'official-insights': 'official-insights',
          'innovative-insights': 'innovative-insights',
          'experimental_statistics': 'innovative-insights',
          'official_statistics': 'statistics-insights',
          'statistics-insights': 'statistics-insights',
          'coi': 'statistics-insights',
        };
        //todo experimental statistics error in indicator list page (experimental statistics added later in map)
        // 'experimental_statistics': 'experimental-statistics',

        if (map.keys.contains(contentType)) {
          cType = map[contentType]!;
        }

        // todo check in recommended indicator list
        final String endpoint = cType == 'innovative-insights'
            ? IndicatorCardApiEndPoints.indicatorDetailsInnovativeInsights(
                contentType: cType,
                id: id,
              )
            : IndicatorCardApiEndPoints.indicatorDetails(
                contentType: cType,
                id: id,
              );

        response = await _httpService.get(
          endpoint,
          server: ApiServer.ifp,
        );
      }

      if (response.isSuccess) {
        return RepoResponse<IndicatorDetailsResponse>.success(
          response: IndicatorDetailsResponse.fromJson(response.response),
        );
      } else {
        return RepoResponse<IndicatorDetailsResponse>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<IndicatorDetailsResponse>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<OverView>> indicatorDetailsOverview({
    required String id,
    required String type,
  }) async {
    try {
      String cType = type;
      const Map<String, String> map = {
        'scad_official_indicator': 'official_statistics',
      };
      if (map.keys.contains(type)) {
        cType = map[type]!;
      }

      final String endpoint = IndicatorCardApiEndPoints.indicatorOverview;

      final response = await _httpService.postJson(
        endpoint,
        server: ApiServer.ifp,
        jsonPayloadMap: {
          'ids': [id],
          'type': cType,
        },
      );

      if (response.isSuccess) {
        return RepoResponse<OverView>.success(
          response: OverView.fromJson(
            (response.response)[id] as Map<String, dynamic>,
          ),
        );
      } else {
        return RepoResponse<OverView>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<OverView>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

}
