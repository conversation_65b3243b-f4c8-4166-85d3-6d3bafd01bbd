import 'dart:convert';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/common/functions/indicator_date_setting.dart';
import 'package:scad_mobile/src/common/widgets/app_box_shadow.dart';
import 'package:scad_mobile/src/common/widgets/app_shimmer.dart';
import 'package:scad_mobile/src/common/widgets/card_custom_clipper.dart';
import 'package:scad_mobile/src/common/widgets/charts/spline_chart.dart';
import 'package:scad_mobile/src/common/widgets/charts/treemap_chart.dart';
import 'package:scad_mobile/src/common/widgets/error_reload_placeholder.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_response_helper.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/presentation/bloc/indicator_card_bloc.dart';
import 'package:scad_mobile/src/common/widgets/indicator_value.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/intro_widget.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/showcaseview.dart';
import 'package:scad_mobile/src/features/details/presentation/bloc/details_bloc.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/extentions/color_extensions.dart';
import 'package:scad_mobile/src/utils/extentions/list_extensions.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_api_cache.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';
import 'package:syncfusion_flutter_treemap/treemap.dart';

class IndicatorCardV2 extends StatelessWidget {
  const IndicatorCardV2({
    required this.id,
    required this.contentType,
    this.overviewContentType,
    super.key,
    this.openButtonKey,
    this.usedForSearch = false,
    this.indicatorDetailsForSearch,
    this.comparedIndicatorName,
    this.onUserGuideBackFromDetailsPage,
  });

  final String id;
  final String contentType;
  final String? overviewContentType;
  final GlobalKey? openButtonKey;
  final bool usedForSearch;
  final IndicatorDetailsResponseHelper? indicatorDetailsForSearch;
  final String? comparedIndicatorName;
  final void Function(bool isPreviousActionTriggered)?
      onUserGuideBackFromDetailsPage;

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => IndicatorCardBloc()),
        BlocProvider(create: (context) => DetailsBloc()),
      ],
      // create: (_) => IndicatorCardBloc(),
      child: _IndicatorCardWidget(
        key: Key('_IndicatorCardWidget.$key'),
        id: id,
        contentType: contentType,
        overviewContentType: overviewContentType ?? contentType,
        openButtonKey: openButtonKey,
        usedForSearch: usedForSearch,
        indicatorDetailsForSearch: indicatorDetailsForSearch,
        comparedIndicatorName: comparedIndicatorName,
        onUserGuideBackFromDetailsPage: onUserGuideBackFromDetailsPage,
      ),
    );
  }
}

class _IndicatorCardWidget extends StatefulWidget {
  const _IndicatorCardWidget({
    required this.id,
    required this.contentType,
    required this.overviewContentType,
    required this.usedForSearch,
    super.key,
    this.openButtonKey,
    this.indicatorDetailsForSearch,
    this.comparedIndicatorName,
    this.onUserGuideBackFromDetailsPage,
  });

  final String id;
  final String contentType;
  final String overviewContentType;
  final GlobalKey? openButtonKey;
  final bool usedForSearch;
  final IndicatorDetailsResponseHelper? indicatorDetailsForSearch;
  final String? comparedIndicatorName;
  final void Function(bool isPreviousActionTriggered)?
      onUserGuideBackFromDetailsPage;

  @override
  State<_IndicatorCardWidget> createState() => __IndicatorCardWidgetState();
}

class __IndicatorCardWidgetState extends State<_IndicatorCardWidget>
    with AutomaticKeepAliveClientMixin {
  IndicatorDetailsResponseHelper? indicatorDetails;

  ///use for getting all data for filteration
  IndicatorDetailsResponse? originalIndicatorData;

  bool isComparisonActive = false;
  bool isPreviousFromUserGuide = false;

  // String? comparedIndicatorName;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();

    getIndicatorDetails();
  }

  void getIndicatorDetails() {
    if (mounted) {
      if (!widget.usedForSearch) {
        if (widget.id.contains('_')) {
          isComparisonActive = true;
          final Map<String, dynamic> payload = {
            'nodes': [
              {'indicatorId': widget.id.split('_').first},
              {'indicatorId': widget.id.split('_').last},
            ],
          };

          context
              .read<DetailsBloc>()
              .add(CompareIndicatorEvent(payload: payload));
        } else {
          context.read<IndicatorCardBloc>().add(
                GetIndicatorDetailsEvent(
                  id: widget.id,
                  contentType: widget.contentType,
                  overviewContentType: widget.overviewContentType,
                ),
              );
        }
      } else {
        indicatorDetails = widget.indicatorDetailsForSearch;
      }
    }
  }

  int myAppsUpdatedAt = 0;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Column(
      children: [
        BlocConsumer<DetailsBloc, DetailsState>(
          listener: (context, state) {
            if (state is CompareIndicatorSuccessState) {
              indicatorDetails =
                  IndicatorDetailsResponseHelper(state.indicatorDetails);
              // comparedIndicatorName = state.domainName;
            }
          },
          builder: (context, state) {
            return BlocConsumer<IndicatorCardBloc, IndicatorCardState>(
              // key: Key('bloc${widget.id}'),
              listener: (context, state) {
                if (state is IndicatorDetailsSuccessState) {
                  if (widget.usedForSearch) {
                    // context.pushRoute(
                    //   DetailsPageRoute(
                    //     indicatorDetails:
                    //         IndicatorDetailsResponseHelper(state.indicatorDetails),
                    //   ),
                    // );
                  } else {
                    indicatorDetails =
                        IndicatorDetailsResponseHelper(state.indicatorDetails);
                    originalIndicatorData = IndicatorDetailsResponse.fromJson(
                      jsonDecode(jsonEncode(state.indicatorDetails))
                          as Map<String, dynamic>,
                    );
                  }
                } else if (state is IndicatorDetailsErrorState) {
                  if (widget.usedForSearch) {
                    AppMessage.showOverlayNotification(
                      '',
                      state.error,
                      msgType: 'error',
                    );
                  }
                }
              },
              builder: (context, state) {
                final rtl = DeviceType.isDirectionRTL(context);
                return SizedBox(
                  child: Stack(
                    children: [
                      Positioned(
                        right: rtl ? null : 0,
                        left: rtl ? 0 : null,
                        top: 0,
                        height: 40,
                        width: 40,
                        child: widget.openButtonKey != null && indicatorDetails != null
                            ? (IntroWidget(
                                    stepKey:
                                        widget.openButtonKey ?? GlobalKey(),
                                    stepIndex: 2,
                                    totalSteps: 7,
                                    // onPrevious: () {
                                    //   if (isPreviousFromUserGuide) {
                                    //     if (HiveUtilsSettings
                                    //             .getUserGuideStatus() ==
                                    //         UserGuides.Home) {
                                    // ShowCaseWidget.of(context).startShowCase(
                                    //   [widget.actionButtonsKey!],
                                    // );
                                    // }
                                    // }
                                    // },
                                    onNext: () {
                                      if (indicatorDetails != null) {
                                        if (indicatorDetails
                                                    ?.indicatorDetails.type ==
                                                'Internal' &&
                                            indicatorDetails!.indicatorDetails
                                                    .multiDrivers ==
                                                true) {
                                          // context.pushRoute(
                                          //   WhatIfDetailsPageRoute(
                                          //     nodeId: widget.id,
                                          //     indicatorDetails: indicatorDetails,
                                          //     originalIndicatorData:
                                          //         originalIndicatorData,
                                          //   ),
                                          // );
                                        } else {
                                          context.router.removeWhere(
                                            (route) =>
                                                route.name ==
                                                DetailsPageRoute.name,
                                          );
                                          context
                                              .pushRoute(
                                            DetailsPageRoute(
                                              indicatorDetails:
                                                  indicatorDetails,
                                              originalIndicatorForFilter:
                                                  originalIndicatorData,
                                              contentType: widget.contentType,
                                            ),
                                          )
                                              .then((value) {
                                            isPreviousFromUserGuide = true;
                                            widget
                                                .onUserGuideBackFromDetailsPage!(
                                              true,
                                            );
                                          });
                                        }
                                      }
                                    },
                                    title: LocaleKeys.expand.tr(),
                                    description: LocaleKeys.expandDesc.tr(),
                                    arrowAlignment: Alignment.bottomRight,
                                    isDownArrow: true,
                                    position: TooltipPosition.top,
                                    crossAxisAlignment: CrossAxisAlignment.end,
                                    arrowPadding: const EdgeInsets.only(
                                      right: 10,
                                      left: 10,
                                    ),
                                    child:
                                        openDetailsButton(context, isLightMode),
                                  ))
                            : openDetailsButton(context, isLightMode),
                      ),
                      ClipPath(
                        clipper: CardCustomClipper(isRtl: rtl),
                        child: Container(
                          decoration: BoxDecoration(
                            color: isLightMode
                                ? AppColors.greyShade7
                                : AppColors.blueShade32,
                            boxShadow: isLightMode ? AppBox.shadow() : null,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          padding: const EdgeInsets.fromLTRB(15, 0, 15, 15),
                          child: Container(
                            constraints: BoxConstraints(
                              minHeight: widget.usedForSearch ? 90 : 150,
                            ),
                            child: state is IndicatorDetailsErrorState &&
                                    !widget.usedForSearch
                                ? Padding(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 20,
                                    ),
                                    child: ErrorReloadPlaceholder(
                                      error: state.error,
                                      onReload: getIndicatorDetails,
                                    ),
                                    // Column(
                                    //   crossAxisAlignment:
                                    //       CrossAxisAlignment.stretch,
                                    //   mainAxisAlignment:
                                    //       MainAxisAlignment.spaceAround,
                                    //   children: [
                                    //     Text(
                                    //       state.error,
                                    //       textAlign: TextAlign.center,
                                    //       textScaler: TextScaler.linear(
                                    //         textScaleFactor.value,
                                    //       ),
                                    //     ),
                                    //     Center(
                                    //       child: TextButton(
                                    //         style: TextButton.styleFrom(
                                    //           backgroundColor:
                                    //               Colors.transparent,
                                    //         ),
                                    //         onPressed: getIndicatorDetails,
                                    //         child: Text(LocaleKeys.reload.tr(),
                                    //           textScaler: TextScaler.linear(textScaleFactor.value),),
                                    //       ),
                                    //     ),
                                    //   ],
                                    // ),
                                  )
                                : (state is IndicatorDetailsLoadingState ||
                                            indicatorDetails == null) &&
                                        (!widget.usedForSearch)
                                    ? Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.stretch,
                                        children: [
                                          Row(
                                            children: [
                                              Expanded(
                                                child: Container(
                                                  margin: const EdgeInsets.only(
                                                    top: 15,
                                                  ),
                                                  child: const Row(
                                                    children: [
                                                      AppShimmer(
                                                        height: 26,
                                                        width: 26,
                                                        radius: 26,
                                                      ),
                                                      SizedBox(width: 10),
                                                      Expanded(
                                                        child: AppShimmer(
                                                          height: 26,
                                                          radius: 4,
                                                        ),
                                                      ),
                                                      SizedBox(width: 10),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                              // Padding(
                                              //   padding: const EdgeInsets.only(
                                              //       top: 10),
                                              //   child: Row(
                                              //     children: [
                                              //       Container(
                                              //         padding:
                                              //         const EdgeInsets
                                              //             .all(8),
                                              //         child: subscription
                                              //             ?.nodeId !=
                                              //             null
                                              //             ? SvgPicture
                                              //             .asset(
                                              //           AppImages
                                              //               .icBell,
                                              //           colorFilter: ColorFilter.mode(
                                              //               AppColors
                                              //                   .blueLight,
                                              //               BlendMode
                                              //                   .srcIn),
                                              //         )
                                              //             : SvgPicture
                                              //             .asset(
                                              //           AppImages
                                              //               .icBell,
                                              //           colorFilter: ColorFilter.mode(
                                              //               AppColors
                                              //                   .black,
                                              //               BlendMode
                                              //                   .srcIn),
                                              //         ),
                                              //       ),
                                              //       Container(
                                              //         padding:
                                              //         const EdgeInsets
                                              //             .all(8),
                                              //         child: subscription
                                              //             ?.nodeId !=
                                              //             null
                                              //             ? SvgPicture
                                              //             .asset(
                                              //           AppImages
                                              //               .icBell,
                                              //           colorFilter: ColorFilter.mode(
                                              //               AppColors
                                              //                   .blueLight,
                                              //               BlendMode
                                              //                   .srcIn),
                                              //         )
                                              //             : SvgPicture
                                              //             .asset(
                                              //           AppImages
                                              //               .icBell,
                                              //           colorFilter: ColorFilter.mode(
                                              //               AppColors
                                              //                   .black,
                                              //               BlendMode
                                              //                   .srcIn),
                                              //         ),
                                              //       ),
                                              //     ],
                                              //   ),
                                              // ),

                                              const SizedBox(width: 40),
                                            ],
                                          ),
                                          const SizedBox(height: 10),
                                          const Row(
                                            children: [
                                              AppShimmer(
                                                height: 20,
                                                width: 250,
                                                radius: 4,
                                              ),
                                            ],
                                          ),
                                          const SizedBox(
                                            height: 5,
                                          ),
                                          const Row(
                                            children: [
                                              AppShimmer(
                                                height: 28,
                                                width: 170,
                                                radius: 4,
                                              ),
                                            ],
                                          ),
                                          const SizedBox(
                                            height: 10,
                                          ),
                                          Container(
                                            decoration: ShapeDecoration(
                                              color: Colors.white,
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                              ),
                                              shadows: const [
                                                BoxShadow(
                                                  color: Color(0x0F000000),
                                                  blurRadius: 10,
                                                  offset: Offset(0, 4),
                                                ),
                                              ],
                                            ),
                                            child: AppShimmer(
                                              radius: 10,
                                              height: 110 *
                                                  (textScaleFactor.value < 1
                                                      ? 1
                                                      : textScaleFactor.value),
                                            ),
                                          ),
                                        ],
                                      )
                                    : indicatorDetails != null
                                        ? Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.stretch,
                                            children: [
                                              Row(
                                                children: [
                                                  Expanded(
                                                    child: Container(
                                                      margin:
                                                          const EdgeInsets.only(
                                                        top: 15,
                                                      ),
                                                      child: Row(
                                                        children: [
                                                          if (!isComparisonActive) ...[
                                                            SvgPicture.network(
                                                              widget
                                                                      .usedForSearch
                                                                  ? HiveUtilsApiCache
                                                                      .getDomainImageByName(
                                                                      indicatorDetails
                                                                          ?.indicatorDetails
                                                                          .domain,
                                                                      rtl,
                                                                    )
                                                                  : HiveUtilsApiCache
                                                                      .getDomainImage(
                                                                      indicatorDetails
                                                                          ?.indicatorDetails
                                                                          .domainId,
                                                                    ),
                                                              width: 18,
                                                              height: 18,
                                                              colorFilter:
                                                                  isLightMode
                                                                      ? const ColorFilter
                                                                          .mode(
                                                                          Colors
                                                                              .black,
                                                                          BlendMode
                                                                              .srcIn,
                                                                        )
                                                                      : const ColorFilter
                                                                          .mode(
                                                                          Colors
                                                                              .white,
                                                                          BlendMode
                                                                              .srcIn,
                                                                        ),
                                                            ),
                                                            const SizedBox(
                                                              width: 10,
                                                            ),
                                                          ],
                                                          Expanded(
                                                            child: Text(
                                                              !isComparisonActive
                                                                  ? indicatorDetails!
                                                                      .domainName
                                                                  : widget.comparedIndicatorName ??
                                                                      LocaleKeys
                                                                          .compareIndicatorsResult
                                                                          .tr(),
                                                              maxLines: 1,
                                                              overflow:
                                                                  TextOverflow
                                                                      .ellipsis,
                                                              style: TextStyle(
                                                                color: isLightMode
                                                                    ? AppColors
                                                                        .black
                                                                    : AppColors
                                                                        .white,
                                                                fontSize: 14,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w400,
                                                              ),
                                                              textScaler:
                                                                  TextScaler
                                                                      .linear(
                                                                textScaleFactor
                                                                    .value,
                                                              ),
                                                            ),
                                                          ),
                                                          const SizedBox(
                                                            width: 10,
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                  const SizedBox(width: 40),
                                                ],
                                              ),
                                              const SizedBox(height: 10),
                                              if (!isComparisonActive) ...[
                                                Padding(
                                                  padding: EdgeInsets.only(
                                                    right: rtl ? 0 : 46,
                                                    left: rtl ? 46 : 0,
                                                  ),
                                                  child: Text.rich(
                                                    TextSpan(
                                                      children: [
                                                        TextSpan(
                                                          text:
                                                              indicatorDetails!
                                                                  .title,
                                                          style: TextStyle(
                                                            color: isLightMode
                                                                ? AppColors
                                                                    .black
                                                                : AppColors
                                                                    .white,
                                                            fontSize: 14,
                                                            fontWeight:
                                                                FontWeight.w500,
                                                          ),
                                                        ),
                                                        WidgetSpan(
                                                          child: Padding(
                                                            padding:
                                                                EdgeInsets.only(
                                                              left: rtl ? 0 : 8,
                                                              right:
                                                                  rtl ? 8 : 0,
                                                            ),
                                                            child: (indicatorDetails!
                                                                        .indicatorDetails
                                                                        .contentClassificationKey ==
                                                                    'official_statistics')
                                                                ? SvgPicture
                                                                    .asset(
                                                                    AppImages
                                                                        .icOfficialActive,
                                                                  )
                                                                : (indicatorDetails!
                                                                            .indicatorDetails
                                                                            .contentClassificationKey ==
                                                                        'experimental_statistics')
                                                                    ? SvgPicture
                                                                        .asset(
                                                                        AppImages
                                                                            .icExperimentalActive,
                                                                      )
                                                                    : const SizedBox
                                                                        .shrink(),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                    maxLines: 2,
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                    textScaler:
                                                        TextScaler.linear(
                                                      textScaleFactor.value,
                                                    ),
                                                  ),
                                                ),
                                                const SizedBox(height: 5),
                                              ],
                                              if (!widget.usedForSearch) ...[
                                                if (!isComparisonActive) ...[
                                                  SizedBox(
                                                    width: double.infinity,
                                                    child: IndicatorValue(
                                                      negativeArrow:
                                                          indicatorDetails!
                                                              .negativeArrow,
                                                      value: indicatorDetails!
                                                          .value,
                                                      unit: indicatorDetails!
                                                          .unit,
                                                      numberUnit:
                                                          indicatorDetails!
                                                              .numberUnit,
                                                    ),
                                                  ),
                                                ],
                                                const SizedBox(height: 10),
                                                _childWidget(
                                                  state,
                                                  isLightMode,
                                                ),
                                              ],
                                            ],
                                          )
                                        : const SizedBox(
                                            width: double.infinity,
                                            height: 100,
                                          ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            );
          },
        ),
      ],
    );
  }

  Widget _childWidget(IndicatorCardState state, bool isLightMode) {
    final String chartType = indicatorDetails?.indicatorDetails
                .indicatorVisualizations?.visualizationsMeta !=
            null
        ? (indicatorDetails?.getFilteredVisualizationMetaList() ?? [])
                .isNotEmpty
            ? indicatorDetails?.getFilteredVisualizationMetaList().first.type ??
                ''
            : ''
        : '';

    List<SeriesMeta> treeMapSeriesMeta = [];
    VisualizationsMeta? vizMeta;
    if (chartType == 'tree-map-with-change-chart') {
      treeMapSeriesMeta = indicatorDetails
              ?.getFilteredVisualizationMetaList()
              .firstOrNull
              ?.seriesMeta ??
          [];
    }
    if (isComparisonActive) {
      vizMeta = indicatorDetails?.getFilteredVisualizationMetaList().first;
    }
    return IgnorePointer(
      child: Container(
        decoration: ShapeDecoration(
          color: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          // shadows: const [
          //   BoxShadow(
          //     color: Color(0x0F000000),
          //     blurRadius: 10,
          //     offset: Offset(0, 4),
          //     spreadRadius: 0,
          //   ),
          // ],
        ),
        child: Column(
          children: [
            if (chartType == 'tree-map-with-change-chart')
              _treeMapChartRepresentation(treeMapSeriesMeta)
            else
              _lineChartRepresentation(isLightMode),
            if (isComparisonActive)
              Padding(
                padding: const EdgeInsets.only(
                  top: 4,
                  bottom: 8,
                  left: 12,
                  right: 12,
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    for (final title in vizMeta!.seriesTitles?.values ?? [])
                      Expanded(
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(top: 5),
                              child: vizMeta.seriesTitles!.values
                                          .toList()
                                          .indexOf(title) ==
                                      0
                                  ? SvgPicture.asset(
                                      AppImages.icLegendGreen,
                                      colorFilter: ColorFilter.mode(
                                        AppColors.chartColorSet[0],
                                        BlendMode.srcIn,
                                      ),
                                    )
                                  : SvgPicture.asset(
                                      AppImages.icLegendPurple,
                                      colorFilter: ColorFilter.mode(
                                        AppColors.chartColorSet[1],
                                        BlendMode.srcIn,
                                      ),
                                    ),
                            ),
                            const SizedBox(width: 10),
                            Expanded(
                              child: Text(
                                title?.toString() ?? '',
                                textAlign: TextAlign.start,
                                style: TextStyle(
                                  color: AppColors.grey,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400,
                                ),
                                maxLines: 4,
                                textScaler: TextScaler.linear(
                                  textScaleFactor.value,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
          ],
        ),
        /*SizedBox(
                height:
                    110 * (textScaleFactor.value < 1 ? 1 : textScaleFactor.value),
                child: SplineChart(
                  chartDataList: [
                    indicatorDetails!.getFilteredSeries().map((e) {
                      return SplineChartData(
                        e['OBS_DT'].toString(),
                        num.tryParse('${e['VALUE']}') ?? 0,
                      );
                    }).toList(),
                  ],
                ),
              ),*/
      ),
    );
  }

  Widget _lineChartRepresentation(bool isLightMode) {
    final List<List<SplineChartData>> nowCast = [];
    final List<List<SplineChartData>> foreCast = [];
    final List<List<SplineChartData>> lowerAreaForecast = [];
    final List<List<SplineChartData>> upperAreaForecast = [];
    List<List<SplineChartData>> areaForecast = [];
    List<SeriesMeta> seriesMeta = [];
    List<Map<String, dynamic>> comparisonSeries = [];

    seriesMeta = indicatorDetails
            ?.getFilteredVisualizationMetaList()
            .elementAtOrNull(0)
            ?.seriesMeta ??
        [];

    if (indicatorDetails?.indicatorDetails.indicatorVisualizations != null) {
      final visualizationMeta = (indicatorDetails?.indicatorDetails
                  .indicatorVisualizations?.visualizationsMeta ??
              [])
          .firstOrNull;
      if ((visualizationMeta?.seriesMeta ?? []).length > 1 &&
          visualizationMeta?.id == 'compare-chart') {
        comparisonSeries =
            indicatorDetails!.getFilteredSeries(seriesMetaIndex: 1);
      }
    }

    final List<List<Map<String, dynamic>>> l =
        indicatorDetails?.getFilteredSeriesForMultiDrivers() ?? [];

    for (var i = 0; i < l.length; i++) {
      l[i].sort(
        (a, b) => a['OBS_DT'].toString().compareTo(b['OBS_DT'].toString()),
      );
    }

    for (int i = 0; i < l.length; i++) {
      if (seriesMeta[i].id!.contains('-forecast')) {
        foreCast.add(
          l[i]
              .map(
                (e) => SplineChartData(
                  e['OBS_DT'].toString(),
                  num.parse('${e['VALUE'] ?? '0'}'),
                ),
              )
              .toList()
              .limitListLengthforCast(),
        );

        final List<SplineChartData> lowData = [];

        for (final e in l[i]) {
          if (e['VALUE_LL'] != null) {
            lowData.add(
              SplineChartData(
                e['OBS_DT'].toString(),
                num.parse('${e['VALUE'] ?? '0'}'),
                y: num.parse('${e['VALUE_LL'] ?? '0'}'),
              ),
            );
          }
        }

        lowerAreaForecast.add(lowData);

        final List<SplineChartData> upData = [];

        for (final e in l[i]) {
          if (e['VALUE_UL'] != null) {
            upData.add(
              SplineChartData(
                e['OBS_DT'].toString(),
                num.parse('${e['VALUE'] ?? '0'}'),
                y: num.parse('${e['VALUE_UL'] ?? '0'}'),
              ),
            );
          }
        }

        upperAreaForecast.add(upData);

        areaForecast = [...lowerAreaForecast, ...upperAreaForecast];
      } else {
        nowCast.add(
          l[i]
              .map(
                (e) => SplineChartData(
                  e['OBS_DT'].toString(),
                  num.parse('${e['VALUE'] ?? '0'}'),
                ),
              )
              .toList()
              .limitListLength(),
        );
      }
    }
    final indicatorDateSetting = IndicatorDateSetting.setFrequancy(
      l: l,
      indicatorDetails: indicatorDetails,
    );

    return SizedBox(
      height: 110 * (textScaleFactor.value < 1 ? 1 : textScaleFactor.value),
      child: SplineChart(
        indicatorCard: true,
        frequance: indicatorDateSetting['selectedFrequencyForFilter'] as String,
        isForecast: foreCast.isNotEmpty,
        chartDataList: nowCast,
        forecastChartDataList: foreCast,
        areaHighlightChartData: areaForecast,
        isCompareActive: isComparisonActive,
        comparisonChartData: isComparisonActive
            ? comparisonSeries
                .map(
                  (e) => SplineChartData(
                    e['OBS_DT'].toString(),
                    double.parse('${e['VALUE'] ?? '0'}'),
                  ),
                )
                .toList()
            : [],
        // dateRepresentation: (indicatorDetails
        //                 ?.indicatorDetails
        //                 .indicatorVisualizations
        //                 ?.visualizationsMeta
        //                 ?.first
        //                 .timeUnit ??
        //             [])
        //         .contains('Monthly')
        //     ? 'Monthly'
        //     : (indicatorDetails?.indicatorDetails.indicatorVisualizations
        //                     ?.visualizationsMeta?.first.timeUnit ??
        //                 [])
        //             .contains('Quarterly')
        //         ? 'Quarterly'
        //         : 'Yearly',
      ),
    );
  }

  Widget _treeMapChartRepresentation(
    List<SeriesMeta> treeMapSeriesMeta,
  ) {
    final List<TreemapColorMapper> colorMappers = [];
    for (int i = 0; i < treeMapSeriesMeta.length; i++) {
      colorMappers.add(
        TreemapColorMapper.value(
          value: treeMapSeriesMeta[i].data?.first['CHANGE'].toString(),
          color: treeMapSeriesMeta[i].color.toString().toColor(),
        ),
      );
    }
    return FittedBox(
      child: SizedBox(
        height: 210 * (textScaleFactor.value < 1 ? 1 : textScaleFactor.value),
        width: MediaQuery.sizeOf(context).width * 0.9,
        child: TreemapChart(
          key: Key(
            'treemap.indicator.v2.${treeMapSeriesMeta.map((e) => e.color).join()}',
          ),
          chartSeriesData: treeMapSeriesMeta,
        ),
      ),
    );
  }

  Widget openDetailsButton(BuildContext context, bool isLightMode) {
    return Container(
      decoration: ShapeDecoration(
        color: isLightMode ? AppColors.greyShade7 : AppColors.blueShade32,
        // gradient: LinearGradient(
        //   begin: Alignment(-0.65, -0.76),
        //   end: Alignment(0.65, 0.76),
        //   colors: [AppColors.blueShade11, AppColors.blueShade12],
        // ),
        shape: const OvalBorder(),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(100),
          onTap: () {
            if (widget.usedForSearch) {
              context.router
                  .removeWhere((route) => route.name == DetailsPageRoute.name);
              context.pushRoute(
                DetailsPageRoute(
                  id: widget.id,
                  contentType: widget.contentType,
                ),
              );
            } else {
              if (indicatorDetails != null) {
                if (indicatorDetails?.indicatorDetails.type == 'Internal' &&
                    indicatorDetails!.indicatorDetails.multiDrivers == true) {
                  context.router.removeWhere(
                    (route) => route.name == WhatIfDetailsPageRoute.name,
                  );
                  context.pushRoute(
                    WhatIfDetailsPageRoute(
                      nodeId: widget.id,
                      indicatorDetails: indicatorDetails,
                      originalIndicatorData: originalIndicatorData,
                    ),
                  );
                } else {
                  context.router.removeWhere(
                    (route) => route.name == DetailsPageRoute.name,
                  );
                  context.pushRoute(
                    DetailsPageRoute(
                      indicatorDetails: indicatorDetails,
                      originalIndicatorForFilter: originalIndicatorData,
                      contentType: widget.contentType,
                      comparedIndicatorName: widget.comparedIndicatorName,
                    ),
                  );
                }
              }
            }
          },
          child: Padding(
            padding: const EdgeInsets.all(5),
            child: RotatedBox(
              quarterTurns: DeviceType.isDirectionRTL(context) ? -1 : 0,
              child: Icon(
                Icons.arrow_outward_rounded,
                color: isLightMode
                    ? const Color(0xFF19386E)
                    : AppColors.blueLightOld,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
