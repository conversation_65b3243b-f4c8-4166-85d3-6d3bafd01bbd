part of 'indicator_card_bloc.dart';

abstract class IndicatorCardState extends Equatable {
  const IndicatorCardState();

  @override
  List<Object> get props => [];
}

class IndicatorDetailsInitState extends IndicatorCardState {}

class IndicatorDetailsLoadingState extends IndicatorCardState {}

class IndicatorDetailsErrorState extends IndicatorCardState {
  const IndicatorDetailsErrorState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}

class IndicatorDetailsSuccessState extends IndicatorCardState {
  const IndicatorDetailsSuccessState({
    required this.indicatorDetails,
    this.isFromChangeDriverCallInitOnce,
    this.isFromDriverForSearch,
    this.contentType,
  });

  final IndicatorDetailsResponse indicatorDetails;
  final bool? isFromChangeDriverCallInitOnce;
  final bool? isFromDriverForSearch;
  final String? contentType;

  @override
  List<Object> get props => [
        indicatorDetails,
        isFromChangeDriverCallInitOnce ?? false,
        isFromDriverForSearch ?? false,
        contentType ?? '',
      ];
}

