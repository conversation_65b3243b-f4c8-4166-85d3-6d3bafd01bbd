import 'dart:async';
import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/route_manager/route_imports.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/domain/repositories/indicator_card_repository_imports.dart';
import 'package:scad_mobile/src/config/dependancy_injection/injection_container.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_api_cache.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

part 'indicator_card_event.dart';
part 'indicator_card_state.dart';

class IndicatorCardBloc extends Bloc<IndicatorCardEvent, IndicatorCardState> {
  IndicatorCardBloc() : super(IndicatorDetailsInitState()) {
    on<GetIndicatorDetailsEvent>(getIndicatorDetailsEventHandler);
  }

  FutureOr<void> getIndicatorDetailsEventHandler(
    GetIndicatorDetailsEvent event,
    Emitter<IndicatorCardState> emit,
  ) async {
    try {
      BuildContext ctx = servicelocator<AppRouter>().navigatorKey.currentContext!;
      final String hiveKey =
          'indicatorDetails:${Localizations.localeOf(ctx).languageCode}:${event.id}:${event.contentType}:${event.overviewContentType}:${event.payload.toString().replaceAll('"', '').replaceAll('\'', '')}}';

      final dynamic res = HiveUtilsApiCache.get(hiveKey);
      if (res != null) {
        emit(
          IndicatorDetailsSuccessState(
            indicatorDetails:
                IndicatorDetailsResponse.fromJson(res as Map<String, dynamic>),
            isFromChangeDriverCallInitOnce: event.isFromChangeDriverCallInitOnce,
            isFromDriverForSearch: event.isFromDriverForSearch,
            contentType: event.contentType,
          ),
        );
        return;
      }

      emit(IndicatorDetailsLoadingState());

      final RepoResponse<IndicatorDetailsResponse> indicatorDetailsResponse =
          await servicelocator<IndicatorCardRepository>().indicatorDetails(
        id: event.id,
        contentType: event.contentType,
        payload: event.payload,
      );

      if (indicatorDetailsResponse.isSuccess) {
        final IndicatorDetailsResponse indicatorDetails =
            indicatorDetailsResponse.response!;

        String overviewContentType = event.overviewContentType;
        if([
'official_statistics'
,
'experimental_statistics',
].contains(indicatorDetails.contentClassificationKey)) {
        
          if (indicatorDetails.contentClassificationKey ==
              'official_statistics') {
            overviewContentType = 'official_statistics';
          } else if (indicatorDetails.contentClassificationKey ==
              'experimental_statistics') {
            overviewContentType = 'experimental_statistics';
          }
        

        if (indicatorDetails.overView == null &&
            (indicatorDetails.indicatorValues?.overviewValuesMeta ?? [])
                .isEmpty &&
            overviewContentType.isNotEmpty) {
          try {
            final RepoResponse<OverView> indicatorOverviewResponse =
                await servicelocator<IndicatorCardRepository>()
                    .indicatorDetailsOverview(
              id: event.id,
              type: overviewContentType,
            );
            if (indicatorOverviewResponse.isSuccess) {
              indicatorDetails.overView = indicatorOverviewResponse.response;
            } else {
              emit(IndicatorDetailsErrorState(
                  error: indicatorDetailsResponse.errorMessage,),);
              return;
            }
          } catch (e, s) {
            Completer<dynamic>().completeError(e, s);
            // emit(IndicatorDetailsErrorState(error: e.toString()));
          }
            }
        }

        // String value = '0';
        // '${r.response!.entries.toList().singleWhere((element) => element.key == nodeId).value!.value}';

        // if(false){ //value is not available in response
        // final RepoResponse<Map<String, IndicatorDetails>> r =
        // await servicelocator<HomeRepository>().indicatorDetails({
        //   'content_type': 'statistics-insights',
        //   // type=='innovative-insights'?'experimental_statistics':type,
        // }, {
        //   'ids': list,
        //   'type': type,
        // });
        // }

        HiveUtilsApiCache.set(hiveKey, indicatorDetails.toJson());

        emit(
          IndicatorDetailsSuccessState(
            indicatorDetails: indicatorDetails,
            isFromChangeDriverCallInitOnce: event.isFromChangeDriverCallInitOnce,
            isFromDriverForSearch: event.isFromDriverForSearch,
            contentType: event.contentType,
          ),
        );
      } else {
        emit(IndicatorDetailsErrorState(
            error: indicatorDetailsResponse.errorMessage,),);
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(IndicatorDetailsErrorState(
          error: LocaleKeys.somethingWentWrong.tr(),),);
    }
  }

}
