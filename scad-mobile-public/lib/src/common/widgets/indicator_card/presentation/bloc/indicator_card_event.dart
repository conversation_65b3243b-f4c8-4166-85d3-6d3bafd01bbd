part of 'indicator_card_bloc.dart';

abstract class IndicatorCardEvent extends Equatable {
  const IndicatorCardEvent();

  @override
  List<Object> get props => [];
}

class GetIndicatorDetailsEvent extends IndicatorCardEvent {
  GetIndicatorDetailsEvent({
    required this.overviewContentType,
    required this.contentType,
    required this.id,
    this.isFromChangeDriverCallInitOnce,
    this.isFromDriverForSearch,
    this.payload = const {},
  }) {
    rnd = Random().nextInt(10000);
  }

  final String overviewContentType;
  final String contentType;
  final String id;
  final Map<String, String> payload;
  final bool? isFromChangeDriverCallInitOnce;
  final bool? isFromDriverForSearch;
  late final int rnd;

  @override
  List<Object> get props =>
      [
        overviewContentType,
        contentType,
        id,
        payload,
        rnd,
        isFromChangeDriverCallInitOnce ?? false,
        isFromDriverForSearch ?? false,
      ];
}


