import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart' show textScaleFactor;
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

class CustomAnimatedExpansionTile extends StatelessWidget {
  const CustomAnimatedExpansionTile({
    required this.title,
    required this.children,
    required this.isExpanded,
    required this.onExpansionChanged,
    this.leading,
    this.padding = const EdgeInsets.only(
      top: 12,
      left: 15,
      right: 10,
      bottom: 11,
    ),
    super.key,
  });

  final String title;
  final List<Widget> children;
  final bool isExpanded;
  final VoidCallback onExpansionChanged;
  final Widget? leading;
  final EdgeInsetsGeometry padding;


  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        InkWell(
          onTap: onExpansionChanged,
          borderRadius: BorderRadius.circular(15),
          child: Padding(
            padding: padding,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (leading != null) ...[
                      leading!,
                      const SizedBox(width: 10),
                    ],
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      child: Text(
                        title,
                        style: TextStyle(
                          color: isLightMode
                              ? AppColors.blackTextTile
                              : AppColors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                        textScaler: TextScaler.linear(textScaleFactor.value),
                      ),
                    ),
                  ],
                ),
                Container(
                  height: 24,
                  width: 24,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isExpanded
                          ? isLightMode
                              ? AppColors.greyShade8
                              : AppColors.blackShade7
                          : isLightMode
                              ? AppColors.blueLightOld
                              : AppColors.grey,
                    ),
                    color: isExpanded
                        ? isLightMode
                            ? AppColors.greyShade8
                            : AppColors.blueShade36
                        : isLightMode
                            ? AppColors.blueShade36
                            : Colors.transparent,
                  ),
                  // padding: EdgeInsets.all(12),
                  child: Icon(
                    isExpanded ? Icons.remove : Icons.add,
                    color: isLightMode
                        ? isExpanded
                            ? AppColors.blueLight
                            : AppColors.white
                        : null,
                    size: 18,
                  ),
                ),
              ],
            ),
          ),
        ),
        AnimatedCrossFade(
          firstChild: Container(),
          secondChild: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: children,
          ),
          duration: const Duration(milliseconds: 300),
          crossFadeState:
              isExpanded ? CrossFadeState.showSecond : CrossFadeState.showFirst,
        ),
      ],
    );
  }
}
