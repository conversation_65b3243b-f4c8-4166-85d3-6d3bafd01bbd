import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';

class ContactCard extends StatelessWidget {
  const ContactCard({
    required this.iconPath,
    required this.contact,
    required this.onTap,
    super.key,
  });

  final String iconPath;
  final String contact;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(15),
      child: Container(
        height: 110,
        decoration: BoxDecoration(
          color: isLightMode ? AppColors.white : AppColors.blueShade32,
          borderRadius: BorderRadius.circular(15),
          boxShadow: [
            BoxShadow(
              color: AppColors.greyShade6.withOpacity(0.08),
              blurRadius: 50,
              spreadRadius: 0.23,
              offset: const Offset(0, 35),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              SvgPicture.asset(
                iconPath,
              ),
              Text(
                textDirection :TextDirection.ltr,
                contact,
                style: AppTextStyles.s16w4cGrey.copyWith(
                  color: !isLightMode ? AppColors.white : null,
                ),
                textScaler: TextScaler.linear(textScaleFactor.value),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
