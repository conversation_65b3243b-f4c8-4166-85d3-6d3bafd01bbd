import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/widgets/app_box_shadow.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

class ExpandableWidget extends StatelessWidget {
  const ExpandableWidget({
    this.expandedChild,
    super.key,
    this.headerChild,
    // this.shape,
    this.onChanged,
    this.iconSize,
    this.iconPadding = 6,
    this.showDivider = true,
    this.leadingIcon,
    this.title,
    this.trailingIcon,
  });

  final Widget? leadingIcon;
  final Widget? trailingIcon;
  final String? title;
  final Widget? expandedChild;
  final Widget? headerChild;
  // final ShapeBorder? shape;
  final ValueChanged<bool>? onChanged;
  final double? iconSize;
  final double iconPadding;
  final bool showDivider;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return ExpandableNotifier(
      child: Container(
        clipBehavior: Clip.antiAlias,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15),
          color: isLightMode ? AppColors.greyShade7 : AppColors.blueShade32,
          boxShadow: isLightMode ? AppBox.shadow() : null,
        ),
        margin: EdgeInsets.zero,
        child: Column(
          children: <Widget>[
            ScrollOnExpand(
              scrollOnCollapse: false,
              child: ExpandablePanel(
                theme: const ExpandableThemeData(
                  headerAlignment: ExpandablePanelHeaderAlignment.center,
                  tapBodyToCollapse: false,
                  hasIcon: false,
                ),
                header: Builder(
                  builder: (context) {
                    final controller = ExpandableController.of(
                      context,
                      required: true,
                    )!;
                    return Material(
                      color: controller.expanded
                          ? isLightMode
                              ? AppColors.blueLightOld
                              : Colors.transparent
                          : Colors.transparent,
                      child: InkWell(
                        onTap: () {
                          controller.toggle();
                          if (onChanged != null) {
                            onChanged?.call(controller.expanded);
                          }
                        },
                        child: Row(
                          children: [
                            Expanded(
                              child: _header(
                                  context, isLightMode, controller.expanded),
                            ),
                            Padding(
                              padding: const EdgeInsets.all(12),
                              child: Container(
                                height: iconSize,
                                width: iconSize,
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: controller.expanded
                                        ? isLightMode
                                            ? AppColors.greyShade8
                                            : AppColors.blue
                                        : isLightMode
                                            ? AppColors.blueLightOld
                                            : AppColors.grey,
                                  ),
                                  color: controller.expanded
                                      ? isLightMode
                                          ? AppColors.greyShade8
                                          : AppColors.blueShade36
                                      : isLightMode
                                          ? AppColors.blueLightOld
                                          : Colors.transparent,
                                ),
                                padding: EdgeInsets.all(iconPadding),
                                child: Icon(
                                  controller.expanded
                                      ? Icons.remove
                                      : Icons.add,
                                  color:
                                      isLightMode
                                      ? controller.expanded
                                          ? AppColors.blueLight
                                          : AppColors.white
                                      : null,
                                  size: 18,
                                ),
                              ),
                            ),
                            // IconButton(
                            //   padding: const EdgeInsets.all(12),
                            //   icon: ,
                            //   onPressed: () {},
                            // ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
                collapsed: const SizedBox(),
                expanded: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    if (showDivider)
                      Divider(
                        height: 1,
                        color: isLightMode
                            ? AppColors.scaffoldBackgroundLight
                            : AppColors.blackShade4,
                      ),
                    expandedChild ?? const SizedBox(),
                  ],
                ),
                builder: (_, collapsed, expanded) {
                  return Expandable(
                    collapsed: collapsed,
                    expanded: expanded,
                    theme: const ExpandableThemeData(crossFadePoint: 0),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _header(BuildContext context, bool isLightMode, bool isExpanded) {
    if (headerChild != null) {
      return headerChild!;
    } else {
      final rtl = DeviceType.isDirectionRTL(context);
      return Padding(
        padding: EdgeInsets.only(left: 16, right: rtl ? 16 : 0),
        child: Row(
          children: [
            leadingIcon ?? const SizedBox(),
            Expanded(
              child: Text(
                title ?? '-',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: isLightMode
                      ? (isExpanded ? AppColors.white : AppColors.black)
                      : AppColors.white,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                textScaler: TextScaler.linear(textScaleFactor.value),
              ),
            ),
            trailingIcon ?? SizedBox(),
          ],
        ),
      );
    }
  }
}
