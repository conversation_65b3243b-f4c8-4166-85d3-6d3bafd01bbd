import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';

class LanguageSwitch extends StatelessWidget {
  const LanguageSwitch({
    required this.switchValue,
    required this.onToggle,
    super.key,
  });

  final bool switchValue;
  final Function onToggle;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => onToggle(),
      child: Container(
        width: 46,
        height: 24,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: AppColors.greyShade8,
          borderRadius: BorderRadius.circular(30),
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            AnimatedPositioned(
              width: 20,
              height: 20,
              left: switchValue ? 3.0 : 22.0,
              duration: const Duration(
                milliseconds: 100,
              ),
              curve: Curves.fastOutSlowIn,
              child: Container(
                decoration: BoxDecoration(
                  color: AppColors.blue,
                  borderRadius: BorderRadius.circular(
                    20,
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(
                left: 7,
                right: 11,
              ),
              child: AnimatedContainer(
                width: 46,
                height: 24,
                duration: const Duration(milliseconds: 200),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SvgPicture.asset(
                      AppImages.icEn,
                      height: 8,
                      width: 8,
                      colorFilter: ColorFilter.mode(
                        switchValue ? AppColors.white : AppColors.greyShade4,
                        BlendMode.srcIn,
                      ),
                    ),
                    SvgPicture.asset(
                      AppImages.icAr,
                      height: 9,
                      width: 9,
                      colorFilter: ColorFilter.mode(
                        !switchValue ? AppColors.white : AppColors.greyShade4,
                        BlendMode.srcIn,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
