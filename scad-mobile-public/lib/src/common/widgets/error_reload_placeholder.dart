import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class ErrorReloadPlaceholder extends StatelessWidget {
  const ErrorReloadPlaceholder(
      {required this.error, this.onReload, super.key});

  final String error;
  final VoidCallback? onReload;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Flexible(
                  child: Text(
                    error,
                    textScaler: TextScaler.linear(
                      textScaleFactor.value,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4,),
            if(onReload != null)
            OutlinedButton(
              onPressed: onReload,
              child: Text(LocaleKeys.reload.tr()),
            ),
          ],
        ),
      ),
    );
  }
}
