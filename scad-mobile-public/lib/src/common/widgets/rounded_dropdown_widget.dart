import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/features/domains/data/models/domain_model/domain_model.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class RoundedDropDownWidget<T> extends StatelessWidget {
  const RoundedDropDownWidget({
    super.key,
    this.showTitle = true,
    this.title,
    this.titleColor,
    this.width,
    this.items,
    this.value,
    this.onChanged,
    this.showBorder = true,
    this.isSquare = false,
    this.boxShadow = false,
    this.compute =false
  });

  final bool? showTitle;
  final String? title;
  final Color? titleColor;
  final double? width;
  final List<T>? items;
  final T? value;
  final bool showBorder;
  final bool isSquare;
  final bool boxShadow;
  final bool compute;
  final void Function(T?)? onChanged;

  @override
  Widget build(BuildContext context) {
    final ValueNotifier<bool> selected = ValueNotifier(false);
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null) ...[
          Text(
            title!,
            style: TextStyle(
              color: titleColor ?? AppColors.greyShade4,
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
            textScaler: TextScaler.linear(textScaleFactor.value),
          ),
          const SizedBox(height: 5),
        ],
        Opacity(
          opacity: (items ?? []).isNotEmpty ? 1 : 0.4,
          child: Container(
            width: width ?? MediaQuery.sizeOf(context).width,
            height: 40,
            decoration: boxShadow
                ? ShapeDecoration(
                    color:
                        isLightMode ? AppColors.white : AppColors.blueShade32,
                    shape: RoundedRectangleBorder(
                      borderRadius: isSquare
                          ? BorderRadius.circular(15)
                          : BorderRadius.circular(70),
                    ),
                    shadows: [
                      BoxShadow(
                        color: AppColors.shadow1,
                        blurRadius: 5,
                        offset: const Offset(1, 4),
                      ),
                    ],
                  )
                : BoxDecoration(
                    color:
                        isLightMode ? AppColors.white : AppColors.blueShade36,
                    borderRadius: isSquare
                        ? BorderRadius.circular(15)
                        : BorderRadius.circular(70),
                    border: showBorder
                        ? Border.all(
                            color: isLightMode
                                ? AppColors.greyShade1
                                : AppColors.blueShade32,
                          )
                        : null,
                  ),
            child:
            compute ?
            LayoutBuilder(
              builder: (BuildContext context, BoxConstraints constraints) {
                return DropdownMenu(
                    initialSelection:  value,
                    hintText:  LocaleKeys.select.tr(),
                  //  width: width ?? ( MediaQuery.sizeOf(context).width * .78),
                    width:constraints.maxWidth,
                    inputDecorationTheme: InputDecorationTheme(
                      labelStyle: TextStyle(
                        fontSize: 4 * textScaleFactor.value,
                      ),
                      prefixStyle: TextStyle(
                        fontSize: 4 * textScaleFactor.value,
                      ) ,
                      isDense: true,
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                      constraints: BoxConstraints.tight(const
                      Size.fromHeight(40) ,

                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    menuStyle: MenuStyle(
                      backgroundColor: MaterialStateProperty.resolveWith((states) {
                        return  isLightMode ? AppColors.white : AppColors.blueShade32; //your desired selected background color
                      }),
                      surfaceTintColor:  MaterialStateProperty.resolveWith((states) {
                        return   AppColors.white ; //your desired selected background color
                      }),
                    ),
                    onSelected: ( value) {
                      if (onChanged != null) {
                        onChanged?.call(value as T);
                        selected.value =false;
                      }
                    },
                    dropdownMenuEntries: [
                      ... items?.map((T value) {
                        return DropdownMenuEntry(
                          style : ButtonStyle(
                            backgroundColor:
                            MaterialStateProperty.resolveWith((states) {
                              return  isLightMode ? AppColors.white : AppColors.blueShade32;
                            }

                            ),
                            overlayColor:   MaterialStateProperty.resolveWith((states) {
                              return isLightMode ? AppColors.white : AppColors.blueShade32;
                            }

                            ),

                            foregroundColor:   MaterialStateProperty.resolveWith((states) {
                              return  isLightMode ?  AppColors.blueShade32: AppColors.white;
                            }


                            ),
                          ),

                          value: value,
                          label:_getDisplayLabel(value, context),
                        );
                      }).toList()??[]
                    ]);
              },
            ) :
            PopupMenuButton<T>(
              color: isLightMode ? AppColors.white : AppColors.blueShade32,
              surfaceTintColor: AppColors.white,
              onSelected: (T value) {
                if (onChanged != null) {
                  onChanged?.call(value);
                  selected.value =false;
                }
              },
              onOpened: () {
                selected.value =true;
              },
              onCanceled:() {
                selected.value =false;
               } ,
              itemBuilder: (BuildContext context) {
                return items?.map((T value) {
                      return PopupMenuItem<T>(
                        value: value,
                        child: Container(
                          width: width ?? MediaQuery.sizeOf(context).width,
                          padding: const EdgeInsets.all(8),
                          child: Text(
                            _getDisplayLabel(value, context),
                            textScaler:
                                TextScaler.linear(textScaleFactor.value),
                          ),
                        ),
                      );
                    }).toList() ??
                    [];
              },
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 15),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                      child: Text(
                        _getDisplayLabel(value, context),
                        style: TextStyle(
                          color: isLightMode
                              ? AppColors.blueShade36
                              : AppColors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                        textScaler: TextScaler.linear(textScaleFactor.value),
                      ),
                    ),
                    ValueListenableBuilder(
                        valueListenable: selected,
                        builder:  (context, value, _) {
                          if(selected.value) {
                            return const Icon(Icons.arrow_drop_up);
                          }else {
                            return  const Icon(Icons.arrow_drop_down);
                          }
                        })

                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  String _getDisplayLabel(T? value, BuildContext context) {
    final rtl = DeviceType.isDirectionRTL(context);
    if (value is String) {
      return value;
    } else if (value is DomainModel) {
      return rtl ? value.domainNameAr ?? '' : value.domainName ?? '';
    } else if (value is Properties) {
      return value.label ?? '';
    } else if (value is Visualizations) {
      return value.componentTitle ?? '';
    } else {
      return LocaleKeys.select.tr();
    }
  }
}
