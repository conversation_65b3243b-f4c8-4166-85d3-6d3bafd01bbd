import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:lottie/lottie.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

class LandingTopButton extends StatelessWidget {
  const LandingTopButton({
    required this.label,
    required this.onTap,
    this.icon,
    this.animatedIcon,
    this.isSmallWidget = false,
    super.key,
  });

  final bool isSmallWidget;
  final VoidCallback onTap;
  final String? icon;
  final String? animatedIcon;
  final String label;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Hero(
      tag: label,
      child: Material(
        color: isLightMode ? AppColors.white : AppColors.blueShade32,
        borderRadius: BorderRadius.circular(isSmallWidget ? 10 : 20),
        child: InkWell(
          customBorder: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(isSmallWidget ? 10 : 20),
          ),
          onTap: onTap,
          child: Container(
            decoration: BoxDecoration(
              boxShadow: [
                BoxShadow(
                  color: AppColors.greyShade12,
                  blurRadius: 50,
                  offset: const Offset(0, 35),
                  spreadRadius: -23,
                ),
              ],
            ),
            child: isSmallWidget
                ? SizedBox(
                    width: double.infinity,
                    height: 50 * textScaleFactor.value,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Flexible(
                            child: Text(
                              label,
                              style: TextStyle(
                                color: isLightMode
                                    ? AppColors.blueShade22
                                    : AppColors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                              textScaler:
                                  TextScaler.linear(textScaleFactor.value),
                            ),
                          ),
                          const SizedBox(width: 8),
                          if (animatedIcon != null)
                            ColorFiltered(
                              colorFilter: ColorFilter.mode(
                                isLightMode
                                    ? AppColors.blueLight
                                    : AppColors.white,
                                BlendMode.srcIn,
                              ),
                              child: Lottie.asset(
                                animatedIcon!,
                                repeat: true,
                                width: 14 * textScaleFactor.value,
                                height: 14 * textScaleFactor.value,
                              ),
                            )
                          else if (icon != null)
                            SvgPicture.asset(
                              icon!,
                              width: 14,
                              height: 14,
                              colorFilter: ColorFilter.mode(
                                isLightMode
                                    ? AppColors.blueLight
                                    : AppColors.white,
                                BlendMode.srcIn,
                              ),
                            ),
                        ],
                      ),
                    ),
                  )
                : Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 40,
                    ),
                    width: double.infinity,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (animatedIcon != null)
                          ColorFiltered(
                            colorFilter: ColorFilter.mode(
                              isLightMode
                                  ? AppColors.blueLightOld
                                  : AppColors.white,
                              BlendMode.srcIn,
                            ),
                            child: Lottie.asset(
                              animatedIcon!,
                              repeat: true,
                              height: 40,
                              width: 40,
                            ),
                          )
                        else if (icon != null)
                          SvgPicture.asset(
                            icon!,
                            height: 32,
                            width: 32,
                            colorFilter: ColorFilter.mode(
                              isLightMode
                                  ? AppColors.blueLight
                                  : AppColors.white,
                              BlendMode.srcIn,
                            ),
                          ),
                        const SizedBox(height: 11),
                        Text(
                          label,
                          style: TextStyle(
                            color: isLightMode
                                ? AppColors.blueShade22
                                : AppColors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                          textScaler: TextScaler.linear(textScaleFactor.value),
                        ),
                      ],
                    ),
                  ),
          ),
        ),
      ),
    );
  }
}
