import 'package:flutter/material.dart';

class CardCustomClipper extends CustomClipper<Path> {
  CardCustomClipper({super.reclip, required this.isRtl});

  final bool isRtl;
  
  @override
  Path getClip(Size size) {

    final double height = size.height;
    final double width = size.width;

    final Path path_0 = Path();
    
  if(isRtl){
    path_0..moveTo(0,65)
    ..cubicTo(0,56.4606,9.881,51,18,51)
    ..cubicTo(35.673,51,50,36.6731,50,19)
    ..cubicTo(50,10.577,55.673,0,64.096,0)
    ..lineTo(width,0)
    ..lineTo(width,height)
    ..lineTo(0,height)
    ..lineTo(0,65)
    ..close();
  }else{
    path_0..lineTo(0, 0)
    ..lineTo(0, height)
    ..lineTo(width, height)
    ..lineTo(width, 65)
    ..cubicTo(width, 56.4606, width - 9.88141, 51, width - 18, 51)
    ..cubicTo(width - 35.6731, 51, width - 50, 36.6731, width - 50, 19)
    ..cubicTo(width - 50, 10.577, width - 55.6727, 0, width - 64.0957, 0)
    ..close();
  }
    return path_0;
    
  }

  @override
  bool shouldReclip(CustomClipper oldClipper) {
    return true;
  }
}
