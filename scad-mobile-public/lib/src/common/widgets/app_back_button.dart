import 'dart:math' as math;

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class AppBackButton extends StatelessWidget {
  const AppBackButton({this.onBack, super.key});

  final Function? onBack;

  @override
  Widget build(BuildContext context) {
    final rtl = DeviceType.isDirectionRTL(context);
    return InkWell(
      onTap: () {
        context.router.pop();

        if (onBack != null) {
          onBack?.call();
        }
      },
      child:Center(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 14,
                      vertical: 14,
                    ),
                    child:
          rtl ?
            Transform(
              transform: Matrix4.rotationY(math.pi),
              child: SvgPicture.asset('assets/images/back-left.svg'),
            ) :
            SvgPicture.asset('assets/images/back-left.svg'),
                  ))
          // Text(
          //   LocaleKeys.back.tr(),
          //   style: TextStyle(
          //     color: AppColors.selectedChipBlue,
          //     fontWeight: FontWeight.w400,
          //   ),
          //   textScaler: TextScaler.linear(textScaleFactor.value),
          // ),
      
    );
  }
}
