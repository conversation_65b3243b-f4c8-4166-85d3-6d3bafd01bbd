import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_response_helper.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/intro_widget.dart';
import 'package:scad_mobile/src/features/details/presentation/widgets/details_page_title.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class FlatAppBar extends StatefulWidget {
  const FlatAppBar({
    required this.title,
    this.mainScreen = false,
    super.key,
    this.onBack,
    this.scrollController,
    this.toHideBellIcon = false,
    this.fromChatScreen = false,
    this.bellIconKey,
    this.searchIconKey,
    this.bottomPadding = 30,
    this.isOfficial = false,
    this.usingForDetailsPage = false,
    this.indicatorDetails,
  });

  final String title;
  final bool mainScreen;
  final VoidCallback? onBack;
  final ScrollController? scrollController;
  final bool toHideBellIcon;
  final bool fromChatScreen;
  final GlobalKey? bellIconKey;
  final GlobalKey? searchIconKey;
  final double? bottomPadding;
  final bool? isOfficial;
  final bool usingForDetailsPage;
  final IndicatorDetailsResponseHelper? indicatorDetails;

  @override
  State<FlatAppBar> createState() => _FlatAppBarState();
}

class _FlatAppBarState extends State<FlatAppBar> {
  ValueNotifier<bool> isExpanded = ValueNotifier<bool>(true);

  @override
  void initState() {
    super.initState();
    if (widget.scrollController != null) {
      widget.scrollController!.addListener(_onScroll);
    }
  }

  void _onScroll() {
    if (widget.scrollController != null) {
      if (widget.scrollController!.position.pixels <
          widget.scrollController!.position.minScrollExtent +
              (widget.fromChatScreen ? 0 : 10)) {
        isExpanded.value = true;
      } else if (widget.scrollController!.position.extentTotal >
          MediaQuery.sizeOf(context).height) {
        isExpanded.value = false;
      }
      if (widget.scrollController!.position.maxScrollExtent >
          (MediaQuery.sizeOf(context).height * 1.5)) {
        if (widget.scrollController!.position.userScrollDirection ==
            ScrollDirection.forward) {
          isExpanded.value = true;
        }
      }
    }

  }

  @override
  void dispose() {
    widget.scrollController?.removeListener(_onScroll);

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    final bool isArabic = HiveUtilsSettings.getAppLanguage() == 'ar';

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
      child: ValueListenableBuilder(
        valueListenable: isExpanded,
        builder: (context, domain, w) {
          return Column(
            children: [
              Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const SizedBox(height: 40),
                  Row(
                    children: [
                      Transform.flip(
                        flipX: isArabic,
                        child: appDrawerController.drawerButton(
                          lightIcon: isLightMode ? false : true,
                        ),
                      ),
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          child: AnimatedOpacity(
                            duration: const Duration(milliseconds: 300),
                            curve: Curves.fastEaseInToSlowEaseOut,
                            opacity: !domain ? 1 : 0,
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                Flexible(
                                  child: Text(
                                    widget.title,
                                    style: AppTextStyles.s20w5cBlackOrWhiteShade
                                        .copyWith(
                                      color: isLightMode
                                          ? AppColors.black
                                          : AppColors.white,
                                    ),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                    textAlign: TextAlign.center,
                                    textScaler: TextScaler.linear(
                                      textScaleFactor.value,
                                    ),
                                  ),
                                ),
                                if (widget.indicatorDetails != null)
                                  widget.indicatorDetails!.indicatorDetails
                                      .contentClassificationKey ==
                                      'official_statistics'
                                      ? Padding(
                                    padding: const EdgeInsets.only(
                                      left: 6,
                                      bottom: 6,
                                    ),
                                    child: SvgPicture.asset(
                                      AppImages.icOfficialActive,
                                    ),
                                  )
                                      : widget
                                      .indicatorDetails!
                                      .indicatorDetails
                                      .contentClassificationKey ==
                                      'experimental_statistics'
                                      ? Padding(
                                    padding: const EdgeInsets.only(
                                      left: 6,
                                      bottom: 6,
                                    ),
                                    child: SvgPicture.asset(
                                      AppImages.icExperimentalActive,
                                    ),
                                  )
                                      : const SizedBox()
                                else
                                  const SizedBox(),
                              ],
                            ),
                          ),
                        ),
                      ),
                      if (widget.searchIconKey != null)
                        IntroWidget(
                          stepKey: widget.searchIconKey ?? GlobalKey(),
                          stepIndex: 2,
                          totalSteps: 2,
                          title: LocaleKeys.globalSearch.tr(),
                          description: LocaleKeys.globalSearchGuideDesc.tr(),
                          arrowAlignment: Alignment.topRight,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          targetBorderRadius: 60,
                          arrowPadding: const EdgeInsets.only(
                            right: 2,
                            left: 2,
                            bottom: 10,
                          ),
                          child: searchIconButton(context),
                        )
                      else
                        searchIconButton(context),
                    ],
                  ),
                  AnimatedSize(
                    duration: const Duration(milliseconds: 300),
                    child: SizedBox(
                      height: isExpanded.value ? null : 0,
                      child: _expandedView(isLightMode),
                    ),
                  ),
                ],
              ),
            ],
          );
        },
      ),
    );
  }

  IconButton searchIconButton(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return IconButton(
      onPressed: () {
        context.router
            .removeWhere((route) => route.name == SearchScreenRoute.name);
        context.pushRoute(SearchScreenRoute());
      },
      icon: SvgPicture.asset(
        AppImages.icSearch,
        colorFilter: ColorFilter.mode(
          isLightMode ? const Color(0xFF37506A) : Colors.grey,
          BlendMode.srcIn,
        ),
      ),
    );
  }

  Widget _expandedView(bool isLightMode) {
    return Column(
      children: [
        Row(
          key: const Key('expanded'),
          // mainAxisSize: MainAxisSize.min,
          // crossAxisAlignment: CrossAxisAlignment.stretch,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,

          children: [
            // SizedBox(
            //   height: (widget.appBarHeight ?? 48) * textScaleFactor.value,
            //   child: widget.mainScreen
            //       ? const SizedBox()
            //       : Row(
            //           children: [
            //               ],
            //         ),
            // ),
            if (widget.mainScreen)
              const SizedBox()
            else
              InkWell(
                onTap: () {
                  Navigator.of(context).maybePop();
                  if (widget.onBack != null) {
                    widget.onBack?.call();
                  }
                },
                borderRadius: BorderRadius.circular(8),
                child: Center(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 14,
                      vertical: 14,
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        RotatedBox(
                          quarterTurns:
                              DeviceType.isDirectionRTL(context) ? 2 : 0,
                          child: SvgPicture.asset(
                            AppImages.icArrowLeft,
                            colorFilter: ColorFilter.mode(
                              isLightMode
                                  ? AppColors.blueLight
                                  : AppColors.blue,
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                        // Text(
                        //   LocaleKeys.back.tr(),
                        //   style: AppTextStyles.s14w4cBlue,
                        //   textScaler:
                        //       TextScaler.linear(textScaleFactor.value),
                        // ),
                      ],
                    ),
                  ),
                ),
              ),

            if (widget.usingForDetailsPage)
              Expanded(
                child: Padding(
                  padding: widget.mainScreen
                      ? const EdgeInsets.symmetric(horizontal: 10)
                      : (DeviceType.isDirectionRTL(context)
                          ? const EdgeInsets.only(left: 16)
                          : const EdgeInsets.only(right: 16)),
                  child: DetailsPageTitle(
                    title: widget.title,
                    isOfficial: widget.isOfficial,
                    indicatorDetails: widget.indicatorDetails,
                  ),
                ),
              )
            else
              Expanded(
                child: Center(
                  child: Padding(
                    padding: widget.mainScreen
                        ? const EdgeInsets.symmetric(horizontal: 8)
                        : (DeviceType.isDirectionRTL(context)
                            ? const EdgeInsets.only(left: 20)
                            : const EdgeInsets.only(right: 20)),
                    child: Text(
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                      widget.title,
                      textAlign: TextAlign.center,
                      style: AppTextStyles.s24w5cBlackOrWhiteShade.copyWith(
                        color: isLightMode
                            ? AppColors.blueTitleText
                            : AppColors.white,
                        fontWeight: FontWeight.w500,
                      ),
                      textScaler: TextScaler.linear(textScaleFactor.value),
                    ),
                  ),
                ),
              ),
          ],
        ),
        SizedBox(height: widget.bottomPadding),
      ],
    );
  }
}
