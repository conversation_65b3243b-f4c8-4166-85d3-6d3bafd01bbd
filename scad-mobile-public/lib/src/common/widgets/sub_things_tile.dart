import 'package:flutter/material.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/widgets/count_widget.dart';
import 'package:scad_mobile/src/common/widgets/small_tile_custom_clipper.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

class SubThingsTile extends StatelessWidget {
  const SubThingsTile({
    required this.thing,
    required this.onTap,
    required this.count,
    super.key,
  });

  final String thing;
  final int count;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    final rtl = DeviceType.isDirectionRTL(context);
    return GestureDetector(
      onTap: onTap,
      child: Stack(
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color:
                  isLightMode ? AppColors.white : AppColors.inidcatorCardDark,
              borderRadius: BorderRadius.circular(15),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
              
                Expanded(
                  
                  child: Text(
                    thing,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      color: isLightMode
                          ? AppColors.blackShade1
                          : AppColors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    textScaler: TextScaler.linear(textScaleFactor.value),
                  ),
                ),
                if (count > 0)
                  Padding(
                    padding: const EdgeInsets.only(right: 26, left: 8),
                    child: CountWidget(count: count),
                  ),
                   const SizedBox(
                  width: 20,
                ),
                     Icon(
                                Icons.chevron_right_rounded,
                                size: 18,
                                color: isLightMode ?  AppColors.blueShade22 :  AppColors.white,
                              ),
               
                 
              ],
            ),
          ),
        ],
      ),
    );
  }
}
