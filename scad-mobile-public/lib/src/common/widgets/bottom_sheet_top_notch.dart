import 'package:flutter/material.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

class BottomSheetTopNotch extends StatelessWidget {
  const BottomSheetTopNotch({super.key});

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Align(
      child: Container(
        width: 50,
        height: 5,
        decoration: ShapeDecoration(
          color: isLightMode ? AppColors.greyNotch : AppColors.blueShade36,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(50),
          ),
        ),
      ),
    );
  }
}
