import 'package:flutter/material.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:shimmer/shimmer.dart';

class AppShimmer extends StatelessWidget {
  const AppShimmer({super.key, this.height, this.width, this.radius = 12});

  final double? height;
  final double? width;
  final double radius;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Shimmer.fromColors(
      baseColor: isLightMode ? AppColors.greyShade10 : AppColors.blueShade30,
      highlightColor: isLightMode ? AppColors.greyShade1 : AppColors
          .blueShade36,
      child: Container(
        decoration: BoxDecoration(
          color: isLightMode ? AppColors.greyShade10 : AppColors.blueShade30,
          borderRadius: BorderRadius.circular(radius),
        ),
        height: height,
        width: width,
      ),
    );
  }
}
