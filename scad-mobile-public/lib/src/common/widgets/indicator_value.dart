import 'dart:ui' as ui;
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:lottie/lottie.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_response_helper.dart';
import 'package:scad_mobile/src/features/details/presentation/widgets/details_page_category_chip.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/animation_asset.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class IndicatorValue extends StatelessWidget {
  const IndicatorValue({
    required this.negativeArrow,
    required this.value,
    required this.unit,
    required this.numberUnit,
    super.key,
    this.onClearFilter,
    this.comparedIndicatorName,
    this.indicatorDetails,
  });

  final bool negativeArrow;
  final String? value;
  final String unit;
  final String numberUnit;
  final VoidCallback? onClearFilter;
  final String? comparedIndicatorName;
  final IndicatorDetailsResponseHelper? indicatorDetails;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return
      //Visibility(
      // visible:  value != null &&  double.tryParse(value ?? '') != null,
      // child:
      Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          if (onClearFilter == null && (value != null &&  double.tryParse(value ?? '') != null))
            Expanded(
              child: Text.rich(
                maxLines: 2,
                textScaler: TextScaler.linear(textScaleFactor.value),
                TextSpan(
                  children: [
                    WidgetSpan(
                      child: Padding(
                        padding: const EdgeInsets.only(bottom: 2),
                        child: RotatedBox(
                          quarterTurns: negativeArrow ? 2 : 0,
                          child: SvgPicture.asset(
                            AppImages.icTrendingUp,
                            colorFilter: ColorFilter.mode(
                              negativeArrow ? AppColors.red : AppColors.green,
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                      ),
                    ),
                    const WidgetSpan(
                      child: SizedBox(
                        width: 3,
                      ),
                    ),
                    WidgetSpan(
                      child: Padding(
                          padding: const EdgeInsets.only(bottom: 4),
                          child: Directionality(
                              textDirection: ui.TextDirection.ltr,
                              child: Text(
                                negativeArrow
                                    ? LocaleKeys.downTo.tr()
                                    : LocaleKeys.upTo.tr(),
                                style: TextStyle(
                                  color: isLightMode ? AppColors.black : AppColors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.w500,
                                ),
                              ))),),
                    const WidgetSpan(
                      child: SizedBox(
                        width: 3,
                      ),
                    ),
                    WidgetSpan(
                      alignment: PlaceholderAlignment.bottom,
                      baseline: TextBaseline.alphabetic,
                      child: Directionality(
                        textDirection: ui.TextDirection.ltr,
                        child: Text(
                          value ?? '',
                          style: TextStyle(
                            color: isLightMode ? AppColors.black : AppColors.white,
                            fontSize: 20,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ),
                    ),
                    const WidgetSpan(
                      child: SizedBox(
                        width: 3,
                      ),
                    ),
                    WidgetSpan(
                      child: Padding(
                          padding: const EdgeInsets.only(bottom: 4),
                          child: Directionality(
                            textDirection: ui.TextDirection.ltr,
                            child:     Text( numberUnit,
                              style: TextStyle(
                                color: isLightMode ? AppColors.black : AppColors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          )),),

                    const WidgetSpan(
                      child: SizedBox(
                        width: 3,
                      ),
                    ),
                    WidgetSpan(
                      child: Padding(
                          padding: const EdgeInsets.only(bottom: 4),
                          child: Directionality(
                            textDirection: ui.TextDirection.ltr,
                            child:  Text(
                              unit,
                              style: TextStyle(
                                color: isLightMode ? AppColors.black : AppColors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.w500,
                              ),
                            ),)),),
                  ],
                ),
              ),
            ),
          if (comparedIndicatorName == null && onClearFilter != null)
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                InkWell(
                  onTap: onClearFilter,
                  borderRadius: BorderRadius.circular(5),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        LocaleKeys.clear.tr(),
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color:  isLightMode
                              ? AppColors.blueLight
                              : AppColors.white,
                        ),
                        textScaler: TextScaler.linear(textScaleFactor.value),
                      ),
                      const SizedBox(width: 6),
                      // SvgPicture.asset(
                      //   AppImages.icSyncOutline,
                      //   height: 16,
                      // ),
                      if (isLightMode)
                        Lottie.asset(
                          AnimationAsset.animationSync,
                          width: 20,
                          height: 20,
                          fit: BoxFit.cover,
                        )
                      else
                        Lottie.asset(
                          AnimationAssetDark.animationSync,
                          width: 20,
                          height: 20,
                          fit: BoxFit.cover,
                        ),
                    ],
                  ),
                ),
              ],
            ),
          if (comparedIndicatorName == null && onClearFilter != null)
            DetailsPageCategoryChip(
              text: indicatorDetails?.indicatorDetails.tagName ?? '',
              icon: 'scenario-driver-icon',
              type: indicatorDetails?.indicatorDetails.type ?? '',
              classificationType:
              indicatorDetails?.indicatorDetails.contentClassificationKey ??
                  '',
            ),
        ],
        // ),
      );

  }
}
