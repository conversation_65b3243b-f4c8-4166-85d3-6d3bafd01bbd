import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

class AppRadioTile extends StatelessWidget {
  const AppRadioTile({
    required this.title,
    required this.isSelected,
    this.trailingIcon,
    this.onTap,
    super.key,
  });

  final String title;
  final bool isSelected;
  final String? trailingIcon;
  final Function? onTap;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return InkWell(
      onTap: () => onTap?.call(),
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 14,
        ),
        decoration: BoxDecoration(
          color: isSelected
              ? isLightMode
                  ? AppColors.blueSelectedRadio
                  : AppColors.blueLightOld.withOpacity(0.28)
              : isLightMode
                  ? Colors.transparent
                  : Colors.transparent,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(
            color: isLightMode
                ? isSelected
                    ?  AppColors.blueLight
                    : AppColors.greyShade1
                : isSelected
                ?  AppColors.blueLight
                : AppColors.greyShade1,
          ),
        ),
        child: Row(
          children: [
            SvgPicture.asset(
              'assets/images/${isSelected ? 'radio-selected' : 'radio-unselected'}.svg',
                colorFilter: isLightMode? ColorFilter.mode(isLightMode ?( isSelected ?   AppColors.blueLight:AppColors.greyShade1 ) :  isSelected ? AppColors.blueLight :  AppColors.white, BlendMode.srcIn):null,
            
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  color: isLightMode ? AppColors.grey : AppColors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                ),
                textScaler: TextScaler.linear(textScaleFactor.value),
              ),
            ),
            if (trailingIcon != null) ...[
              SvgPicture.asset(
                'assets/images/$trailingIcon.svg',
                colorFilter: ColorFilter.mode(
                  isSelected
                      ? AppColors.blueButton
                      : isLightMode
                          ? AppColors.grey
                          : AppColors.white,
                  BlendMode.srcIn,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
