import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:lottie/lottie.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/animation_asset.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class NoDataPlaceholder extends StatelessWidget {
  const NoDataPlaceholder({
    this.msg,
    super.key, this.padding = EdgeInsets.zero,
  });
  final String? msg;
  final EdgeInsets padding;

  @override
  Widget build(BuildContext context) {
        final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Padding(
      padding: padding,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (isLightMode)
            Lottie.asset(
              AnimationAsset.animationNoData,
              height: 110,
              width: 140,
            )
          else
            Lottie.asset(
              AnimationAssetDark.animationNoData,
              height: 110,
              width: 140,
            ),
          const SizedBox(height: 6),
          Text(
            msg ?? LocaleKeys.noDataAvailable.tr(),
            textAlign: TextAlign.center,
            style: TextStyle(
              color: AppColors.grey,
              fontSize: 12,
              fontWeight: FontWeight.w400,
            ),
            textScaler: TextScaler.linear(textScaleFactor.value),
          ),
        ],
      ),
    );
  }
}
