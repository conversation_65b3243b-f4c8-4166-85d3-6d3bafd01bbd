import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/functions/indicator_date_setting.dart';
import 'package:scad_mobile/src/common/functions/insight_discovery_value_setting.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/extentions/date_time_extensions.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

class SplineChart extends StatefulWidget {
  const SplineChart({
    required this.frequance,
    this.indicatorCard = false,
    this.chartDataList = const [],
    this.comparisonChartData = const [],
    this.forecastChartDataList = const [],
    super.key,
    this.isForecast = false,
    this.showMarker = true,
    // this.isSingleLineForecast = false,
    // this.isMultiLine = false,
    this.areaHighlightChartData = const [],
    this.isCompareActive = false,
    this.isLightMode,
    this.overrideAxisColorToDark = false,
    this.driver = false,
  });

  final List<List<SplineChartData>> chartDataList;
  final List<SplineChartData> comparisonChartData;
  final List<List<SplineChartData>> areaHighlightChartData;
  final List<List<SplineChartData>> forecastChartDataList;
  final bool? isForecast;
  final bool? showMarker;
  final bool indicatorCard;
  // final bool? isSingleLineForecast;
  // final bool? isMultiLine;
  final bool isCompareActive;
  final bool? isLightMode;
  final bool driver;
  final String frequance;
  final bool overrideAxisColorToDark;

  @override
  State<SplineChart> createState() => _SplineChartState();
}

class _SplineChartState extends State<SplineChart> {
  late final bool isLightMode;
  double zoomFactor = 0;
  double zoomPosition = 0;

  @override
  void initState() {
    isLightMode = widget.isLightMode ??
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final Map<String, DateTime> maxAndMin =
        IndicatorDateSetting.setMaxAndmMinValue(
            chartDataList: widget.chartDataList,
            forcast: widget.forecastChartDataList,
            frequancy: widget.frequance);
    final Map<String,double> maxAndMinXAxis =
    IndicatorDateSetting.setMaxAndmMinXAxisValue(
        chartDataList: widget.chartDataList,
        forcast: widget.forecastChartDataList,
        frequancy: widget.frequance);

    return SizedBox(
      height: 100,
      // constraints: const BoxConstraints(
      //   minHeight: 260,
      //   maxHeight: 400,
      // ),
      child: Padding(
        padding: const EdgeInsets.only(right: 1),
        child: SfCartesianChart(
          // tooltipBehavior: TooltipBehavior(
          //   builder: (
          //     dynamic data,
          //     dynamic point,
          //     dynamic series,
          //     int pointIndex,
          //     int seriesIndex,
          //   ) {
          //     return Container(
          //       padding: const EdgeInsets.all(8),
          //       decoration: BoxDecoration(
          //         color: isLightMode
          //             ? AppColors.newmorphicDark
          //             : AppColors.newmorphicLight,
          //         borderRadius: BorderRadius.circular(12),
          //       ),
          //       child: Text(
          //         '${DateTime.tryParse(point.x.toString())?.toFormattedDateTimeString('dd-MMM-yyyy')}: ${IsightDiscoveryValueSetting.getNumberUnit1(
          //               point.y,
          //             ) ?? ''}',
          //         style: TextStyle(color: AppColors.whiteOrBlack),
          //       ),
          //     );
          //   },
          //   enable: true,
          // ),
          trackballBehavior: TrackballBehavior(
            enable: true,
            activationMode: ActivationMode.singleTap,
            markerSettings: const TrackballMarkerSettings(
              height: 10,
              width: 10,
              markerVisibility: TrackballVisibilityMode.visible,
              borderColor: Colors.black,
              color: Colors.blue,
            ),
            tooltipAlignment: ChartAlignment.near,
            tooltipDisplayMode: TrackballDisplayMode.nearestPoint,
            builder: (context, trackballDetails) {
              return FittedBox(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8,vertical: 6,),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: AppColors.blackShade1,
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    '${IndicatorDateSetting.setupNameAll(
                      widget.frequance,
                      (trackballDetails.point?.x as DateTime)
                          .toFormattedDateTimeString('yyyy-MM-dd'),
                    )} : ${NumberFormat.compact().format(trackballDetails.point?.y)}',
                    textAlign: TextAlign.left,
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                ),
              );
            },
          ),
          backgroundColor: isLightMode
              ? widget.indicatorCard
                  ? Colors.transparent
                  : AppColors.blueShade29
              : widget.indicatorCard
                  ?  Colors.transparent
                  :  Colors.transparent,
          onZooming: (ZoomPanArgs args) {
            if (args.axis?.name == 'primaryXAxis') {
              setState(() {
                zoomFactor = args.currentZoomFactor;
                zoomPosition = args.currentZoomPosition;
              });
            }
          },
          onZoomReset: (ZoomPanArgs args) {
            setState(() {
              zoomFactor = 0;
              zoomPosition = 0;
            });
          },
          zoomPanBehavior: ZoomPanBehavior(
            maximumZoomLevel: 0.5,
            enablePinching: true,
            zoomMode: ZoomMode.x,
            enablePanning: true,
            enableDoubleTapZooming: true,
            enableMouseWheelZooming: true,
          ),
          plotAreaBorderWidth: 0,
          primaryXAxis: DateTimeAxis(
            // visibleMaximum: widget.isForecast == null ?  DateTime.tryParse(widget.chartDataList.first.last.label ?? '') : DateTime.tryParse(widget.forecastChartDataList.first.last.label ?? '') ,
            maximum: maxAndMin['max'],
            minimum: maxAndMin['min'],
            axisLine: AxisLine(
              width: 0.3,
              color: widget.overrideAxisColorToDark
                  ? (AppColors.greyF3F3F3)
                  : isLightMode
                      ? AppColors.black
                      : AppColors.white,
            ),
            majorTickLines: const MajorTickLines(width: 0),
            minorTickLines: const MinorTickLines(width: 0),
            majorGridLines: MajorGridLines(
              width: 10,
              color: (widget.overrideAxisColorToDark
                      ? (AppColors.greyF3F3F3)
                      : isLightMode
                          ? widget.driver
                              ? AppColors.blueShade21
                              : AppColors.greyShade15_1
                          : AppColors.blueShade33)
                  .withOpacity(.8),
            ),
            minorGridLines: const MinorGridLines(
              width: 1,
              color: Colors.transparent,
            ),
            interval: IndicatorDateSetting.getQuatrelyInterverl(
              widget.chartDataList,
              widget.forecastChartDataList,
              widget.frequance,
              maxAndMin,
            ),
            plotOffset: 18,
            //edgeLabelPlacement : EdgeLabelPlacement.shift,
            intervalType: widget.frequance == 'Yearly'
                ? DateTimeIntervalType.years
                : DateTimeIntervalType.months,
            axisLabelFormatter: (axisLabelRenderArgs) {
              return ChartAxisLabel(
                ' ${IndicatorDateSetting.setupNameAll(
                  widget.frequance,
                  DateTime.fromMillisecondsSinceEpoch(
                    axisLabelRenderArgs.value as int,
                  ).toFormattedDateTimeString('yyyy-MM-dd'),
                )} ',
                TextStyle(
                  color: widget.overrideAxisColorToDark
                      ? (AppColors.grey)
                      : isLightMode
                          ? AppColors.black
                          : AppColors.white,
                  fontSize: 10 * textScaleFactor.value,
                ),
              );
            },
          ),
          primaryYAxis: NumericAxis(
            maximum: maxAndMinXAxis['max'],
            minimum: maxAndMinXAxis['min'],
            name: 'yAxis1',
            numberFormat: NumberFormat.compactSimpleCurrency(name: ''),
            rangePadding: ChartRangePadding.round,
            axisLine: const AxisLine(
              width: 0,
            ),
            axisLabelFormatter: (axisLabelRenderArgs) {
              return ChartAxisLabel(
                axisLabelRenderArgs.text,
                TextStyle(
                  fontSize: 10 * textScaleFactor.value,
                  color: widget.comparisonChartData.isNotEmpty
                      ? isLightMode
                          ? AppColors.chartColorSet.first
                          : AppColors.chartColorSetDark.first
                      : widget.overrideAxisColorToDark
                          ? (AppColors.grey)
                          : isLightMode
                              ? AppColors.black
                              : AppColors.white,
                ),
              );
            },
            majorTickLines: const MajorTickLines(width: 0),
            minorTickLines: const MinorTickLines(width: 0),
            majorGridLines: const MajorGridLines(width: 0),
            minorGridLines: const MinorGridLines(width: 0),
          ),
          axes:
          <ChartAxis>[
            if(widget.isCompareActive)
            NumericAxis(
              maximum: maxAndMinXAxis['max'],
              minimum: maxAndMinXAxis['min'],
              name: 'yAxis2',
              opposedPosition: true,
              numberFormat: NumberFormat.compactSimpleCurrency(name: ''),
              rangePadding: ChartRangePadding.round,
              axisLine: const AxisLine(
                width: 0,
              ),
              axisLabelFormatter: (axisLabelRenderArgs) {
                return ChartAxisLabel(
                  axisLabelRenderArgs.text,
                  TextStyle(
                    fontSize: 10 * textScaleFactor.value,
                    color: AppColors.chartColorSet[1],
                  ),
                );
              },
              majorTickLines: const MajorTickLines(width: 0),
              minorTickLines: const MinorTickLines(width: 0),
              majorGridLines: const MajorGridLines(width: 0),
              minorGridLines: const MinorGridLines(width: 0),
            ),
            // DateTimeAxis(
            //   name: 'xAxis2',
            //   opposedPosition: true,
            //   // numberFormat: NumberFormat.compactSimpleCurrency(name: ''),
            //   rangePadding: ChartRangePadding.round,
            //   axisLine: AxisLine(
            //       color: isLightMode ? AppColors.black : AppColors.white),
            //   axisLabelFormatter: (axisLabelRenderArgs) {
            //     return ChartAxisLabel(
            //       IndicatorDateSetting.setupNameAll(
            //         widget.frequance,
            //         DateTime.fromMillisecondsSinceEpoch(
            //           axisLabelRenderArgs.value as int,
            //         ).toFormattedDateTimeString('yyyy-MM-dd'),
            //       ),
            //       TextStyle(
            //         fontSize: 10 * textScaleFactor.value,
            //         color: AppColors.compareChartLine,
            //       ),
            //     );
            //   },
            //   intervalType: widget.frequance == 'Yearly'
            //       ? DateTimeIntervalType.years
            //       : DateTimeIntervalType.months,
            //   majorTickLines: const MajorTickLines(width: 0),
            //   minorTickLines: const MinorTickLines(width: 0),
            //   majorGridLines: const MajorGridLines(width: 0),
            //   minorGridLines: const MinorGridLines(width: 0),
            //
            //   // enableAutoIntervalOnZooming: true,
            // ),
          ],
          series: <CartesianSeries<SplineChartData, DateTime>>[
            /// ACTUAL DATA

            for (int i = 0; i < widget.chartDataList.length; i++)
              SplineSeries<SplineChartData, DateTime>(
                name: 'xAxis',
                splineType: SplineType.cardinal,
                dataSource: widget.chartDataList[i],
                xValueMapper: (SplineChartData data, _) =>
                    IndicatorDateSetting.dateFormatterForChart(
                  widget.frequance,
                  data.label ?? '',
                ),
                yValueMapper: (SplineChartData data, _) => data.x,
                color: isLightMode
                    ? AppColors.chartColorSet[
                        widget.chartDataList.indexOf(widget.chartDataList[i]) %
                            AppColors.chartColorSet.length]
                    : AppColors.chartColorSetDark[
                        widget.chartDataList.indexOf(widget.chartDataList[i]) %
                            AppColors.chartColorSetDark.length],
                cardinalSplineTension: 0,
                markerSettings: MarkerSettings(
                  width: 6,
                  height: 6,
                  isVisible: widget.comparisonChartData.length > 24 ||
                          widget.chartDataList[i].length > 24
                      ? false
                      : true,
                  color: isLightMode
                      ? AppColors.chartColorSet[widget.chartDataList
                              .indexOf(widget.chartDataList[i]) %
                          AppColors.chartColorSet.length]
                      : AppColors.chartColorSetDark[widget.chartDataList
                              .indexOf(widget.chartDataList[i]) %
                          AppColors.chartColorSetDark.length],
                ),
                yAxisName: widget.isCompareActive
                    ? i == 0
                        ? 'yAxis1'
                        : 'yAxis2'
                    : 'yAxis1',
              ),

            // /// To display comparison data
            // if (widget.comparisonChartData.isNotEmpty)
            //   SplineSeries<SplineChartData, String>(
            //     splineType: SplineType.cardinal,
            //     dataSource: widget.comparisonChartData,
            //     xValueMapper: (SplineChartData data, _) => data.label,
            //     yValueMapper: (SplineChartData data, _) => data.x,
            //     color: AppColors.compareChartLine,
            //     cardinalSplineTension: 0,
            //     markerSettings: MarkerSettings(
            //       isVisible:
            //           widget.comparisonChartData.length > 24 ? false : true,
            //       color: AppColors.compareChartLine,
            //     ),
            //     yAxisName: 'yAxis2',
            //     xAxisName: 'xAxis2',
            //   ),

            /// to show triangular forecase
            if (widget.isForecast ?? false) ...[
              /// FORECASTING - first line
              for (final forecastChartData in widget.forecastChartDataList)
                SplineSeries<SplineChartData, DateTime>(
                  dataSource: forecastChartData,
                  xValueMapper: (SplineChartData sales, _) =>
                      IndicatorDateSetting.dateFormatterForChart(
                    widget.frequance,
                    sales.label ?? '',
                  ),
                  yValueMapper: (SplineChartData sales, _) => sales.x,
                  dashArray: const <double>[3, 3],
                  color: isLightMode
                      ? AppColors.chartColorSet[widget.forecastChartDataList
                              .indexOf(forecastChartData) %
                          AppColors.chartColorSet.length]
                      : AppColors.chartColorSetDark[widget.forecastChartDataList
                              .indexOf(forecastChartData) %
                          AppColors.chartColorSetDark.length],
                  name: 'Forecast',
                  markerSettings: MarkerSettings(
                    isVisible: (widget
                                    .chartDataList[widget.forecastChartDataList
                                        .indexOf(forecastChartData)]
                                    .length +
                                forecastChartData.length) >
                            24
                        ? false
                        : true,
                    color: isLightMode
                        ? AppColors.chartColorSet[widget.forecastChartDataList
                                .indexOf(forecastChartData) %
                            AppColors.chartColorSet.length]
                        : AppColors.chartColorSetDark[widget
                                .forecastChartDataList
                                .indexOf(forecastChartData) %
                            AppColors.chartColorSetDark.length],
                    shape: DataMarkerType.rectangle,
                  ),
                ),

              // /// FORECASTING - last line
              for (final dataList in widget.areaHighlightChartData)
                SplineSeries<SplineChartData, DateTime>(
                  dataSource: dataList,
                  xValueMapper: (SplineChartData sales, _) =>
                      IndicatorDateSetting.dateFormatterForChart(
                    widget.frequance,
                    sales.label ?? '',
                  ),
                  yValueMapper: (SplineChartData sales, _) => sales.y,
                  dashArray: const <double>[3, 3],
                  splineType: SplineType.cardinal,
                  color: isLightMode
                      ? AppColors.chartColorSet.first
                      : AppColors.chartColorSetDark.first,
                  name: 'Forecast',
                ),

              // /// FORECASTING - area highlighting
              for (final dataList in widget.areaHighlightChartData)
                SplineRangeAreaSeries<SplineChartData, DateTime>(
                  dataSource: dataList,
                  xValueMapper: (SplineChartData sales, _) =>
                      IndicatorDateSetting.dateFormatterForChart(
                    widget.frequance,
                    sales.label ?? '',
                  ),
                  highValueMapper: (SplineChartData sales, _) => sales.x,
                  lowValueMapper: (SplineChartData sales, _) => sales.y,
                  splineType: SplineType.cardinal,
                  color: isLightMode
                      ? AppColors.chartColorSet.first.withOpacity(0.2)
                      : AppColors.chartColorSetDark.first.withOpacity(0.2),
                  name: 'Forecast',
                ),

              // SplineSeries<SplineChartData, String>(
              //   dataSource: [
              //     SplineChartData('May', 124.625),
              //     SplineChartData('Jun', 131.5619496),
              //   ],
              //   xValueMapper: (SplineChartData sales, _) => sales.label,
              //   yValueMapper: (SplineChartData sales, _) => sales.x,
              //   splineType: SplineType.cardinal,
              //   markerSettings: MarkerSettings(
              //     isVisible: true,
              //     color: AppColors.green3BD6AD,
              //   ),
              //   color: AppColors.green3BD6AD,
              //   name: 'Forecast',
              // ),
              /*
              //   /// FORECASTING - centre line
              //   SplineSeries<SplineChartData, String>(
              //     dataSource: [
              //       SplineChartData('May', 124.625),
              //       SplineChartData('Jun', 131.5619496),
              //     ],
              //     xValueMapper: (SplineChartData sales, _) => sales.label,
              //     yValueMapper: (SplineChartData sales, _) => sales.x,
              //     splineType: SplineType.cardinal,
              //     markerSettings: MarkerSettings(
              //       isVisible: true,
              //       color: AppColors.green3BD6AD,
              //     ),
              //     color: AppColors.green3BD6AD,
              //     name: 'Forecast',
              //   ),
              //
              //   /// FORECASTING - last line
              //   SplineSeries<SplineChartData, String>(
              //     dataSource: [
              //       SplineChartData('May', 124.62499823991354),
              //       SplineChartData('Jun', 124.36989633192702),
              //     ],
              //     xValueMapper: (SplineChartData sales, _) => sales.label,
              //     yValueMapper: (SplineChartData sales, _) => sales.x,
              //     dashArray: const <double>[3, 3],
              //     splineType: SplineType.cardinal,
              //     color: AppColors.green3BD6AD,
              //     name: 'Forecast',
              //   ),
              //
              //   /// FORECASTING - area highlighting
              //   SplineRangeAreaSeries<SplineChartData, String>(
              //     dataSource: [
              //       SplineChartData('May', 124.62499823991354,
              //           y: 124.36989633192702),
              //       SplineChartData('Jun', 124.62499823991354,
              //           y: 140.31325555426776),
              //     ],
              //     xValueMapper: (SplineChartData sales, _) => sales.label,
              //     highValueMapper: (SplineChartData sales, _) => sales.x,
              //     lowValueMapper: (SplineChartData sales, _) => sales.y,
              //     splineType: SplineType.cardinal,
              //     color: AppColors.green3BD6AD.withOpacity(0.2),
              //     name: 'Forecast',
              //   ),
              // ],
              //
              // /// to show single line dotted forecase
              // if (isSingleLineForecast ?? false) ...[
              //   SplineSeries<SplineChartData, String>(
              //     dataSource: [
              //       SplineChartData('May', 124.62499823991354),
              //       SplineChartData('Jun', 124.36989633192702),
              //     ],
              //     xValueMapper: (SplineChartData sales, _) => sales.label,
              //     yValueMapper: (SplineChartData sales, _) => sales.x,
              //     splineType: SplineType.cardinal,
              //     dashArray: const <double>[3, 3],
              //     color: AppColors.green3BD6AD,
              //     markerSettings: MarkerSettings(
              //       isVisible: showMarker,
              //       color: AppColors.green3BD6AD,
              //     ),
              //     name: 'Forecast',
              //   ),
            ],

            /// to show multi line spline charts
            if (isMultiLine ?? false) ...[
              /// Line 2
              SplineSeries<SplineChartData, String>(
                splineType: SplineType.cardinal,
                dataSource: [
                  SplineChartData('Jan', 120.32691),
                  SplineChartData('Feb', 125.5229),
                  SplineChartData('Mar', 127.77586),
                  SplineChartData('Apr', 130.33594),
                  SplineChartData('May', 114.625),
                ],
                xValueMapper: (SplineChartData data, _) => data.label,
                yValueMapper: (SplineChartData data, _) => data.x,
                color: AppColors.blue30619D,
                markerSettings: MarkerSettings(
                  isVisible: showMarker,
                  color: AppColors.blue30619D,
                ),
              ),

              /// Line 3
              SplineSeries<SplineChartData, String>(
                splineType: SplineType.cardinal,
                dataSource: [
                  SplineChartData('Jan', 123.32691),
                  SplineChartData('Feb', 127.5229),
                  SplineChartData('Mar', 136.77586),
                  SplineChartData('Apr', 140.33594),
                  SplineChartData('May', 150.625),
                ],
                xValueMapper: (SplineChartData data, _) => data.label,
                yValueMapper: (SplineChartData data, _) => data.x,
                color: AppColors.green98C21B,
                markerSettings: MarkerSettings(
                  isVisible: showMarker,
                  color: AppColors.green98C21B,
                ),
              ),*/
            ],
          ],
        ),
      ),
    );
  }
}

/// Temporary model only for testing
class SplineChartData {
  SplineChartData(
    this.label,
    this.x, {
    this.y,
    this.legend,
    this.tempLabel,
  });

  final String? label;
  final DateTime? tempLabel;
  final num? x;
  final num? y;
  final String? legend;
}

/// test
String fetchMonth(String date) {
  final List<String> months = [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Oct',
    'Nov',
    'Dec',
  ];

  final List<String> dateParts = date.split('-');
  final int month = int.tryParse(dateParts[1]) ?? 0;

  if (month >= 1 && month <= 12) {
    return months[month - 1];
  }

  return '';
}
