import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/extentions/num_extension.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

typedef TooltipTextFormatter = String Function(dynamic chartPoint);

class AppChart {
  static TooltipBehavior tooltipBehavior({String? formatter}) {
    return TooltipBehavior(
      enable: true,
      tooltipPosition: TooltipPosition.pointer,
      shadowColor: Colors.transparent,
      color: AppColors.blackShade1,
      elevation: 0,
      borderWidth: 0,
      format: formatter??'point.x: point.y',
      textAlignment: ChartAlignment.near,
      textStyle: const TextStyle(color: Colors.white, fontSize: 12),
      // builder: (data, chartPoint, chartSeries, int pointIndex, int seriesIndex) {
      //   return FittedBox(
      //     child: Container(
      //       padding: const EdgeInsets.symmetric(
      //         horizontal: 8,
      //         vertical: 6,
      //       ),
      //       decoration: BoxDecoration(
      //         borderRadius: BorderRadius.circular(10),
      //         color: AppColors.blackShade1,
      //       ),
      //       alignment: Alignment.center,
      //       child: Text(
      //         '${chartSeries.dataSource?[pointIndex].y}',
      //         // formatter?.call(data) ??
      //         //     '${data.x}: ${num.tryParse(data.y.toString())?.truncateDecimalPoints()}',
      //         style: const TextStyle(color: Colors.white, fontSize: 12),
      //       ),
      //     ),
      //   );
      // },
    );
  }

  // This TrackballBehavior should not be used for horizontal charts
  // unless it is updated to show the tooltip for higher values.
  static TrackballBehavior trackballBehavior({
    String Function(TrackballDetails? trackballDetails)? tooltipStringFn,
  }) {
    return TrackballBehavior(
      enable: true,
      activationMode: ActivationMode.singleTap,
      markerSettings: const TrackballMarkerSettings(
        height: 10,
        width: 10,
        markerVisibility: TrackballVisibilityMode.visible,
        borderColor: Colors.black,
        color: Colors.blue,
      ),
      tooltipAlignment: ChartAlignment.near,
      tooltipDisplayMode: TrackballDisplayMode.nearestPoint,
      builder: (context, trackballDetails) {
        return FittedBox(
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 8,
              vertical: 6,
            ),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: AppColors.blackShade1,
            ),
            alignment: Alignment.center,
            child: Text(
              tooltipStringFn?.call(trackballDetails) ??
                  '${trackballDetails.point?.x} : ${NumberFormat.compact().format(trackballDetails.point?.y)}',
              textAlign: TextAlign.left,
              style: const TextStyle(color: Colors.white, fontSize: 12),
            ),
          ),
        );
      },
    );
  }
}
