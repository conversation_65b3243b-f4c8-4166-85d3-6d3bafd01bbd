import 'package:flutter/material.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/extentions/color_extensions.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:syncfusion_flutter_core/theme.dart';
import 'package:syncfusion_flutter_treemap/treemap.dart';

class TreemapChart extends StatefulWidget {
  const TreemapChart({
    required this.chartSeriesData,
    // required this.colorMappers,
    super.key,
  });
  final List<SeriesMeta> chartSeriesData;
  // final List<TreemapColorMapper> colorMappers;

  @override
  State<TreemapChart> createState() => _TreemapChartState();
}

class _TreemapChartState extends State<TreemapChart> {
  List<TreemapColorMapper> colorMappers = [];
  List<TreeMapChartData> chartData = [];

  @override
  void didUpdateWidget(covariant TreemapChart oldWidget) {
    getChartData();
    super.didUpdateWidget(oldWidget);
  }

  void getChartData() {
    final rtl = DeviceType.isDirectionRTL(context);
    chartData = widget.chartSeriesData
        .map(
          (e) => TreeMapChartData(
            sector: e.data?.firstOrNull?[rtl ? 'SECTOR_AR' : 'SECTOR'].toString() ?? '',
            currentValue: double.parse(
              '${e.data?.firstOrNull?['VALUE_CURRENT'] ?? '0'}',
            ),
            forecastedValue: double.parse(
              '${e.data?.firstOrNull?['VALUE_FORECAST'] ?? '0'}',
            ),
            qqChange: double.parse(
              '${e.data?.firstOrNull?['CHANGE'] ?? '0'}',
            ),
            yyChange: double.parse(
              '${e.data?.firstOrNull?['CHANGE_PY'] ?? '0'}',
            ),
            proportionOfTotalEconomy: double.parse(
              '${e.data?.firstOrNull?['VALUE_PERC_ECO'] ?? '0'}',
            ),
          ),
        )
        .toList();
    colorMappers.clear();
    for (int i = 0; i < widget.chartSeriesData.length; i++) {
      colorMappers.add(
        TreemapColorMapper.value(
          value: widget.chartSeriesData[i].data?.first['CHANGE'].toString(),
          color: widget.chartSeriesData[i].color.toString().toColor(),
        ),
      );
    }
    setState(() {});
    // Future.delayed(const Duration(milliseconds: 200), () {
    //   if (chartData.isNotEmpty) {
    //   }
    // });
  }

  @override
  Widget build(BuildContext context) {
    getChartData();
    if (chartData.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    return SfTreemap(
      dataCount: chartData.length,
      weightValueMapper: (int index) {
        return chartData[index].proportionOfTotalEconomy;
      },
      colorMappers: colorMappers,
      levels: [
        TreemapLevel(
          groupMapper: (int index) {
            return chartData[index].sector;
          },
          colorValueMapper: (TreemapTile tile) {
            return chartData[tile.indices.first].qqChange.toString();
          },
          labelBuilder: (BuildContext context, TreemapTile tile) {
            return Center(
              child: Text.rich(
                textScaler: TextScaler.linear(textScaleFactor.value),
                maxLines: 2,
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
                TextSpan(
                  children: [
                    TextSpan(
                      text: '${tile.group}:',
                      style: const TextStyle(fontSize: 10),
                    ),
                    TextSpan(
                      text:
                          ' ${chartData[tile.indices.first].qqChange.toStringAsFixed(1)}',
                      style: const TextStyle(fontSize: 10),
                    ),
                  ],
                ),
              ),
            );
          },
          tooltipBuilder: (BuildContext context, TreemapTile tile) {
            return Padding(
              padding: const EdgeInsets.all(8),
              child: Text(
                toolTipString(tile),
                textScaler: TextScaler.linear(textScaleFactor.value),
                style: AppTextStyles.s12w5cBlack,
              ),
            );
          },
        ),
      ],
      tooltipSettings: TreemapTooltipSettings(
        color: AppColors.greyFAFAFA,
      ),
    );
  }

  String toolTipString(TreemapTile tile) {
    return '${tile.group}\n'
        'Current Value: ${chartData[tile.indices.first].currentValue.toStringAsFixed(1)}\n'
        'Forecasted Value: ${chartData[tile.indices.first].forecastedValue.toStringAsFixed(1)}\n'
        'Q/Q Change: ${chartData[tile.indices.first].qqChange.toStringAsFixed(1)}\n'
        'Y/Y Change: ${chartData[tile.indices.first].yyChange.toStringAsFixed(1)}\n'
        'Proportion of Total Economy: ${chartData[tile.indices.first].proportionOfTotalEconomy.toStringAsFixed(1)}%';
  }
}

class TreeMapChartData {
  const TreeMapChartData({
    required this.sector,
    required this.proportionOfTotalEconomy,
    required this.qqChange,
    required this.yyChange,
    required this.currentValue,
    required this.forecastedValue,
  });

  final String sector; // SECTOR/SECTOR_AR
  final double proportionOfTotalEconomy; // VALUE_PERC_ECO
  final double qqChange; // CHANGE
  final double yyChange; // CHANGE_PY
  final double currentValue; // VALUE_CURRENT
  final double forecastedValue; // VALUE_FORECAST
}
