import 'dart:developer';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/functions/indicator_date_setting.dart';
import 'package:scad_mobile/src/common/functions/insight_discovery_value_setting.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/extentions/date_time_extensions.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

class ColumnChart extends StatefulWidget {
  const ColumnChart({
    required this.chartDataList,
    required this.frequance,
    super.key,
    // this.isMultiColumn = false,
    this.isForecast = false,
    this.comparisonChartData = const [],
    this.forecastChartDataList = const [],
    this.dateRepresentation,
    this.isLightMode,
    this.isCompareActive = false,
    this.overrideAxisColorToDark = false,
  });

  // final bool? isMultiColumn;
  final bool isForecast;
  final List<List<ColumnChartData>> chartDataList;
  final List<List<ColumnChartData>> forecastChartDataList;
  final List<ColumnChartData> comparisonChartData;
  final String? dateRepresentation;
  final bool? isLightMode;
  final String frequance;
  final bool isCompareActive;
  final bool overrideAxisColorToDark;

  @override
  State<ColumnChart> createState() => _ColumnChartState();
}

class _ColumnChartState extends State<ColumnChart> {
  late final bool isLightMode;
  double zoomFactor = 0;
  double zoomPosition = 0;

  @override
  void initState() {
    isLightMode = widget.isLightMode ??
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final Map<String, DateTime> maxAndMin =
        IndicatorDateSetting.setMaxAndmMinValueColumnChart(
      chartDataList: widget.chartDataList,
      forcast: widget.forecastChartDataList,
      frequancy: widget.frequance,
    );
    return SizedBox(
      height: 100,
      child: Padding(
        padding: const EdgeInsets.only(right: 1),
        child: SfCartesianChart(
          trackballBehavior: TrackballBehavior(
            enable: true,
            activationMode: ActivationMode.singleTap,
            markerSettings: const TrackballMarkerSettings(
              height: 10,
              width: 10,
              markerVisibility: TrackballVisibilityMode.visible,
              borderColor: Colors.black,
              color: Colors.blue,
            ),
            tooltipAlignment: ChartAlignment.near,
            tooltipDisplayMode: TrackballDisplayMode.nearestPoint,
            builder: (context, trackballDetails) {
              return FittedBox(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8,vertical: 6,),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: AppColors.blackShade1,
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    '${IndicatorDateSetting.setupNameAll(
                      widget.frequance,
                      (trackballDetails.point?.x as DateTime)
                          .toFormattedDateTimeString('yyyy-MM-dd'),
                    )} : ${NumberFormat.compact().format(trackballDetails.point?.y)}',
                    textAlign: TextAlign.left,
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                ),
              );
            },
          ),
          onZooming: (ZoomPanArgs args) {
            if (args.axis?.name == 'primaryXAxis') {
              setState(() {
                zoomFactor = args.currentZoomFactor;
                zoomPosition = args.currentZoomPosition;
              });
            }
          },
          onZoomReset: (ZoomPanArgs args) {
            setState(() {
              zoomFactor = 0;
              zoomPosition = 0;
            });
          },
          zoomPanBehavior: ZoomPanBehavior(
            maximumZoomLevel: 0.5,
            enablePinching: true,
            zoomMode: ZoomMode.x,
            enablePanning: true,
            enableDoubleTapZooming: true,
            enableMouseWheelZooming: true,
          ),
          backgroundColor:
              isLightMode ? AppColors.blueShade29 : AppColors.blueShade32,
          plotAreaBorderWidth: 0,
          primaryXAxis: DateTimeCategoryAxis(
            maximum: maxAndMin['max'],
            minimum: maxAndMin['min'],
            axisLine: AxisLine(
              color: widget.overrideAxisColorToDark
                  ? (AppColors.greyF3F3F3)
                  : AppColors.greyF3F4F6,
            ),
            majorTickLines: const MajorTickLines(width: 0),
            majorGridLines: MajorGridLines(
              width: 10,
              color: widget.overrideAxisColorToDark
                  ? (AppColors.greyF3F3F3)
                  : isLightMode
                      ? AppColors.greyShade15_1
                      : AppColors.blueShade33,
            ),
            interval: widget.frequance == 'Yearly'
                ? 1
                : widget.frequance == 'Quarterly'
                    ? 2
                    : 3,
            plotOffset: 18,
            dateFormat: widget.frequance == 'Monthly'
                ? DateFormat('MMM yyyy')
                : widget.frequance == 'Yearly'
                    ? DateFormat('yyyy')
                    : DateFormat('yyyy-M-dd'),
            axisLabelFormatter: (axisLabelRenderArgs) {
              log('axisLabelRenderArgs.value -> ${axisLabelRenderArgs.value} --- axisLabelRenderArgs.text -> ${axisLabelRenderArgs.text}');
              log(axisLabelRenderArgs.currentDateFormat.toString());
              return ChartAxisLabel(
                widget.frequance == 'Quarterly'
                    ? IndicatorDateSetting.setupNameAll(
                        'Quarterly', axisLabelRenderArgs.text)
                    : axisLabelRenderArgs.text,
                TextStyle(
                  color: widget.overrideAxisColorToDark
                      ? (AppColors.grey)
                      : isLightMode
                          ? AppColors.black
                          : AppColors.white,
                  fontSize: 10 * textScaleFactor.value,
                ),
              );
            },
          ),
          primaryYAxis: NumericAxis(
            name: 'yAxis1',
            numberFormat: NumberFormat.compactSimpleCurrency(name: ''),
            rangePadding: ChartRangePadding.round,
            axisLine: AxisLine(color: AppColors.greyF3F4F6),
            axisLabelFormatter: (axisLabelRenderArgs) {
              return ChartAxisLabel(
                axisLabelRenderArgs.text,
                TextStyle(
                  fontSize: 10 * textScaleFactor.value,
                  color: widget.comparisonChartData.isNotEmpty
                      ? isLightMode
                          ? AppColors.chartColorSet.first
                          : AppColors.chartColorSetDark.first
                      : widget.overrideAxisColorToDark
                          ? (AppColors.grey)
                          : isLightMode
                              ? AppColors.black
                              : AppColors.white,
                ),
              );
            },
            majorTickLines: const MajorTickLines(width: 0),
            minorTickLines: const MinorTickLines(width: 0),
            majorGridLines: const MajorGridLines(width: 0),
            minorGridLines: const MinorGridLines(width: 0),
          ),
          axes: [
            if (widget.isCompareActive)
              NumericAxis(
                name: 'yAxis2',
                opposedPosition: true,
                numberFormat: NumberFormat.compactSimpleCurrency(name: ''),
                rangePadding: ChartRangePadding.round,
                axisLine: AxisLine(color: AppColors.greyF3F4F6),
                axisLabelFormatter: (axisLabelRenderArgs) {
                  return ChartAxisLabel(
                    axisLabelRenderArgs.text,
                    TextStyle(
                      fontSize: 10 * textScaleFactor.value,
                      color: AppColors.chartColorSet[1],
                    ),
                  );
                },
                majorTickLines: const MajorTickLines(width: 0),
                minorTickLines: const MinorTickLines(width: 0),
                majorGridLines: const MajorGridLines(width: 0),
                minorGridLines: const MinorGridLines(width: 0),
              ),
          ],
          series: <CartesianSeries<ColumnChartData, DateTime>>[
            for (final chartData in widget.chartDataList)
              ColumnSeries<ColumnChartData, DateTime>(
                name: 'xAxis',
                width: 0.5,
                spacing: 0.4,
                dataSource: chartData,
                xValueMapper: (ColumnChartData data, _) {
                  print(
                      'data.x -> ${data.x} --- ${IndicatorDateSetting.dateFormatterForChart(
                    widget.frequance,
                    data.x ?? '',
                  )}');
                  return IndicatorDateSetting.dateFormatterForChart(
                    widget.frequance,
                    data.x ?? '',
                  );
                },
                yValueMapper: (ColumnChartData data, _) => data.y,
                color: isLightMode
                    ? AppColors.chartColorSet[
                        widget.chartDataList.indexOf(chartData) %
                            AppColors.chartColorSet.length]
                    : AppColors.chartColorSetDark[
                        widget.chartDataList.indexOf(chartData) %
                            AppColors.chartColorSetDark.length],
                yAxisName: widget.isCompareActive
                    ? widget.chartDataList.indexOf(chartData) == 0
                        ? 'yAxis1'
                        : 'yAxis2'
                    : 'yAxis1',
              ),
            if (widget.comparisonChartData.isNotEmpty)
              ColumnSeries<ColumnChartData, DateTime>(
                width: 0.3,
                spacing: 0.2,
                dataSource: widget.comparisonChartData,
                xValueMapper: (ColumnChartData data, _) =>
                    IndicatorDateSetting.dateFormatterForChart(
                  widget.frequance,
                  data.x ?? '',
                ),
                yValueMapper: (ColumnChartData data, _) => data.y,
                color: AppColors.compareChartLine,
                yAxisName: 'yAxis2',
              ),
          ],
        ),
      ),
    );
  }
}

class ColumnChartData {
  ColumnChartData(this.x, this.y, this.y1, this.y2);

  final String x;
  final double? y;
  final double? y1;
  final double? y2;
}
