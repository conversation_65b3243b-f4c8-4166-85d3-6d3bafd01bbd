import 'dart:async';
import 'dart:convert';
import 'package:intl/intl.dart';
import 'package:scad_mobile/src/common/widgets/charts/column_chart.dart';
import 'package:scad_mobile/src/common/widgets/charts/spline_chart.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_response_helper.dart';
import 'package:scad_mobile/src/features/details/domain/usecases/frequency_selector_model.dart';

import '../widgets/indicator_card/data/models/indicator_details_response.dart';

class IndicatorDateSetting {
  /// for getting quarterlyValue to yearly
  static List<List<Map<String, dynamic>>> getQuarterlyValue(
      {List<List<Map<String, dynamic>>?> data = const [],
      bool isForecast = false,
      Map<String, dynamic>? joinValue}) {
    const String type = 'year';
    final List<List<Map<String, dynamic>>> filteredValue = [];

    for (final values in data) {
      if (values != null && values.isNotEmpty) {
        final List<String> yearlastList =
            values.last['OBS_DT'].toString().split('-');
        int balaceValue = ((int.parse(yearlastList[1]) % 12) / 3).floor() +
            ((int.parse(yearlastList[1]) % 12) == 0 ? 0 : 1);
        final List<Map<String, dynamic>> filteredList = List.from(values);
        List<Map<String, dynamic>> balanceList = [];

        if (balaceValue > 0) {
          if ((filteredList.length - balaceValue) < 0) {
            // filteredList.length = balaceValue;
            balaceValue = 0;
          } else {
            balanceList = List.from(
              filteredList.reversed
                  .take(balaceValue)
                  .toList()
                  .reversed
                  .toList(),
            );
            filteredList.length = filteredList.length - balaceValue;
          }
        }
        final List<List<Map<String, dynamic>>> splitedData =
            sliceIt(chunkSize: 4, list: filteredList);
        if (balanceList.isNotEmpty) {
          splitedData.add(balanceList);
        }
        final List<Map<String, dynamic>> finalFilterList = getSplitedYear(
            list: splitedData,
            type: type,
            isForecast: isForecast,
            joinValue: joinValue);
        filteredValue.add(finalFilterList);
      } else if (values != null) {
        filteredValue.add(values);
      }
    }
    return filteredValue;
  }

  /// for getting monthlyValue to quatery and yearly
  static List<List<Map<String, dynamic>>> getMonthlyValue(
      {List<List<Map<String, dynamic>>?> data = const [],
      String type = 'qr',
      bool isForecast = false,
      Map<String, dynamic>? joinValue}) {
    final List<List<Map<String, dynamic>>> filteredValue = [];

    for (final values in data) {
      if (values != null && values.isNotEmpty) {
        final List<String> yearlastList =
            values.last['MAX_OBS_DT'].toString().split('-');
        final int balaceValue = type == 'qr'
            ? int.parse(yearlastList[1]) % 3
            : int.parse(yearlastList[1]) % 12;
        final List<Map<String, dynamic>> filteredList = List.from(values);
        List<Map<String, dynamic>> balanceList = [];

        if (balaceValue > 0) {
          balanceList = List.from(
            filteredList.reversed.take(balaceValue).toList().reversed.toList(),
          );
          filteredList.length = filteredList.length - balaceValue;
        }

        final List<List<Map<String, dynamic>>> splitedData =
            sliceIt(chunkSize: type == 'qr' ? 3 : 12, list: filteredList);
        if (balanceList.isNotEmpty) {
          splitedData.add(balanceList);
        }
        final List<Map<String, dynamic>> finalFilterList = getSplitedYear(
            list: splitedData,
            type: type,
            isForecast: isForecast,
            joinValue: joinValue);
        filteredValue.add(finalFilterList);
      }
    }
    return filteredValue;
  }

  /// for getting splitter year
  static List<Map<String, dynamic>> getSplitedYear(
      {List<List<Map<String, dynamic>>> list = const [],
      String type = 'qr',
      bool isForecast = false,
      Map<String, dynamic>? joinValue}) {
    final List<Map<String, dynamic>> filterdList = [];
    for (final element in list) {
      double total = 0;

      for (final splitedYear in element) {
        total = (double.tryParse(splitedYear['VALUE'].toString()) ?? 0) + total;
      }
      final Map<String, dynamic> currentYearValue =
          jsonDecode(jsonEncode(isForecast ? element.first : element.last))
              as Map<String, dynamic>;
      if (isForecast && element.isNotEmpty) {
        filterdList.add(
          jsonDecode(jsonEncode(joinValue)) as Map<String, dynamic>,
        );
      }
      final List<String> yearValueList =
          currentYearValue['OBS_DT'].toString().split('-');
      currentYearValue['OBS_DT'] =
          '${yearValueList[0]}-${type == 'qr' ? yearValueList[1] : '01'}-${yearValueList[2]}';
      currentYearValue['VALUE'] = total / element.length;

      filterdList.add(currentYearValue);
    }
    return filterdList;
  }

  /// function for slicing the data
  static List<List<Map<String, dynamic>>> sliceIt({
    List<Map<String, dynamic>> list = const [],
    int chunkSize = 3,
  }) {
    final List<List<Map<String, dynamic>>> chunks = [];
    for (var i = 0; i < list.length; i += chunkSize) {
      chunks.add(
        list.sublist(
          i,
          i + chunkSize > list.length ? list.length : i + chunkSize,
        ),
      );
    }
    return chunks;
  }

  /// to change to redable date for Quarter/Year
  static List<List<Map<String, dynamic>>> setUpName({
    List<List<Map<String, dynamic>>> list = const [],
    String type = 'qr',
  }) {
    final List<List<Map<String, dynamic>>> filterdList = [];

    for (final collection in list) {
      final List<Map<String, dynamic>> filterdListNew = [];
      for (final element in collection) {
        final Map<String, dynamic> currentYearValue =
            jsonDecode(jsonEncode(element)) as Map<String, dynamic>;
        final List<String> yearValueList =
            currentYearValue['OBS_DT'].toString().split('-');
        if (yearValueList.isNotEmpty && yearValueList.length > 1) {
          if (type == 'qr') {
            final int quter = (int.parse(yearValueList[1]) / 3).ceil();
            currentYearValue['OBS_DT'] = 'Q$quter ${yearValueList.first}';
            filterdListNew.add(currentYearValue);
          } else {
            currentYearValue['OBS_DT'] = yearValueList.first;
            filterdListNew.add(currentYearValue);
          }
        } else {
          filterdListNew.add(currentYearValue);
        }
      }
      filterdList.add(filterdListNew);
    }
    return filterdList;
  }

  /// to change to redable date for Month
  static List<List<Map<String, dynamic>>> setUpNameMonth({
    List<List<Map<String, dynamic>>> list = const [],
  }) {
    final List<List<Map<String, dynamic>>> filterdList = [];

    for (final collection in list) {
      final List<Map<String, dynamic>> filterdListNew = [];
      for (final element in collection) {
        final Map<String, dynamic> currentYearValue =
            jsonDecode(jsonEncode(element)) as Map<String, dynamic>;
        final String yearValueList =
            currentYearValue['OBS_DT'].toString().trim();

        final temp = DateTime.tryParse(yearValueList);
        if (temp != null) {
          currentYearValue['OBS_DT'] = DateFormat.yMMM().format(
            DateTime.parse(yearValueList),
          );
        }

        filterdListNew.add(currentYearValue);
      }
      filterdList.add(filterdListNew);
    }
    return filterdList;
  }

  static List<FrequencySelectorModel> getFrequencyList(
    IndicatorDetailsResponseHelper? indicatorDetails,
  ) {
    final List<String> dataList = List.from(
      indicatorDetails?.indicatorDetails.indicatorVisualizations
              ?.visualizationsMeta?.firstOrNull?.timeUnit ??
          [],
    );

    final List<FrequencySelectorModel> frequencyList = [];

    for (final element in dataList) {
      frequencyList.add(
        FrequencySelectorModel(title: element, isSelected: false),
      );
    }

    if ((indicatorDetails?.indicatorDetails.indicatorVisualizations
                    ?.visualizationsMeta?.firstOrNull?.showQuarterlyIntervals ==
                'true' ||
            indicatorDetails?.indicatorDetails.indicatorVisualizations
                    ?.visualizationsMeta?.firstOrNull?.showQuarterlyIntervals ==
                true) &&
        dataList.isEmpty) {
      frequencyList.addAll([
        FrequencySelectorModel(title: 'Yearly', isSelected: false),
        FrequencySelectorModel(title: 'Quarterly', isSelected: false),
      ]);
    }

    if (dataList.contains('Monthly') && !dataList.contains('Quarterly')) {
      frequencyList.insert(
        1,
        FrequencySelectorModel(title: 'Quarterly', isSelected: false),
      );
    }

    final bool isSelectedSet = false;

    // for (final element in frequencyList) {
    //   if (element.title == context.read<DetailsBloc>().selectedFrequency) {
    //     element.isSelected = true;
    //     isSelectedSet = true;
    //     break;
    //   }
    // }

    if (!isSelectedSet) {
      for (final element in frequencyList.reversed.toList()) {
        if (element.title == 'Monthly') {
          element.isSelected = true;
          break;
        } else if (element.title == 'Quarterly') {
          element.isSelected = true;
          break;
        }
      }
    }

    if (!isSelectedSet &&
        frequencyList.isNotEmpty &&
        frequencyList.every((element) => element.isSelected == false)) {
      frequencyList.first.isSelected = true;
    }

    return frequencyList;
  }

  static DateTime dateFormatterForChart(
    String selectedFrequencyForFilter,
    String value,
  ) {
    return DateTime.tryParse(value) ?? DateTime.now();
  }

  static String setupNameAll(String selectedFrequencyForFilter, String value) {
    final String yearValue = value.trim();
    final List<String> yearValueList = yearValue.split('-');
    String newValue = '';
    if (selectedFrequencyForFilter == 'Quarterly') {
      final int quter = (int.parse(yearValueList[1]) / 3).ceil();
      newValue = 'Q$quter ${yearValueList.first}';
    } else if (selectedFrequencyForFilter == 'Yearly') {
      newValue = yearValueList.first;
    } else {
      final temp = DateTime.tryParse(yearValue);
      if (temp != null) {
        newValue = DateFormat.yMMM().format(
          DateTime.parse(yearValue),
        );
      }
    }
    return newValue;
  }

  static double getQuatrelyInterverl(
    List<List<dynamic>> chartData,
    List<List<SplineChartData>>? forcat,
    String frequancy,
    Map<String, DateTime> maxAndMin,
  ) {
    const int dataSplit = 5;
    double valueDiffrence = 1;
    int forcatCout = 0;
    if (forcat != null && forcat.isNotEmpty && frequancy != 'Yearly') {
      forcatCout = forcat.first.length;
    }
    final int chartLength = (chartData.firstOrNull?.length ?? 0) + forcatCout;
    final int balance = chartLength % dataSplit;
    int noramlCount = chartLength - balance;

    if (frequancy == 'Yearly') {
      final int days =
          maxAndMin['orgMax']?.difference(maxAndMin['orgMin']!).inDays ?? 0;
      if (days < 360) {
        valueDiffrence = days / 300;
      }
    } else {
      valueDiffrence =
          noramlCount == 0 ? balance.toDouble() : noramlCount.toDouble();
    }

    noramlCount = noramlCount <= 0 ? 1 : noramlCount;
    double value = noramlCount < dataSplit
        ? ((frequancy == 'Quarterly'
            ? (noramlCount * 3 / dataSplit) + valueDiffrence
            : valueDiffrence))
        : ((frequancy == 'Quarterly' ? noramlCount * 3 : noramlCount) /
            dataSplit);
    value = double.parse((value).toStringAsFixed(2));
    return value <= 0
        ? 1
        : value >= 40
            ? 40
            : value;
  }

  static double getQuatrelyInterverlColumnChart(
    List<List<dynamic>> chartData,
    List<List<ColumnChartData>>? forcat,
    String frequancy,
    Map<String, DateTime> maxAndMin,
  ) {
    const int dataSplit = 5;
    double valueDiffrence = 1;
    int forcatCout = 0;
    if (forcat != null && forcat.isNotEmpty && frequancy != 'Yearly') {
      forcatCout = forcat.first.length;
    }
    if (frequancy == 'Yearly') {
      final int days =
          maxAndMin['orgMax']?.difference(maxAndMin['orgMin']!).inDays ?? 0;
      if (days < 360) {
        valueDiffrence = days / 300;
      }
    }

    final int chartLength = (chartData.firstOrNull?.length ?? 0) + forcatCout;
    final int balance = chartLength % dataSplit;
    int noramlCount = chartLength - balance;
    noramlCount = noramlCount <= 0 ? 1 : noramlCount;
    final double value = noramlCount < dataSplit
        ? ((frequancy == 'Quarterly'
            ? (noramlCount * 3 / dataSplit) + valueDiffrence
            : valueDiffrence))
        : ((frequancy == 'Quarterly' ? noramlCount * 3 : noramlCount) /
            dataSplit);
    return value <= 0 ? 1 : value;
  }

  static Map<String, dynamic> setFrequancy({
    List<List<Map<String, dynamic>>> l = const [],
    IndicatorDetailsResponseHelper? indicatorDetails,
  }) {
    try {
      final indicatorDateSetting =
          IndicatorDateSetting.getFrequencyList(indicatorDetails);
      String selectedFrequencyForFilter = '';
      final List<List<Map<String, dynamic>>> value = l;
      if (indicatorDateSetting.isNotEmpty) {
        final String val1 =
            (l.firstOrNull?.firstOrNull?['OBS_DT'] as String?) ?? '';
        final DateTime? from = DateTime.tryParse(val1.trim());
        selectedFrequencyForFilter = indicatorDateSetting
                .where((element) => element.isSelected)
                .firstOrNull
                ?.title ??
            '';
        if (from != null) {
          if (selectedFrequencyForFilter == 'Monthly') {
            //  value = IndicatorDateSetting.setUpNameMonth(list: l);
          } else if (selectedFrequencyForFilter == 'Quarterly') {
            //  value = IndicatorDateSetting.setUpName(list: l);
          } else {
            //   value = IndicatorDateSetting.setUpName(list: l, type: 'yearly');
          }
        }
      } else if (l.isNotEmpty) {
        try {
          int difference = 0;
          final String val1 =
              (l.firstOrNull?.lastOrNull?['OBS_DT'] as String?) ?? '';

          if ((l.firstOrNull?.length ?? 0) > 1) {
            final String val2 = l.firstOrNull?[(l.firstOrNull?.length ?? 0) - 2]
                ['OBS_DT'] as String;
            DateTime? from = DateTime.tryParse(val2.trim());
            DateTime? to = DateTime.tryParse(val1.trim());

            if (from != null && to != null) {
              from = DateTime(from.year, from.month, from.day);
              to = DateTime(to.year, to.month, to.day);
              difference = (to.difference(from).inDays).round();

              if (difference <= 32) {
                selectedFrequencyForFilter = 'Monthly';
                // value = setUpNameMonth(list: l);
              } else if (difference > 32 && difference < 100) {
                selectedFrequencyForFilter = 'Quarterly';
                //value = setUpName(list: l);
              } else if (difference > 360) {
                selectedFrequencyForFilter = 'Yearly';
                //value = setUpName(list: l, type: 'yearly');
              } else {
                selectedFrequencyForFilter = 'Monthly';
              }
            }
          }
        } catch (e, s) {
          Completer<dynamic>().completeError(e, s);
        }
      }
      return {
        'value': value,
        'selectedFrequencyForFilter': selectedFrequencyForFilter,
      };
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return {
        'value': l,
        'selectedFrequencyForFilter': 'Monthly',
      };
    }
  }

  static Map<String, DateTime> setMaxAndmMinValue({
    List<List<SplineChartData>>? chartDataList = const [],
    List<List<SplineChartData>> forcast = const [],
    String frequancy = '',
  }) {
    try {
      List<int> date = [];
      int maxDuration = 30;
      int minDuration = 30;
      for (final colection in (chartDataList ?? [])) {
        for (final (element as SplineChartData) in colection) {
          date.add(
            dateFormatterForChart(frequancy, element.label ?? '')
                .millisecondsSinceEpoch,
          );
        }
      }
      for (final colection in forcast) {
        for (final (element as SplineChartData) in colection) {
          date.add(
            dateFormatterForChart(frequancy, element.label ?? '')
                .millisecondsSinceEpoch,
          );
        }
      }
      if (date.length > 1) {
        maxDuration = DateTime.fromMillisecondsSinceEpoch(date[1])
            .difference(DateTime.fromMillisecondsSinceEpoch(date.first))
            .inDays;
        maxDuration = maxDuration == 0
            ? (frequancy == 'Yearly'
                ? 300
                : (frequancy == 'Quarterly' ? 92 : 31))
            : maxDuration;
        minDuration = maxDuration;
      }

      final Map<String, DateTime> outPut = {
        'max':
            DateTime.fromMillisecondsSinceEpoch((date..sort()).lastOrNull ?? 0)
                .add(Duration(days: maxDuration)),
        'min': DateTime.fromMillisecondsSinceEpoch(
          (date..sort()).firstOrNull ?? 0,
        ),
        'orgMin': DateTime.fromMillisecondsSinceEpoch(
          (date..sort()).firstOrNull ?? 0,
        ),
        'orgMax':
            DateTime.fromMillisecondsSinceEpoch((date..sort()).lastOrNull ?? 0),
      };
      // final Map<String, DateTime> outPut = {
      //   'max': DateTime.fromMillisecondsSinceEpoch(date.reduce(max))
      //       .add(Duration(days: maxDuration)),
      //   'min': DateTime.fromMillisecondsSinceEpoch(date.reduce(min)),
      //   'orgMin': DateTime.fromMillisecondsSinceEpoch(date.reduce(min)),
      //   'orgMax': DateTime.fromMillisecondsSinceEpoch(date.reduce(max)),
      // };
      return outPut;
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      final Map<String, DateTime> outPut = {
        'max': DateTime.now(),
        'min': DateTime.now(),
        'orgMin': DateTime.now(),
        'orgMax': DateTime.now(),
      };
      return outPut;
    }
  }

  static Map<String, double> setMaxAndmMinXAxisValue({
    List<List<SplineChartData>>? chartDataList = const [],
    List<List<SplineChartData>> forcast = const [],
    String frequancy = '',
  }) {
    try {
      final List<double> data = [];
      for (final colection in (chartDataList ?? [])) {
        for (final (element as SplineChartData) in colection) {
          data.add((element.x ?? 0).toDouble());
        }
      }
      for (final colection in forcast) {
        for (final (element as SplineChartData) in colection) {
          data.add((element.x ?? 0).toDouble());
        }
      }

      final Map<String, double> outPut = {
        'max': (data..sort()).lastOrNull ?? 0,
        'min': (data..sort()).firstOrNull ?? 0,
      };

      if (outPut['max'] != null && outPut['min'] != null) {
        double differents = outPut['max']! - outPut['min']!;
        double avarage = differents * 0.20;
        outPut['max'] = outPut['max']! + avarage;
        outPut['min'] = outPut['min']! - avarage;
      }

      return outPut;
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      final Map<String, double> outPut = {
        'max': 10000.0,
        'min': 0.0,
      };
      return outPut;
    }
  }

  static Map<String, DateTime> setMaxAndmMinValueColumnChart({
    List<List<ColumnChartData>>? chartDataList = const [],
    List<List<ColumnChartData>> forcast = const [],
    String frequancy = '',
  }) {
    try {
      List<int> date = [];
      int maxDuration = 30;
      int minDuration = 30;
      for (final colection in (chartDataList ?? [])) {
        for (final (element as ColumnChartData) in colection) {
          date.add(
            dateFormatterForChart(frequancy, element.x ?? '')
                .millisecondsSinceEpoch,
          );
        }
      }
      for (final colection in forcast) {
        for (final (element as ColumnChartData) in colection) {
          date.add(
            dateFormatterForChart(frequancy, element.x ?? '')
                .millisecondsSinceEpoch,
          );
        }
      }
      if (date.length > 1) {
        maxDuration = DateTime.fromMillisecondsSinceEpoch(date[1])
            .difference(DateTime.fromMillisecondsSinceEpoch(date.first))
            .inDays;
        minDuration = maxDuration == 0
            ? (frequancy == 'Yearly'
                ? 300
                : (frequancy == 'Quarterly' ? 92 : 31))
            : maxDuration;
      }

      final Map<String, DateTime> outPut = {
        'max':
            DateTime.fromMillisecondsSinceEpoch((date..sort()).lastOrNull ?? 0)
                .add(Duration(days: maxDuration)),
        'min': DateTime.fromMillisecondsSinceEpoch(
          (date..sort()).firstOrNull ?? 0,
        ).subtract(Duration(days: minDuration)),
        'orgMin': DateTime.fromMillisecondsSinceEpoch(
          (date..sort()).firstOrNull ?? 0,
        ),
        'orgMax':
            DateTime.fromMillisecondsSinceEpoch((date..sort()).lastOrNull ?? 0),
      };
      // final Map<String, DateTime> outPut = {
      //   'max': DateTime.fromMillisecondsSinceEpoch(date.reduce(max))
      //       .add(Duration(days: maxDuration)),
      //   'min': DateTime.fromMillisecondsSinceEpoch(date.reduce(min)),
      //   'orgMin': DateTime.fromMillisecondsSinceEpoch(date.reduce(min)),
      //   'orgMax': DateTime.fromMillisecondsSinceEpoch(date.reduce(max)),
      // };
      return outPut;
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      final Map<String, DateTime> outPut = {
        'max': DateTime.now(),
        'min': DateTime.now(),
        'orgMin': DateTime.now(),
        'orgMax': DateTime.now(),
      };
      return outPut;
    }
  }

  static Map<String, double> setMaxAndmMinXAxisValueColumnChart({
    List<List<ColumnChartData>>? chartDataList = const [],
    List<List<ColumnChartData>> forcast = const [],
    String frequancy = '',
  }) {
    try {
      final List<double> data = [];
      for (final colection in (chartDataList ?? [])) {
        for (final (element as ColumnChartData) in colection) {
          data.add((element.y ?? 0).toDouble());
        }
      }
      for (final colection in forcast) {
        for (final (element as ColumnChartData) in colection) {
          data.add((element.y ?? 0).toDouble());
        }
      }

      final Map<String, double> outPut = {
        'max': (data..sort()).lastOrNull ?? 0,
        'min': (data..sort()).firstOrNull ?? 0,
      };

      if (outPut['max'] != null && outPut['min'] != null) {
        double differents = outPut['max']! - outPut['min']!;
        double avarage = differents * 0.20;
        outPut['max'] = outPut['max']! + avarage;
        outPut['min'] = outPut['min']! - avarage;
      }

      return outPut;
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      final Map<String, double> outPut = {
        'max': 10000.0,
        'min': 0.0,
      };
      return outPut;
    }
  }

  static List<Properties> removeDuplicates(List<Properties> items) {
    final List<Properties> uniqueItems = []; // uniqueList
    final uniqueIDs = items
        .map((e) => e.path)
        .toSet(); //list if UniqueID to remove duplicates
    // ignore: cascade_invocations
    for (final e in uniqueIDs) {
      uniqueItems.add(items.firstWhere((i) => i.path == e));
    } // populate uniqueItems with equivalent original Batch items
    return uniqueItems; //send back the unique items list
  }

  static double setZoomfactor(int length) {
    const totalPoint = 20;
    final zoom = (totalPoint / length);
    return zoom <= 0 ? .001 : (zoom > 1 ? 1 : zoom);
  }

  static double setMaxZoomfactor(int length) {
    const totalPoint = 10;
    final zoom = (totalPoint / length);
    return zoom <= 0 ? .001 : (zoom > 1 ? 1 : zoom);
  }
}
