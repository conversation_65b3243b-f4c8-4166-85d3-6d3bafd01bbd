import 'package:flutter/material.dart';
import 'package:scad_mobile/main.dart';

class TextUtils{
  static int getLineLength({required BuildContext context, required String text, required bool isArabic,required TextStyle style,}) {
    final span = TextSpan(text: text, style: style);
    final tp = TextPainter(
      text: span,
      textScaler: TextScaler.linear(textScaleFactor.value),
      textDirection: isArabic ? TextDirection.rtl : TextDirection.ltr,
    )..layout(maxWidth: MediaQuery.of(context).size.width - 80);
    final numLines = tp.computeLineMetrics().length;
    return numLines;
  }

  static bool isRegexMatch(String text, String pattern) {
    try {
      return RegExp(pattern).hasMatch(text);
    } catch (e) {
      return false;
    }
  }

}