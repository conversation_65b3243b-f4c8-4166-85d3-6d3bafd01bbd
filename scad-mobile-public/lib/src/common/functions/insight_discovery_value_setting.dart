import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';

class IsightDiscoveryValueSetting {
  static String getUnit(IndicatorDetailsResponse? inidcatorDetails) {
    return inidcatorDetails?.unit ?? '';
  }

  static String? getValue(Visualizations? selectedVisualization) {
    if (selectedVisualization?.indicatorValues?.valuesMeta != null &&
        (selectedVisualization?.indicatorValues?.valuesMeta ?? []).isNotEmpty) {
      final double val = double.tryParse(
            selectedVisualization?.indicatorValues?.valuesMeta?.first.value ??
                '0.00000',
          ) ??
          0.0000;

      num returnValue = val;

      if (val > 1000000000000) {
        returnValue = val / 1000000000000;
      } else if (val > 1000000000) {
        returnValue = val / 1000000000;
      } else if (val > 1000000) {
        returnValue = val / 1000000;
      } else if (val > 10000) {
        returnValue = val / 10000;
      }

      return returnValue.toStringAsFixed(2);
    } else {
      return null;
    }
  }

  static String? getNumberUnit(Visualizations? selectedVisualization) {
    if (selectedVisualization?.indicatorValues?.valuesMeta != null &&
        (selectedVisualization?.indicatorValues?.valuesMeta ?? []).isNotEmpty) {
      final double val = double.parse(
        selectedVisualization?.indicatorValues?.valuesMeta?.first.value ??
            '0.0',
      );

      String returnValueUnit = '';
      if (val > 1000000000000) {
        returnValueUnit = 'T';
      } else if (val > 1000000000) {
        returnValueUnit = 'B';
      } else if (val > 1000000) {
        returnValueUnit = 'M';
      } else if (val > 10000) {
        returnValueUnit = 'k';
      }

      return returnValueUnit;
    } else {
      return null;
    }
  }

  static String? getNumberUnit1(dynamic value) {

    final double val = double.parse(value.toString());

    String returnValue = val.toString();

    if (val > 1000000000000) {
      returnValue = '${(val / 1000000000000).toStringAsFixed(2)} T';
    } else if (val > 1000000000) {
      returnValue = '${(val / 1000000000).toStringAsFixed(2)} B';
    } else if (val > 1000000) {
      returnValue = '${(val / 1000000).toStringAsFixed(2)} M';
    } else if (val > 10000) {
      returnValue = '${(val / 10000).toStringAsFixed(2)} k';
    } else if (val is !int || val != val.roundToDouble()) {
      returnValue = val.toStringAsFixed(2);
    }

    return returnValue;

  }
}
