import 'dart:io';

import 'package:hive_flutter/hive_flutter.dart';
import 'package:path_provider/path_provider.dart';
import 'package:scad_mobile/src/config/app_config/secret.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_keys.dart';

class HiveConfig {
  static Future<void> initConfig() async {
    await Hive.initFlutter();

    final Directory directory = await getApplicationSupportDirectory();

    Hive.init(directory.path);

    final HiveAesCipher hiveAesCipher = HiveAesCipher(Secret.hiveEncryptionKey.split('').map((e) => int.parse(e)).toList());

    await Hive.openBox<dynamic>(HiveKeys.boxSettings, encryptionCipher: hiveAesCipher);
    await Hive.openBox<dynamic>(HiveKeys.boxApiCache, encryptionCipher: hiveAesCipher);
    await Hive.openBox<dynamic>(HiveKeys.boxAuth, encryptionCipher: hiveAesCipher);
    await Hive.openBox<dynamic>(HiveKeys.boxPersistent, encryptionCipher: hiveAesCipher);
  }
}
