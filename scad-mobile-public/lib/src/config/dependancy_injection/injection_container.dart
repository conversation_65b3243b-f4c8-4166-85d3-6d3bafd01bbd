import 'package:get_it/get_it.dart';
import 'package:scad_mobile/route_manager/route_imports.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/repositories/indicator_card_repository_impl.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/domain/repositories/indicator_card_repository_imports.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/presentation/bloc/indicator_card_bloc.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/repositories/chat_with_sme_repo_implementation.dart';
import 'package:scad_mobile/src/features/chat_with_sme/domain/repositories/chat_with_sme_repository_imports.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/bloc/chat_with_sme_bloc.dart';
import 'package:scad_mobile/src/features/details/data/repositories/details_repo_impl.dart';
import 'package:scad_mobile/src/features/details/domain/repositories/details_repository_imports.dart';
import 'package:scad_mobile/src/features/details/presentation/bloc/details_bloc.dart';
import 'package:scad_mobile/src/features/domains/data/repositories/domains_repo_impl.dart';
import 'package:scad_mobile/src/features/domains/domain/repositories/domains_repository_imports.dart';
import 'package:scad_mobile/src/features/domains/presentation/bloc/domains_bloc.dart';
import 'package:scad_mobile/src/features/drawer_items/about_this_app/data/repositories/about_repo_impl.dart';
import 'package:scad_mobile/src/features/drawer_items/about_this_app/domain/repositories/about_app_repository_imports.dart';
import 'package:scad_mobile/src/features/drawer_items/about_this_app/presentation/bloc/about_app_bloc.dart';
import 'package:scad_mobile/src/features/drawer_items/glossary/data/repositories/glossary_repo_implementation.dart';
import 'package:scad_mobile/src/features/drawer_items/glossary/domain/repositories/glossary_repository_imports.dart';
import 'package:scad_mobile/src/features/drawer_items/glossary/presentation/bloc/glossary_bloc.dart';
import 'package:scad_mobile/src/features/drawer_items/terms_and_conditions/data/repositories/t_and_c_repo_impl.dart';
import 'package:scad_mobile/src/features/drawer_items/terms_and_conditions/domain/repositories/t_and_c_repository_imports.dart';
import 'package:scad_mobile/src/features/drawer_items/terms_and_conditions/presentation/bloc/t_and_c_bloc.dart';
import 'package:scad_mobile/src/features/home/<USER>/repositories/home_repo_impl.dart';
import 'package:scad_mobile/src/features/home/<USER>/repositories/home_repository_imports.dart';
import 'package:scad_mobile/src/features/home/<USER>/bloc/home_bloc/home_bloc.dart';
import 'package:scad_mobile/src/features/products/data/repositories/products_repo_impl.dart';
import 'package:scad_mobile/src/features/products/domain/repositories/products_repository_imports.dart';
import 'package:scad_mobile/src/features/products/presentation/bloc/products_bloc.dart';
import 'package:scad_mobile/src/features/search/data/repositories/search_repo_impl.dart';
import 'package:scad_mobile/src/features/search/domain/repositories/search_repository_imports.dart';
import 'package:scad_mobile/src/features/search/presentation/bloc/search_bloc.dart';
import 'package:scad_mobile/src/features/settings/presentation/bloc/setting_bloc.dart';
import 'package:scad_mobile/src/services/http_service_impl.dart';

//initialise get_it //
final servicelocator = GetIt.instance;

//dependacy container//
Future<void> getInit() async {
  //register bloc//
  servicelocator
    ..registerSingleton<AppRouter>(AppRouter())
    ..registerLazySingleton<HttpServiceRequests>(HttpServiceRequests.new)
    ..registerFactory<DomainsBloc>(() => DomainsBloc())
    ..registerLazySingleton<DomainsRepository>(
      DomainsRepositoryImpl.new,
    )
    ..registerFactory<SearchBloc>(() => SearchBloc())
    ..registerLazySingleton<SearchRepository>(
      SearchRepositoryImpl.new,
    )
    ..registerFactory<ChatWithSmeBloc>(() => ChatWithSmeBloc(servicelocator()))
    ..registerLazySingleton<ChatWithSmeRepository>(
      ChatWithSmeRepoImplementation.new,
    )
    ..registerFactory<HomeBloc>(() => HomeBloc(servicelocator()))
    ..registerLazySingleton<HomeRepository>(
      HomeRepositoryImpl.new,
    )
    ..registerFactory<IndicatorCardBloc>(IndicatorCardBloc.new)
    ..registerLazySingleton<IndicatorCardRepository>(
      IndicatorCardImpl.new,
    )
    ..registerFactory<DetailsBloc>(DetailsBloc.new)
    ..registerLazySingleton<DetailsRepository>(
      DetailsRepositoryImpl.new,
    )
    // ..registerFactory<SettingsBloc>(SettingsBloc.new)
    // ..registerFactory<ThemeBloc>(ThemeBloc.new)
    ..registerFactory<AboutAppBloc>(() => AboutAppBloc(servicelocator()))
    ..registerLazySingleton<AboutAppRepository>(
      AboutAppRepositoryImpl.new,
    )
    ..registerFactory<TAndCBloc>(() => TAndCBloc(servicelocator()))
    ..registerLazySingleton<TAndCRepository>(
      TAndCRepositoryImpl.new,
    )
    ..registerFactory<SettingBloc>(SettingBloc.new)
    ..registerFactory<ProductsBloc>(ProductsBloc.new)
    ..registerLazySingleton<ProductsRepository>(
      ProductsRepositoryImpl.new,
    )
    ..registerFactory<GlossaryBloc>(() => GlossaryBloc(servicelocator()))
    ..registerLazySingleton<GlossaryRepository>(
      GlossaryRepoImplementation.new,
    );
}

void closeAllBloc() {
  servicelocator<ChatWithSmeBloc>().close();
  servicelocator<DetailsBloc>().close();
  servicelocator<DomainsBloc>().close();
  servicelocator<AboutAppBloc>().close();
  servicelocator<GlossaryBloc>().close();
  servicelocator<TAndCBloc>().close();
  servicelocator<HomeBloc>().close();
  servicelocator<ProductsBloc>().close();
  servicelocator<SettingBloc>().close();
}
