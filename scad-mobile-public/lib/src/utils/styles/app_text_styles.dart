import 'package:flutter/material.dart' show FontWeight, TextStyle;

import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';

class AppTextStyles {
  static TextStyle get s28w6cBlueGreyShade1 => TextStyle(
        color: AppColors.blueGreyShade1,
        fontSize: 28,
        fontWeight: FontWeight.w600,
      );
  static TextStyle get s24w5cBlackOrWhiteShade => TextStyle(
        color: AppColors.blackShade1OrWhite,
        fontSize: 24,
        fontWeight: FontWeight.w500,
      );
  static TextStyle get s20w5cBlackOrWhiteShade => TextStyle(
        color: AppColors.blackShade1OrWhite,
        fontSize: 20,
        fontWeight: FontWeight.w500,
      );

  static TextStyle get s22w4cGrey => TextStyle(
        color: AppColors.grey,
        fontSize: 22,
        fontWeight: FontWeight.w400,
      );

  static TextStyle get s16w5cBlueTitleText => TextStyle(
        color: AppColors.blueTitleText,
        fontSize: 16,
        fontWeight: FontWeight.w500,
      );

  static TextStyle get s14w4cBlue => TextStyle(
        color: AppColors.blueShade22,
        fontSize: 14,
        fontWeight: FontWeight.w400,
      );

  static TextStyle get s12w4cGreyShade4 => TextStyle(
        color: AppColors.greyShade4,
        fontSize: 12,
        fontWeight: FontWeight.w400,
      );

  static TextStyle get s14w3cGrey => TextStyle(
        color: AppColors.grey,
        fontSize: 14,
        fontWeight: FontWeight.w300,
      );

  static TextStyle get s14w4cBlueTitleText => TextStyle(
        color: AppColors.blueTitleText,
        fontSize: 14,
        fontWeight: FontWeight.w400,
      );

  static TextStyle get s14w3cHintColor => TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w300,
        color: AppColors.grey,
      );

  static TextStyle get s16w4cBlueGreyShade1 => TextStyle(
        color: AppColors.blueGreyShade1,
        fontSize: 16,
        fontWeight: FontWeight.w400,
      );

  static TextStyle get s16w5cWhite => TextStyle(
        color: AppColors.white,
        fontSize: 16,
        fontWeight: FontWeight.w500,
      );

  static TextStyle get s16w5cBlueLight => TextStyle(
        color: AppColors.blueLight,
        fontSize: 16,
        fontWeight: FontWeight.w500,
      );

  static TextStyle get s15w5cBlueGreyShade1 => TextStyle(
        color: AppColors.blueGreyShade1,
        fontSize: 15,
        fontWeight: FontWeight.w500,
      );

  static TextStyle get s14w4cblackShade4 => TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: AppColors.blackShade4,
      );

  static TextStyle get s18w5cBlackShade1 => TextStyle(
        color: AppColors.blackShade1,
        fontSize: 18,
        fontWeight: FontWeight.w500,
      );

  static TextStyle get s24w5cBlackShade1 => TextStyle(
        color: AppColors.blackShade1,
        fontSize: 24,
        fontWeight: FontWeight.w500,
      );

  static TextStyle get s14w5cBlack => TextStyle(
        color: AppColors.black,
        fontSize: 14,
        fontWeight: FontWeight.w500,
      );

  static TextStyle get s14w6cBlue => TextStyle(
        color: AppColors.blue,
        fontSize: 14,
        fontWeight: FontWeight.w600,
      );
  static TextStyle get s12w4cblueGreyShade1 => TextStyle(
        color: AppColors.blueGreyShade1,
        fontSize: 12,
        fontWeight: FontWeight.w400,
      );
  static TextStyle get s16w5cBlackShade1 => TextStyle(
        color: AppColors.blackShade1,
        fontSize: 16,
        fontWeight: FontWeight.w500,
      );
  static TextStyle get s16w4cGrey => TextStyle(
        color: AppColors.grey,
        fontSize: 16,
        fontWeight: FontWeight.w400,
      );
  static TextStyle get s18w5cBlackShade => TextStyle(
        color: AppColors.blackShade1,
        fontSize: 18,
        fontWeight: FontWeight.w500,
      );
  static TextStyle get s12w5cBlack => TextStyle(
        color: AppColors.black,
        fontSize: 12,
        fontWeight: FontWeight.w500,
      );
  static TextStyle get s19w6cBlack => TextStyle(
        color: AppColors.black,
        fontSize: 19,
        fontWeight: FontWeight.w600,
      );
  static TextStyle get s16w6cWhite => TextStyle(
        color: AppColors.white,
        fontSize: 16,
        fontWeight: FontWeight.w600,
      );
  static TextStyle get s10w5cWhite => TextStyle(
        color: AppColors.white,
        fontSize: 10,
        fontWeight: FontWeight.w500,
      );
  static TextStyle get s12w5cWhite => TextStyle(
        color: AppColors.white,
        fontSize: 12,
        fontWeight: FontWeight.w500,
      );
  static TextStyle get s17w5cBlack => TextStyle(
        color: AppColors.black,
        fontSize: 17,
        fontWeight: FontWeight.w600,
      );
  static TextStyle get s14w3cWhite => TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w300,
        color: AppColors.white,
      );
}
