import 'package:flutter/material.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

class AppColors {
  static bool isLightMode = HiveUtilsSettings.getThemeMode() == ThemeMode.light;
  static Color red = const Color(0xFFEB0029);
  static Color white = const Color(0xFFFFFFFF);
  static Color whiteShade1 = const Color(0x1AFFFFFF);
  static Color whiteShade2 = const Color(0x99FFFFFF);
  static Color whiteShade3 = const Color(0xFFEFF6FF);
  static Color whiteShade4 = const Color(0xFFE8F3FF);
  static Color whiteShade5 = const Color(0xffF8F8F8);
  static Color whiteShade6 = const Color(0x77FFFFFF);
  static Color black = const Color(0xFF000000);
  static Color blackShade1 = const Color(0xFF1E2937);
  static Color blackShade2 = const Color(0xFF37516E);
  static Color blackShade3 = const Color(0xFF05264A);
  static Color blackShade4 = const Color(0xFF4A5662);
  static Color blackShade20 = const Color(0xFF171717);
  static Color blackShade21 = const Color(0xFF252525);
  static Color blackShade23 = const Color(0xFF393939);
  static Color blueShade1 = const Color(0x192587FC);
  static Color blueShade2 = const Color(0xff152f5e);
  static Color blueShade3 = const Color(0xff26559d);
  static Color blueShade4 = const Color(0x6D172E5C);
  static Color blueShade10 = const Color(0xFF093077);
  static Color blueShade9 = const Color(0xFF1955B1);
  static Color blueShade6 = const Color(0xFF2787FB);
  static Color blueShade7 = const Color(0xFF3B88DF);
  static Color blueShade8 = const Color(0xFFEAF3FF);
  static Color blueShade11 = const Color(0xFF0092E8);
  static Color blueShade12 = const Color(0xFF0054B8);
  static Color blueShade13 = const Color(0xFFDAEBFF);
  static Color blueShade14 = const Color(0xFF012A9A);
  static Color blueShade18 = const Color(0xFFDBEAFF);
  static Color blueShade20 = const Color(0xFFF4F8FB);
  static Color blueShade21 = const Color(0xFFE4E8EB);
  static Color blueShade22 = const Color(0xFF0054B8);
  static Color blueShade23 = const Color(0xFF235094);
  static Color blueShade24 = const Color(0xFF396EA5);
  static Color blueShade25 = const Color(0xFF79A0C9);
  static Color blueShade26 = const Color(0xFFAECDED);
  static Color blueShade27 = const Color(0xFF1C407B);
  static Color blueShade32 = const Color(0xFF1F466F);
  static Color blueShade33 = const Color(0xFF355D87);
  static Color blueShade34 = const Color(0xFF184575);
  static Color blueShade35 = const Color(0xFF003998);
  static Color blueShade36 = const Color(0xFF37506A);
  static Color blueLight = const Color(0xFF0054B8);
  static Color blueLightOld = const Color(0xFF2687FD);
  static Color blueDark = const Color(0xFF3288F1);
  static Color skyBlue = const Color(0xFFAFCCF0);
  static Color blueDarkShade1 = const Color(0xFF3267FF);
  static Color blueGreyShade1 = const Color(0xFF364151);
  static Color grey = const Color(0xFF6A7180);
  static Color greyShade1 = const Color(0xFFD1D5DA);
  static Color greyShade2 = const Color(0xFFE7EEFA);
  static Color greyShade3 = const Color(0xFFCCDDF1);
  static Color greyShade4 = const Color(0xFF9DA2AE);
  static Color greyShade5 = const Color(0xFFD6DADF);
  static Color greyShade6 = const Color(0xFF707B9C);
  static Color greyShade7 = const Color(0xFFFAFAFA);
  static Color greyShade8 = const Color(0xFFF3F4F6);
  static Color greyShade9 = const Color(0xFFCDD4DB);
  static Color greyShade10 = const Color(0xFFefeff0);
  static Color greyShade11 = const Color(0xFF9BA8B7);
  static Color greyShade12 = const Color(0x146F7A9C);
  static Color greyShade13 = const Color(0x4CD1D5DA);
  static Color greyShade15 = const Color(0xFFD9D9D9);
  static Color greyShade15_1 = const Color(0xFFD4E5F9);
  static Color greyShade16 = const Color(0xFF323232);
  static Color greyShade17 = const Color(0xFFA3B1BF);
  static Color greyShade18 = const Color(0xFF3E546A);
  static Color green = const Color(0xFF1DAB85);
  static Color blackTextTile = const Color(0xFF364151);
  static Color greyNotch = const Color(0xFFD5DADF);
  static Color blueButton = const Color(0xFF2787FB);
  static Color blackChatText = const Color(0xFF4A5662);
  static Color greySwitchOff = const Color(0xFFCDD4DB);
  static Color greySlider = const Color(0x471847DF);
  static Color blueSliderThumb = const Color(0xFF1847DF);
  static Color blueSliderThumbOuter = const Color(0xFF92A2E1);
  static Color blueSelectedRadio = const Color(0xFFE9F3FF);
  static Color redButton = const Color(0xFFE53232);
  static Color shadow1 = const Color(0x0A4E4F51);
  static Color shadow2 = const Color(0x0A4E4F52);
  static Color shadow3 = const Color(0x0F000000);
  static Color shadow4 = const Color(0xFF576AA2);
  static Color readMessageDot = const Color(0xFFE6F1FF);
  static Color notificationDivider = const Color(0xFFE7F2FF);
  static Color whiteShade7 = const Color(0xFFF6F9FF);
  static Color blackShade5 = const Color(0xFF181818);
  static Color newmorphicLight = const Color(0xFFF1F1F2);
  static Color newmorphicDark = const Color(0xFF2E2E2E);
  static Color greyBorder = const Color(0xFF3F3F3F);
  static Color inidcatorCardDark = const Color(0xFF232323);
  static Color greyShade14 = const Color(0xFF464646);
  static Color drawerDark = const Color(0xFF233E58);
  static Color blackShade6 = const Color(0xFF313131);
  static Color blackShade7 = const Color(0xFF575757);
  static Color blackShade8 = const Color(0xFF585858);

  static Color blueGradientShade1 = const Color(0xFF028AE2);
  static Color blueGradientShade2 = const Color(0xFF0334A1);
  static Color blueGradientShade3 = const Color(0xFF0185DE);
  static Color blueGradientShade4 = const Color(0xFF0233A1);
  static Color blueShadeTabInset = const Color(0xFFA8C5E3);
  static Color blueGreyShade2 = const Color(0xFF37506A);
  static Color blueTitleText = const Color(0xFF052445);
  static Color whiteShade8 = const Color(0xFFF1F5FF);

  /// blue shades
  static Color blue30619D = const Color(0Xff30619D);
  static Color blueCFDEEE = const Color(0XffCFDEEE);
  static Color selectedChipBlue = const Color(0xFF2587FC);
  static Color lightBlueContainer = const Color(0xFFE8F0FD);
  static Color blueShade15 = const Color(0xFF018EE5);
  static Color blueShade16 = const Color(0x472587FC);
  static Color blueShade17 = const Color(0x662587FC);
  static Color blueShade19 = const Color(0xFF4880DC);
  static Color lightBlue = const Color(0xffE8EEFB);
  static Color blueShade28 = const Color(0xFFE6ECF3);
  static Color blueShade29 = const Color(0xFFF2F7FC);
  static Color blueShade30 = const Color(0xff19386E);

  /// green shades
  static Color green3BD6AD = const Color(0xff3BD6AD);
  static Color green98C21B = const Color(0xff98c21b);

  /// grey shades
  static Color greyFAFAFA = const Color(0xffFAFAFA);
  static Color greyF3F4F6 = const Color(0xffF3F4F6);
  static Color greyF3F3F3 = const Color(0xffF3F3F3);
  static Color greyF2F2F3 = const Color(0xffF2F2F3);
  static Color greyECECEC = const Color(0xffECECEC);

  static Color green2 = const Color(0xFF2ACFC5);
  static Color green3 = const Color(0xFF81E8EE);
  static Color green4 = const Color(0xFF5EBD67);
  static Color greenLight = const Color(0xFF38D6AD);
  static Color chartGreen = const Color(0xFF9AD75D);
  static Color chartBlue = const Color(0xFF796DFA);
  static Color purple = const Color(0xFFA855CB);
  static Color purple2 = const Color(0xFF9772E4);
  static Color amber = const Color(0xFFE3C34E);
  static Color blue2 = const Color(0xFF3C6FF6);
  static Color blue3 = const Color(0xFF15ADEC);
  static Color brown = const Color(0xFFEE8B42);
  static Color compareChartLine = const Color(0xFFA660FF);
  static Color redWarning = const Color(0xFFBA0202);

  /// TreeMap P: Positive, N: Negative
  static Color treeMapP1 = const Color(0xFFC2DDCE);
  static Color treeMapP2 = const Color(0xFF92C8AA);
  static Color treeMapP3 = const Color(0xFF61B285);
  static Color treeMapP4 = const Color(0xFF319D61);
  static Color treeMapP5 = const Color(0xFF00873C);
  static Color treeMapN1 = const Color(0xFFF5CBCB);
  static Color treeMapN2 = const Color(0xFFF8A5A5);
  static Color treeMapN3 = const Color(0xFFFA7F7F);
  static Color treeMapN4 = const Color(0xFFFD5959);
  static Color treeMapN5 = const Color(0xFFFF3333);
  static Color treeMapNeutral = const Color(0xFFF2F2F2);
  static Color spatialAnalyticsRed = const Color(0xFFd75e5d);
  static Color spatialAnalyticsBlue = const Color(0xFF594ede);
  static Color spatialAnalyticsPink = const Color(0xFFFF00CC);
  static Color spatialAnalyticsBlueLight = const Color(0xFF297DE3);

  static Color scaffoldBackgroundLight = const Color(0xFFE5EFFB);
  static Color scaffoldBackgroundDark = const Color(0xFF0F3155);
  static Color get scaffoldBackground =>
      isLightMode ? scaffoldBackgroundLight : scaffoldBackgroundDark;
  static Color get scaffoldBackgroundLand =>
      isLightMode ? greyF2F2F3 : blackShade21;

  static Color get blackShade1OrWhite => isLightMode ? blackShade1 : white;

  static Color get whiteOrBlack => isLightMode ? white : black;

  static Color get blue => isLightMode ? blueLight : blueDark;

  static final chartColorSet = [
    Color(0xff19386E),
    green3BD6AD,
    chartBlue,
    const Color(0xFF2fccec),
    const Color(0xFF873600),
    const Color(0xff4363d8),
    const Color(0xfff58231),
    const Color(0xff911eb4),
    const Color(0xfff032e6),
    const Color(0xfffabed4),
    const Color(0xffbfef45),
    const Color(0xff9A6324),
    const Color(0xffaaffc3),
    amber,
    red,
    const Color(0xff800000),
    const Color(0xffdcbeff),
    const Color(0xff808000),
    const Color(0xffffd8b1),
  ];
  static final chartColorSetDark = [
    blueShade19,
    green3BD6AD,
    chartBlue,
    const Color(0xFF2fccec),
    const Color(0xFF873600),
    const Color(0xff4363d8),
    const Color(0xfff58231),
    const Color(0xff911eb4),
    const Color(0xfff032e6),
    const Color(0xfffabed4),
    const Color(0xffbfef45),
    const Color(0xff9A6324),
    const Color(0xffaaffc3),
    amber,
    red,
    const Color(0xff800000),
    const Color(0xffdcbeff),
    const Color(0xff808000),
    const Color(0xffffd8b1),
  ];
}
