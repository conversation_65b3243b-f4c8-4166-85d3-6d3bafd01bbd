import 'dart:async';

extension NumHelper on num {
  String truncateDecimalPoints({int? decimalPoints = 2}) {
    try {
      final List<String> list = toDouble().toString().split('.');
      final num d = num.tryParse(list[1]+'00'.substring(0, decimalPoints)) ?? 0;

      return d > 0
          ? '${list[0]}.${list[1].substring(0, decimalPoints)}'
          : list[0];
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return toString();
    }
  }

  String short() {
    final num val = this;

    num returnValue = val;
    String returnValueUnit = '';

    if (val > 1000000000000) {
      returnValue = val / 1000000000000;
      returnValueUnit = 'T';
    } else if (val > 1000000000) {
      returnValue = val / 1000000000;
      returnValueUnit = 'B';
    } else if (val > 1000000) {
      returnValue = val / 1000000;
      returnValueUnit = 'M';
    } else if (val > 10000) {
      returnValue = val / 10000;
      returnValueUnit = 'k';
    }

    return '$returnValue $returnValueUnit';
  }
}
