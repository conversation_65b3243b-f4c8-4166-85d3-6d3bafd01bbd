import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:scad_mobile/src/services/http_services.dart'
    show RequestParamsMap;

extension StringHelper on String {
  /// Convert Datetime string to DateTime object
  DateTime? toDateTime() {
    try {
      return DateTime.parse(this);
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return null;
    }
  }

  /// Convert Datetime string to formatted date time
  String toFormattedDateTimeString(String format) {
    var str = '-';
    try {
      str = DateFormat(format).format(DateTime.parse(this));
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
    }
    return str;
  }

  String setUrlParams(RequestParamsMap requestParams) {
    String s = this;
    for (final e in requestParams.entries) {
      s = s.replaceFirst('{{${e.key}}}', e.value);
    }
    return s;
  }

  String formatDateTimeForChat({bool showDateOnly = false}) {
    if (isNotEmpty) {
      final DateTime dateTime = DateTime.parse(this).toLocal();
      final DateTime now = DateTime.now();
      final DateTime yesterday =
          DateTime.now().subtract(const Duration(days: 1));

      if (dateTime.day == now.day &&
          dateTime.month == now.month &&
          dateTime.year == now.year) {
        // Today
        return DateFormat.jm().format(dateTime); // Format: 11:00 AM
      } else if (dateTime.day == yesterday.day &&
          dateTime.month == yesterday.month &&
          dateTime.year == yesterday.year) {
        // Yesterday Format: Yesterday, 11:00 AM
        return 'Yesterday, ${DateFormat.jm().format(dateTime)}';
      } else {
        // Other days

        if (showDateOnly) {
          return DateFormat('dd MMM yyyy').format(dateTime);
          // Format: 23 Dec, 2023
        } else {
          return DateFormat('dd MMM yyyy, ${DateFormat.jm().pattern}')
              .format(dateTime);
          // Format: 23 Dec, 2023, 11:00 AM
        }
      }
    } else {
      return '';
    }
  }

  String formatToTime() {
    final DateTime dateTime = DateTime.parse(this).toLocal();
    return DateFormat.jm().format(dateTime);
  }

  String capitalize() {
    return "${this[0].toUpperCase()}${this.substring(1)}";
  }

  /// format YEARS to Y and MONTHS to M
  String formatDuration() {
    final List<String> parts = split(' ');
    if (parts.length == 2) {
      final String value = parts.firstOrNull ?? '';
      final String unit = parts[1].toUpperCase();

      if (unit == 'YEARS' || unit == 'YEAR') {
        return '${value}Y';
      } else if (unit == 'MONTHS' || unit == 'MONTH') {
        return '${value}M';
      }
    }

    // Default case, return the original string if the format is not as expected
    return this;
  }

  bool isNumber() {
    return num.tryParse(this) != null;
  }

}
