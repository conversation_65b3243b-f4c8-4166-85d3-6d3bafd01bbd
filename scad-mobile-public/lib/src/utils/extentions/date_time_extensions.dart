import 'dart:async';

import 'package:easy_localization/easy_localization.dart';

final _quarters = [1, 4, 7, 10];

extension DateTimeHelper on DateTime {
  String toFormattedDateTimeString(String format) {
    var str = '-';
    try {
      str = DateFormat(format).format(this);
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
    }
    return str;
  }

  DateTime abs() => (isUtc ? DateTime.utc : DateTime.new)(year, month, day);

  DateTime previousQuarter() {
    if (_quarters.contains(month)) {
      return DateTime(year, month - 3);
    }

    final q = month ~/ 4;
    return DateTime(year, _quarters[q]);
  }

  DateTime get beginningOfPreviousMonth {
    if (month == 1) {
      return DateTime(year - 1, 12);
    }
    return DateTime(year, month - 1);
  }

  DateTime  get beginningOfNextMonth {
    if (month == 12) {
      return DateTime(year + 1);
    }
    return DateTime(year, month + 1);
  }

  DateTime get beginningOfQuarter {
    if (_quarters.contains(month)) {
      return DateTime(year, month);
    }

    final q = month ~/ 4;
    return DateTime(year, _quarters[q % _quarters.length]);
  }

  DateTime get previousYear => DateTime(year - 1, month, day);

  DateTime get nextYear => DateTime(year + 1, month, day);

  DateTime get beginningOfNextQuarter {
    if (_quarters.contains(month)) {
      return DateTime(year, month);
    }

    final q = (month ~/ 4) + 1;
    if (q >= _quarters.length) {
      return DateTime(year + 1, _quarters[q % _quarters.length]);
    }
    return DateTime(year, _quarters[q]);
  }

  DateTime nextQuarter() {
    if (_quarters.contains(month)) {
      return DateTime(year, month + 3);
    }

    final q = (month / 4).ceil();
    return DateTime(year, q);
  }

  DateTime toDateOnly() {
    return DateTime(year, month, day);
  }
}
