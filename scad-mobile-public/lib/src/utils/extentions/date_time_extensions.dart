import 'dart:async';

import 'package:easy_localization/easy_localization.dart';

extension DateTimeHelper on DateTime {
  String toFormattedDateTimeString(String format) {
    var str = '-';
    try {
      str = DateFormat(format).format(this);
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
    }
    return str;
  }

  DateTime toDateOnly() {
    return DateTime(year, month, day);
  }
}
