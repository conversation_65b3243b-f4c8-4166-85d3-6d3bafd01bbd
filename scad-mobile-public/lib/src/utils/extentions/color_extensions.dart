import 'package:flutter/material.dart';

extension HexColorExtension on String? {
  Color toColor() {
    if(this != null) {
      // eg. #e54d35
      if (this!.startsWith('#')) {
        String hex = this!.substring(1);
        if (hex.length == 6) {
          hex = 'FF$hex';
        }
        if (hex.length == 8) {
          return Color(int.parse(hex, radix: 16));
        }
      }

      // eg. rgba(56, 101, 255, 0.3)
      final rgbaMatch = RegExp(r'rgba?\((\d+),\s*(\d+),\s*(\d+),?\s*(\d?\.?\d+)?\)').firstMatch(this!);
      if (rgbaMatch != null) {
        final r = int.parse(rgbaMatch.group(1)!);
        final g = int.parse(rgbaMatch.group(2)!);
        final b = int.parse(rgbaMatch.group(3)!);
        final a = rgbaMatch.group(4) != null ? (double.parse(rgbaMatch.group(4)!) * 255).round() : 255;
        return Color.fromARGB(a, r, g, b);
      }
    }
    return Colors.transparent;
  }
}
