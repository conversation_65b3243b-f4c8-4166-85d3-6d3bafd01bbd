import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:root_jailbreak_sniffer/rjsniffer.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/config/app_config/secret.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';

class SecureDeviceUtils {
  static Future<bool> isSecureDevice(BuildContext context) async {
    if (!Secret.isProduction) {
      return true;
    }
    bool isSecure = false;
    if (await Rjsniffer.amICompromised() ?? false) {
      if (Platform.isIOS) {
        if (context.mounted) {
          _isSecureDeviceMsgDialog(
            context,
            'Jailbreak Detected',
            'To ensure the app functions securely, we recommend using it on a non-jailbroken device. Jailbroken devices may have vulnerabilities that could compromise your data.',
          );
        }
      } else if (Platform.isAndroid) {
        if (context.mounted) {
          _isSecureDeviceMsgDialog(
            context,
            'Root Detected',
            'To ensure the app functions securely, we recommend using it on a device without root access. Rooted devices may have increased security vulnerabilities.',
          );
        }
      } else {
        if (context.mounted) {
          _isSecureDeviceMsgDialog(context, '', 'Invalid platform');
        }
      }
    } else if (await Rjsniffer.amIEmulator() ?? false) {
      if (context.mounted) {
        _isSecureDeviceMsgDialog(
          context,
          'Emulator Detected',
          'We recommend using a real device instead of an emulator to ensure the app functions securely.',
        );
      }
    } else if (await Rjsniffer.amIDebugged() ?? false) {
      if (context.mounted) {
        _isSecureDeviceMsgDialog(
          context,
          'Developer Option Enabled',
          'To help secure your data, please turn off USB debugging by disabling Developer options on your device.',
        );
      }
    } else {
      isSecure = true;
    }
    return isSecure;
  }

  static void _isSecureDeviceMsgDialog(
      BuildContext context, String title, String message,) {
    showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          contentPadding: const EdgeInsets.all(20),
          insetPadding: const EdgeInsets.all(20),
          backgroundColor: AppColors.white,
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(20)),
          ),
          elevation: 0,
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Icon(
                Icons.cancel_rounded,
                size: 60,
                color: AppColors.red,
              ),
              const SizedBox(height: 14),
              Text(
                title,
                style: AppTextStyles.s16w5cBlackShade1,
                textAlign: TextAlign.center,
                textScaler: TextScaler.linear(textScaleFactor.value),
              ),
              const SizedBox(height: 14),
              Text(
                message,
                style: AppTextStyles.s14w4cBlueTitleText,
                textAlign: TextAlign.center,
                textScaler: TextScaler.linear(textScaleFactor.value),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  if (Platform.isIOS) {
                    exit(0);
                  } else {
                    unawaited(SystemNavigator.pop());
                  }
                },
                child: Text(
                  'Exit',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: AppColors.white,
                  ),
                  textScaler: TextScaler.linear(textScaleFactor.value),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
