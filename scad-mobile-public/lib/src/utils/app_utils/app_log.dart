import 'dart:async';

import 'package:flutter/foundation.dart';

class AppLog {
  static void error(Object e, StackTrace s, [dynamic message]) {
    if (kDebugMode) {
      debugPrint(
        '\x1B[31mAppError:${message == null ? '' : message.toString()}\x1B[0m',
      );
      // ignore: inference_failure_on_instance_creation
      Completer<dynamic>().completeError(e, s);
    }
  }

  static void info(dynamic message) {
    if (kDebugMode) debugPrint('\x1B[34mAppInfo:$message\x1B[0m');
  }

  static void warning(dynamic message) {
    if (kDebugMode) {
      debugPrint('\x1B[33mAppWarning:$message\x1B[0m');
    }
  }
}
