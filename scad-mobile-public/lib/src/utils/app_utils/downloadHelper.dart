import 'dart:async';
import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path_provider/path_provider.dart';
import 'package:scad_mobile/src/common/functions/text_utils.dart';
import 'package:scad_mobile/src/config/app_config/api_config.dart';
import 'package:scad_mobile/src/utils/app_utils/app_log.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class DownloadHelper {
  static Future<bool> downloadFileToDownloadsDir(
    BuildContext context,
    String url, {
    String? fileName,
  }) async {
    AppMessage.showOverlayNotification(
      '',
      LocaleKeys.downloading.tr(),
    );
    try {
      String dirPath = '';

      if (Platform.isIOS) {
        final Directory directory = await getApplicationDocumentsDirectory();
        dirPath = '${directory.path}/Download';
      } else {
        Directory? directory = Directory('/storage/emulated/0');
        if (!directory.existsSync()) {
          directory = await getExternalStorageDirectory();
        }
        dirPath = '${directory?.path}/Download/Bayaan';
      }

      String downloadFileName = fileName ??
          Uri.decodeQueryComponent(url.substring(url.lastIndexOf('/') + 1));

      String filePath = '$dirPath/$downloadFileName';

      if (!Directory(dirPath).existsSync()) {
        await Directory(dirPath).create(recursive: true);
      }

      if (File(filePath).existsSync()) {
        final List<String> l = downloadFileName.split('.');
        l[l.length - 1] =
            '${DateTime.now().millisecondsSinceEpoch}.${l[l.length - 1]}';
        downloadFileName = l.join('.');
        filePath = '$dirPath/$downloadFileName';
      }

      final File? file = await _downloadFile(url, filePath);

      if (file != null) {
        AppMessage.showOverlayNotification(
          '',
          '${LocaleKeys.fileDownloaded.tr()} $downloadFileName',
          onTap: () async {
            unawaited(openFile(filePath));
            // if (Platform.isAndroid) {
            //   final String fileFormat = fileName.split('.').last;
            //   if ((fileFormat == 'jpg' ||
            //           fileFormat == 'png' ||
            //           fileFormat == 'jpeg') &&
            //       (await DeviceInfoPlugin().androidInfo).version.sdkInt >= 33) {
            //     if (await Permission.photos.request().isGranted) {
            //       final result = await OpenFile.open(filePath);
            //       if (result.type != ResultType.done) {
            //         AppMessage.showOverlayNotification('', result.message);
            //       }
            //     } else {
            //       await AppPermissions.checkPermissions(
            //           servicelocator<AppRouter>().navigatorKey.currentContext!,
            //           [AppPermission.photos],);
            //     }
            //   } else if (await Permission.manageExternalStorage
            //       .request()
            //       .isGranted) {
            //     final result = await OpenFile.open(filePath);
            //     if (result.type != ResultType.done) {
            //       AppMessage.showOverlayNotification('', result.message);
            //     }
            //   } else {
            //     await AppPermissions.checkPermissions(
            //         servicelocator<AppRouter>().navigatorKey.currentContext!,
            //         [AppPermission.manageExternalStorage],);
            //   }
            // } else {
            //   unawaited(OpenFile.open(filePath));
            // }
          },
        );
        return true;
      } else {
        AppMessage.showOverlayNotification('', LocaleKeys.unableToDownloadFile.tr(), msgType: 'error');
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      AppMessage.showOverlayNotification(
        '',
        LocaleKeys.somethingWentWrong.tr(),
        msgType: 'error',
      );
    }
    return false;
  }

  static Future<String?> downloadFileToSupportDir({
    required String url,
    required String folder,
    String? fileName,
    bool replace = true,
    bool checkFileExists = false,
  }) async {
    try {
      String downloadFolder = folder;
      if (downloadFolder.startsWith('/')) {
        downloadFolder = downloadFolder.substring(1);
      }
      if (downloadFolder.endsWith('/')) {
        downloadFolder = downloadFolder.substring(0, downloadFolder.length - 1);
      }

      final String dirPath =
          '${(await getApplicationSupportDirectory()).path}/$downloadFolder';
      final String filePath = '$dirPath/${fileName ?? getFileName(url)}';

      if (!Directory(dirPath).existsSync()) {
        await Directory(dirPath).create(recursive: true);
      }
      if (checkFileExists && File(filePath).existsSync()) {
        return filePath;
      }
      return (await _downloadFile(url, filePath))?.path;
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return null;
    }
  }

  static Future<File?> _downloadFile(String url, String filePath) async {
    try {
      final HttpClient httpClient = HttpClient();

      HttpClientRequest request;

      const String regex = r'https:\/\/ifp-cms(.|\-)*.scad\.gov\.ae\/sites\/';
      if (TextUtils.isRegexMatch(url, regex)) {
        final String filepath = url.replaceFirst(RegExp(regex), '');
        request = await httpClient.getUrl(Uri.parse(
            '${ApiConfig.ifpApiPath}/common/download?filename=$filepath'));
        request.headers
          ..add('Content-type', 'application/json')
          ..add('Accept-Language', HiveUtilsSettings.getAppLanguage());
      } else {
        request = await httpClient.getUrl(Uri.parse(url));
      }

      AppLog.info('File url:${request.uri}');
      final response = await request.close();

      if (response.statusCode == 200) {
        final bytes = await consolidateHttpClientResponseBytes(response);
        final File file = File(filePath);
        await file.writeAsBytes(bytes);
        AppLog.info('File downloaded: $filePath');
        return file;
      } else {
        AppLog.warning('Download failed: ${response.statusCode}\n$response');
      }
      return null;
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return null;
    }
  }

  static String getFileName(String url) {
    return url.substring(url.lastIndexOf('/') + 1);
  }

  static Future<OpenResult> openFile(String filePath) async {
    final Map<String, String> typeMap = Platform.isAndroid
        ? {
            '.3gp': 'video/3gpp',
            '.torrent': 'application/x-bittorrent',
            '.kml': 'application/vnd.google-earth.kml+xml',
            '.gpx': 'application/gpx+xml',
            '.csv': 'application/vnd.ms-excel',
            '.apk': 'application/vnd.android.package-archive',
            '.asf': 'video/x-ms-asf',
            '.avi': 'video/x-msvideo',
            '.bin': 'application/octet-stream',
            '.bmp': 'image/bmp',
            '.c': 'text/plain',
            '.class': 'application/octet-stream',
            '.conf': 'text/plain',
            '.cpp': 'text/plain',
            '.doc': 'application/msword',
            '.docx':
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            '.xls': 'application/vnd.ms-excel',
            '.xlsx':
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            '.exe': 'application/octet-stream',
            '.gif': 'image/gif',
            '.gtar': 'application/x-gtar',
            '.gz': 'application/x-gzip',
            '.h': 'text/plain',
            '.htm': 'text/html',
            '.html': 'text/html',
            '.jar': 'application/java-archive',
            '.java': 'text/plain',
            '.jpeg': 'image/jpeg',
            '.jpg': 'image/jpeg',
            '.js': 'application/x-javascript',
            '.log': 'text/plain',
            '.m3u': 'audio/x-mpegurl',
            '.m4a': 'audio/mp4a-latm',
            '.m4b': 'audio/mp4a-latm',
            '.m4p': 'audio/mp4a-latm',
            '.m4u': 'video/vnd.mpegurl',
            '.m4v': 'video/x-m4v',
            '.mov': 'video/quicktime',
            '.mp2': 'audio/x-mpeg',
            '.mp3': 'audio/x-mpeg',
            '.mp4': 'video/mp4',
            '.mpc': 'application/vnd.mpohun.certificate',
            '.mpe': 'video/mpeg',
            '.mpeg': 'video/mpeg',
            '.mpg': 'video/mpeg',
            '.mpg4': 'video/mp4',
            '.mpga': 'audio/mpeg',
            '.msg': 'application/vnd.ms-outlook',
            '.ogg': 'audio/ogg',
            '.pdf': 'application/pdf',
            '.png': 'image/png',
            '.pps': 'application/vnd.ms-powerpoint',
            '.ppt': 'application/vnd.ms-powerpoint',
            '.pptx':
                'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            '.prop': 'text/plain',
            '.rc': 'text/plain',
            '.rmvb': 'audio/x-pn-realaudio',
            '.rtf': 'application/rtf',
            '.sh': 'text/plain',
            '.tar': 'application/x-tar',
            '.tgz': 'application/x-compressed',
            '.txt': 'text/plain',
            '.wav': 'audio/x-wav',
            '.wma': 'audio/x-ms-wma',
            '.wmv': 'audio/x-ms-wmv',
            '.wps': 'application/vnd.ms-works',
            '.xml': 'text/plain',
            '.z': 'application/x-compress',
            '.zip': 'application/x-zip-compressed',
            '': '*/*',
          }
        : Platform.isIOS
            ? {
                '.rtf': 'public.rtf',
                '.txt': 'public.plain-text',
                '.html': 'public.html',
                '.htm': 'public.html',
                '.xml': 'public.xml',
                '.tar': 'public.tar-archive',
                '.gz': 'org.gnu.gnu-zip-archive',
                '.gzip': 'org.gnu.gnu-zip-archive',
                '.tgz': 'org.gnu.gnu-zip-tar-archive',
                '.jpg': 'public.jpeg',
                '.jpeg': 'public.jpeg',
                '.png': 'public.png',
                '.avi': 'public.avi',
                '.mpg': 'public.mpeg',
                '.mpeg': 'public.mpeg',
                '.mp4': 'public.mpeg-4',
                '.3gpp': 'public.3gpp',
                '.3gp': 'public.3gpp',
                '.mp3': 'public.mp3',
                '.zip': 'com.pkware.zip-archive',
                '.gif': 'com.compuserve.gif',
                '.bmp': 'com.microsoft.bmp',
                '.ico': 'com.microsoft.ico',
                '.doc': 'com.microsoft.word.doc',
                '.xls': 'com.microsoft.excel.xls',
                '.ppt': 'com.microsoft.powerpoint.ppt',
                '.wav': 'com.microsoft.waveform-audio',
                '.wm': 'com.microsoft.windows-media-wm',
                '.wmv': 'com.microsoft.windows-media-wmv',
                '.pdf': 'com.adobe.pdf',
                '': '*/*',
              }
            : {};

    final String fileExtension =
        filePath.substring(0, filePath.lastIndexOf('/') + 1);

    final OpenResult r =
        await OpenFilex.open(filePath, type: typeMap[fileExtension]);
    if(r.type!=ResultType.done){
      String msg = LocaleKeys.somethingWentWrong.tr();
      if (r.type == ResultType.noAppToOpen) {
        msg = 'No compatible app found';
      } else if (r.type == ResultType.permissionDenied) {
        msg = 'Permission denied';
      } else if (r.type == ResultType.fileNotFound) {
        msg = 'File not found';
      }
      AppMessage.showOverlayNotification('', msg, msgType: 'error');
    }
    return r;
  }
}
