import 'package:flutter/material.dart';
import 'package:intl/intl.dart' as intl;

class DeviceType {
  static bool isDirectionRTL(BuildContext context) {
    return intl.Bidi.isRtlLanguage(
        Localizations.localeOf(context).languageCode);
  }

  static bool isTab(BuildContext context) =>
      MediaQuery.of(context).size.shortestSide > 550;

  static bool isLandsCape(BuildContext context) =>
      MediaQuery.of(context).orientation == Orientation.landscape;
}
