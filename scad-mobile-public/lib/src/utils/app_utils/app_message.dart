import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:lottie/lottie.dart';
import 'package:overlay_support/overlay_support.dart';
import 'package:scad_mobile/route_manager/route_imports.dart';
import 'package:scad_mobile/src/config/dependancy_injection/injection_container.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/animation_asset.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/extentions/widget_extensions.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

final GlobalKey<ScaffoldMessengerState> snackBarKey = GlobalKey<ScaffoldMessengerState>(debugLabel: 'snackBarKey');

enum SnackBarDuration {
  fast,
  short,
  long,
  indefinite,
}

class AppMessage {
  static OverlaySupportEntry? showOverlayNotificationSuccess({
    String title = '',
    String message = '',
    Duration? duration,
    VoidCallback? onTap,
    bool showIcon = true,
    NotificationPosition notificationPosition = NotificationPosition.top,
  }) {
    if (title.isNotEmpty || message.isNotEmpty) {
      return _showOverlayNotification(
        title: title,
        msg: message,
        msgType: 'success',
        duration: duration ?? const Duration(seconds: 3),
        onTap: onTap,
        showIcon: showIcon,
        notificationPosition: notificationPosition,
      );
    }
    return null;
  }

  static void showOverlayNotificationError({
    String title = '',
    String message = '',
    Duration? duration,
    VoidCallback? onTap,
    bool showIcon = true,
    NotificationPosition notificationPosition = NotificationPosition.top,
  }) {
    if (title.isNotEmpty || message.isNotEmpty) {
      _showOverlayNotification(
        title: title,
        msg: message,
        msgType: 'error',
        duration: duration ?? const Duration(seconds: 3),
        onTap: onTap,
        showIcon: showIcon,
        notificationPosition: notificationPosition,
      );
    }
  }

  static OverlaySupportEntry _showOverlayNotification({
    required String title,
    required String msg,
    required String msgType,
    required Duration duration,
    VoidCallback? onTap,
    bool showIcon = true,
    NotificationPosition notificationPosition = NotificationPosition.top,
  }) {
    final isLightMode = HiveUtilsSettings.isLightMode;
    bool enable = true;
    return showSimpleNotification(
      StatefulBuilder(
        builder: (BuildContext context, setState) {
          return IgnorePointer(
            ignoring: !enable,
            child: GestureDetector(
              onTap: () {
                onTap?.call();
                setState(() {
                  enable = false;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: ShapeDecoration(
                  color: isLightMode ? AppColors.white : AppColors.blueShade32,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  shadows: const [
                    BoxShadow(
                      color: Color(0x0F000000),
                      blurRadius: 10,
                      offset: Offset(0, 4),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    if (showIcon)
                      Padding(
                        padding: const EdgeInsets.only(right: 16),
                        child: SvgPicture.asset(
                          msgType == 'success' ? AppImages.icGreenCircleTick : AppImages.icAlertCircle,
                          height: 40,
                          width: 40,
                        ),
                      ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          if (title.isNotEmpty)
                            Padding(
                              padding: const EdgeInsets.only(bottom: 6),
                              child: Text(
                                title,
                                style: TextStyle(
                                  color: isLightMode ? AppColors.black : AppColors.white,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          Text(
                            msg,
                            style: TextStyle(
                              color: isLightMode ? AppColors.black : AppColors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.w300,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ).wrapInConstraintBox(),
          );
        },
      ),
      elevation: 0,
      position: notificationPosition,
      background: Colors.transparent,
      slideDismissDirection: DismissDirection.horizontal,
      duration: duration,
    );
  }

  static Future<bool> showAppDialog({
    String? title,
    String? description,
    WidgetBuilder? contentBuilder,
    VoidCallback? onProceed,
    VoidCallback? onCancel,
    bool dismissible = true,
  }) async {
    final BuildContext context = servicelocator<AppRouter>().navigatorKey.currentContext!;
    bool isProceed = false;

    await showDialog<void>(
      context: context,
      barrierDismissible: dismissible,
      builder: (BuildContext context) {
        return AlertDialog(
          contentPadding: const EdgeInsets.all(20),
          insetPadding: const EdgeInsets.all(20),
          backgroundColor: AppColors.white,
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(20)),
          ),
          elevation: 0,
          content: PopScope(
            canPop: dismissible,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Lottie.asset(AnimationAsset.animationAlert),
                const SizedBox(height: 14),
                ...contentBuilder != null
                    ? [
                        contentBuilder.call(context),
                      ]
                    : [
                        if (title != null)
                          Text(
                            title,
                            style: AppTextStyles.s16w5cBlackShade1,
                            textAlign: TextAlign.center,
                          ),
                        const SizedBox(height: 14),
                        if (description != null)
                          Text(
                            description,
                            style: AppTextStyles.s14w4cBlueTitleText,
                            textAlign: TextAlign.center,
                          ),
                      ],
                const SizedBox(height: 24),
                Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        style: TextButton.styleFrom(
                          shape: RoundedRectangleBorder(
                            side: BorderSide(color: AppColors.blue),
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        onPressed: () {
                          isProceed = false;
                          Navigator.pop(context);
                          onCancel?.call();
                        },
                        child: Text(
                          LocaleKeys.cancel.tr(),
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: AppColors.blue,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 14),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () async {
                          isProceed = true;
                          Navigator.pop(context);
                          onProceed?.call();
                        },
                        child: Text(
                          LocaleKeys.proceed.tr(),
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: AppColors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );

    return isProceed;
  }
}
