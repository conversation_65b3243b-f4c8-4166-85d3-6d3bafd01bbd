import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:overlay_support/overlay_support.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/utils/app_utils/flutter_toast.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

final GlobalKey<ScaffoldMessengerState> snackBarKey =
    GlobalKey<ScaffoldMessengerState>();

enum SnackBarDuration {
  fast,
  short,
  long,
  indefinite,
}

class AppMessage {
  static void showSnackBar(
    String? msg, {
    SnackBarDuration duration = SnackBarDuration.short,
  }) {
    if (msg == null) {
      return;
    }

    Duration duration0;
    if (duration == SnackBarDuration.fast) {
      duration0 = const Duration(milliseconds: 500);
    } else if (duration == SnackBarDuration.short) {
      duration0 = const Duration(seconds: 2);
    } else if (duration == SnackBarDuration.long) {
      duration0 = const Duration(seconds: 10);
    } else {
      duration0 = const Duration(hours: 1);
    }

   

    final snackBar = SnackBar(
      duration: duration0,
      content: Text(
        msg,
        textScaler: TextScaler.linear(textScaleFactor.value),
      ),
      action: SnackBarAction(
        label: 'Dismiss',
        textColor: Colors.yellow,
        onPressed: () {
          snackBarKey.currentState!.hideCurrentSnackBar();
        },
      ),
    );
    snackBarKey.currentState?.showSnackBar(snackBar);
  }

  static void showOverlayNotification(String title, String msg,
      {String msgType = 'success',
      Duration duration = const Duration(seconds: 3),
      VoidCallback? onTap}) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
   
    bool expanded = true;
    showSimpleNotification(
      StatefulBuilder(
        builder: (BuildContext context, setState) {
          return SizedBox(
            height: expanded ? null : 0,
            child: GestureDetector(
              onTap: () {
                onTap?.call();
                setState(() {
                  expanded = false;
                });
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: ShapeDecoration(
                  color:
                      isLightMode ? AppColors.white : AppColors.blueShade32,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  shadows: const [
                    BoxShadow(
                      color: Color(0x0F000000),
                      blurRadius: 10,
                      offset: Offset(0, 4),
                      spreadRadius: 0,
                    )
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SvgPicture.asset(
                      msgType == 'success'
                          ? 'assets/images/green-circle-tick.svg'
                          : 'assets/images/alert-circle-outline.svg',
                      height: 40,
                      width: 40,
                    ),
                    const SizedBox(
                      width: 16,
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          if (title.isNotEmpty)
                            Padding(
                              padding: const EdgeInsets.only(bottom: 6.0),
                              child: Text(
                                title,
                                style: TextStyle(
                                  color: isLightMode
                                      ? AppColors.black
                                      : AppColors.white,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          Text(
                            msg,
                            style: TextStyle(
                              color: isLightMode
                                  ? AppColors.black
                                  : AppColors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.w300,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
      elevation: 0,
      background: Colors.transparent,
      slideDismissDirection: DismissDirection.horizontal,
      duration: duration,
    );
  }
}
