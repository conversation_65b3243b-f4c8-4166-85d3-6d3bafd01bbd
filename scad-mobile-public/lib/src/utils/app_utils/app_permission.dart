import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

import '../../../main.dart';
import '../../../translations/locale_keys.g.dart';
import '../constants/asset_constants/animation_asset.dart';
import '../styles/app_text_styles.dart';

enum AppPermission {
  notification,
  location,
  storageAndroid,
  photos,
  manageExternalStorage
}

class AppPermissions {

  static Future<bool> checkPermissions(BuildContext context, List<AppPermission> permissions, {VoidCallback? onCancel}) async {

    final List<Permission> list = [];

    for(int i = 0;i<permissions.length;i++){
      if(permissions[i] == AppPermission.notification){
        list.add(Permission.notification);
      } else if(permissions[i] == AppPermission.location) {
        list.add(Permission.location);
      }else if (permissions[i] == AppPermission.storageAndroid &&
          Platform.isAndroid &&
          (await DeviceInfoPlugin().androidInfo).version.sdkInt < 33) {
        list.add(Permission.storage);
      } else if(permissions[i] == AppPermission.location) {
        list.add(Permission.location);
      }
       else if(permissions[i] == AppPermission.photos) {
        list.add(Permission.photos);
      } else if(permissions[i] == AppPermission.manageExternalStorage) {
        list.add(Permission.manageExternalStorage);
      }
    }

    final Map<Permission, PermissionStatus> statuses = await list.request();

    if(statuses.values.toList().any((element) => element != PermissionStatus.granted)){
          final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;

      await showDialog<void>(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              contentPadding: const EdgeInsets.all(20),
              insetPadding: const EdgeInsets.all(20),
              backgroundColor: AppColors.white,
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(20)),
              ),
              elevation: 0,
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  isLightMode ?

                  Lottie.asset(AnimationAsset.animationAlert) :  Lottie.asset(AnimationAssetDark.animationAlert) ,
                  const SizedBox(height: 14),
                  Text(
                    LocaleKeys.permissionDenied.tr(),
                    style: AppTextStyles.s16w5cBlackShade1,
                    textAlign: TextAlign.center,
                    textScaler: TextScaler.linear(textScaleFactor.value),
                  ),
                  const SizedBox(height: 14),
                  Text(
                    LocaleKeys.openAppSettingsForPermission.tr(),
                    style: AppTextStyles.s14w4cBlueTitleText,
                    textAlign: TextAlign.center,
                    textScaler: TextScaler.linear(textScaleFactor.value),
                  ),
                  const SizedBox(height: 24),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      ElevatedButton(
                        onPressed: () async {
                          if(permissions.contains(AppPermission.manageExternalStorage) && !(await Permission.manageExternalStorage.isGranted)) {
                            Permission.manageExternalStorage.request();
                          } else {
                            openAppSettings();
                          }
                          Navigator.pop(context);
                        },
                        child: Text(
                          LocaleKeys.proceed.tr(),
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: AppColors.white,
                          ),
                          textScaler: TextScaler.linear(textScaleFactor.value),
                        ),
                      ),
                      const SizedBox(height: 14),
                      TextButton(
                        style: TextButton.styleFrom(
                          shape: RoundedRectangleBorder(
                            side: BorderSide(color: AppColors.blue),
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        onPressed: () {
                          Navigator.pop(context);
                          onCancel?.call();
                        },
                        child: Text(
                          LocaleKeys.cancel.tr(),
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: AppColors.blue,
                          ),
                          textScaler: TextScaler.linear(textScaleFactor.value),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );});
    }

    return list.isEmpty || !statuses.values.toList().any((element) => element != PermissionStatus.granted);
  }

}
