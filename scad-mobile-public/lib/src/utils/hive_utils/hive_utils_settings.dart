import 'package:flutter/foundation.dart' show describeEnum;

import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_keys.dart';

enum UserGuides { None, Home, Domains, MyDashboards, Products, AskUs }

class HiveUtilsSettings {
  static Box<dynamic> box() => Hive.box<dynamic>(HiveKeys.boxSettings);

  static String boxStatus() {
    return getThemeMode().toString() +
        getTextSizeFactor().toString() +
        getAppLanguage();
  }

  static ThemeMode getThemeMode() {
    final String val = box()
        .get(HiveKeys.keyTheme, defaultValue: ThemeMode.system.name)
        .toString();

    if (val == ThemeMode.system.name) {
      final brightness =
          SchedulerBinding.instance.platformDispatcher.platformBrightness;
      if (brightness == Brightness.dark) {
        setThemeMode(ThemeMode.dark);
        return ThemeMode.dark;
      } else {
        setThemeMode(ThemeMode.light);
        return ThemeMode.light;
      }
    }

    return ThemeMode.values
        .firstWhere((d) => describeEnum(d) == val.toLowerCase());
  }

  static Future<void> setThemeMode(ThemeMode value) async {
    await box().put(
      HiveKeys.keyTheme,
      value.name,
    );
    AppColors.isLightMode = value == ThemeMode.light;
  }

  static double getTextSizeFactor() {
    return double.parse('${box().get(HiveKeys.keyTextSizeFactor, defaultValue: 1.0) ?? 1.0}');
  }

  static Future<void> setTextSizeFactor(double value) async {
    await box().put(
      HiveKeys.keyTextSizeFactor,
      value,
    );
  }

  static String getAppLanguage() {
    return (box().get(HiveKeys.keyLocale, defaultValue: 'en') ?? 'en')
        as String;
  }

  static Future<void> setAppLanguage(String langCode) async {
    await box().put(HiveKeys.keyLocale, langCode);
  }

  static Future<void> setDefaultSetting() async {
    final box = await Hive.openBox<dynamic>(HiveKeys.boxSettings);
    AppColors.isLightMode = getThemeMode() == ThemeMode.light;
  }


  static Future<void> saveUserGuideStatus({
    UserGuides value = UserGuides.None,
  }) async {
    final box = Hive.box<dynamic>(HiveKeys.boxSettings);
    await box.put(HiveKeys.showUserGuide, value.name);
  }

  static UserGuides getUserGuideStatus() {
    final box = Hive.box<dynamic>(HiveKeys.boxSettings);
    final selectedType =
        box.get(HiveKeys.showUserGuide, defaultValue: UserGuides.None.name) ??
            UserGuides.None.name;
    return UserGuides.values
        .firstWhere((element) => element.name == selectedType);
  }

  static Future<void> setSearchSuggestions({
    List<String> suggestions = const [],
  }) async {
    final box = Hive.box<dynamic>(HiveKeys.boxSettings);
    await box.put(HiveKeys.searchSuggestions, suggestions);
  }

  static List<String> getSearchSuggestions() {
    final box = Hive.box<dynamic>(HiveKeys.boxSettings);
    return (box.get(HiveKeys.searchSuggestions, defaultValue: <String>[]) ??
        <String>[]) as List<String>;
  }

}
