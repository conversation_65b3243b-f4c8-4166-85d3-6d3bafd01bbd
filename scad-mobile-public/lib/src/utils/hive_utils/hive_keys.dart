class HiveKeys {
  HiveKeys._();

  static const String boxAuth = 'boxAuthS';
  static const String boxSettings = 'boxSettingsS';
  static const String boxApiCache = 'boxApiCacheS';
  static const String boxPersistent = 'boxPersistentS';

  static const String keyTheme = 'keyTheme';
  static const String keyToken = 'keyToken';
  static const String keyRefreshToken = 'keyRefreshToken';
  static const String keyIsDomainsSelected = 'keyIsDomainsSelected';
  static const String keyLocale = 'keyLocale';
  static const String keyTextSizeFactor = 'keyTextSizeFactor';

  static const String keyAppNotifications = 'keyAppNotifications';
  static const String unreadAppNotifications = 'readAppNotifications';
  static const String keyMailNotifications = 'keyMailNotifications';
  static const String keyProfilePic = 'keyProfilePic';
  static const String showUserGuide = 'showUserGuide';
  static const String onboarding = 'onboarding';
  static const String deviceToken = 'deviceToken';
  static const String searchSuggestions = 'searchSuggestions';
  static const String keyAuthWebReportAndPublication = 'authWebReportAndPublication';
  static const String uuid = 'uuid';
  static const String loginMakeEmailDefault = 'loginMakeEmailDefault';

  static const String cacheHash = 'cacheHash';

  // domains cache keys
  static const String keyDomainList = 'domainList';
  static const String keyDomainListIfp = 'domainListIfp';
  static const String keyIndicatorList = 'keyIndicatorList';
  static const String keySavedDomainList = 'keySavedDomainList';
  static const String keyRecommendations = 'keyRecommendations';
  static const String productDataList = 'productDataList';
  static const String arcGisDataList = 'arcGisDataList';
  static const String newsletterList = 'newsletterList';

  static const String keyLazyItemStatusTmp = 'lazyItemStatusTmp';
  static const String keyHomeItemStatusTmp = 'homeItemStatusTmp';

  static String keyIndicatorStatus(String nodeId) => 'indicatorStatus-$nodeId';
}
