import 'dart:convert';

import 'package:hive_flutter/hive_flutter.dart';
import 'package:scad_mobile/src/features/domains/data/models/domain_model/domain_model.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_keys.dart';

class HiveUtilsApiCache {
  static String domainListKey = HiveKeys.keyDomainList;
  static List<String> persistentValueKeys = [
    domainListKey,
  ];

  static Box<dynamic> box() {
    return Hive.box<dynamic>(HiveKeys.boxApiCache);
  }

  static dynamic get(String uriKey) {
    var val = box().get(uriKey, defaultValue: null);
    return val == null ? null : jsonDecode(val.toString());
  }

  static Future<void> set(String uriKey, dynamic value) async {
    await box().put(
      uriKey,
      jsonEncode(value),
    );
  }

  static Future<void> clear({bool clearPersistent = true}) async {
    if (clearPersistent) {
      await box().clear();
    } else {
      final keys = box().keys.toList();
      for (final key in keys) {
        if (!persistentValueKeys.contains(key)) {
          await box().delete(key);
        }
      }
    }
  }

  static String getDomainImage(String? domainId) {
    final dynamic res = HiveUtilsApiCache.get(HiveKeys.keyDomainList);
    String iconUrl = '';
    if (res != null) {
      final List<DomainModel> list = (res as List<dynamic>)
          .map((e) => DomainModel.fromJson(e as Map<String, dynamic>))
          .toList();
      final int i = list.indexWhere((element) => element.domainId == domainId);
      if (i >= 0) {
        iconUrl = list[i].domainIcon ?? '';
      }
    }
    return iconUrl;
  }

  static String getDomainImageByName(String? domainName, bool rtl) {
    final dynamic res = HiveUtilsApiCache.get(HiveKeys.keyDomainList);
    String iconUrl = '';
    if (res != null) {
      final List<DomainModel> list = (res as List<dynamic>)
          .map((e) => DomainModel.fromJson(e as Map<String, dynamic>))
          .toList();
      final int i = list.indexWhere(
        (element) =>
            (rtl ? element.domainNameAr : element.domainName) == domainName,
      );
      if (i >= 0) {
        iconUrl = list[i].domainIcon ?? '';
      }
    }
    return iconUrl;
  }
}
