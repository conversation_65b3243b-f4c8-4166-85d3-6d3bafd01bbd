import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_udid/flutter_udid.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_keys.dart';

class HiveUtilsPersistent {
  HiveUtilsPersistent._();

  static Box<dynamic> get box => Hive.box<dynamic>(HiveKeys.boxPersistent);

  static dynamic get(String uriKey) {
    final val = box.get(uriKey, defaultValue: null);
    return val == null ? null : jsonDecode(val.toString());
  }

  static Future<void> set(String uriKey, dynamic value) async {
    await box.put(
      uriKey,
      jsonEncode(value),
    );
  }

  static Future<void> setDeviceToken(String deviceToken) async {
    await box.put(HiveKeys.deviceToken, deviceToken);
  }

  static String getDeviceToken() {
    return box.get(HiveKeys.deviceToken, defaultValue: '').toString();
  }

  static Future<void> setUuid(String uuid) async {
    await box.put(HiveKeys.uuid, uuid);
  }

  static String getUuid() {
    return box.get(HiveKeys.uuid, defaultValue: '').toString();
  }

  static Future<void> initUuid() async {
    if (HiveUtilsPersistent.getUuid().isEmpty) {
      String udid;
      try {
        udid = await FlutterUdid.udid;
        await setUuid(udid);
      } catch (e) {
        debugPrint('exception to find uuid e -> $e');
      }
    }
  }

  static bool getLoginMakeEmailDefault() {
    return bool.parse(box.get(HiveKeys.loginMakeEmailDefault, defaultValue: false).toString());
  }

  static Future<void> setLoginMakeEmailDefault(bool makeEmailDefault) async {
    await box.put(HiveKeys.loginMakeEmailDefault, makeEmailDefault);
  }

  static String? getCacheHash() {
    return box.get(HiveKeys.cacheHash, defaultValue: null).toString();
  }

  static Future<void> setCacheHash(String hash) async {
    await box.put(HiveKeys.cacheHash, hash);
  }
}
