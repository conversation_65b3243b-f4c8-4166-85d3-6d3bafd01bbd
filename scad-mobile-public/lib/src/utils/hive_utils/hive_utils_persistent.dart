import 'dart:convert';

import 'package:hive_flutter/hive_flutter.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_keys.dart';

class HiveUtilsPersistent {
  static Box<dynamic> box() {
    return Hive.box<dynamic>(HiveKeys.boxPersistent);
  }

  static dynamic get(String uriKey) {
    var val = box().get(uriKey, defaultValue: null);
    return val == null ? null : jsonDecode(val.toString());
  }

  static Future<void> set(String uriKey, dynamic value) async {
    await box().put(
      uriKey,
      jsonEncode(value),
    );
  }

  static Future<void> setDeviceToken(String deviceToken) async {
    final box = Hive.box<dynamic>(HiveKeys.boxPersistent);
    await box.put(HiveKeys.deviceToken, deviceToken);
  }

  static String getDeviceToken() {
    final box = Hive.box<dynamic>(HiveKeys.boxPersistent);
    return box.get(HiveKeys.deviceToken, defaultValue: '').toString();
  }
}
