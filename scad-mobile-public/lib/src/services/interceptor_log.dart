import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

class InterceptorLog extends Interceptor {
  @override
  void onRequest(RequestOptions request, RequestInterceptorHandler handler) {
    // var msg =
    //     '\n=======REQUEST-${request.method}=========BEGIN========================\n';
    // msg += '[URL] :${request.uri}\n';
    // // msg += '[TOKEN] :$token\n';
    // msg += '[HEADERS] :${json.encode(request.headers)}\n';
    // if (request.data != null) {
    //   try {
    //     var d = request.data as FormData;
    //     d.fields.toList().forEach((element) {
  
    //     });
    //   } catch (e) {
    //     msg += '[JSON_PAYLOAD] :${request.data}\n';
    //   }
    // }
    // msg +=
    //     '=======REQUEST-${request.method}=========END==========================\n';


    _log(request.method, request.uri, request.headers, request.data);
    super.onRequest(request, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    _log(
      response.requestOptions.method,
      response.requestOptions.uri,
      response.requestOptions.headers,
      response.requestOptions.data,
      response: response,
    );
    super.onResponse(response, handler);
  }

  @override
  Future<void> onError(DioException err, ErrorInterceptorHandler handler) async {
    _log(
      err.requestOptions.method,
      err.requestOptions.uri,
      err.requestOptions.headers,
      err.requestOptions.data,
      error: err,
    );

    super.onError(err, handler);
  }

  void _log(
    String method,
    Uri uri,
    Map<String, dynamic> headers,
    dynamic payload, {
    Response<dynamic>? response,
    DioException? error,
  }) {
    try {
      // if (response != null) {
        // String msg = '[URL] :${uri.toString()}\n[RESPONSE_DATA] :${json.encode(response.data)}\n';
    
      // }
      // return;
      final isRequest = response == null && error == null;
      var msg = '';

      msg +=
          '\n=======${isRequest ? 'REQUEST' : 'RESPONSE'}-$method=========BEGIN========================\n';
      msg += '[URL] :${uri.toString()}\n';
      msg += '[HEADERS] :${json.encode(headers)}\n';
      try {
        if(payload is FormData) {
          final FormData data = payload;
          var str = '';
          data.fields.toList().forEach((element) {
            str +=  '${element.key}:${element.value}\n'; //json.encode(element);
          });
          msg += '[MULTIPART_PAYLOAD] :$str';
        }else{
          msg += '[JSON_PAYLOAD] :$payload\n';
        }
      } catch (e,s) {
        Completer<dynamic>().completeError(e,s);
        msg += '[_PAYLOAD] :$payload\n';
      }
      if (response != null) {
        msg += '[RESPONSE_CODE] :${response.statusCode}\n';
        msg += '[RESPONSE_DATA] :${json.encode(response.data)}\n';
      }
      if (error != null) {
        msg += '[RESPONSE_CODE] :${error.response?.statusCode}\n';
        msg += '[ERRORt] :${error.type}\n';
        msg += '[ERRORm] :${error.message}\n';
        msg += '[ERRORe] :${error.error}\n';
        msg += '[ERRORr] :${json.encode(error.response?.data)}\n';
      }
      msg +=
      '=======${isRequest ? 'REQUEST' : 'RESPONSE'}-$method=========BEGIN========================\n';
    if(kDebugMode){
log(msg);
    }
      
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
    }
  }
}
