import 'package:scad_mobile/src/utils/app_utils/app_log.dart';

class HttpException implements Exception {
  HttpException([this.title, this.message]) {
    //todo
    AppLog.info(
        'show error in dialog > ${title ?? 'Error'},${message ?? 'Something went wrong'}',);
    // if (Get.isDialogOpen ?? false) {
    //   Get.close(1);
    // }
    // AppDialogs.simpleMessageDialog(title: title ?? 'Error', message: message ?? 'Something went wrong');
  }

  final String? title;
  final String? message;
}

class BadRequestException extends HttpException {
  BadRequestException([String? message]) : super('Bad Request', message);
}

class FetchDataException extends HttpException {
  FetchDataException([String? message]) : super('Unable to process', message);
}

class ApiNotRespondingException extends HttpException {
  ApiNotRespondingException([String? message])
      : super('The connection has timed out', message);
}

class UnAuthorizedException extends HttpException {
  UnAuthorizedException([String? message]) : super('Login Required', message) {
    AppLog.info('check is unauthorized');
    //todo exception
    // if (Get.currentRoute != Routes.LOGIN) {
    //   AppStorage.logOut();
    // }
  }
}

class NotFoundException extends HttpException {
  NotFoundException([String? message]) : super('Not found', message);
}
