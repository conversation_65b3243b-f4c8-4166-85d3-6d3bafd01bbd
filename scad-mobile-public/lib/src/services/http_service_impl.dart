import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:scad_mobile/src/common/models/response_models/api_response.dart';
import 'package:scad_mobile/src/config/app_config/api_config.dart';
import 'package:scad_mobile/src/config/app_config/secret.dart';
import 'package:scad_mobile/src/services/http_services.dart';
import 'package:scad_mobile/src/services/interceptor_log.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

enum ApiServer {
  ifp,
  app,
  scad,
}

class HttpServiceRequests implements HttpService {

  Future<IOHttpClientAdapter> getIOHttpClientAdapter({bool isScad = false}) async {
    // ByteData clientCertificateWildcard = await rootBundle.load('assets/certificates/wildcard_scad_gov_ae.pem');
    // ByteData clientCertificateScad = await rootBundle.load('assets/certificates/scad.gov.ae.cer');

    return IOHttpClientAdapter(
      createHttpClient: () {
        if(isScad){
          return HttpClient(
            context: SecurityContext()
              ..setTrustedCertificatesBytes(base64.decode(Secret.scad))
            // ..setTrustedCertificatesBytes(clientCertificateScad.buffer.asUint8List(),)
            ,);
        } else {
          return HttpClient(
            context: SecurityContext()
              ..setTrustedCertificatesBytes(base64.decode(Secret.wildcard))
            // ..setTrustedCertificatesBytes(clientCertificateWildcard.buffer.asUint8List(),)
            ,);
        }
      },
    );
  }

  String apiBaseUrl(ApiServer server) {
    return server == ApiServer.scad
        ? ApiConfig.scadBaseUrl
        : server == ApiServer.ifp
            ? ApiConfig.ifpBaseUrl
            : ApiConfig.appBaseUrl;
  }

  @override
  Future<ApiResponse> postJson(
    String endpoint, {
    ApiServer server = ApiServer.app,
    Map<String, dynamic>? jsonPayloadMap,
    List<dynamic>? jsonPayloadList,
    bool shouldAuthenticate = true,
    bool encodedHeader = false,
    String token='',
    bool shouldIntercept = true,
  }) async {
    try {
      // final String apiToken = token ?? (shouldAuthenticate ? apiToken1 : '');

      final Dio dio = encodedHeader
          ? Dio(
              BaseOptions(
                headers: {
                  'Content-type': 'application/x-www-form-urlencoded',
                  'Accept-Language': HiveUtilsSettings.getAppLanguage(),
                },
              ),
            )
          : Dio(
              BaseOptions(
                baseUrl: apiBaseUrl(server),
                headers: {
                  'Content-type': 'application/json',
                  'Accept-Language': HiveUtilsSettings.getAppLanguage(),
                  if (token.isNotEmpty) 'Authorization': 'Bearer $token',
                },
              ),
            );
      if(shouldIntercept) {
        dio.interceptors.add(InterceptorLog());
      }

      dio.httpClientAdapter = await getIOHttpClientAdapter(isScad : server == ApiServer.scad);

      // (dio.httpClientAdapter as IOHttpClientAdapter).onHttpClientCreate =
      //     (HttpClient client) {
      //   client.badCertificateCallback =
      //       (X509Certificate cert, String host, int port) => true;
      //   return client;
      // };

      dynamic data;
      if (jsonPayloadMap != null) {
        data = jsonPayloadMap;
      } else if (jsonPayloadList != null) {
        data = json.encode(jsonPayloadList);
      }

      final response = await dio.post<dynamic>(
        endpoint,
        data: data,
      );
      return _processResponse(response);
    } on SocketException {
      return ApiResponse.error(LocaleKeys.networkError.tr());
    } on TimeoutException {
      return ApiResponse.error(LocaleKeys.requestHasBeenTimedOut.tr());
    } catch (error, s) {
      Completer<dynamic>().completeError(error, s);

      if (error is DioException) {
        return _processResponse(error.response!);
      } else {
        return ApiResponse.error(LocaleKeys.somethingWentWrong.tr());
      }
    }
  }

  @override
  Future<ApiResponse> postMultipart(
    String endpoint, {
    ApiServer server = ApiServer.app,
    Map<String, dynamic>? formDataPayload,
    List<dynamic>? jsonPayloadList,
    Map<String, File> filePayload = const {},
  }) async {
    try {
      // String apiToken = apiToken1;

      final Dio dio = Dio(
        BaseOptions(
          baseUrl: apiBaseUrl(server),
          headers: {
            'Content-type': 'multipart/form-data',
            'Accept-Language': HiveUtilsSettings.getAppLanguage(),
            // if (apiToken.isNotEmpty) 'Authorization': 'Bearer $apiToken',
          },
        ),
      );
      dio.interceptors.add(InterceptorLog());

      dio.httpClientAdapter = await getIOHttpClientAdapter(isScad : server == ApiServer.scad);

      // (dio.httpClientAdapter as IOHttpClientAdapter).onHttpClientCreate =
      //     (HttpClient client) {
      //   client.badCertificateCallback =
      //       (X509Certificate cert, String host, int port) => true;
      //   return client;
      // };

      dynamic data;
      if (formDataPayload != null) {
        data = FormData.fromMap(formDataPayload);
        // formDataPayload.forEach((key, value) {
        //   formData.fields.add(MapEntry(key, value.toString()));
        // });
      } else if (jsonPayloadList != null) {
        data = json.encode(jsonPayloadList);
      }

      if (filePayload != {}) {
        data ??= FormData();
        for (final MapEntry<String, File> entry in filePayload.entries) {
          data.files.add(
            MapEntry(
              entry.key,
              await MultipartFile.fromFile(
                entry.value.path,
                filename: entry.value.path.split('/').last,
              ),
            ),
          );
        }
      }

      final response = await dio.post<dynamic>(endpoint, data: data);
      return _processResponse(response);
    } on SocketException {
      return ApiResponse.error(LocaleKeys.networkError.tr());
    } on TimeoutException {
      return ApiResponse.error(LocaleKeys.requestHasBeenTimedOut.tr());
    } catch (error, s) {
      Completer<dynamic>().completeError(error, s);

      if (error is DioException) {
        return _processResponse(error.response!);
      } else {
        return ApiResponse.error( LocaleKeys.somethingWentWrong.tr());
      }
    }
  }

  @override
  Future<ApiResponse> get(
    String endpoint, {
    ApiServer server = ApiServer.app,
    String token='',
  }) async {
    try {
      // final String apiToken = apiToken1;

      final Dio dio = Dio(
        BaseOptions(
          baseUrl: apiBaseUrl(server),
          headers: {
            // 'cache-control':'no-cache',
            'Content-type': 'application/json',
            'Accept-Language': HiveUtilsSettings.getAppLanguage(),
            if (token.isNotEmpty) 'Authorization': 'Bearer $token',
          },
        ),
      );
      dio.interceptors.add(InterceptorLog());

      dio.httpClientAdapter = await getIOHttpClientAdapter(isScad : server == ApiServer.scad);

      // (dio.httpClientAdapter as IOHttpClientAdapter).onHttpClientCreate =
      //     (HttpClient client) {
      //   client.badCertificateCallback =
      //       (X509Certificate cert, String host, int port) => true;
      //   return client;
      // };

      final response = await dio.get<dynamic>(endpoint);
      return _processResponse(response);
    } on SocketException {
      return ApiResponse.error(LocaleKeys.networkError.tr());
    } on TimeoutException {
      return ApiResponse.error(LocaleKeys.requestHasBeenTimedOut.tr());
    } catch (error, s) {
      Completer<dynamic>().completeError(error, s);

      if (error is DioException) {
        return _processResponse(error.response!);
      } else {
        return ApiResponse.error( LocaleKeys.somethingWentWrong.tr());
      }
    }
  }

  ApiResponse _processResponse(Response<dynamic> response) {
    switch (response.statusCode) {
      case 200:
        if (response.data is Map) {
          return ApiResponse.success(response.data as Map<String, dynamic>);
        } else {
          return ApiResponse.success({'data': response.data});
        }
      case 423:
        ApiResponse.error('${LocaleKeys.badResponse.tr()} ${response.statusCode}');
      case 424:
        return ApiResponse.error('${LocaleKeys.unknownResponse.tr()} ${response.statusCode}');
      case 201:
        if (response.data is Map) {
          return ApiResponse.success(response.data as Map<String, dynamic>);
        } else {
          return ApiResponse.success({'data': response.data});
        }
      case 301:
        return ApiResponse.success({});
      case 400:
        String msg = LocaleKeys.badRequest.tr();
        try {
          final Map map = response.data as Map;
          if (map.containsKey('message')) {
            return ApiResponse.error(map['message'].toString());
          } else if (map.containsKey('error')) {
            return ApiResponse.error(map['error'].toString());
          }

          if (map.containsKey('current_password') &&
              (map['current_password'] as List).isNotEmpty) {
            //user profile reset password
            msg = map['current_password'].firstOrNull.toString();
          } else if (map.containsKey('non_field_errors') &&
              map['non_field_errors'].toString().isNotEmpty) {
            //user profile reset password
            msg = map['non_field_errors'].firstOrNull.toString();
          }

          return ApiResponse.error(msg);
        } catch (e, s) {
          Completer<dynamic>().completeError(e, s);
        }
        // if(showErrorDialog) {
        //   throw BadRequestException(msg ??
        //       'Server cannot or will not process the request due to something that is perceived to be a client error');
        // }
        ApiResponse.error(msg);
      case 401:
        String msg = LocaleKeys.unauthorized.tr();
        try {
          final Map map = response.data as Map;
          if (map.containsKey('error')) {
            msg = map['error'].toString();
          }
          if (msg == 'inactive_userprofile') {
            AppMessage.showOverlayNotification(
              '',
              LocaleKeys.inactiveProfileStatusMessage.tr(),
              msgType: 'error',
            );
          }
          if (map['code'].toString() == 'user_not_found') {
            AppMessage.showOverlayNotification(
              '',
              LocaleKeys.inactiveProfileStatusMessage.tr(),
              msgType: 'error',
            );
          }
        } catch (e, s) {
          Completer<dynamic>().completeError(e, s);
        }

        return ApiResponse.error(msg);
      case 403:
        return ApiResponse.error(
          LocaleKeys.sessionExpired.tr(),
        );
      case 404:
        String msg = LocaleKeys.requestedResourceIsMissing.tr();
        try {
          final Map map = response.data as Map;
          if (map.containsKey('message')) {
            msg = map['message'].toString();
          }
        } catch (e, s) {
          Completer<dynamic>().completeError(e, s);
        }
        // if(showErrorDialog) {
        //   throw NotFoundException(msg ?? 'Requested resource is missing');
        // }
        return ApiResponse.error(msg);
      case 422:
        // if(showErrorDialog) {
        //   throw BadRequestException('Unable to process the request');
        // }
        ApiResponse.error(LocaleKeys.unableToProcessTheRequest.tr());
      case 500:
      default:
        String? msg;
        try {
          final Map map = response.data as Map;
          if (map.containsKey('detail')) {
            msg = map['detail'].toString();
          }
        } catch (e, s) {
          Completer<dynamic>().completeError(e, s);
        }
        if (msg != null) {
          // if(showErrorDialog) {
          //   throw FetchDataException(msg);
          // }
          ApiResponse.error(msg);
        }
    }
    return ApiResponse.error(LocaleKeys.unknownErrorOccurred.tr());
  }

  @override
  Future<ApiResponse> putJson(
    String endpoint, {
    ApiServer server = ApiServer.app,
    Map<String, dynamic> jsonPayload = const {},
    bool shouldAuthenticate = true,
  }) async {
    try {
      // final String apiToken = shouldAuthenticate ? apiToken1 : '';

      final Dio dio = Dio(
        BaseOptions(
          baseUrl: apiBaseUrl(server),
          headers: {
            'Content-type': 'application/json',
            'Accept-Language': HiveUtilsSettings.getAppLanguage(),
            // if (apiToken.isNotEmpty) 'Authorization': 'Bearer $apiToken',
          },
        ),
      );
      dio.interceptors.add(InterceptorLog());

      dio.httpClientAdapter = await getIOHttpClientAdapter(isScad : server == ApiServer.scad);

      // (dio.httpClientAdapter as IOHttpClientAdapter).onHttpClientCreate =
      //     (HttpClient client) {
      //   client.badCertificateCallback =
      //       (X509Certificate cert, String host, int port) => true;
      //   return client;
      // };

      final response = await dio.put<dynamic>(endpoint, data: jsonPayload);
      return _processResponse(response);
    } on SocketException {
      return ApiResponse.error(LocaleKeys.networkError.tr());
    } on TimeoutException {
      return ApiResponse.error(LocaleKeys.requestHasBeenTimedOut.tr());
    } catch (error, s) {
      Completer<dynamic>().completeError(error, s);

      if (error is DioException) {
        return _processResponse(error.response!);
      } else {
        return ApiResponse.error( LocaleKeys.somethingWentWrong.tr());
      }
    }
  }

  @override
  Future<ApiResponse> putMultipart(
    String endpoint, {
    ApiServer server = ApiServer.app,
    Map<String, dynamic>? formDataPayload,
    List<dynamic>? jsonPayloadList,
    Map<String, File> filePayload = const {},
  }) async {
    try {
      // String apiToken = apiToken1;

      final Dio dio = Dio(
        BaseOptions(
          baseUrl: apiBaseUrl(server),
          headers: {
            'Content-type': 'multipart/form-data',
            'Accept-Language': HiveUtilsSettings.getAppLanguage(),
            // if (apiToken.isNotEmpty) 'Authorization': 'Bearer $apiToken',
          },
        ),
      );
      dio.interceptors.add(InterceptorLog());

      dio.httpClientAdapter = await getIOHttpClientAdapter(isScad : server == ApiServer.scad);

      // (dio.httpClientAdapter as IOHttpClientAdapter).onHttpClientCreate =
      //     (HttpClient client) {
      //   client.badCertificateCallback =
      //       (X509Certificate cert, String host, int port) => true;
      //   return client;
      // };

      dynamic data;
      if (formDataPayload != null) {
        data = FormData.fromMap(formDataPayload);
        // formDataPayload.forEach((key, value) {
        //   formData.fields.add(MapEntry(key, value.toString()));
        // });
      } else if (jsonPayloadList != null) {
        data = json.encode(jsonPayloadList);
      }

      if (filePayload != {}) {
        data ??= FormData();
        for (final MapEntry<String, File> entry in filePayload.entries) {
          data.files.add(
            MapEntry(
              entry.key,
              await MultipartFile.fromFile(
                entry.value.path,
                filename: entry.value.path.split('/').last,
              ),
            ),
          );
        }
      }

      final response = await dio.put<dynamic>(endpoint, data: data);
      return _processResponse(response);
    } on SocketException {
      return ApiResponse.error(LocaleKeys.networkError.tr());
    } on TimeoutException {
      return ApiResponse.error(LocaleKeys.requestHasBeenTimedOut.tr());
    } catch (error, s) {
      Completer<dynamic>().completeError(error, s);

      if (error is DioException) {
        return _processResponse(error.response!);
      } else {
        return ApiResponse.error( LocaleKeys.somethingWentWrong.tr());
      }
    }
  }

  @override
  Future<ApiResponse> delete(
    String endpoint, {
    ApiServer server = ApiServer.app,
    String token='',
  }) async {
    try {
      // final String apiToken = token ?? apiToken1;

      final Dio dio = Dio(
        BaseOptions(
          baseUrl: apiBaseUrl(server),
          headers: {
            'Content-type': 'application/json',
            'Accept-Language': HiveUtilsSettings.getAppLanguage(),
            if (token.isNotEmpty) 'Authorization': 'Bearer $token',
          },
        ),
      );
      dio.interceptors.add(InterceptorLog());

      dio.httpClientAdapter = await getIOHttpClientAdapter(isScad : server == ApiServer.scad);

      // (dio.httpClientAdapter as IOHttpClientAdapter).onHttpClientCreate =
      //     (HttpClient client) {
      //   client.badCertificateCallback =
      //       (X509Certificate cert, String host, int port) => true;
      //   return client;
      // };

      final response = await dio.delete<dynamic>(endpoint);
      return _processResponse(response);
    } on SocketException {
      return ApiResponse.error(LocaleKeys.networkError.tr());
    } on TimeoutException {
      return ApiResponse.error(LocaleKeys.requestHasBeenTimedOut.tr());
    } catch (error, s) {
      Completer<dynamic>().completeError(error, s);

      if (error is DioException) {
        return _processResponse(error.response!);
      } else {
        return ApiResponse.error( LocaleKeys.somethingWentWrong.tr());
      }
    }
  }
}
