class LanguageUpdateRequest {
  LanguageUpdateRequest({
    this.name,
    this.deviceRegId,
  });

  LanguageUpdateRequest.fromJson(Map<String, dynamic> json)
      : name = json['language'] as String?,
        deviceRegId = json['device_reg_id'] as String?;

  final String? name;
  final String? deviceRegId;

  Map<String, dynamic> toJson() => {
        'language': name,
        'device_reg_id': deviceRegId,
      };
}
