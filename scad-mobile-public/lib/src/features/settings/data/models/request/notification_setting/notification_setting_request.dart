import 'package:json_annotation/json_annotation.dart';

part 'notification_setting_request.g.dart';

@JsonSerializable()
class NotificationSettingRequestModel {
  NotificationSettingRequestModel({
    this.appNotifications,
    this.mailNotifications,
    this.deviceRegId,
  });

  factory NotificationSettingRequestModel.fromJson(Map<String, dynamic> json) =>
      _$NotificationSettingRequestModelFromJson(json);

  @<PERSON><PERSON><PERSON><PERSON>(name: 'app_notifications')
  final bool? appNotifications;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'mail_notifications')
  final bool? mailNotifications;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'device_reg_id')
  final String? deviceRegId;

  Map<String, dynamic> toJson() =>
      _$NotificationSettingRequestModelToJson(this);
}
