import 'package:json_annotation/json_annotation.dart';

part 'password_reset_request.g.dart';

@JsonSerializable()
class PasswordResetRequestModel {
  PasswordResetRequestModel({
    this.currentPassword,
    this.newPassword,
    this.reTypePassword,
    this.refreshToken,
  });

  factory PasswordResetRequestModel.fromJson(Map<String, dynamic> json) =>
      _$PasswordResetRequestModelFromJson(json);
  @<PERSON><PERSON><PERSON><PERSON>(name: 'current_password')
  final String? currentPassword;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'new_password')
  final String? newPassword;
  @<PERSON><PERSON><PERSON><PERSON>(name: 're_type_password')
  final String? reTypePassword;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'refresh')
  final String? refreshToken;

  Map<String, dynamic> toJson() => _$PasswordResetRequestModelToJson(this);
}
