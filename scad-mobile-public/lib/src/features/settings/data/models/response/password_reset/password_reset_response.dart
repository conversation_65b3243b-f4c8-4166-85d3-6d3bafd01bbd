import 'package:json_annotation/json_annotation.dart';

part 'password_reset_response.g.dart';

@JsonSerializable()
class PasswordResetResponseModel {
  PasswordResetResponseModel({
    this.status,
    this.message,
  });

  factory PasswordResetResponseModel.fromJson(Map<String, dynamic> json) =>
      _$PasswordResetResponseModelFromJson(json);
  final String? status;
  final String? message;

  Map<String, dynamic> toJson() => _$PasswordResetResponseModelToJson(this);
}
