class ProfilePicUpdateResponse {
  ProfilePicUpdateResponse({
    this.status,
    this.message,
    this.profilePic,
  });

  ProfilePicUpdateResponse.fromJson(Map<String, dynamic> json)
      : status = json['name'] as String?,
        message = json['message'] as String?,
        profilePic = json['profile_pic'] as String?;

  final String? status;
  final String? message;
  final String? profilePic;

  Map<String, dynamic> toJson() => {
        'status': status,
        'message': message,
        'profile_pic': profilePic,
      };
}
