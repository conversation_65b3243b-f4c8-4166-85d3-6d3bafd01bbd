import 'package:json_annotation/json_annotation.dart';

part 'setting_response.g.dart';

@JsonSerializable()
class SettingResponseModel {
  SettingResponseModel(
      {this.appNotifications,
      this.mailNotifications,
      this.status,
      this.message,
        this.notificationUnread,
        this.language});

  factory SettingResponseModel.fromJson(
    Map<String, dynamic> json,
  ) =>
      _$SettingResponseModelFromJson(json);

  @<PERSON><PERSON><PERSON><PERSON>(name: 'app_notifications')
  final bool? appNotifications;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'mail_notifications')
  final bool? mailNotifications;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'notification_unread')
  final bool? notificationUnread;
  final String? status;
  final String? message;
  final String? language;

  Map<String, dynamic> toJson() => _$SettingResponseModelToJson(this);
}
