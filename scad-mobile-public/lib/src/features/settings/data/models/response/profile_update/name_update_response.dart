class NameUpdateResponse {
  NameUpdateResponse({
    this.name,
    this.email,
    this.role,
    this.status,
    this.emiratesId,
    this.uuid,
    this.message,
  });

  NameUpdateResponse.fromJson(Map<String, dynamic> json)
      : name = json['name'] as String?,
        email = json['email'] as String?,
        role = json['role'] as String?,
        status = json['status'] as String?,
        emiratesId = json['emirates_id'] as String?,
        uuid = json['uuid'] as String?,
        message = json['message'] as String?;

  final String? name;
  final String? email;
  final String? role;
  final String? status;
  final String? emiratesId;
  final String? uuid;
  final String? message;

  Map<String, dynamic> toJson() => {
        'name': name,
        'email': email,
        'role': role,
        'status': status,
        'emirates_id': emiratesId,
        'uuid': uuid,
        'message': message,
      };
}
