import 'package:json_annotation/json_annotation.dart';

part 'default_setting_response.g.dart';

@JsonSerializable()
class DefaultSettingResponseModel {
  DefaultSettingResponseModel({
    this.appNotifications,
    this.mailNotifications,
    this.language,
    this.textSize,
    this.profilePic,
    this.name,
    this.notificationUnread,
  });

  factory DefaultSettingResponseModel.fromJson(
    Map<String, dynamic> json,
  ) =>
      _$DefaultSettingResponseModelFromJson(json);

  @<PERSON><PERSON><PERSON><PERSON>(name: 'app_notifications')
  final bool? appNotifications;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'mail_notifications')
  final bool? mailNotifications;
  final String? language;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'text_size')
  final String? textSize;
  @<PERSON>son<PERSON><PERSON>(name: 'profile_pic')
  final String? profilePic;
  final String? name;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'notification_unread')
  final bool? notificationUnread;

  Map<String, dynamic> toJson() => _$DefaultSettingResponseModelToJson(this);
}
