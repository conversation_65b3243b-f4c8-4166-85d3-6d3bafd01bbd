import 'package:json_annotation/json_annotation.dart';

part 'default_setting_response.g.dart';

@JsonSerializable()
class DefaultSettingResponseModel {
  DefaultSettingResponseModel({
    this.appNotifications,
    this.mailNotifications,
    this.language,
    this.textSize,
    this.profilePic,
    this.notificationUnread,
  });

  factory DefaultSettingResponseModel.fromJson(
    Map<String, dynamic> json,
  ) =>
      _$DefaultSettingResponseModelFromJson(json);

  @<PERSON>son<PERSON><PERSON>(name: 'app_notifications')
  final bool? appNotifications;
  @<PERSON>son<PERSON><PERSON>(name: 'mail_notifications')
  final bool? mailNotifications;
  final String? language;
  @J<PERSON><PERSON><PERSON>(name: 'text_size')
  final String? textSize;
  @Json<PERSON>ey(name: 'profile_pic')
  final String? profilePic;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'notification_unread')
  final bool? notificationUnread;

  Map<String, dynamic> toJson() => _$DefaultSettingResponseModelTo<PERSON>son(this);
}
