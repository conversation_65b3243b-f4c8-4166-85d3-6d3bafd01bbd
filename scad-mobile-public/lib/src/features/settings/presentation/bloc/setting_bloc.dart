import 'dart:async';
import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/route_manager/route_imports.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/config/dependancy_injection/injection_container.dart';
import 'package:scad_mobile/src/features/settings/data/models/request/language_update/language_update_request.dart';
import 'package:scad_mobile/src/features/settings/data/models/response/password_reset/password_reset_response.dart';
import 'package:scad_mobile/src/features/settings/data/models/response/profile_update/name_update_response.dart';
import 'package:scad_mobile/src/features/settings/data/models/response/profile_update/profile_pic_upload_response.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_api_cache.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

part 'setting_event.dart';
part 'setting_state.dart';

class SettingBloc extends Bloc<SettingEvent, SettingState> {
  SettingBloc() : super(SettingLoadingState()) {
    on<DefaultSettingLoadEvent>(_onLoadDefaultSetting);
    on<LanguageUpdateEvent>(_onLanguageUpdate);
  }

  Future<void> _onLoadDefaultSetting(
    DefaultSettingLoadEvent event,
    Emitter<SettingState> emit,
  ) async {
    try {
      await HiveUtilsSettings.setDefaultSetting();
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(
        SettingFailureState(error:  LocaleKeys.somethingWentWrong.tr()),
      );
    }
  }

  Future<void> _onLanguageUpdate(
    LanguageUpdateEvent event,
    Emitter<SettingState> emit,
  ) async {
      final BuildContext ctx = servicelocator<AppRouter>().navigatorKey.currentContext!;

    try {
        await ctx.setLocale(Locale(event.languageUpdateRequest.name ?? 'en',''));

        await HiveUtilsApiCache.clear();

        await HiveUtilsSettings.setAppLanguage(event.languageUpdateRequest.name ?? 'en',
        );
           final BuildContext ctx1 = servicelocator<AppRouter>().navigatorKey.currentContext!;
          unawaited(AutoRouter.of(ctx1).replaceAll([HomeNavigationRoute()]));

    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(SettingFailureState(error:  LocaleKeys.somethingWentWrong.tr()));
    } 
  }
}
