part of 'setting_bloc.dart';

abstract class SettingEvent extends Equatable {
  const SettingEvent();

  @override
  List<Object> get props => [];
}


class DefaultSettingLoadEvent extends SettingEvent {
  const DefaultSettingLoadEvent();

  @override
  List<Object> get props => [];
}

class LanguageUpdateEvent extends SettingEvent {
  const LanguageUpdateEvent({
    required this.languageUpdateRequest,
  });

  final LanguageUpdateRequest languageUpdateRequest;

  @override
  List<Object> get props => [languageUpdateRequest];
}
