part of 'setting_bloc.dart';

abstract class SettingState extends Equatable {
  const SettingState();

  @override
  List<Object> get props => [];
}

class SettingLoadingState extends SettingState {}

class SettingSuccessState extends SettingState {
  const SettingSuccessState({
    required this.updatedSettingResponse,
  });

  final SettingResponseModel updatedSettingResponse;

  @override
  List<Object> get props => [updatedSettingResponse];
}

class DefaultSettingSuccessState extends SettingState {
  const DefaultSettingSuccessState({
    required this.defaultSettingResponse,
  });

  final DefaultSettingResponseModel defaultSettingResponse;

  @override
  List<Object> get props => [defaultSettingResponse];
}

class SettingFailureState extends SettingState {
  const SettingFailureState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}

class PasswordInitialState extends SettingState {}

class PasswordLoadingState extends SettingState {}

class PasswordSuccessState extends SettingState {
  const PasswordSuccessState({required this.passwordSuccessResponse});

  final PasswordResetResponseModel passwordSuccessResponse;

  @override
  List<Object> get props => [passwordSuccessResponse];
}

class PasswordFailureState extends SettingState {
  const PasswordFailureState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}

class ProfilePicSelectionState extends SettingState {
  const ProfilePicSelectionState({required this.selectedFile});

  final File? selectedFile;

  @override
  List<Object> get props => [selectedFile!];
}

class ProfilePicLoadingState extends SettingState {}

class ProfilePicSuccessState extends SettingState {
  const ProfilePicSuccessState({required this.response});

  final ProfilePicUpdateResponse response;

  @override
  List<Object> get props => [response];
}

class ProfilePicFailureState extends SettingState {
  const ProfilePicFailureState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}

class UpdateCheckSuccessState extends SettingState {

  const UpdateCheckSuccessState({
    required this.data,
  });
  final UpdateResponse data;

  bool get hasUpdate => data.error == null;

  @override
  List<Object> get props => [data];
}

class UpdateCheckingState extends SettingState {

  const UpdateCheckingState();

  @override
  List<Object> get props => [];
}

class UpdateCheckFailed extends SettingState {

  const UpdateCheckFailed({required this.error});
  final String error;

  @override
  List<Object> get props => [error];
}
