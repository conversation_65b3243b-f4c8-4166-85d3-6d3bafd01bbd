part of 'setting_bloc.dart';

abstract class SettingState extends Equatable {
  const SettingState();

  @override
  List<Object> get props => [];
}

class SettingLoadingState extends SettingState {}


class SettingFailureState extends SettingState {
  const SettingFailureState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}

class PasswordInitialState extends SettingState {}

class PasswordLoadingState extends SettingState {}

class PasswordSuccessState extends SettingState {
  const PasswordSuccessState({required this.passwordSuccessResponse});

  final PasswordResetResponseModel passwordSuccessResponse;

  @override
  List<Object> get props => [passwordSuccessResponse];
}

class PasswordFailureState extends SettingState {
  const PasswordFailureState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}

class NameEditButtonTapState extends SettingState {
  const NameEditButtonTapState({this.isButtonTapped = false});

  final bool isButtonTapped;

  @override
  List<Object> get props => [isButtonTapped];
}

class NameEditInitState extends SettingState {}

class NameEditFailureState extends SettingState {
  const NameEditFailureState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}

class NameEditSuccessState extends SettingState {
  const NameEditSuccessState({required this.response});

  final NameUpdateResponse response;

  @override
  List<Object> get props => [response];
}

class ProfilePicSelectionState extends SettingState {
  const ProfilePicSelectionState({required this.selectedFile});

  final File? selectedFile;

  @override
  List<Object> get props => [selectedFile!];
}

class ProfilePicLoadingState extends SettingState {}

class ProfilePicSuccessState extends SettingState {
  const ProfilePicSuccessState({required this.response});

  final ProfilePicUpdateResponse response;

  @override
  List<Object> get props => [response];
}

class ProfilePicFailureState extends SettingState {
  const ProfilePicFailureState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}
