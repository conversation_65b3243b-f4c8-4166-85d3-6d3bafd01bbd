part of 'chat_with_sme_repository_imports.dart';

abstract class ChatWithSmeRepository {
  /// function for getting faq list
  Future<RepoResponse<FaqListModel>> getFaqList({
  required  String domainFilter ,
  });

  /// function for getting the domain list
  Future<RepoResponse<DomainThemeSubThemeModelResponse>> getDomainList();
  Future<RepoResponse<List<DomainModel>>> getDomainListFaq();
  /// function for creating chatThread
  Future<RepoResponse<ChatThreadCreateModel>> createChatThread({
    required String domain,
    required int domainId,
    required String theme,
    required String subTheme,
    required String subject,
  });

  /// function for getting chat thread list
  Future<RepoResponse<ChatThreadListModel>> getChatThreadList();

  /// function for getting message list of a chat thread
  Future<RepoResponse<MessageListModel>> getMessageList({
    required String chatThreadId,
  });

  /// function for sending message
  Future<RepoResponse<SendMessageModel>> sendMessage({
    required String chatThreadId,
    required Map<String, dynamic> messageData,
    File? attachment,
  });

  /// function for sending feedback
  Future<RepoResponse<FeedbackResponseModel>> sendFeedback({
    required String messageId,
    required Map<String, dynamic> messageData,
  });

}
