import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/src/common/widgets/appbar/flat_app_bar.dart';
import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';
import 'package:scad_mobile/src/common/widgets/no_data_placeholder.dart';
import 'package:scad_mobile/src/common/widgets/rounded_dropdown_widget.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/faq/faq_model.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/bloc/chat_with_sme_bloc.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/widgets/landing/faq_list_widget.dart';
import 'package:scad_mobile/src/features/domains/data/models/domain_model/domain_model.dart';
import 'package:scad_mobile/src/features/home/<USER>/widgets/home_page_list_toggler/home_page_list_toggler.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

@RoutePage()
class FaqPage extends StatefulWidget {
  const FaqPage({super.key});

  @override
  State<FaqPage> createState() => _FaqPageState();
}

class _FaqPageState extends State<FaqPage>
    with TickerProviderStateMixin {

  final ScrollController faqScrollController = ScrollController();
  ValueNotifier<int> faqTabIndex = ValueNotifier(0);
  List<DomainModel>? domainList;
  DomainModel? selectedDomain;
  DomainModel general =
      DomainModel(domainId: 'general', domainName: 'general', domainIcon: '');
  ValueNotifier<String> selectedDomainId = ValueNotifier('');
  List<FaqModel> faqList = [];

  @override
  void initState() {
    super.initState();
    context.read<ChatWithSmeBloc>().add(const FaqLoadDomainDataEvent());
  }

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return AppDrawer(
      child: Scaffold(
        body: Column(
          children: [
            FlatAppBar(
              title: LocaleKeys.faq.tr(),
              scrollController: faqScrollController,
              bottomPadding: 0,
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    ValueListenableBuilder(
                      valueListenable: faqTabIndex,
                      builder: (context, i, w) {
                        return Container(
                          decoration: BoxDecoration(
                            color: isLightMode
                                ? AppColors.blueShadeTabInset
                                : AppColors.blueShade32,
                            borderRadius: BorderRadius.circular(40),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Expanded(
                                child: HomePageListToggler(
                                  title: LocaleKeys.general.tr(),
                                  isSelected: faqTabIndex.value == 0,
                                  onTap: () {
                                    faqTabIndex.value = 0;
                                    _loadFAQ(general);
                                  },
                                ),
                              ),
                              Expanded(
                                child: HomePageListToggler(
                                  title: LocaleKeys.domains.tr(),
                                  isSelected: faqTabIndex.value == 1,
                                  onTap: () {
                                    faqTabIndex.value = 1;
                                    _loadFAQ(domainList?.first);
                                  },
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                    const SizedBox(height: 25),
                    Align(
                      alignment: HiveUtilsSettings.getAppLanguage() == 'en'
                          ? Alignment.centerLeft
                          : Alignment.centerRight,
                      child: SizedBox(
                        width: MediaQuery.sizeOf(context).width * .5,
                        child: BlocConsumer<ChatWithSmeBloc, ChatWithSmeState>(
                          listener: (context, state) {
                           if (state is FaqDomainDataState) {
                              domainList = state.domainList;
                              if (faqTabIndex.value == 1) {
                                _loadFAQ(state.domainList?.first);
                              } else {
                                _loadFAQ(general);
                              }
                            }
                          },
                          builder: (context, state) {
                            return ValueListenableBuilder(
                              valueListenable: selectedDomainId,
                              builder: (context, i, w) {
                                if (faqTabIndex.value == 0) {
                                  return const SizedBox();
                                }
                                return Column(
                                  children: [
                                    RoundedDropDownWidget<DomainModel>(
                                      boxShadow: true,
                                      showBorder: false,
                                      isSquare: true,
                                      title: LocaleKeys.selectDomain.tr(),
                                      items: domainList,
                                      value: selectedDomain,
                                      onChanged: (val) {
                                        _loadFAQ(val);
                                      },
                                    ),
                                    const SizedBox(height: 25),
                                  ],
                                );
                              },
                            );
                          },
                        ),
                      ),
                    ),
                    Expanded(
                      child:
                      BlocConsumer<ChatWithSmeBloc, ChatWithSmeState>(
                        listener: (context, state) {
                          if (state is FaqStateLoadDataState) {
                            faqList = state.faqList ?? [];
                          }
                        },
                        builder: (context, state) {
                          if (state is FaqStateLoadingState) {
                            return const Center(
                              child: CircularProgressIndicator(),
                            );
                          } else if (state
                          is ChatWithSmeFaqListErrorState) {
                            return NoDataPlaceholder(msg: state.errorText);
                          }
                          return faqList.isEmpty
                              ? const Center(
                            child: Padding(
                              padding: EdgeInsets.only(bottom: 150),
                              child: NoDataPlaceholder(),
                            ),
                          )
                              : SingleChildScrollView(
                            controller: faqScrollController,
                            padding:
                            const EdgeInsets.only(bottom: 150),
                            child: FaqListWidget(faqList: faqList),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _loadFAQ(DomainModel? domain, ) {
    selectedDomain = domain;
    selectedDomainId.value = domain?.domainId ?? '';
    context.read<ChatWithSmeBloc>().add(
      FaqStateLoadDataEvent(
        domainFilter:
        selectedDomain?.domainId ??
            '',
      ),
    );
  }

}
