import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/config/dependancy_injection/injection_container.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/chat_thread/chat_thread_create_model.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/chat_thread/chat_thread_list_model.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/chat_thread/chat_thread_model.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/domain_theme_sub_theme_model.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/faq/faq_list_model.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/faq/faq_model.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/message/feedback_response_model.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/message/message_list_model.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/message/message_model.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/message/send_message_model.dart';
import 'package:scad_mobile/src/features/chat_with_sme/domain/repositories/chat_with_sme_repository_imports.dart';
import 'package:scad_mobile/src/features/domains/data/models/domain_model/domain_model.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_keys.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_api_cache.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

part 'chat_with_sme_event.dart';
part 'chat_with_sme_state.dart';

class ChatWithSmeBloc extends Bloc<ChatWithSmeEvent, ChatWithSmeState> {
  ChatWithSmeBloc(this.chatWithSmeRepository)
      : super(const ChatWithSmeLoadingState()) {
    on<ChatWithSmeEvent>((event, emit) {});
    on<ChatWithSmeLoadDataEvent>(_onLoadData);
    on<FaqStateLoadDataEvent>(_onLoadDataFaq);
    on<ChatWithSmeToggleExpansionEvent>(_onToggleExpansion);
    on<ChatWithSmeLoadDomainDataEvent>(_onGetDomainData);
    on<ChatWithSmeDomainDropdownEvent>(_onDomainDropdown);
    on<FaqLoadDomainDataEvent>(_onGetDomainDataFaq);
    on<ChatWithSmeThemeDropdownEvent>(_onThemeDropdown);
    on<ChatWithSmeSubThemeDropdownEvent>(_onSubThemeDropdown);
    on<Max60CharacterEvent>(_onMax60Char);
    on<ChatWithSmeCreateChatThreadEvent>(_onChatThreadCreate);
    on<ChatWithSmeLoadInboxEvent>(_onInboxLoad);
    on<ChatWithSmeSendMessageEvent>(_onMessageSent);
    on<ChatWithSmeSendFeedbackEvent>(_onFeedbackSent);
  }

  ///constructors
  final ChatWithSmeRepository chatWithSmeRepository;

  /// first load to get the chatThread list
  Future<void> _onLoadData(
    ChatWithSmeLoadDataEvent event,
    Emitter<ChatWithSmeState> emit,
  ) async {
    emit(const ChatWithSmeLoadingState());

    try {
      final RepoResponse<ChatThreadListModel> chatThreadResponse =
          await servicelocator<ChatWithSmeRepository>().getChatThreadList();

      if (chatThreadResponse.isSuccess) {
        emit(
          ChatWithSmeLoadDataState(
            chatThreadList: chatThreadResponse.response?.chatThreadList ?? [],
          ),
        );
      } else if (!chatThreadResponse.isSuccess) {
        emit(
          const ChatWithSmeChatThreadListErrorState(
            errorText: 'Question Threads list error',
          ),
        );
      } else {
        emit(ChatWithSmeErrorState(
            errorText: LocaleKeys.somethingWentWrong.tr()));
      }
    } catch (e,s) {
      Completer<dynamic>().completeError(e,s);
      emit(ChatWithSmeErrorState(errorText: LocaleKeys.somethingWentWrong.tr()));
    }
  }

  /// first load to get the  faqList
  Future<void> _onLoadDataFaq(
    FaqStateLoadDataEvent event,
    Emitter<ChatWithSmeState> emit,
  ) async {
    emit(const FaqStateLoadingState());

    try {
      final RepoResponse<FaqListModel> faqResponse =
          await servicelocator<ChatWithSmeRepository>()
              .getFaqList(domainFilter: event.domainFilter);

      if (faqResponse.isSuccess) {
        emit(
          FaqStateLoadDataState(
            faqList: faqResponse.response?.faqList ?? [],
          ),
        );
      } else {
        emit(ChatWithSmeFaqListErrorState(
            errorText: LocaleKeys.somethingWentWrong.tr()));
      }
    } catch (e,s) {
      Completer<dynamic>().completeError(e,s);
      emit(ChatWithSmeFaqListErrorState(errorText: LocaleKeys.somethingWentWrong.tr()));
    }
  }

  /// function for opening and closing the faq expansion tile
  Future<void> _onToggleExpansion(
    ChatWithSmeToggleExpansionEvent event,
    Emitter<ChatWithSmeState> emit,
  ) async {
    final List<FaqModel> updatedList = event.updatedList;

    updatedList[event.index].isOpened = !updatedList[event.index].isOpened;

    emit(const ChatWithSmeLoadingState());

    emit(
      ChatWithSmeFaqToggleState(
        updatedList: updatedList,
      ),
    );
  }

  /// function for fetching the domain list
  Future<void> _onGetDomainData(
    ChatWithSmeLoadDomainDataEvent event,
    Emitter<ChatWithSmeState> emit,
  ) async {
    emit(const ChatWithSmeDomainLoadingState());
    try {
      final RepoResponse<DomainThemeSubThemeModelResponse> response =
          await servicelocator<ChatWithSmeRepository>().getDomainList();
      if (response.isSuccess) {
        final List<Domain> domainList = [];

        for (final DomainThemeSubThemeModel element
            in response.response?.dataList ?? []) {
          if (element.key == 'official_statistics') {
            domainList.addAll(element.domains ?? []);
          }
        }

        emit(
          ChatWithSmeDomainDataState(
            domainList: domainList,
          ),
        );
      } else {
        emit(
          ChatWithSmeDomainErrorState(errorText: response.errorMessage),
        );
      }
    } catch (e,s) {
      Completer<dynamic>().completeError(e,s);
      emit(
        ChatWithSmeDomainErrorState(errorText: LocaleKeys.somethingWentWrong.tr()),
      );
    }
  }

  /// function for fetching the domain list
  Future<void> _onGetDomainDataFaq(
    FaqLoadDomainDataEvent event,
    Emitter<ChatWithSmeState> emit,
  ) async {
    emit(const FaqDomainLoadingState());
    try {
      final dynamic res = HiveUtilsApiCache.get(HiveKeys.keyDomainList);

      if (res != null) {
        emit(
          FaqDomainDataState(
            domainList: (res as List<dynamic>)
                .map((e) => DomainModel.fromJson(e as Map<String, dynamic>))
                .toList(),
          ),
        );
        return;
      } else {
        final RepoResponse<List<DomainModel>> response =
            await servicelocator<ChatWithSmeRepository>().getDomainListFaq();
        if (response.isSuccess) {
          emit(
            FaqDomainDataState(
              domainList: response.response,
            ),
          );
        } else {
          emit(
            FaqDomainErrorState(errorText: response.errorMessage),
          );
        }
      }
    } catch (e,s) {
      Completer<dynamic>().completeError(e,s);
      emit(
        FaqDomainErrorState(errorText: LocaleKeys.somethingWentWrong.tr()),
      );
    }
  }

  /// function for using the domain dropdown in creating chat thread
  Future<void> _onDomainDropdown(
    ChatWithSmeDomainDropdownEvent event,
    Emitter<ChatWithSmeState> emit,
  ) async {
    final List<Domain> domainList = event.domainList;

    final List<Subdomain> subDomainList = [];

    for (final element in domainList) {
      if (element == event.selectedValue) {
        subDomainList.addAll(element.subdomains ?? []);
      }
    }

    emit(
      ChatWithSmeDomainDropdownState(
        selectedValue: event.selectedValue,
        subDomainList: subDomainList,
      ),
    );
  }

  /// function for using the theme dropdown in creating chat thread
  Future<void> _onThemeDropdown(
    ChatWithSmeThemeDropdownEvent event,
    Emitter<ChatWithSmeState> emit,
  ) async {
    final List<Subdomain> subDomainList = event.subDomainList;

    final List<Subtheme> subThemeList = [];

    for (final element in subDomainList) {
      if (element == event.selectedValue) {
        subThemeList.addAll(element.subthemes ?? []);
      }
    }

    emit(
      ChatWithSmeThemeDropdownState(
        selectedValue: event.selectedValue,
        subThemeList: subThemeList,
      ),
    );
  }

  /// function for using the sub theme dropdown in creating chat thread
  Future<void> _onSubThemeDropdown(
    ChatWithSmeSubThemeDropdownEvent event,
    Emitter<ChatWithSmeState> emit,
  ) async {
    emit(ChatWithSmeSubThemeDropdownState(selectedValue: event.selectedValue));
  }

  /// function for checking max 60 char while creating chat thread
  Future<void> _onMax60Char(
    Max60CharacterEvent event,
    Emitter<ChatWithSmeState> emit,
  ) async {
    emit(
      Max60CharacterState(
        count: event.value?.length.toString() ?? '0',
        actualText: event.value,
      ),
    );
  }

  /// function for creating the chat thread
  Future<void> _onChatThreadCreate(
    ChatWithSmeCreateChatThreadEvent event,
    Emitter<ChatWithSmeState> emit,
  ) async {
    try {
      final RepoResponse<ChatThreadCreateModel> response =
          await servicelocator<ChatWithSmeRepository>().createChatThread(
        domain: event.domain,
        domainId: event.domainId,
        theme: event.theme,
        subTheme: event.subTheme,
        subject: event.subject,
      );

      if (response.isSuccess) {
        emit(
          ChatWithSmeCreateChatThreadSuccessState(
            chatThreadId: response.response?.uuid,
            domain: event.domain,
            domainId: event.domainId,
            theme: event.theme,
            subTheme: event.subTheme,
            subject: event.subject,
            ticketId: response.response?.ticketId,
            chatThreadClosed: response.response?.chatThreadClosed,
            chatDisabled: response.response?.chatDisabled,
          ),
        );
      } else {
        emit(
          const ChatWithSmeCreateChatThreadErrorState(
            errorText: 'chat creation failed!',
          ),
        );
      }
    } catch (e,s) {
      Completer<dynamic>().completeError(e,s);
      emit(ChatWithSmeCreateChatThreadErrorState(errorText: LocaleKeys.somethingWentWrong.tr()));
    }
  }

  /// for loading inbox data
  Future<void> _onInboxLoad(
    ChatWithSmeLoadInboxEvent event,
    Emitter<ChatWithSmeState> emit,
  ) async {
    emit(const ChatWithSmeInboxLoadingState());

    try {
      final RepoResponse<MessageListModel> response =
          await servicelocator<ChatWithSmeRepository>()
              .getMessageList(chatThreadId: event.chatThreadId);

      if (response.isSuccess) {
        emit(
          ChatWithSmeInboxDataState(
            messageList: response.response?.messageList ?? [],
            chatThreadId: event.chatThreadId,
          ),
        );
      } else {
        emit(
          ChatWithSmeChatInboxErrorState(
            errorText: LocaleKeys.somethingWentWrong.tr(),
          ),
        );
      }
    } catch (e,s) {
      Completer<dynamic>().completeError(e,s);
      emit(ChatWithSmeChatInboxErrorState(errorText: LocaleKeys.somethingWentWrong.tr()));
    }
  }

  /// function for sending the message
  Future<void> _onMessageSent(
    ChatWithSmeSendMessageEvent event,
    Emitter<ChatWithSmeState> emit,
  ) async {
    emit(const ChatWithSmeSendLoadingMessageState(isLoading: true));
    try {
      final RepoResponse<SendMessageModel> response =
          await servicelocator<ChatWithSmeRepository>().sendMessage(
        chatThreadId: event.chatThreadId,
        messageData: {
          'data': json.encode({'message': event.message}),
        },
        attachment: event.attachment,
      );

      if (response.isSuccess) {
        final RepoResponse<MessageListModel> chatResponse =
            await servicelocator<ChatWithSmeRepository>()
                .getMessageList(chatThreadId: event.chatThreadId);
        {
          if (chatResponse.isSuccess) {
            emit(
              ChatWithSmeSendMessageState(
                messageList:
                    chatResponse.response?.messageList?.reversed.toList() ?? [],
                uuid: event.chatThreadId,
                chatDisabled: response.response?.chatDisabled ?? false,
                chatThreadClosed: response.response?.chatThreadClosed ?? false,
              ),
            );
            emit(const ChatWithSmeSendLoadingMessageState(isLoading: false));
          }
        }
      } else {
        emit(const ChatWithSmeSendLoadingMessageState(isLoading: false));

        emit(
          ChatWithSmeChatInboxErrorState(
            errorText: LocaleKeys.somethingWentWrong.tr(),
          ),
        );
      }
    } catch (e,s) {
      Completer<dynamic>().completeError(e,s);
      emit(const ChatWithSmeSendLoadingMessageState(isLoading: false));

      emit(ChatWithSmeChatInboxErrorState(errorText: LocaleKeys.somethingWentWrong.tr()));
    }
  }

  /// function for sending the feedback
  Future<void> _onFeedbackSent(
    ChatWithSmeSendFeedbackEvent event,
    Emitter<ChatWithSmeState> emit,
  ) async {
    try {
      final RepoResponse<FeedbackResponseModel> response =
          await servicelocator<ChatWithSmeRepository>().sendFeedback(
        messageId: event.messageId,
        messageData: event.comment != null
            ? {
                'satisfied': event.isSatisfied,
                'comments': event.comment,
              }
            : {
                'satisfied': event.isSatisfied,
              },
      );

      if (response.isSuccess) {
        emit(
          ChatWithSmeSendFeedbackState(
            isSuccess: response.isSuccess,
            text: response.response?.message ?? '',
            chatDisabled: response.response?.chatDisabled ?? false,
            chatThreadClosed: response.response?.chatThreadClosed ?? false,
          ),
        );
      } else {
        emit(
          ChatWithSmeSendFeedbackState(
            isSuccess: false,
            text: LocaleKeys.somethingWentWrong.tr(),
            chatDisabled: false,
            chatThreadClosed: false,
          ),
        );
      }
    } catch (e,s) {
      Completer<dynamic>().completeError(e,s);
      emit(
        ChatWithSmeSendFeedbackState(
          isSuccess: false,
          text: LocaleKeys.somethingWentWrong.tr(),
          chatDisabled: false,
          chatThreadClosed: false,
        ),
      );
    }
  }
}
