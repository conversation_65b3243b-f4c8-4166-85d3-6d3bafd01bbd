import 'package:json_annotation/json_annotation.dart';

part 'recipient_model.g.dart';

@JsonSerializable()
class Recipient {
  Recipient({
    this.name,
    this.email,
    this.uuid,
  });

  factory Recipient.fromJson(Map<String, dynamic> json) =>
      _$RecipientFromJson(json);

  @J<PERSON><PERSON>ey(name: 'name')
  String? name;
  @Json<PERSON>ey(name: 'email')
  String? email;
  @JsonKey(name: 'uuid')
  String? uuid;

  Map<String, dynamic> toJson() => _$RecipientToJson(this);
}
