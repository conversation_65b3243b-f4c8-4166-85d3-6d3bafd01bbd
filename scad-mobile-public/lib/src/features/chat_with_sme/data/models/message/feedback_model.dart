import 'package:json_annotation/json_annotation.dart';

part 'feedback_model.g.dart';

@JsonSerializable()
class FeedbackModel {
  FeedbackModel({
    this.satisfied,
    this.comments,
    this.createdTime,
    this.updatedTime,
  });

  factory FeedbackModel.fromJson(Map<String, dynamic> json) =>
      _$FeedbackModelFromJson(json);

  @Json<PERSON>ey(name: 'satisfied')
  bool? satisfied;
  @Json<PERSON>ey(name: 'comments')
  String? comments;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_time')
  String? createdTime;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_time')
  String? updatedTime;

  Map<String, dynamic> toJson() => _$FeedbackModelToJson(this);
}
