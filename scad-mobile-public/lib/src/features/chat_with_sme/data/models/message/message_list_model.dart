import 'package:json_annotation/json_annotation.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/message/message_model.dart';

part 'message_list_model.g.dart';

@JsonSerializable()
class MessageListModel {
  MessageListModel({
    this.messageList,
  });

  factory MessageListModel.fromJson(Map<String, dynamic> json) =>
      _$MessageListModelFromJson(json);

  @JsonKey(name: 'data')
  List<MessageModel>? messageList;

  Map<String, dynamic> toJson() => _$MessageListModelToJson(this);
}
