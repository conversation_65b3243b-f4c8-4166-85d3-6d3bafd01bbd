class DomainThemeSubThemeModelResponse {
  DomainThemeSubThemeModelResponse({this.dataList});

  factory DomainThemeSubThemeModelResponse.fromJson(Map<String, dynamic> json) {
    return DomainThemeSubThemeModelResponse(
      dataList: (json['data'] as List<dynamic>?)
          ?.map(
            (e) => DomainThemeSubThemeModel.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
    );
  }

  List<DomainThemeSubThemeModel>? dataList;
}

class DomainThemeSubThemeModel {
  DomainThemeSubThemeModel({
    this.id,
    this.name,
    this.key,
    this.lightIcon,
    this.darkIcon,
    this.nodeCount,
    this.showTree,
    this.domains,
  });

  factory DomainThemeSubThemeModel.fromJson(Map<String, dynamic> json) =>
      DomainThemeSubThemeModel(
        id: json['id'] as String?,
        name: json['name'] as String?,
        key: json['key'] as String?,
        lightIcon: json['light_icon'] as String?,
        darkIcon: json['dark_icon'] as String?,
        nodeCount: json['nodeCount'] as int?,
        showTree: json['showTree'] as bool?,
        domains: (json['domains'] as List<dynamic>?)
            ?.map((e) => Domain.fromJson(e as Map<String, dynamic>))
            .toList(),
      );

  String? id;
  String? name;
  String? key;
  String? lightIcon;
  String? darkIcon;
  int? nodeCount;
  bool? showTree;
  List<Domain>? domains;
}

class Domain {
  Domain({
    this.id,
    this.name,
    this.lightIcon,
    this.darkIcon,
    this.route,
    this.showTree,
    this.nodeCount,
    this.subdomains,
    this.nodes,
  });

  factory Domain.fromJson(Map<String, dynamic> json) => Domain(
        id: json['id'] as String?,
        name: json['name'] as String?,
        lightIcon: json['light_icon'] as String?,
        darkIcon: json['dark_icon'] as String?,
        route: json['route'] as String?,
        showTree: json['showTree'] as bool?,
        nodeCount: json['nodeCount'] as int?,
        subdomains: (json['subdomains'] as List<dynamic>?)
            ?.map((e) => Subdomain.fromJson(e as Map<String, dynamic>))
            .toList(),
        nodes: (json['nodes'] as List<dynamic>?)
            ?.map((e) => DomainNode.fromJson(e as Map<String, dynamic>))
            .toList(),
      );

  String? id;
  String? name;
  String? lightIcon;
  String? darkIcon;
  String? route;
  bool? showTree;
  int? nodeCount;
  List<Subdomain>? subdomains;
  List<DomainNode>? nodes;
}

class DomainNode {
  DomainNode({
    this.id,
    this.title,
    this.contentType,
    this.category,
    this.appType,
    this.contentClassification,
    this.contentClassificationKey,
  });

  factory DomainNode.fromJson(Map<String, dynamic> json) => DomainNode(
        id: json['id'] as String?,
        title: json['title'] as String?,
        contentType: json['content_type'] as String?,
        category: json['category'] == null
            ? null
            : Category.fromJson(json['category'] as Map<String, dynamic>),
        appType: json['app_type'] as String?,
        contentClassification: json['content_classification'] as String?,
        contentClassificationKey: json['content_classification_key'] as String?,
      );

  String? id;
  String? title;
  String? contentType;
  Category? category;
  String? appType;
  String? contentClassification;
  String? contentClassificationKey;

  Map<String, dynamic> toJson() => {
        'id': id,
        'title': title,
        'content_type': contentType,
        'category': category?.toJson(),
        'app_type': appType,
        'content_classification': contentClassification,
        'content_classification_key': contentClassificationKey,
      };
}

class Category {
  Category({
    this.id,
    this.name,
    this.darkIcon,
    this.lightIcon,
  });

  factory Category.fromJson(Map<String, dynamic> json) => Category(
        id: json['id'] as String?,
        name: json['name'] as String?,
        darkIcon: json['dark_icon'] as String?,
        lightIcon: json['light_icon'] as String?,
      );

  String? id;
  String? name;
  String? darkIcon;
  String? lightIcon;

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'dark_icon': darkIcon,
        'light_icon': lightIcon,
      };
}

class Subdomain {
  Subdomain({
    this.id,
    this.name,
    this.route,
    this.showTree,
    this.subthemes,
    this.screener,
    this.count,
  });

  factory Subdomain.fromJson(Map<String, dynamic> json) => Subdomain(
        id: json['id'] as String?,
        name: json['name'] as String?,
        route: json['route'] as String?,
        showTree: json['showTree'] as bool?,
        subthemes: (json['subthemes'] as List<dynamic>?)
            ?.map((e) => Subtheme.fromJson(e as Map<String, dynamic>))
            .toList(),
        screener: json['screener'] as bool?,
        count: json['count'] as int?,
      );

  String? id;
  String? name;
  String? route;
  bool? showTree;
  List<Subtheme>? subthemes;
  bool? screener;
  int? count;
}

class Subtheme {
  Subtheme({
    this.id,
    this.name,
    this.showTree,
    this.route,
    this.products,
    this.screener,
  });

  factory Subtheme.fromJson(Map<String, dynamic> json) => Subtheme(
        id: json['id'] as String?,
        name: json['name'] as String?,
        showTree: json['showTree'] as bool?,
        route: json['route'] as String?,
        products: (json['products'] as List<dynamic>?)
            ?.map((e) => Product.fromJson(e as Map<String, dynamic>))
            .toList(),
        screener: json['screener'] as bool?,
      );

  String? id;
  String? name;
  bool? showTree;
  String? route;
  List<Product>? products;
  bool? screener;
}

class Product {
  Product({
    this.name,
    this.showTree,
    this.nodes,
  });

  factory Product.fromJson(Map<String, dynamic> json) => Product(
        name: json['name'] as String?,
        showTree: json['showTree'] as bool?,
        nodes: (json['nodes'] as List<dynamic>?)
            ?.map((e) => ProductNode.fromJson(e as Map<String, dynamic>))
            .toList(),
      );

  String? name;
  bool? showTree;
  List<ProductNode>? nodes;
}

class ProductNode {
  ProductNode({
    this.id,
    this.title,
    this.contentType,
    this.appType,
    this.domain,
    this.subdomain,
    this.subtheme,
    this.product,
    this.sortOrder,
    this.screenerView,
  });

  factory ProductNode.fromJson(Map<String, dynamic> json) => ProductNode(
        id: json['id'] == null ? null : json['id'].toString(),
        title: json['title'] as String?,
        contentType: json['content_type'] as String?,
        appType: json['app_type'] as String?,
        domain: json['domain'] as String?,
        subdomain: json['subdomain'] as String?,
        subtheme: json['subtheme'] as String?,
        product: json['product'] as String?,
        sortOrder: json['sort_order'] as int?,
        screenerView: json['screenerView'] as String?,
      );

  String? id;
  String? title;
  String? contentType;
  String? appType;
  String? domain;
  String? subdomain;
  String? subtheme;
  String? product;
  int? sortOrder;
  String? screenerView;
}
