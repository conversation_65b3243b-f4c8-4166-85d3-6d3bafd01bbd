import 'package:json_annotation/json_annotation.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/message/message_model.dart';

part 'chat_thread_model.g.dart';

@JsonSerializable()
class ChatThreadModel {
  ChatThreadModel({
    this.domain,
    this.domainId,
    this.theme,
    this.subTheme,
    this.subject,
    this.latestMessage,
    this.uuid,
    this.indicatorNodeId,
    this.indicatorAppType,
    this.indicatorContentType,
    this.indicatorKey,
    this.indicatorName,
    this.createdTime,
    this.updatedTime,
    this.ticketId,
  });

  factory ChatThreadModel.fromJson(Map<String, dynamic> json) =>
      _$ChatThreadModelFromJson(json);

  @Json<PERSON>ey(name: 'domain')
  String? domain;
  @JsonKey(name: 'domain_id')
  String? domainId;
  @JsonKey(name: 'theme')
  String? theme;
  @Json<PERSON>ey(name: 'sub_theme')
  String? subTheme;
  @Json<PERSON>ey(name: 'subject')
  String? subject;
  @Json<PERSON>ey(name: 'latest_message')
  MessageModel? latestMessage;
  @J<PERSON><PERSON><PERSON>(name: 'uuid')
  String? uuid;
  @Json<PERSON>ey(name: 'indicator_node_id')
  String? indicatorNodeId;
  @JsonKey(name: 'indicator_app_type')
  String? indicatorAppType;
  @JsonKey(name: 'indicator_content_type')
  String? indicatorContentType;
  @JsonKey(name: 'indicator_key')
  String? indicatorKey;
  @JsonKey(name: 'indicator_name')
  String? indicatorName;
  @JsonKey(name: 'created_time')
  String? createdTime;
  @JsonKey(name: 'updated_time')
  String? updatedTime;
  @JsonKey(name: 'ticket_id')
  String? ticketId;
  @JsonKey(name: 'chat_disabled')
  bool? chatDisabled;
  @JsonKey(name: 'chat_thread_closed')
  bool? chatThreadClosed;

  Map<String, dynamic> toJson() => _$ChatThreadModelToJson(this);
}
