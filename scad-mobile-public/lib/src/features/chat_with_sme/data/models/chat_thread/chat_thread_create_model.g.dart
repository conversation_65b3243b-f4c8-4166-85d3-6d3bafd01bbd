// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_thread_create_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ChatThreadCreateModel _$ChatThreadCreateModelFromJson(
        Map<String, dynamic> json) =>
    ChatThreadCreateModel(
      status: json['status'] as String?,
      message: json['message'] as String?,
      uuid: json['uuid'] as String?,
      createdTime: json['created_time'] as String?,
      ticketId: json['ticket_id'] as String?,
      chatDisabled: json['chat_disabled'] as bool?,
      chatThreadClosed: json['chat_thread_closed'] as bool?,
    );

Map<String, dynamic> _$ChatThreadCreateModelToJson(
        ChatThreadCreateModel instance) =>
    <String, dynamic>{
      'status': instance.status,
      'message': instance.message,
      'uuid': instance.uuid,
      'created_time': instance.createdTime,
      'ticket_id': instance.ticketId,
      'chat_disabled': instance.chatDisabled,
      'chat_thread_closed': instance.chatThreadClosed,
    };
