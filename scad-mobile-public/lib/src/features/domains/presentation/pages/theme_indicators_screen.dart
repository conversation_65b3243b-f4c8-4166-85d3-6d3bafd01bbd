import 'package:auto_route/annotations.dart';
import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:lottie/lottie.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/widgets/appbar/flat_app_bar.dart';
import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/presentation/pages/indicator_card_v2.dart';
import 'package:scad_mobile/src/common/widgets/multi_select/models/value_item.dart';
import 'package:scad_mobile/src/common/widgets/multi_select/multiselect_dropdown.dart';
import 'package:scad_mobile/src/common/widgets/no_data_placeholder.dart';
import 'package:scad_mobile/src/features/domains/data/models/domain_classification_model/domain_classification_model.dart';
import 'package:scad_mobile/src/features/domains/data/models/domain_model/domain_model.dart';
import 'package:scad_mobile/src/features/domains/data/models/experimental_filters_response_item.dart';
import 'package:scad_mobile/src/features/domains/data/models/theme_indicator_list_response.dart';
import 'package:scad_mobile/src/features/domains/data/models/theme_subtheme_response.dart';
import 'package:scad_mobile/src/features/domains/presentation/bloc/domains_bloc.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/animation_asset.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

@RoutePage()
class ThemeIndicatorsScreen extends StatefulWidget {
  const ThemeIndicatorsScreen({
    required this.title,
    required this.domain,
    required this.classification,
    required this.subTheme,
    required this.subDomain,
    super.key,
    this.screenerConfiguration,
    this.isFromMainScreen = false,
  });

  final String title;
  final DomainModel domain;
  final DomainClassificationModel classification;
  final Subthemes subTheme;
  final ThemeSubThemeResponse subDomain;
  final ScreenerConfiguration? screenerConfiguration;
  final bool isFromMainScreen;

  @override
  State<ThemeIndicatorsScreen> createState() => _ThemeIndicatorsScreenState();
}

class _ThemeIndicatorsScreenState extends State<ThemeIndicatorsScreen> {
  ScrollController scrollController = ScrollController();
  int pageNo = 0;
  int? count;

  List<ThemeIndicatorListItem> indicatorList = [];
  List<ExperimentalFiltersResponseItem> experimentalFilters = [];
  ValueNotifier<List<Items?>> selectedValueList = ValueNotifier([]);
  Map<String, List<Items>> experimentalFilterMap = {};

  void _pageReset() {
    indicatorList = [];
    count = null;
    pageNo = 1;
  }

  @override
  void initState() {
    super.initState();

    pageNo = 0;
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      if (widget.classification.key == 'experimental_statistics') {
        context.read<DomainsBloc>().add(
              GetThemeExperimentalFiltersEvent(
                screenerIndicator:
                    widget.screenerConfiguration?.screenerIndicator ?? '',
              ),
            );
      } else {
        context.read<DomainsBloc>().add(
              GetThemeIndicatorsEvent(
                subDomainId: widget.subDomain.id!,
                subThemeId: widget.subTheme.id!,
                pageNo: pageNo,
                domainId: widget.domain.domainId!,
                classificationId: widget.classification.id!,
              ),
            );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return !widget.isFromMainScreen
        ? buildIndicatorList()
        : AppDrawer(child: buildIndicatorList());
  }

  Scaffold buildIndicatorList() {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Scaffold(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          FlatAppBar(
            title: widget.title,
            scrollController: scrollController,
            mainScreen: !widget.isFromMainScreen,
          ),
          Expanded(
            child: BlocConsumer<DomainsBloc, DomainsState>(
              listener: (context, state) {
                if (state is ThemeIndicatorsListState) {
                  indicatorList =
                      state.themeIndicatorListResponse.results ?? [];
                }
                if (state is ExperimentalFilterListState) {
                  experimentalFilters = state.experimentalFilters;

                  selectedValueList.value = List.generate(
                    experimentalFilters.length,
                    (index) => null,
                  );

                  for (int i = 0; i < experimentalFilters.length; i++) {
                    final Items? defaultVal = experimentalFilters[i].defaultVal;
                    selectedValueList.value[i] = defaultVal;
                  }

                  _initExperimentalFilterMap();
                  _pageReset();
                  _loadExperimentalIndicators();
                } else if (state
                    is ThemeExperimentalIndicatorListResponseState) {
                  count = state.response.totalCount ?? 0;
                  final List<ThemeIndicatorListItem> list =
                      (state.response.data ?? [])
                          .map(
                            (e) => ThemeIndicatorListItem(
                              id: e.indicatorId,
                              title: e.title,
                              contentType: 'innovative-insights',
                            ),
                          )
                          .toList()
                        ..removeWhere(
                          (element) => indicatorList
                              .any((element1) => element.id == element1.id),
                        );

                  indicatorList = [...indicatorList, ...list];
                }
              },
              builder: (context, state) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // if (widget.classification.key == 'innovative-insights') // if experimental
                    if (experimentalFilters.isNotEmpty)
                      Row(
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(left: 12),
                            child: InkWell(
                              borderRadius: BorderRadius.circular(8),
                              onTap: () {
                                showModalBottomSheet<void>(
                                  context: context,
                                  isScrollControlled: true,
                                  useRootNavigator: true,
                                  builder: buildFiltersBottomSheet,
                                );
                              },
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 8,
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    SvgPicture.asset(
                                      AppImages.icFilters,
                                      colorFilter: ColorFilter.mode(
                                        AppColors.blueLight,
                                        BlendMode.srcIn,
                                      ),
                                    ),
                                    const SizedBox(width: 5),
                                    Text(
                                      LocaleKeys.filters.tr(),
                                      style: AppTextStyles.s14w5cBlack.copyWith(
                                        color: isLightMode
                                            ? AppColors.black
                                            : AppColors.white,
                                      ),
                                      textScaler: TextScaler.linear(
                                        textScaleFactor.value,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),

                    Expanded(
                      child: state is ThemeIndicatorsLoadingState
                          ? const Center(child: CircularProgressIndicator())
                          : indicatorList.isEmpty
                              ? const Center(
                                  child: NoDataPlaceholder(),
                                )
                              : ListView.separated(
                                  controller: scrollController,
                                  shrinkWrap: true,
                                  padding: const EdgeInsets.fromLTRB(
                                    20,
                                    15,
                                    20,
                                    150,
                                  ),
                                  itemCount: indicatorList.length,
                                  separatorBuilder: (context, index) =>
                                      const SizedBox(
                                    height: 25,
                                  ),
                                  itemBuilder: (context, index) {
                                    final ThemeIndicatorListItem item =
                                        indicatorList[index];

                                    return IndicatorCardV2(
                                      key: Key(
                                        'theme.indicators.IndicatorCardV2-${item.id}',
                                      ),
                                      id: item.id!,
                                      contentType: item.contentType!,
                                    );
                                  },
                                ),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget buildFiltersBottomSheet(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;

    final Map<String, List<Items>> tempFilterMap = {};
    for (int i = 0; i < experimentalFilterMap.entries.length; i++) {
      tempFilterMap[experimentalFilterMap.entries.elementAt(i).key] = [
        ...experimentalFilterMap.entries.elementAt(i).value,
      ];
    }

    return Container(
      decoration: BoxDecoration(
        color: isLightMode ? AppColors.white : AppColors.blueShade32,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.fromLTRB(20, 10, 20, 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Center(
              child: Container(
                height: 5,
                width: 50,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(50),
                  color:isLightMode ? AppColors.greyShade5 : AppColors.blueShade36,
                ),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              LocaleKeys.filters.tr(),
              style: TextStyle(
                color: isLightMode ? AppColors.blackShade1 : AppColors.white,
                fontSize: 16,
              ),
              textScaler: TextScaler.linear(textScaleFactor.value),
            ),
            const SizedBox(height: 16),
            Flexible(
              child: GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  mainAxisExtent: 80 * textScaleFactor.value,
                  crossAxisCount: 2,
                  crossAxisSpacing: 20,
                  mainAxisSpacing: 8,
                ),
                itemCount: experimentalFilters.length,
                itemBuilder: (context, index) {
                  return MultiSelectDropDown<Items>(
                    label: experimentalFilters[index].name!,
                    minSelection: 1,
                    enableSelectAllOption: true,
                    compareFn: (v1, v2) {
                      return v1.value == v2.value;
                    },
                    selectedOptions:
                        tempFilterMap[experimentalFilters[index].key!]!
                            .map(
                                (e) => ValueItem(label: e.name ?? '', value: e),)
                            .toList(),
                    updatedSelection: (List<ValueItem<Items>> selectedOptions) {
                      tempFilterMap[experimentalFilters[index].key!] =
                          selectedOptions.map((e) => e.value!).toList();
                    },
                    options: experimentalFilters[index]
                        .items!
                        .map(
                          (Items? item) =>
                              ValueItem(label: item?.name ?? '', value: item),
                        )
                        .toList(),
                  );
                },
              ),
            ),
            const SizedBox(height: 16),
            TextButton(
              style: TextButton.styleFrom(
                backgroundColor:
                    isLightMode ? AppColors.blueLight.withOpacity(0.1) : null,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                  side: isLightMode
                      ? BorderSide.none
                      : BorderSide(
                          color: AppColors.white,
                        ),
                ),
              ),
              onPressed: () {
                selectedValueList.value =
                    experimentalFilters.map((e) => e.defaultVal!).toList();

                Navigator.pop(context);

                _initExperimentalFilterMap();
                _pageReset();
                _loadExperimentalIndicators();
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    LocaleKeys.clear.tr(),
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color:
                          isLightMode ? AppColors.blueLight : AppColors.white,
                    ),
                    textScaler: TextScaler.linear(textScaleFactor.value),
                  ),
                  const SizedBox(width: 6),
                  // SvgPicture.asset(
                  //   AppImages.icSyncOutline,
                  //   height: 20,
                  // ),
                  if (isLightMode)
                    Lottie.asset(
                      AnimationAsset.animationSync,
                      width: 20,
                      height: 20,
                      fit: BoxFit.cover,
                    )
                  else
                    Lottie.asset(
                      AnimationAssetDark.animationSync,
                      width: 20,
                      height: 20,
                      fit: BoxFit.cover,
                    ),
                ],
              ),
            ),
            const SizedBox(height: 14),
            TextButton(
              style: TextButton.styleFrom(
                backgroundColor:
                    isLightMode ? AppColors.blueLight : AppColors.blueLightOld,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              onPressed: () {
                experimentalFilterMap = tempFilterMap;
                Navigator.pop(context);
                _pageReset();
                _loadExperimentalIndicators();
              },
              child: Text(
                LocaleKeys.apply.tr(),
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: AppColors.white,
                ),
                textScaler: TextScaler.linear(textScaleFactor.value),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _initExperimentalFilterMap() {
    for (int i = 0; i < experimentalFilters.length; i++) {
      experimentalFilterMap['${experimentalFilters[i].key}'] = [
        experimentalFilters[i].defaultVal!,
      ];
    }
  }

  void _loadExperimentalIndicators() {
    final Map<String, List<String>> filter = {};
    for (int i = 0; i < experimentalFilterMap.entries.length; i++) {
      filter[experimentalFilterMap.entries.elementAt(i).key] =
          experimentalFilterMap.entries
              .elementAt(i)
              .value
              .map((e) => e.value!)
              .toList();
    }

    final Map<String, dynamic> body = {
      'viewName': widget.screenerConfiguration?.screenerView,
      'filters': filter,
      'sortBy': {'alphabetical': 'asc'},
    };
    context.read<DomainsBloc>().add(
          GetThemeExperimentalIndicatorListEvent(
            pageNo: pageNo,
            filters: body,
          ),
        );
  }
}
