import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/common/widgets/appbar/flat_app_bar.dart';
import 'package:scad_mobile/src/common/widgets/domain_tile.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/src/showcase_widget.dart';
import 'package:scad_mobile/src/features/domains/data/models/domain_model/domain_model.dart';
import 'package:scad_mobile/src/features/domains/presentation/bloc/domains_bloc.dart';
import 'package:scad_mobile/src/features/domains/presentation/pages/themes_page.dart';
import 'package:scad_mobile/src/features/home/<USER>/widgets/persistent_bottom_nav_bar/persistent_bottom_nav_bar_v2.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

@RoutePage()
class DomainsPage extends StatefulWidget {
  const DomainsPage({super.key});

  @override
  State<DomainsPage> createState() => _DomainsPageState();
}

class _DomainsPageState extends State<DomainsPage>
    with AutomaticKeepAliveClientMixin {
  List<DomainModel> domainList = [];

  final GlobalKey bellIconKey = GlobalKey();
  final GlobalKey searchIconKey = GlobalKey();
  List<GlobalKey> steps = [];
  BuildContext? myContext;
  ScrollController scrollController = ScrollController();

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    steps = [bellIconKey, searchIconKey];
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      if (HiveUtilsSettings.getUserGuideStatus() == UserGuides.Domains) {
        ShowCaseWidget.of(myContext!).startShowCase(steps);
      }
      context.read<DomainsBloc>().add(const DomainsInitEvent());
    });
  }

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    super.build(context);
    return ShowCaseWidget(
      builder: Builder(
        builder: (context) {
          myContext = context;
          return Scaffold(
            body: Column(
              children: [
                FlatAppBar(
                  title: LocaleKeys.statisticalDomains.tr(),
                  mainScreen: true,
                  bellIconKey: bellIconKey,
                  searchIconKey: searchIconKey,
                  scrollController: scrollController,
                ),
                Expanded(
                  child: BlocConsumer<DomainsBloc, DomainsState>(
                    listener: (context, state) {
                      if (state is DomainShowResponseState) {
                        domainList = state.list;
                      }
                    },
                    builder: (context, state) {
                      if (state is DomainLoadingState) {
                        return const Center(
                            child: CircularProgressIndicator());
                      } else if (state is DomainErrorState) {
                        return Text(
                          state.error,
                          textScaler:
                              TextScaler.linear(textScaleFactor.value),
                        );
                      } else if (state is DomainShowResponseState) {}
                      return ListView.separated(
                        controller: scrollController,
                        shrinkWrap: true,
                        padding: const EdgeInsets.only(
                          bottom: 150,
                          right: 24,
                          left: 24,
                        ),
                        itemCount: domainList.length,
                        itemBuilder: (context, index) {
                          return DomainTile(
                            domain: DeviceType.isDirectionRTL(context)
                                ? domainList[index].domainNameAr
                                : domainList[index].domainName,
                            icon: domainList[index].domainIcon,
                            onTap: () {
                              // AutoRouter.of(context).push(
                              //   ThemesPageRoute(
                              //     domainId: domainList[index].domainId!,
                              //   ),
                              // );
                              pushScreen(
                                context,
                                withNavBar: true,
                                screen: ThemesPage(
                                  domainId: domainList[index].domainId,
                                ),
                              );
                            },
                          );
                        },
                        separatorBuilder: (context, index) =>
                            const SizedBox(height: 22),
                      );
                    },
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
