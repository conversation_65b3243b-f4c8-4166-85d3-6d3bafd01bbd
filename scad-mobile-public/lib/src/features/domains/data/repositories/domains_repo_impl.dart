import 'dart:async';
import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/features/domains/data/data_sources/api_end_points.dart';
import 'package:scad_mobile/src/features/domains/data/models/domain_classification_model/domain_classification_model.dart';
import 'package:scad_mobile/src/features/domains/data/models/domain_model/domain_model.dart';
import 'package:scad_mobile/src/features/domains/data/models/experimental_filters_response_item.dart';
import 'package:scad_mobile/src/features/domains/data/models/experimental_indicator_list_response.dart';
import 'package:scad_mobile/src/features/domains/data/models/theme_indicator_list_response.dart';
import 'package:scad_mobile/src/features/domains/data/models/theme_subtheme_response.dart';
import 'package:scad_mobile/src/features/domains/domain/repositories/domains_repository_imports.dart';
import 'package:scad_mobile/src/services/http_service_impl.dart';
import 'package:scad_mobile/src/services/http_services.dart';
import 'package:scad_mobile/src/utils/extentions/string_extentions.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_keys.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_api_cache.dart';

import 'package:scad_mobile/translations/locale_keys.g.dart';

class DomainsRepositoryImpl implements DomainsRepository {
  DomainsRepositoryImpl() {
    _httpService = HttpServiceRequests();
  }

  late HttpService _httpService;

  @override
  Future<RepoResponse<List<DomainModel>>> getDomainList() async {
    try {
      final dynamic res = HiveUtilsApiCache.get(HiveKeys.keyDomainList);

      if (res != null) {
        return RepoResponse<List<DomainModel>>.success(
          response: (res as List<dynamic>)
              .map((e) => DomainModel.fromJson(e as Map<String, dynamic>))
              .toList(),
        );
      }

      final response = await _httpService.get(
        DomainsEndPoints.getDomainList,
      );

      if (response.isSuccess) {
        final List<DomainModel> list = [];
        final List<dynamic> data = response.response['data'] as List<dynamic>;

        for (int i = 0; i < data.length; i++) {
          list.add(DomainModel.fromJson(data[i] as Map<String, dynamic>));
        }

        await HiveUtilsApiCache.set(HiveKeys.keyDomainList, data);

        return RepoResponse<List<DomainModel>>.success(
          response: list,
        );
      } else {
        return RepoResponse<List<DomainModel>>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<List<DomainModel>>.error(errorMessage:LocaleKeys.somethingWentWrong.tr());
    }
  }

  @override
  Future<RepoResponse<List<DomainClassificationModel>>>
      getDomainClassificationList(RequestParamsMap requestParams) async {
    try {
      final response = await _httpService.get(
        DomainsEndPoints.getDomainClassificationList
            .setUrlParams(requestParams),
        server: ApiServer.ifp,
      );

      if (response.isSuccess) {
        final List<DomainClassificationModel> list = (response
                .response['classification'] as List<dynamic>)
            .map((e) =>
                DomainClassificationModel.fromJson(e as Map<String, dynamic>),)
            .toList()
          ..removeWhere((element) => ![
                'official_statistics',
                'experimental_statistics',
              ].contains(element.key),);

        return RepoResponse<List<DomainClassificationModel>>.success(
          response: list,
        );
      } else {
        return RepoResponse<List<DomainClassificationModel>>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<List<DomainClassificationModel>>.error(
          errorMessage: LocaleKeys.somethingWentWrong.tr(),);
    }
  }

  // ThemeListResponseEntity
  @override
  Future<RepoResponse<List<ThemeSubThemeResponse>>> getThemeList(
      RequestParamsMap requestParams,) async {
    try {
      final response = await _httpService.get(
        DomainsEndPoints.getThemeList.setUrlParams(requestParams),
        server: ApiServer.ifp,
      );

      if (response.isSuccess) {
        final List<ThemeSubThemeResponse> list = [];
        for (var m in response.response['data'] as List<dynamic>) {
          list.add(ThemeSubThemeResponse.fromJson(m as Map<String, dynamic>));
        }

        return RepoResponse<List<ThemeSubThemeResponse>>.success(
          response: list,
        );
      } else {
        return RepoResponse<List<ThemeSubThemeResponse>>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<List<ThemeSubThemeResponse>>.error(
          errorMessage:  LocaleKeys.somethingWentWrong.tr(),);
    }
  }

  @override
  Future<RepoResponse<ThemeIndicatorListResponse>> getThemeIndicatorList(
      RequestParamsMap requestParams,) async {
    try {
      final response = await _httpService.get(
        DomainsEndPoints.getThemeIndicatorList.setUrlParams(requestParams),
        server: ApiServer.ifp,
      );

      if (response.isSuccess) {
        return RepoResponse<ThemeIndicatorListResponse>.success(
          response: ThemeIndicatorListResponse.fromJson(response.response),
        );
      } else {
        return RepoResponse<ThemeIndicatorListResponse>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<ThemeIndicatorListResponse>.error(
          errorMessage:  LocaleKeys.somethingWentWrong.tr(),);
    }
  }

  @override
  Future<RepoResponse<ExperimentalIndicatorListResponse>>
      getThemeExperimentalIndicatorList(
          {required int pageNo, required Map<String, dynamic> filters,}) async {
    try {
      final String endpoint = DomainsEndPoints.getThemeExperimentalIndicatorList
          .setUrlParams({'page': '$pageNo'});
      final response = await _httpService.postJson(
        endpoint,
        jsonPayloadMap: filters,
        server: ApiServer.ifp,
      );

      if (response.isSuccess) {
        return RepoResponse<ExperimentalIndicatorListResponse>.success(
          response:
              ExperimentalIndicatorListResponse.fromJson(response.response),
        );
      } else {
        return RepoResponse<ExperimentalIndicatorListResponse>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<ExperimentalIndicatorListResponse>.error(
          errorMessage:  LocaleKeys.somethingWentWrong.tr(),);
    }
  }

  @override
  Future<RepoResponse<List<ExperimentalFiltersResponseItem>>>
      getExperimentalFilters({
    required String screenerIndicator,
  }) async {
    try {
      final String endpoint =
          DomainsEndPoints.getExperimentalFilters(screenerIndicator);

      final response = await _httpService.get(
        endpoint,
        server: ApiServer.ifp,
      );

      if (response.isSuccess) {
        final List<ExperimentalFiltersResponseItem> list =
            (response.response['data'] as List<dynamic>)
                .map((e) => ExperimentalFiltersResponseItem.fromJson(
                    e as Map<String, dynamic>,),)
                .toList();
        return RepoResponse<List<ExperimentalFiltersResponseItem>>.success(
          response: list,
        );
      } else {
        return RepoResponse<List<ExperimentalFiltersResponseItem>>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<List<ExperimentalFiltersResponseItem>>.error(
        errorMessage:  LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }
}
