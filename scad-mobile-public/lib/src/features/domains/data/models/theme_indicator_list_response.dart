class ThemeIndicatorListResponse {
  List<ThemeIndicatorListItem>? results;
  int? totalCount;
  List<Products>? products;

  ThemeIndicatorListResponse({
    this.results,
    this.totalCount,
    this.products,
  });

  ThemeIndicatorListResponse.fromJson(Map<String, dynamic> json) {
    results = (json['results'] as List?)
        ?.map((dynamic e) =>
            ThemeIndicatorListItem.fromJson(e as Map<String, dynamic>))
        .toList();
    totalCount = json['total_count'] as int?;
    products = (json['products'] as List?)
        ?.map((dynamic e) => Products.fromJson(e as Map<String, dynamic>))
        .toList();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['results'] = results?.map((e) => e.toJson()).toList();
    json['total_count'] = totalCount;
    json['products'] = products?.map((e) => e.toJson()).toList();
    return json;
  }
}

class ThemeIndicatorListItem {
  String? id;
  String? indicatorId;
  bool? isMultiDimension;
  String? title;
  String? contentType;
  Category? category;

  ThemeIndicatorListItem({
    this.id,
    this.indicatorId,
    this.isMultiDimension,
    this.title,
    this.contentType,
    this.category,
  });

  ThemeIndicatorListItem.fromJson(Map<String, dynamic> json) {
    id = json['id'] as String?;
    indicatorId = json['note'] as String?;
    isMultiDimension = json['isMultiDimension'] as bool?;
    title = json['title'] as String?;
    contentType = json['content_type'] as String?;
    category = json['category'] is List
        ? null
        : (json['category'] as Map<String, dynamic>?) != null
            ? Category.fromJson(json['category'] as Map<String, dynamic>)
            : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['id'] = id;
    json['note'] = indicatorId;
    json['isMultiDimension'] = isMultiDimension;
    json['title'] = title;
    json['content_type'] = contentType;
    json['category'] = category?.toJson();
    return json;
  }
}

class Category {
  String? id;
  String? name;
  String? darkIcon;
  String? lightIcon;

  Category({
    this.id,
    this.name,
    this.darkIcon,
    this.lightIcon,
  });

  Category.fromJson(Map<String, dynamic> json) {
    id = json['id'] as String?;
    name = json['name'] as String?;
    darkIcon = json['dark_icon'] as String?;
    lightIcon = json['light_icon'] as String?;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['id'] = id;
    json['name'] = name;
    json['dark_icon'] = darkIcon;
    json['light_icon'] = lightIcon;
    return json;
  }
}

class Products {
  String? pid;
  String? count;
  String? title;

  Products({
    this.pid,
    this.count,
    this.title,
  });

  Products.fromJson(Map<String, dynamic> json) {
    pid = json['pid'] as String?;
    count = json['count'] as String?;
    title = json['title'] as String?;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['pid'] = pid;
    json['count'] = count;
    json['title'] = title;
    return json;
  }
}
