part of 'search_repository_imports.dart';

abstract class SearchRepository {
  Future<RepoResponse<List<SearchDashboardResponseItem>>> searchDashboardQuery({
    required String query,
  });

  Future<RepoResponse<SearchIfpResponse>> searchIfpQuery({
    required String query,
  });

  Future<RepoResponse<PublicationsResult>> searchPublicationQuery({
    required String query,
    required int pageNo,
    required int pageSize,
    required String token,
  });

  Future<RepoResponse<Webreports>> searchWebReportQuery({
    required String query,
    required int pageNo,
    required int pageSize,
    required String token,
  });

  Future<RepoResponse<Authentification>> authentication();
}
