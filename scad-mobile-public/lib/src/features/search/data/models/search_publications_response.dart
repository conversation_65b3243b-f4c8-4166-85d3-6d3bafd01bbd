class PublicationsResult {
  PublicationsResult({
    this.actions,
    this.facets,
    this.items,
    this.lastPage,
    this.page,
    this.pageSize,
    this.totalCount,
  });

  PublicationsResult.fromJson(Map<String, dynamic> json)
      : actions = (json['actions'] as Map<String, dynamic>?) != null
            ? Actions.fromJson(json['actions'] as Map<String, dynamic>)
            : null,
        facets = json['facets'] as List?,
        items = (json['items'] as List?)
            ?.map(
              (dynamic e) =>
                  PublicationItem.fromJson(e as Map<String, dynamic>),
            )
            .toList(),
        lastPage = json['lastPage'] as int?,
        page = json['page'] as int?,
        pageSize = json['pageSize'] as int?,
        totalCount = json['totalCount'] as int?;
  final Actions? actions;
  final List<dynamic>? facets;
  final List<PublicationItem>? items;
  final int? lastPage;
  final int? page;
  final int? pageSize;
  final int? totalCount;

  Map<String, dynamic> toJson() => {
        'actions': actions?.toJson(),
        'facets': facets,
        'items': items?.map((e) => e.toJson()).toList(),
        'lastPage': lastPage,
        'page': page,
        'pageSize': pageSize,
        'totalCount': totalCount,
      };
}

class Actions {
  Actions({
    this.get,
  });

  Actions.fromJson(Map<String, dynamic> json)
      : get = (json['get'] as Map<String, dynamic>?) != null
            ? Get.fromJson(json['get'] as Map<String, dynamic>)
            : null;
  final Get? get;

  Map<String, dynamic> toJson() => {'get': get?.toJson()};
}

class Get {
  Get({
    this.method,
    this.href,
  });

  Get.fromJson(Map<String, dynamic> json)
      : method = json['method'] as String?,
        href = json['href'] as String?;
  final String? method;
  final String? href;

  Map<String, dynamic> toJson() => {'method': method, 'href': href};
}

class PublicationItem {
  PublicationItem({
    this.actions,
    this.availableLanguages,
    this.contentFields,
    this.contentStructureId,
    this.creator,
    this.customFields,
    this.dateCreated,
    this.dateModified,
    this.datePublished,
    this.description,
    this.externalReferenceCode,
    this.friendlyUrlPath,
    this.id,
    this.key,
    this.keywords,
    this.numberOfComments,
    this.priority,
    this.relatedContents,
    this.renderedContents,
    this.siteId,
    this.structuredContentFolderId,
    this.subscribed,
    this.taxonomyCategoryBriefs,
    this.title,
    this.uuid,
    this.open = true
  });

  PublicationItem.fromJson(Map<String, dynamic> json)
      : actions = (json['actions'] as Map<String, dynamic>?) != null
            ? ItemsActions.fromJson(json['actions'] as Map<String, dynamic>)
            : null,
        availableLanguages = (json['availableLanguages'] as List?)
            ?.map((dynamic e) => e as String)
            .toList(),
        contentFields = (json['contentFields'] as List?)
            ?.map(
              (dynamic e) => ContentFields.fromJson(e as Map<String, dynamic>),
            )
            .toList(),
        contentStructureId = json['contentStructureId'] as int?,
        creator = (json['creator'] as Map<String, dynamic>?) != null
            ? Creator.fromJson(json['creator'] as Map<String, dynamic>)
            : null,
        customFields = (json['customFields'] as List?)
            ?.map(
              (dynamic e) => CustomFields.fromJson(e as Map<String, dynamic>),
            )
            .toList(),
        dateCreated = json['dateCreated'] as String?,
        dateModified = json['dateModified'] as String?,
        datePublished = json['datePublished'] as String?,
        description = json['description'] as String?,
        externalReferenceCode = json['externalReferenceCode'] as String?,
        friendlyUrlPath = json['friendlyUrlPath'] as String?,
        id = json['id'] as int?,
        key = json['key'] as String?,
        keywords = json['keywords'] as List?,
        numberOfComments = json['numberOfComments'] as int?,
        priority = json['priority'] as double?,
        relatedContents = json['relatedContents'] as List?,
        renderedContents = (json['renderedContents'] as List?)
            ?.map(
              (dynamic e) =>
                  RenderedContents.fromJson(e as Map<String, dynamic>),
            )
            .toList(),
        siteId = json['siteId'] as int?,
        structuredContentFolderId = json['structuredContentFolderId'] as int?,
        subscribed = json['subscribed'] as bool?,
        taxonomyCategoryBriefs = (json['taxonomyCategoryBriefs'] as List?)
            ?.map(
              (dynamic e) =>
                  TaxonomyCategoryBriefs.fromJson(e as Map<String, dynamic>),
            )
            .toList(),
        title = json['title'] as String?,
        uuid = json['uuid'] as String?,
        open = true;
  final ItemsActions? actions;
  final List<String>? availableLanguages;
  final List<ContentFields>? contentFields;
  final int? contentStructureId;
  final Creator? creator;
  final List<CustomFields>? customFields;
  final String? dateCreated;
  final String? dateModified;
  final String? datePublished;
  final String? description;
  final String? externalReferenceCode;
  final String? friendlyUrlPath;
  final int? id;
  final String? key;
  final List<dynamic>? keywords;
  final int? numberOfComments;
  final double? priority;
  final List<dynamic>? relatedContents;
  final List<RenderedContents>? renderedContents;
  final int? siteId;
  final int? structuredContentFolderId;
  final bool? subscribed;
  final List<TaxonomyCategoryBriefs>? taxonomyCategoryBriefs;
  final String? title;
  final String? uuid;
    bool  open;
  Map<String, dynamic> toJson() => {
        'actions': actions?.toJson(),
        'availableLanguages': availableLanguages,
        'contentFields': contentFields?.map((e) => e.toJson()).toList(),
        'contentStructureId': contentStructureId,
        'creator': creator?.toJson(),
        'customFields': customFields?.map((e) => e.toJson()).toList(),
        'dateCreated': dateCreated,
        'dateModified': dateModified,
        'datePublished': datePublished,
        'description': description,
        'externalReferenceCode': externalReferenceCode,
        'friendlyUrlPath': friendlyUrlPath,
        'id': id,
        'key': key,
        'keywords': keywords,
        'numberOfComments': numberOfComments,
        'priority': priority,
        'relatedContents': relatedContents,
        'renderedContents': renderedContents?.map((e) => e.toJson()).toList(),
        'siteId': siteId,
        'structuredContentFolderId': structuredContentFolderId,
        'subscribed': subscribed,
        'taxonomyCategoryBriefs':
            taxonomyCategoryBriefs?.map((e) => e.toJson()).toList(),
        'title': title,
        'uuid': uuid,
        'open': open
      };
}

class ItemsActions {
  ItemsActions({
    this.getRenderedContent,
    this.getRenderedContentByDisplayPage,
    this.subscribe,
    this.unsubscribe,
    this.get,
    this.replace,
    this.update,
    this.delete,
  });

  ItemsActions.fromJson(Map<String, dynamic> json)
      : getRenderedContent =
            (json['getRenderedContent'] as Map<String, dynamic>?) != null
                ? GetRenderedContent.fromJson(
                    json['getRenderedContent'] as Map<String, dynamic>,
                  )
                : null,
        getRenderedContentByDisplayPage =
            (json['getRenderedContentByDisplayPage']
                        as Map<String, dynamic>?) !=
                    null
                ? GetRenderedContentByDisplayPage.fromJson(
                    json['getRenderedContentByDisplayPage']
                        as Map<String, dynamic>,
                  )
                : null,
        subscribe = (json['subscribe'] as Map<String, dynamic>?) != null
            ? Subscribe.fromJson(json['subscribe'] as Map<String, dynamic>)
            : null,
        unsubscribe = (json['unsubscribe'] as Map<String, dynamic>?) != null
            ? Unsubscribe.fromJson(json['unsubscribe'] as Map<String, dynamic>)
            : null,
        get = (json['get'] as Map<String, dynamic>?) != null
            ? Get.fromJson(json['get'] as Map<String, dynamic>)
            : null,
        replace = (json['replace'] as Map<String, dynamic>?) != null
            ? Replace.fromJson(json['replace'] as Map<String, dynamic>)
            : null,
        update = (json['update'] as Map<String, dynamic>?) != null
            ? Update.fromJson(json['update'] as Map<String, dynamic>)
            : null,
        delete = (json['delete'] as Map<String, dynamic>?) != null
            ? Delete.fromJson(json['delete'] as Map<String, dynamic>)
            : null;
  final GetRenderedContent? getRenderedContent;
  final GetRenderedContentByDisplayPage? getRenderedContentByDisplayPage;
  final Subscribe? subscribe;
  final Unsubscribe? unsubscribe;
  final Get? get;
  final Replace? replace;
  final Update? update;
  final Delete? delete;

  Map<String, dynamic> toJson() => {
        'getRenderedContent': getRenderedContent?.toJson(),
        'getRenderedContentByDisplayPage':
            getRenderedContentByDisplayPage?.toJson(),
        'subscribe': subscribe?.toJson(),
        'unsubscribe': unsubscribe?.toJson(),
        'get': get?.toJson(),
        'replace': replace?.toJson(),
        'update': update?.toJson(),
        'delete': delete?.toJson(),
      };
}

class GetRenderedContent {
  GetRenderedContent({
    this.method,
    this.href,
  });

  GetRenderedContent.fromJson(Map<String, dynamic> json)
      : method = json['method'] as String?,
        href = json['href'] as String?;
  final String? method;
  final String? href;

  Map<String, dynamic> toJson() => {'method': method, 'href': href};
}

class GetRenderedContentByDisplayPage {
  GetRenderedContentByDisplayPage({
    this.method,
    this.href,
  });

  GetRenderedContentByDisplayPage.fromJson(Map<String, dynamic> json)
      : method = json['method'] as String?,
        href = json['href'] as String?;
  final String? method;
  final String? href;

  Map<String, dynamic> toJson() => {'method': method, 'href': href};
}

class Subscribe {
  Subscribe({
    this.method,
    this.href,
  });

  Subscribe.fromJson(Map<String, dynamic> json)
      : method = json['method'] as String?,
        href = json['href'] as String?;
  final String? method;
  final String? href;

  Map<String, dynamic> toJson() => {'method': method, 'href': href};
}

class Unsubscribe {
  Unsubscribe({
    this.method,
    this.href,
  });

  Unsubscribe.fromJson(Map<String, dynamic> json)
      : method = json['method'] as String?,
        href = json['href'] as String?;
  final String? method;
  final String? href;

  Map<String, dynamic> toJson() => {'method': method, 'href': href};
}

class Replace {
  Replace({
    this.method,
    this.href,
  });

  Replace.fromJson(Map<String, dynamic> json)
      : method = json['method'] as String?,
        href = json['href'] as String?;
  final String? method;
  final String? href;

  Map<String, dynamic> toJson() => {'method': method, 'href': href};
}

class Update {
  Update({
    this.method,
    this.href,
  });

  Update.fromJson(Map<String, dynamic> json)
      : method = json['method'] as String?,
        href = json['href'] as String?;
  final String? method;
  final String? href;

  Map<String, dynamic> toJson() => {'method': method, 'href': href};
}

class Delete {
  Delete({
    this.method,
    this.href,
  });

  Delete.fromJson(Map<String, dynamic> json)
      : method = json['method'] as String?,
        href = json['href'] as String?;
  final String? method;
  final String? href;

  Map<String, dynamic> toJson() => {'method': method, 'href': href};
}

class ContentFields {
  ContentFields({
    this.contentFieldValue,
    this.dataType,
    this.label,
    this.name,
    this.nestedContentFields,
    this.repeatable,
  });

  ContentFields.fromJson(Map<String, dynamic> json)
      : contentFieldValue = (json['contentFieldValue'] != null)
            ? ContentFieldValue.fromJson(
                Map<String, dynamic>.from(json['contentFieldValue'] as Map),
              )
            : null,
        dataType = json['dataType'] as String?,
        label = json['label'] as String?,
        name = json['name'] as String?,
        nestedContentFields = json['nestedContentFields'] as List?,
        repeatable = json['repeatable'] as bool?;
  final ContentFieldValue? contentFieldValue;
  final String? dataType;
  final String? label;
  final String? name;
  final List<dynamic>? nestedContentFields;
  final bool? repeatable;

  Map<String, dynamic> toJson() => {
        'contentFieldValue': contentFieldValue?.toJson(),
        'dataType': dataType,
        'label': label,
        'name': name,
        'nestedContentFields': nestedContentFields,
        'repeatable': repeatable,
      };
}

class ContentFieldValue {
  ContentFieldValue({
    this.document,
  });

  ContentFieldValue.fromJson(Map<String, dynamic> json)
      : document = (json['document'] as Map<String, dynamic>?) != null
            ? Document.fromJson(json['document'] as Map<String, dynamic>)
            : null;
  final Document? document;

  Map<String, dynamic> toJson() => {'document': document?.toJson()};
}

class Document {
  Document({
    this.contentType,
    this.contentUrl,
    this.description,
    this.encodingFormat,
    this.fileExtension,
    this.id,
    this.sizeInBytes,
    this.title,
  });

  Document.fromJson(Map<String, dynamic> json)
      : contentType = json['contentType'] as String?,
        contentUrl = json['contentUrl'] as String?,
        description = json['description'] as String?,
        encodingFormat = json['encodingFormat'] as String?,
        fileExtension = json['fileExtension'] as String?,
        id = json['id'] as int?,
        sizeInBytes = json['sizeInBytes'] as int?,
        title = json['title'] as String?;
  final String? contentType;
  final String? contentUrl;
  final String? description;
  final String? encodingFormat;
  final String? fileExtension;
  final int? id;
  final int? sizeInBytes;
  final String? title;

  Map<String, dynamic> toJson() => {
        'contentType': contentType,
        'contentUrl': contentUrl,
        'description': description,
        'encodingFormat': encodingFormat,
        'fileExtension': fileExtension,
        'id': id,
        'sizeInBytes': sizeInBytes,
        'title': title,
      };
}

class Creator {
  Creator({
    this.additionalName,
    this.contentType,
    this.familyName,
    this.givenName,
    this.id,
    this.name,
  });

  Creator.fromJson(Map<String, dynamic> json)
      : additionalName = json['additionalName'] as String?,
        contentType = json['contentType'] as String?,
        familyName = json['familyName'] as String?,
        givenName = json['givenName'] as String?,
        id = json['id'] as int?,
        name = json['name'] as String?;
  final String? additionalName;
  final String? contentType;
  final String? familyName;
  final String? givenName;
  final int? id;
  final String? name;

  Map<String, dynamic> toJson() => {
        'additionalName': additionalName,
        'contentType': contentType,
        'familyName': familyName,
        'givenName': givenName,
        'id': id,
        'name': name,
      };
}

class CustomFields {
  CustomFields({
    this.customValue,
    this.dataType,
    this.name,
  });

  CustomFields.fromJson(Map<String, dynamic> json)
      : customValue = (json['customValue'] as Map<String, dynamic>?) != null
            ? CustomValue.fromJson(json['customValue'] as Map<String, dynamic>)
            : null,
        dataType = json['dataType'] as String?,
        name = json['name'] as String?;
  final CustomValue? customValue;
  final String? dataType;
  final String? name;

  Map<String, dynamic> toJson() => {
        'customValue': customValue?.toJson(),
        'dataType': dataType,
        'name': name,
      };
}

class CustomValue {
  CustomValue({
    this.data,
  });

  CustomValue.fromJson(Map<String, dynamic> json)
      : data = json['data'].toString();
  final String? data;

  Map<String, dynamic> toJson() => {'data': data};
}

class RenderedContents {
  RenderedContents({
    this.contentTemplateId,
    this.contentTemplateName,
    this.markedAsDefault,
    this.renderedContentURL,
  });

  RenderedContents.fromJson(Map<String, dynamic> json)
      : contentTemplateId = json['contentTemplateId'] as String?,
        contentTemplateName = json['contentTemplateName'] as String?,
        markedAsDefault = json['markedAsDefault'] as bool?,
        renderedContentURL = json['renderedContentURL'] as String?;
  final String? contentTemplateId;
  final String? contentTemplateName;
  final bool? markedAsDefault;
  final String? renderedContentURL;

  Map<String, dynamic> toJson() => {
        'contentTemplateId': contentTemplateId,
        'contentTemplateName': contentTemplateName,
        'markedAsDefault': markedAsDefault,
        'renderedContentURL': renderedContentURL,
      };
}

class TaxonomyCategoryBriefs {
  TaxonomyCategoryBriefs({
    this.taxonomyCategoryId,
    this.taxonomyCategoryName,
  });

  TaxonomyCategoryBriefs.fromJson(Map<String, dynamic> json)
      : taxonomyCategoryId = json['taxonomyCategoryId'] as int?,
        taxonomyCategoryName = json['taxonomyCategoryName'] as String?;
  final int? taxonomyCategoryId;
  final String? taxonomyCategoryName;

  Map<String, dynamic> toJson() => {
        'taxonomyCategoryId': taxonomyCategoryId,
        'taxonomyCategoryName': taxonomyCategoryName,
      };
}
