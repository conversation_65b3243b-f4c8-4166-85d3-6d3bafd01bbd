class SearchDashboardResponseItem {

  SearchDashboardResponseItem({
    this.uuid,
    this.addedBy,
    this.editedBy,
    this.productType,
    this.domainId,
    this.domainName,
    this.domainNameAr,
    this.name,
    this.nameAr,
    this.url,
    this.urlAr,
    this.active,
    this.createdTime,
    this.updatedTime,
    this.urlDark,
    this.urlArDark,
  });

  SearchDashboardResponseItem.fromJson(Map<String, dynamic> json) {
    uuid = json['uuid'] as String?;
    if ((json['added_by'] as Map<String,dynamic>?) != null) {
      addedBy = AddedBy.fromJson(json['added_by'] as Map<String,dynamic>);
    } else {
      addedBy = null;
    }
    if ((json['edited_by'] as Map<String,dynamic>?) != null) {
      editedBy = EditedBy.fromJson(json['edited_by'] as Map<String,dynamic>);
    } else {
      editedBy = null;
    }
    productType = json['product_type'] as String?;
    domainId = json['domain_id'] as String?;
    domainName = json['domain_name'] as String?;
    domainNameAr = json['domain_name_ar'] as String?;
    name = json['name'] as String?;
    nameAr = json['name_ar'] as String?;
    url = json['url'] as String?;
    urlAr = json['url_ar'] as String?;
    active = json['active'] as bool?;
    createdTime = json['created_time'] as String?;
    updatedTime = json['updated_time'] as String?;
    urlDark = json['url_dark'] as String?;
    urlArDark = json['url_dark_ar'] as String?;
  }
  String? uuid;
  AddedBy? addedBy;
  EditedBy? editedBy;
  String? productType;
  String? domainId;
  String? domainName;
  String? domainNameAr;
  String? name;
  String? nameAr;
  String? url;
  String? urlAr;
  bool? active;
  String? createdTime;
  String? updatedTime;
  String? urlDark;
  String? urlArDark;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['uuid'] = uuid;
    json['added_by'] = addedBy?.toJson();
    json['edited_by'] = editedBy?.toJson();
    json['product_type'] = productType;
    json['domain_id'] = domainId;
    json['domain_name'] = domainName;
    json['domain_name_ar'] = domainNameAr;
    json['name'] = name;
    json['name_ar'] = nameAr;
    json['url'] = url;
    json['url_ar'] = urlAr;
    json['active'] = active;
    json['created_time'] = createdTime;
    json['updated_time'] = updatedTime;
    json['url_dark'] = urlDark;
    json['url_dark_ar'] = urlArDark;
    return json;
  }
}

class AddedBy {

  AddedBy({
    this.uuid,
    this.name,
  });

  AddedBy.fromJson(Map<String, dynamic> json) {
    uuid = json['uuid'] as String?;
    name = json['name'] as String?;
  }
  String? uuid;
  String? name;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['uuid'] = uuid;
    json['name'] = name;
    return json;
  }
}

class EditedBy {

  EditedBy({
    this.uuid,
    this.name,
  });

  EditedBy.fromJson(Map<String, dynamic> json) {
    uuid = json['uuid'] as String?;
    name = json['name'] as String?;
  }
  String? uuid;
  String? name;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['uuid'] = uuid;
    json['name'] = name;
    return json;
  }
}
