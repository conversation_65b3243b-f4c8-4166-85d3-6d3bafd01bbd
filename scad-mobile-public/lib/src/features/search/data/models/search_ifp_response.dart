class SearchIfpResponse {
  SearchIfpResponse({
    this.query,
    this.numberOfResults,
    this.result,
  });

  SearchIfpResponse.fromJson(Map<String, dynamic> json) {
    query = json['query'] as String?;
    numberOfResults = json['numberOfResults'] as int?;
    result = (json['result'] as Map<String, dynamic>?) != null
        ? Result.fromJson(json['result'] as Map<String, dynamic>)
        : null;
  }

  String? query;
  int? numberOfResults;
  Result? result;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['query'] = query;
    json['numberOfResults'] = numberOfResults;
    json['result'] = result?.toJson();
    return json;
  }
}

class Result {
  Result({
    this.contentTypes,
  });

  Result.fromJson(Map<String, dynamic> json) {
    contentTypes = (json['contentTypes'] as List?)
        ?.map(
          (dynamic e) => ResultContentTypes.fromJson(e as Map<String, dynamic>),
        )
        .toList();
  }

  List<ResultContentTypes>? contentTypes;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['contentTypes'] = contentTypes?.map((e) => e.toJson()).toList();
    return json;
  }
}

class ResultContentTypes {
  ResultContentTypes({
    this.title,
    this.contentType,
    this.key,
    this.darkIcon,
    this.lightIcon,
    this.items,
    this.categories,
    this.isSelected,
    this.machineName,
  });

  ResultContentTypes.fromJson(Map<String, dynamic> json) {
    title = json['title'] as String?;
    contentType = json['contentType'] as String?;
    key = json['key'] as String?;
    darkIcon = json['dark_icon'] as String?;
    lightIcon = json['light_icon'] as String?;
    items = (json['items'] as List?)
        ?.map((dynamic e) => Items.fromJson(e as Map<String, dynamic>))
        .toList();
    categories = json[
        'categories']; //(json['categories'] as Map<String,dynamic>?) != null ? Categories.fromJson(json['categories'] as Map<String,dynamic>) : null;
    isSelected = json['isSelected'] as bool?;
    machineName = json['machineName'] as String?;
  }

  String? title;
  String? contentType;
  String? key;
  String? darkIcon;
  String? lightIcon;
  List<Items>? items;
  dynamic categories;
  bool? isSelected;
  String? machineName;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['title'] = title;
    json['contentType'] = contentType;
    json['key'] = key;
    json['dark_icon'] = darkIcon;
    json['light_icon'] = lightIcon;
    json['items'] = items?.map((e) => e.toJson()).toList();
    json['categories'] = categories?.toJson();
    json['isSelected'] = isSelected;
    json['machineName'] = machineName;
    return json;
  }
}

class Items {
  Items({
    this.id,
    this.type,
    this.title,
    // this.subTitle,
    this.contentClassification,
    this.pageCategory,
    // this.topic,
    // this.theme,
    // this.subtheme,
    // this.product,
    // this.body,
    this.note,
    this.appType,
  });

  Items.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    type = json['type'] as String?;
    title = json['title'] as String?;
    // subTitle = json['subTitle'] as String?;
    contentClassification = json['contentClassification'] as String?;
    pageCategory = json['pageCategory'] as String?;
    // topic = (json['topic'] as Map<String, dynamic>?) != null
    //     ? Topic.fromJson(json['topic'] as Map<String, dynamic>)
    //     : null;
    // theme = json['theme'] as String?;
    // subtheme = json['subtheme'] as String?;
    // product = json['product'] as String?;
    // body = json['body'] as String?;
    note = json['note']?.toString();
    appType = json['appType']?.toString();
  }

  String? id;
  String? type;
  String? title;
  // String? subTitle;
  String? contentClassification;
  String? pageCategory;
  // Topic? topic;
  // String? theme;
  // String? subtheme;
  // String? product;
  // String? body;
  String? note;
  String? appType;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['id'] = id;
    json['type'] = type;
    json['title'] = title;
    // json['subTitle'] = subTitle;
    json['contentClassification'] = contentClassification;
    json['pageCategory'] = pageCategory;
    // json['topic'] = topic?.toJson();
    // json['theme'] = theme;
    // json['subtheme'] = subtheme;
    // json['product'] = product;
    // json['body'] = body;
    json['note'] = note;
    json['appType'] = appType;
    return json;
  }
}

class Topic {
  Topic({
    this.id,
    this.name,
  });

  Topic.fromJson(Map<String, dynamic> json) {
    id = json['id'] as String?;
    name = json['name'] as String?;
  }

  String? id;
  String? name;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['id'] = id;
    json['name'] = name;
    return json;
  }
}
