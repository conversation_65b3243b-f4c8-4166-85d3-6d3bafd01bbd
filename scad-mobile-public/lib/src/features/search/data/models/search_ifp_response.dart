class SearchIfpResponse {
  SearchIfpResponse({
    this.query,
    this.numberOfResults,
    this.result,
  });

  SearchIfpResponse.fromJson(Map<String, dynamic> json) {
    query = json['query'] as String?;
    numberOfResults = json['numberOfResults'] as int?;
    result = (json['result'] as Map<String, dynamic>?) != null
        ? Result.fromJson(json['result'] as Map<String, dynamic>)
        : null;
  }

  String? query;
  int? numberOfResults;
  Result? result;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['query'] = query;
    json['numberOfResults'] = numberOfResults;
    json['result'] = result?.toJson();
    return json;
  }
}

class Result {
  Result({
    this.contentTypes,
  });

  Result.fromJson(Map<String, dynamic> json) {
    contentTypes = (json['contentTypes'] as List?)
        ?.map(
          (dynamic e) => ResultContentTypes.fromJson(e as Map<String, dynamic>),
        )
        .toList();
  }

  List<ResultContentTypes>? contentTypes;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['contentTypes'] = contentTypes?.map((e) => e.toJson()).toList();
    return json;
  }
}

class ResultContentTypes {
  ResultContentTypes({
    this.title,
    this.contentType,
    this.key,
    this.darkIcon,
    this.lightIcon,
    this.items,
    this.categories,
    this.isSelected,
    this.machineName,
  });

  ResultContentTypes.fromJson(Map<String, dynamic> json) {
    title = json['title'] as String?;
    contentType = json['contentType'] as String?;
    key = json['key'] as String?;
    darkIcon = json['dark_icon'] as String?;
    lightIcon = json['light_icon'] as String?;
    items = (json['items'] as List?)
        ?.map((dynamic e) => Items.fromJson(e as Map<String, dynamic>))
        .toList();
    categories = json[
        'categories']; //(json['categories'] as Map<String,dynamic>?) != null ? Categories.fromJson(json['categories'] as Map<String,dynamic>) : null;
    isSelected = json['isSelected'] as bool?;
    machineName = json['machineName'] as String?;
  }

  String? title;
  String? contentType;
  String? key;
  String? darkIcon;
  String? lightIcon;
  List<Items>? items;
  dynamic categories;
  bool? isSelected;
  String? machineName;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['title'] = title;
    json['contentType'] = contentType;
    json['key'] = key;
    json['dark_icon'] = darkIcon;
    json['light_icon'] = lightIcon;
    json['items'] = items?.map((e) => e.toJson()).toList();
    json['categories'] = categories?.toJson();
    json['isSelected'] = isSelected;
    json['machineName'] = machineName;
    return json;
  }
}

class Items {
  Items({
    this.id,
    this.title,
    this.contentClassification,
    this.subtitle,
    this.domains,
    this.type,
    this.appType,
    this.category,
  });

  Items.fromJson(Map<String, dynamic> json) {
    id = json['id'].toString();
    title = json['title'] as String?;
    contentClassification = json['content_classification'] as String?;
    subtitle = json['subtitle'] as String?;
    domains =
        (json['domains'] as List?)?.map((dynamic e) => e as String).toList();
    type = json['type'] as String?;
    appType = json['app_type'] as String?;
    category = json['category'] as String?;
  }

  String? id;
  String? title;
  String? contentClassification;
  String? subtitle;
  List<String>? domains;
  String? type;
  String? appType;
  String? category;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['id'] = id;
    json['title'] = title;
    json['content_classification'] = contentClassification;
    json['subtitle'] = subtitle;
    json['domains'] = domains;
    json['type'] = type;
    json['app_type'] = appType;
    json['category'] = category;
    return json;
  }
}
