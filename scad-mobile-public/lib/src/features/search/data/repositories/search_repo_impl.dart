import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/features/products/data/data_sources/products_end_points.dart';
import 'package:scad_mobile/src/features/products/data/models/authntification_response.dart';
import 'package:scad_mobile/src/features/products/data/models/web_report_response.dart';
import 'package:scad_mobile/src/features/search/data/data_sources/api_end_points.dart';
import 'package:scad_mobile/src/features/search/data/models/search_dashboard_response_item.dart';
import 'package:scad_mobile/src/features/search/data/models/search_ifp_response.dart';
import 'package:scad_mobile/src/features/search/data/models/search_publications_response.dart';
import 'package:scad_mobile/src/features/search/domain/repositories/search_repository_imports.dart';
import 'package:scad_mobile/src/services/http_service_impl.dart';
import 'package:scad_mobile/src/services/http_services.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class SearchRepositoryImpl implements SearchRepository {
  SearchRepositoryImpl() {
    _httpService = HttpServiceRequests();
  }

  late HttpService _httpService;

  @override
  Future<RepoResponse<List<SearchDashboardResponseItem>>> searchDashboardQuery({
    required String query,
  }) async {
    try {
      final response = await _httpService.get(
        SearchEndPoints.searchDashboards + query,
      );

      if (response.isSuccess) {
        return RepoResponse<List<SearchDashboardResponseItem>>.success(
          response: (response.response['data'] as List<dynamic>)
              .map(
                (e) => SearchDashboardResponseItem.fromJson(
                  e as Map<String, dynamic>,
                ),
              )
              .toList(),
        );
      } else {
        return RepoResponse<List<SearchDashboardResponseItem>>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<List<SearchDashboardResponseItem>>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<SearchIfpResponse>> searchIfpQuery({
    required String query,
  }) async {
    try {
      final response = await _httpService.postJson(
        SearchEndPoints.searchIfp,
        jsonPayloadMap: {'query': query},
      );

      if (response.isSuccess) {
        return RepoResponse<SearchIfpResponse>.success(
          response: SearchIfpResponse.fromJson(response.response),
        );
      } else {
        return RepoResponse<SearchIfpResponse>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<SearchIfpResponse>.error(errorMessage:  LocaleKeys.somethingWentWrong.tr());
    }
  }

  @override
  Future<RepoResponse<PublicationsResult>> searchPublicationQuery({
    required String query,
    required int pageNo,
    required int pageSize,
    required String token,
  }) async {
    try {
      final String endpoint =
          '${SearchEndPoints.publication}&page=$pageNo&pageSize=$pageSize&search=$query';
      final response = await _httpService.get(endpoint, token: token, server: ApiServer.scad);

      if (response.isSuccess) {
        return RepoResponse<PublicationsResult>.success(
          response: PublicationsResult.fromJson(response.response),
        );
      } else {
        return RepoResponse<PublicationsResult>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<PublicationsResult>.error(errorMessage:  LocaleKeys.somethingWentWrong.tr());
    }
  }

  @override
  Future<RepoResponse<Webreports>> searchWebReportQuery({
    required String query,
    required int pageNo,
    required int pageSize,
    required String token,
  }) async {
    try {
      final String endpoint =
          '${SearchEndPoints.webReport}&page=$pageNo&pageSize=$pageSize&search=$query';

      final response = await _httpService.get(endpoint, token: token, server: ApiServer.scad);

      if (response.isSuccess) {
        return RepoResponse<Webreports>.success(
          response: Webreports.fromJson(response.response),
        );
      } else {
        return RepoResponse<Webreports>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<Webreports>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<Authentification>> authentication() async {
    try {
      final String endpoint = ProductsEndPoints.auth;

      final response =
          await _httpService.get(endpoint, server: ApiServer.app);
      if (response.isSuccess) {
        return RepoResponse<Authentification>.success(
          response: Authentification.fromJson(response.response),
        );
      } else {
        return RepoResponse<Authentification>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<Authentification>.error(
        errorMessage:  LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }
}
