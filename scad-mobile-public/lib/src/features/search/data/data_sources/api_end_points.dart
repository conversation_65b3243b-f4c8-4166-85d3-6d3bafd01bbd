import 'package:scad_mobile/src/config/app_config/api_config.dart';

class SearchEndPoints extends ApiConfig {
  static String appPath = ApiConfig.appApiPath;
  static String ifpPath = ApiConfig.ifpApiPath;
  static String scadPath = ApiConfig.scadApiPath;

  static String searchDashboards = '$appPath/product/search/?search=';
  static String searchIfp = '$ifpPath/indicator-search';
  static String publication =
      '$scadPath/o/headless-delivery/v1.0/content-structures/108409/'
      'structured-contents?fields=title,contentFields';
  static String webReport =
      '$scadPath/o/headless-delivery/v1.0/content-structures/1625810/'
      'structured-contents?fields=title,contentFields';
}
