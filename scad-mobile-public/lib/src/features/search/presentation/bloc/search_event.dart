part of 'search_bloc.dart';

abstract class SearchEvent extends Equatable {
  const SearchEvent();

  @override
  List<Object> get props => [];
}

class SearchInitialEvent extends SearchEvent {
  SearchInitialEvent(){
    rnd= Random().nextInt(10000);
  }

  late final int rnd;

  @override
  List<Object> get props => [rnd];
}
class OnSearchDashboardEvent extends SearchEvent {
  const OnSearchDashboardEvent({required this.query});

  final String query;

  @override
  List<Object> get props => [query];
}

class OnSearchDashboardToggleEvent extends SearchEvent {
  const OnSearchDashboardToggleEvent({required this.seeMore});

  final bool seeMore;

  @override
  List<Object> get props => [seeMore];
}

class OnSearchIfpEvent extends SearchEvent {
  const OnSearchIfpEvent({required this.query});

  final String query;

  @override
  List<Object> get props => [query];
}

class OnSearchIfpToggleEvent extends SearchEvent {
  const OnSearchIfpToggleEvent({required this.seeMoreStatusList});

  final List<bool> seeMoreStatusList;

  @override
  List<Object> get props => [seeMoreStatusList];
}

class OnSearchWebReportsEvent extends SearchEvent {
  const OnSearchWebReportsEvent({
    required this.query,
    required this.pageNo,
    required this.pageSize,
    required this.authToken,
  });

  final String query;
  final int pageNo;
  final int pageSize;
  final String authToken;

  @override
  List<Object> get props => [query, pageNo, pageSize, authToken];
}

class OnSearchWebReportsToggleEvent extends SearchEvent {
  const OnSearchWebReportsToggleEvent({required this.seeMore});

  final bool seeMore;

  @override
  List<Object> get props => [seeMore];
}

class OnSearchPublicationsEvent extends SearchEvent {
  const OnSearchPublicationsEvent({
    required this.query,
    required this.pageNo,
    required this.pageSize,
    required this.authToken,
  });

  final String query;
  final int pageNo;
  final int pageSize;
  final String authToken;

  @override
  List<Object> get props => [query, pageNo, pageSize, authToken];
}

class OnSearchPublicationsToggleEvent extends SearchEvent {
  const OnSearchPublicationsToggleEvent({required this.seeMore});

  final bool seeMore;

  @override
  List<Object> get props => [seeMore];
}

class AuthenticationsEvent extends SearchEvent {
  const AuthenticationsEvent();

  @override
  List<Object> get props => [];
}
