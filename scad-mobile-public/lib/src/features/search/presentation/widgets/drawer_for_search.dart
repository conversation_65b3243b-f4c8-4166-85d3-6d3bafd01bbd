// import 'package:auto_route/auto_route.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:scad_mobile/route_manager/route_imports.gr.dart';
// import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';
// import 'package:scad_mobile/src/features/notification/presentation/bloc/notification_bloc.dart';
// import 'package:scad_mobile/src/features/settings/presentation/bloc/setting_bloc.dart';
// import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
// import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
// import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
// import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
//
// class DrawerForSearch extends StatefulWidget {
//   const DrawerForSearch({super.key});
//
//   @override
//   State<DrawerForSearch> createState() => _DrawerForSearchState();
// }
//
// class _DrawerForSearchState extends State<DrawerForSearch> {
//   bool isUnreadAllNotification = false;
//
//   @override
//   void initState() {
//     isUnreadAllNotification =
//         HiveUtilsSettings.getAllNotificationsUnReadStatus();
//     super.initState();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     final bool isLightMode =
//         HiveUtilsSettings.getThemeMode() == ThemeMode.light;
//     final Widget child = Container(
//       height: 11,
//       width: 11,
//       decoration: BoxDecoration(
//         shape: BoxShape.circle,
//         color: AppColors.red,
//       ),
//     );
//     return Padding(
//      padding: const EdgeInsets.only(left: 10, right:10, top: 50,bottom: 10),
//       child: Row(
//         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//         children: [
//           RotatedBox(
//             quarterTurns: DeviceType.isDirectionRTL(context) ? 2 : 0,
//             child: appDrawerController.drawerButton(
//               lightIcon: isLightMode ? false : true,
//             ),
//           ),
//           IconButton(
//             onPressed: () {
//               context.pushRoute(const NotificationListRoute());
//             },
//             icon: Stack(
//               children: [
//                 SvgPicture.asset(
//                   AppImages.icNotification,
//                   colorFilter: const ColorFilter.mode(
//                     Colors.grey,
//                     BlendMode.srcIn,
//                   ),
//                 ),
//                 BlocConsumer<SettingBloc, SettingState>(
//                   listener: (context, state) {
//                     if (state is DefaultSettingSuccessState) {
//                       isUnreadAllNotification =
//                           state.defaultSettingResponse.notificationUnread ??
//                               false;
//                     }
//                   },
//                   builder: (context, state) =>
//                       BlocConsumer<NotificationBloc, NotificationState>(
//                     listener: (context, state) {
//                       if (state is ReadNotificationSuccessState) {
//                         isUnreadAllNotification = state.isAllUnRead;
//                       }
//                     },
//                     builder: (context, state) => isUnreadAllNotification
//                         ? DeviceType.isDirectionRTL(context)
//                             ? Positioned(
//                                 top: 0,
//                                 left: 0,
//                                 child: child,
//                               )
//                             : Positioned(
//                                 top: 0,
//                                 right: 0,
//                                 child: child,
//                               )
//                         : const SizedBox(),
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
