import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/src/common/widgets/card_custom_clipper.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_response_helper.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/presentation/bloc/indicator_card_bloc.dart';
import 'package:scad_mobile/src/features/search/data/models/search_ifp_response.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/api_cache/api_cache.dart';

class SearchIndicatorItem extends StatelessWidget {
  const SearchIndicatorItem({
    required this.searchItemData,
    required this.index,
    super.key,
    this.indicatorDetails,
  });

  final Items searchItemData;
  final IndicatorDetailsResponseHelper? indicatorDetails;
  final int index;

  @override
  Widget build(BuildContext context) {
    return _SearchIndicatorItem(
      searchItemData: searchItemData,
      index: index,
      indicatorDetails: indicatorDetails,
      key: key,
    );
  }
}

class _SearchIndicatorItem extends StatefulWidget {
  const _SearchIndicatorItem({
    required this.searchItemData,
    required this.index,
    super.key,
    this.indicatorDetails,
  });

  final Items searchItemData;
  final IndicatorDetailsResponseHelper? indicatorDetails;
  final int index;

  @override
  State<_SearchIndicatorItem> createState() => _SearchIndicatorItemState();
}

class _SearchIndicatorItemState extends State<_SearchIndicatorItem> {
  Map<String, String> classificationKeyMap = {
    'official_statistics': 'Official Statistics',
    'experimental_statistics': 'Experimental Statistics',
    'analytical_apps': 'Analytical Apps',
  };

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final rtl = DeviceType.isDirectionRTL(context);
    final iconUrl = HiveApiCacheBox.instance.getDomainImageByName(
      widget.indicatorDetails?.indicatorDetails.domain,
      rtl,
    );

    return Padding(
      padding: const EdgeInsets.only(
        right: 24,
        left: 24,
        top: 8,
        bottom: 8,
      ),
      child: Stack(
        children: [
          Positioned(
            right: rtl ? null : 0,
            left: rtl ? 0 : null,
            top: 0,
            height: 40,
            width: 40,
            child: openDetailsButton(context),
          ),
          ClipPath(
            clipper: CardCustomClipper(isRtl: rtl),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
              ),
              padding: const EdgeInsets.fromLTRB(15, 0, 15, 15),
              child: Container(
                constraints: const BoxConstraints(minHeight: 70),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            margin: const EdgeInsets.only(
                              top: 15,
                            ),
                            child: Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(
                                    8,
                                  ),
                                  decoration: BoxDecoration(
                                    color: AppColors.green,
                                    borderRadius: BorderRadius.circular(100),
                                  ),
                                  child: SvgPicture.network(
                                    iconUrl,
                                    width: 10,
                                    height: 10,
                                    colorFilter: const ColorFilter.mode(
                                      Colors.white,
                                      BlendMode.srcIn,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 10),
                                Expanded(
                                  child: Text(
                                    widget.indicatorDetails?.domainName ?? '',
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    style: const TextStyle(
                                      color: AppColors.grey,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 10),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(width: 40),
                      ],
                    ),
                    const SizedBox(height: 10),
                    Padding(
                      padding: const EdgeInsets.only(right: 46),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Flexible(
                            child: Text(
                              widget.searchItemData.title ?? '',
                              style: const TextStyle(
                                color: AppColors.black,
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          const SizedBox(width: 8),
                          if (classificationKeyMap.keys.firstWhere(
                                (k) => classificationKeyMap[k] == widget.searchItemData.contentClassification,
                                orElse: () => '',
                              ) !=
                              'official_statistics')
                            SvgPicture.asset(
                              AppImages.icOfficialActive,
                            )
                          else
                            SvgPicture.asset(
                              AppImages.icExperimentalActive,
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget openDetailsButton(BuildContext context) {
    return Container(
      decoration: const ShapeDecoration(
        gradient: LinearGradient(
          begin: Alignment(-0.65, -0.76),
          end: Alignment(0.65, 0.76),
          colors: [AppColors.blueShade11, AppColors.blueShade22],
        ),
        shape: OvalBorder(),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(100),
          onTap: () {
            context.read<IndicatorCardBloc>().add(
                  GetIndicatorDetailsEvent(
                    id: widget.searchItemData.id!,
                    contentType: classificationKeyMap.keys.firstWhere(
                      (k) => classificationKeyMap[k] == widget.searchItemData.contentClassification,
                      orElse: () => '',
                    ),

                    //TODO: overviewContentType
                    overviewContentType: '',
                  ),
                );
          },
          child: Padding(
            padding: const EdgeInsets.all(5),
            child: RotatedBox(
              quarterTurns: DeviceType.isDirectionRTL(context) ? -1 : 0,
              child: const Icon(
                Icons.arrow_outward_rounded,
                color: AppColors.white,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
