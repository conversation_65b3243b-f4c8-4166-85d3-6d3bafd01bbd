import 'dart:async';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';
import 'package:scad_mobile/src/common/widgets/error_reload_placeholder.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_helper_v2.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_response_helper.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/presentation/bloc/indicator_card_bloc.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/presentation/pages/indicator_card_v2.dart';
import 'package:scad_mobile/src/common/widgets/no_data_placeholder.dart';
import 'package:scad_mobile/src/features/details_page/base/helper/route_helper.dart';
import 'package:scad_mobile/src/features/products/data/models/product_dashboard.dart';
import 'package:scad_mobile/src/features/products/data/models/scad_api/publications_response.dart'
    as publication;
import 'package:scad_mobile/src/features/products/data/models/scad_api/web_report_response.dart';
import 'package:scad_mobile/src/features/products/presentation/widgets/publication_list_item.dart';
import 'package:scad_mobile/src/features/products/presentation/widgets/web_report_item.dart';
import 'package:scad_mobile/src/features/search/data/models/search_ifp_response.dart';
import 'package:scad_mobile/src/features/search/presentation/bloc/search_bloc.dart';
import 'package:scad_mobile/src/features/search/presentation/widgets/flat_appbar_search.dart';
import 'package:scad_mobile/src/features/search/presentation/widgets/list_expansion_button.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';
import 'package:url_launcher/url_launcher.dart';

enum SearchTypes { indicator, compareIndicators, all }

@RoutePage()
class SearchScreen extends StatefulWidget {
  const SearchScreen({
    super.key,
    this.type,
    this.contentType,
    this.initialNodeIdForComparison,
  });

  final SearchTypes? type;
  final String? contentType;
  final String? initialNodeIdForComparison;

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final TextEditingController _textController = TextEditingController();

  List<TableauDashboardResponseItem> dashboardsResult = [];
  List<ResultContentTypes> ifpResult = [];
  List<ItemsWebReport> listWebReport = [];
  List<publication.PublicationItem> listPublication = [];

  bool dashboardExpanded = false;
  bool publicationsExpanded = false;
  bool webReportsExpanded = false;
  List<bool> ifpExpandedStatusList = [];
  int searchResultMinLimit = 5;
  ValueNotifier<bool> isTouchIgnored = ValueNotifier(false);

  List<String> keys = [
    'official_statistics',
    'experimental_statistics',
    'analytical-apps',
  ];

  Map<String, String> classificationKeyMap = {
    'official_statistics': 'Official Statistics',
    'experimental_statistics': 'Experimental Statistics',
    'analytical_apps': 'Analytical Apps',
  };

  ValueNotifier<int> loadingQueue = ValueNotifier(0);
  String authToken = '';
  int pageNoPublication = 1;

  @override
  void initState() {
    super.initState();
    _resetList();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      context.read<SearchBloc>().add(SearchInitialEvent());
    });
  }

  void _resetList() {
    dashboardsResult = [];
    ifpResult = [];
    listWebReport = [];
    listPublication = [];
    ifpExpandedStatusList = [];
  }

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.isLightMode;
    return AppDrawer(
      child: Scaffold(
        body: GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
          child: Column(
            children: [
              BlocListener<IndicatorCardBloc, IndicatorCardState>(
                listener: (context, state) {
                  if(state is IndicatorDetailsErrorState){
                    AppMessage.showOverlayNotificationError(message: state.error);
                    isTouchIgnored.value = false;
                  } else {
                    if (state is IndicatorDetailsSuccessState) {
                      if (state.isFromDriverForSearch != null) return;

                      DetailsPageRouteHelper.indicatorDetailsPageRoute(
                        context,
                        id: state.id,
                        contentType: state.contentType,
                        title: state.indicatorDetails.componentTitle ?? '',
                        indicatorType: state.indicatorDetails.getIndicatorType(),
                      ).then((value) {
                        isTouchIgnored.value = false;
                      });
                    }
                  }
                },
                child: const SizedBox(),
              ),
              FlatAppBarSearch(
                textController: _textController,
                onSubmitted: _onSubmit,
              ),
              ValueListenableBuilder(
                valueListenable: loadingQueue,
                builder: (context, loading, _) {
                  return SizedBox(
                    height: 4,
                    child: loadingQueue.value > 0
                        ? const LinearProgressIndicator()
                        : const SizedBox(),
                  );
                },
              ),
              Expanded(
                child: BlocConsumer<SearchBloc, SearchState>(
                  listener: (context, state) {
                    if (state is SearchLoadingBeginState) {
                      ++loadingQueue.value;
                    } else if (state is SearchLoadingEndState) {
                      --loadingQueue.value;
                    } else if (state is SearchDashboardSuccessState) {
                      if (widget.type != SearchTypes.indicator &&
                          widget.type != SearchTypes.compareIndicators) {
                        dashboardsResult = state.searchResult;
                      }
                    } else if (state is SearchIfpSuccessState) {
                      state.searchResult.removeWhere((e) => e.key?.toLowerCase() == 'reports');
                      if (widget.type == SearchTypes.indicator) {
                        ifpResult = [];
                        ifpExpandedStatusList = [];
                        for (final String key in keys) {
                          for (final ResultContentTypes result
                              in state.searchResult) {
                            if (result.key == key) {
                              ifpResult.add(result);
                              ifpExpandedStatusList.add(false);
                            }
                          }
                        }
                      } else if (widget.type == SearchTypes.compareIndicators) {
                        ifpResult = [];
                        ifpExpandedStatusList = [];
                        for (final ResultContentTypes result
                            in state.searchResult) {
                          if (result.key == widget.contentType) {
                            ifpResult.add(result);
                            ifpExpandedStatusList.add(false);
                          }
                        }
                      } else {
                        ifpExpandedStatusList = [];
                        while (ifpExpandedStatusList.length <
                            state.searchResult.length) {
                          ifpExpandedStatusList.add(false);
                        }
                        ifpResult = state.searchResult;
                      }
                    } else if (state is SearchWebReportsSuccessState) {
                      if (widget.type != SearchTypes.indicator &&
                          widget.type != SearchTypes.compareIndicators) {
                        listWebReport = state.searchResult?.items ?? [];
                        for (final (_, item) in listWebReport.indexed) {
                          for (final (_, content)
                              in (item.contentFields ?? []).indexed) {
                            if (content.label == 'iframeURL English') {
                              item.dashboardEn =
                                  content.contentFieldValue?.data ?? '';
                            } else if (content.label == 'iframeURL Arabic') {
                              item.dashboardAr =
                                  content.contentFieldValue?.data ?? '';
                            }
                          }
                        }
                      }
                    } else if (state is SearchPublicationsSuccessState) {
                      if (widget.type != SearchTypes.indicator &&
                          widget.type != SearchTypes.compareIndicators) {
                        listPublication
                          ..clear()
                          ..addAll(
                              (state.searchResult?.items ?? []).map((e) {
                                e.contentFields?.removeWhere(
                                      (e) => e.contentFieldValue?.document?.contentUrl == null,
                                );
                                return e;
                              }).toList()
                                ..removeWhere((e) => (e.contentFields ?? []).isEmpty),
                          );
                      }
                    } else if (state is SearchDashboardToggleState) {
                      dashboardExpanded = state.seeMore;
                    } else if (state is SearchPublicationsToggleState) {
                      publicationsExpanded = state.seeMore;
                    } else if (state is SearchWebReportsToggleState) {
                      webReportsExpanded = state.seeMore;
                    }
                  },
                  builder: (context, state) {
                    return Stack(
                      alignment: Alignment.center,
                      children: [
                        _resultStatus(state, isLightMode) ??
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 12,
                                    horizontal: 24,
                                  ),
                                  child: Text(
                                    LocaleKeys.searchResults.tr(),
                                    style: TextStyle(
                                      color: isLightMode
                                          ? const Color(0xFF1E2937)
                                          : AppColors.white,
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                      height: 0,
                                    ),
                                  ),
                                ),
                                Expanded(
                                  child: SingleChildScrollView(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.stretch,
                                      children: [
                                        ListView.builder(
                                          key: UniqueKey(),
                                          shrinkWrap: true,
                                          padding: EdgeInsets.zero,
                                          physics:
                                              const NeverScrollableScrollPhysics(),
                                          itemCount: dashboardExpanded
                                              ? dashboardsResult.length
                                              : dashboardsResult.length >
                                                      searchResultMinLimit
                                                  ? searchResultMinLimit
                                                  : dashboardsResult.length,
                                          itemBuilder: (context, i) {
                                            return Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.stretch,
                                              children: [
                                                if (i == 0)
                                                  Padding(
                                                    padding:
                                                        const EdgeInsets.symmetric(
                                                      horizontal: 24,
                                                    ),
                                                    child: Text(
                                                      LocaleKeys.dashboards.tr(),
                                                      style: TextStyle(
                                                        color: isLightMode
                                                            ? const Color(0xFF1E2937)
                                                            : AppColors.white,
                                                        fontSize: 16,
                                                        fontWeight: FontWeight.w500,
                                                      ),
                                                    ),
                                                  ),
                                                _searchResultTextItem(
                                                  i,
                                                  dashboardsResult[i].name!,
                                                  isLightMode,
                                                  () {
                                                    context.pushRoute(
                                                      DashboardWebViewPageRoute(
                                                        title: dashboardsResult[i].name!,
                                                        dashboard: dashboardsResult[i],
                                                      ),
                                                    );
                                                  },
                                                ),
                                              ],
                                            );
                                          },
                                        ),
                                        if (dashboardsResult.isNotEmpty &&
                                            dashboardsResult.length >
                                                searchResultMinLimit)
                                          ListExpansionButton(
                                            seeMore: dashboardExpanded,
                                            onTap: () {
                                              context.read<SearchBloc>().add(
                                                    OnSearchDashboardToggleEvent(
                                                      seeMore: !dashboardExpanded,
                                                    ),
                                                  );
                                            },
                                          ),
                                        ListView.builder(
                                          shrinkWrap: true,
                                          padding: EdgeInsets.zero,
                                          physics:
                                              const NeverScrollableScrollPhysics(),
                                          itemCount: ifpResult.length,
                                          itemBuilder: (context, i) {
                                            final ResultContentTypes item =
                                                ifpResult[i];
                                            final List<Items>? resultItems =
                                                item.items;
                                            resultItems?.removeWhere(
                                              (element) =>
                                                  element.appType == 'dashboard' ||
                                                  element.appType ==
                                                      'tableau_internal',
                                            );
                                            return Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.stretch,
                                              children: [
                                                Padding(
                                                  padding:
                                                      const EdgeInsets.symmetric(
                                                    horizontal: 24,
                                                  ),
                                                  child: Text(
                                                    item.contentType!,
                                                    style: TextStyle(
                                                      color: isLightMode
                                                          ? const Color(0xFF1E2937)
                                                          : AppColors.white,
                                                      fontSize: 16,
                                                      fontWeight: FontWeight.w500,
                                                    ),
                                                  ),
                                                ),
                                                ListView.builder(
                                                  shrinkWrap: true,
                                                  padding: EdgeInsets.zero,
                                                  physics:
                                                      const NeverScrollableScrollPhysics(),
                                                  itemCount: ifpExpandedStatusList[
                                                          i]
                                                      ? (resultItems ?? []).length
                                                      : (resultItems ?? []).length >
                                                              searchResultMinLimit
                                                          ? searchResultMinLimit
                                                          : (resultItems ?? [])
                                                              .length,
                                                  itemBuilder:
                                                      (context, childIndex) {
                                                    final e = (resultItems ??
                                                        [])[childIndex];
                                                    return widget.type ==
                                                            SearchTypes.indicator
                                                        ? Padding(
                                                            padding:
                                                                const EdgeInsets
                                                                    .fromLTRB(
                                                              24,
                                                              6,
                                                              24,
                                                              10,
                                                            ),
                                                            child: IndicatorCardV2(
                                                              key: Key(
                                                                'search.IndicatorCardV2-${e.id}',
                                                              ),
                                                              usedForSearch: true,
                                                              id: e.id!,
                                                              contentType:
                                                                  classificationKeyMap
                                                                      .keys
                                                                      .firstWhere(
                                                                (k) =>
                                                                    classificationKeyMap[
                                                                        k] ==
                                                                    e.contentClassification,
                                                                orElse: () => '',
                                                              ),
                                                              indicatorDetailsForSearch:
                                                                  IndicatorDetailsResponseHelper(
                                                                IndicatorDetailsResponse(
                                                                  type: e.type,
                                                                  indicatorId: e.id,
                                                                  contentClassificationKey:
                                                                      classificationKeyMap
                                                                          .keys
                                                                          .firstWhere(
                                                                    (k) =>
                                                                        classificationKeyMap[
                                                                            k] ==
                                                                        e.contentClassification,
                                                                    orElse: () =>
                                                                        '',
                                                                  ),
                                                                  componentTitle:
                                                                      e.title,
                                                                  // domain: e.topic?.name,
                                                                ),
                                                              ),
                                                            ),
                                                          )
                                                        : _searchResultTextItem(
                                                            childIndex,
                                                            e.title!,
                                                            isLightMode,
                                                            () {
                                                              if (!isTouchIgnored
                                                                  .value) {
                                                                if (item.machineName ==
                                                                    'Glossary') {
                                                                  AutoRouter.of(context).push(
                                                                    GlossaryScreenRoute(
                                                                      initialSearchTerm: e.title ?? '',
                                                                    ),
                                                                  );
                                                                } else if (widget
                                                                        .type ==
                                                                    SearchTypes
                                                                        .compareIndicators) {
                                                                  if (widget
                                                                          .initialNodeIdForComparison ==
                                                                      e.id) {
                                                                    AppMessage.showOverlayNotificationError(
                                                                      message:
                                                                          LocaleKeys.sameIndicatorComparisonError.tr(),
                                                                    );
                                                                  } else {
                                                                    context
                                                                        .read<
                                                                            IndicatorCardBloc>()
                                                                        .add(
                                                                          GetIndicatorDetailsEvent(
                                                                            id: e.id!,
                                                                            contentType:
                                                                                e.type!,
                                                                            overviewContentType:
                                                                                e.contentClassification!,
                                                                          ),
                                                                        );
                                                                  }
                                                                } else {
                                                                  isTouchIgnored
                                                                          .value =
                                                                      true;
                                                                  context
                                                                      .read<
                                                                          IndicatorCardBloc>()
                                                                      .add(
                                                                        GetIndicatorDetailsEvent(
                                                                          id: e
                                                                              .id!,
                                                                          contentType:
                                                                              e.type!,
                                                                          overviewContentType:
                                                                              e.contentClassification!,
                                                                        ),
                                                                      );
                                                                }
                                                              }
                                                            },
                                                          );
                                                  },
                                                ),
                                                if (resultItems!.isNotEmpty &&
                                                    resultItems.length > 5)
                                                  ListExpansionButton(
                                                    seeMore:
                                                        ifpExpandedStatusList[i],
                                                    onTap: () {
                                                      setState(() {
                                                        ifpExpandedStatusList[i] =
                                                            !ifpExpandedStatusList[
                                                                i];
                                                      });
                                                    },
                                                  ),
                                              ],
                                            );
                                          },
                                        ),
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            const SizedBox(height: 10),
                                            if (listWebReport.isNotEmpty)
                                              Padding(
                                                padding: const EdgeInsets.symmetric(
                                                  horizontal: 24,
                                                ),
                                                child: Text(
                                                  LocaleKeys.webReports.tr(),
                                                  style: TextStyle(
                                                    color: isLightMode
                                                        ? const Color(0xFF1E2937)
                                                        : AppColors.white,
                                                    fontSize: 16,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                              ),
                                            ListView.builder(
                                              shrinkWrap: true,
                                              padding: const EdgeInsets.fromLTRB(
                                                20,
                                                6,
                                                20,
                                                0,
                                              ),
                                              physics:
                                                  const NeverScrollableScrollPhysics(),
                                              itemCount: webReportsExpanded
                                                  ? listWebReport.length
                                                  : listWebReport.length >
                                                          searchResultMinLimit
                                                      ? searchResultMinLimit
                                                      : listWebReport.length,
                                              itemBuilder: (e, i) {
                                                return WebReportItem(
                                                  webReport: listWebReport[i],
                                                );
                                              },
                                            ),
                                            if (listWebReport.isNotEmpty &&
                                                listWebReport.length >
                                                    searchResultMinLimit)
                                              ListExpansionButton(
                                                seeMore: webReportsExpanded,
                                                onTap: () {
                                                  context.read<SearchBloc>().add(
                                                        OnSearchWebReportsToggleEvent(
                                                          seeMore:
                                                              !webReportsExpanded,
                                                        ),
                                                      );
                                                },
                                              ),
                                            if (listPublication.isNotEmpty)
                                              Padding(
                                                padding: const EdgeInsets.symmetric(
                                                  horizontal: 24,
                                                ),
                                                child: Text(
                                                  LocaleKeys.publications.tr(),
                                                  style: TextStyle(
                                                    color: isLightMode
                                                        ? const Color(0xFF1E2937)
                                                        : AppColors.white,
                                                    fontSize: 16,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                              ),
                                            ListView.builder(
                                              shrinkWrap: true,
                                              padding: const EdgeInsets.fromLTRB(
                                                24,
                                                6,
                                                24,
                                                0,
                                              ),
                                              physics:
                                                  const NeverScrollableScrollPhysics(),
                                              itemCount: publicationsExpanded
                                                  ? listPublication.length
                                                  : listPublication.length >
                                                          searchResultMinLimit
                                                      ? searchResultMinLimit
                                                      : listPublication.length,
                                              itemBuilder: (e, i) {
                                                return PublicationListItem(
                                                  publicationItem: listPublication[i],
                                                );
                                              },
                                            ),
                                            if (listPublication.isNotEmpty &&
                                                listPublication.length >
                                                    searchResultMinLimit)
                                              ListExpansionButton(
                                                seeMore: publicationsExpanded,
                                                onTap: () {
                                                  context.read<SearchBloc>().add(
                                                        OnSearchPublicationsToggleEvent(
                                                          seeMore:
                                                              !publicationsExpanded,
                                                        ),
                                                      );
                                                },
                                              ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                        ValueListenableBuilder(
                          valueListenable: isTouchIgnored,
                          builder: (context, _, __) {
                            return Visibility(
                              visible: isTouchIgnored.value,
                              child: Center(
                                child: Container(
                                  width: 50,
                                  height: 50,
                                  padding: const EdgeInsets.all(10),
                                  decoration: BoxDecoration(
                                    color: Colors.transparent.withValues(alpha: 0.2),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: const CircularProgressIndicator(),
                                ),
                              ),
                            );
                          },
                        ),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget? _resultStatus(SearchState state, bool isLightMode) {
    if (dashboardsResult.isEmpty &&
        ifpResult.isEmpty &&
        listWebReport.isEmpty &&
        listPublication.isEmpty) {
      if (state is SearchErrorState) {
        return ErrorReloadPlaceholder(
          error: state.error,
          onReload: () {
            _onSubmit(_textController.text);
          },
        );
      } else if (state is SearchInitial) {
        return recentSearches(isLightMode);
      } else if (loadingQueue.value > 0) {
        return const SizedBox();
      } else if (_textController.text != '') {
        return const NoDataPlaceholder();
      }
    }
    return null;
  }

  Column recentSearches(bool isLightMode) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (HiveUtilsSettings.getSearchSuggestions().isNotEmpty)
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 24,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Text(
                  LocaleKeys.recentSearches.tr(),
                  style: TextStyle(
                    color:
                        isLightMode ? const Color(0xFF1E2937) : AppColors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    height: 0,
                  ),
                ),
                const SizedBox(height: 12),
                Wrap(
                  spacing: 16,
                  runSpacing: 16,
                  children: [
                    for (final String suggestion
                        in HiveUtilsSettings.getSearchSuggestions().take(4))
                      searchSuggestionTile(
                        suggestion,
                        isLightMode,
                      ),
                  ],
                ),
              ],
            ),
          ),
        Expanded(
          child: _textController.text != ''
              ? const Center(
                  child: NoDataPlaceholder(),
                )
              : const SizedBox(),
        ),
      ],
    );
  }

  Widget searchSuggestionTile(String suggestion, bool isLightMode) {
    return InkWell(
      borderRadius: BorderRadius.circular(20),
      onTap: () {
        _textController.text = suggestion;
        _onSubmit(suggestion);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 5),
        decoration: BoxDecoration(
          color: !isLightMode
              ? AppColors.blueLightOld.withValues(alpha: 0.3)
              : AppColors.lightBlue,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(
              AppImages.icFeatherSearch,
              colorFilter: ColorFilter.mode(
                !isLightMode ? AppColors.white : AppColors.blackShade4,
                BlendMode.srcIn,
              ),
            ),
            const SizedBox(width: 8),
            Flexible(
              child: Text(
                suggestion,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: AppTextStyles.s14w4cblackShade4.copyWith(
                  color: !isLightMode ? AppColors.white : AppColors.blackShade4,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Future<String> _prepareSaveDir() async {
  //   final String localPath = (await getApplicationDocumentsDirectory()).absolute.path;
  //   final savedDir = Directory(localPath);
  //   if (!savedDir.existsSync()) {
  //     await savedDir.create();
  //   }
  //   return localPath;
  // }

//   Future<void> download(String url, String name) async {
//     final Uri uri = Uri.parse(url);
// AppMessage.showOverlayNotification(
//               '',
//               '${LocaleKeys.downloading.tr()} $name...',
//
//             );
//
//     final taskId = await FlutterDownloader.enqueue(
//       saveInPublicStorage: true,
//       url: uri.toString(),
//       savedDir: await _prepareSaveDir(),
//       showNotification:
//           true, // show download progress in status bar (for Android)
//       openFileFromNotification:
//           true, // click on notification to open downloaded file (for Android)
//     ).then((value) {
//       if (value != null) {
//         Future.delayed(Duration(seconds: 2), () {
//           AppMessage.showOverlayNotification(
//               '',
//               LocaleKeys.fileDownloaded.tr(),
//
//             );
//         });
//       } else {
//         Future.delayed(Duration(seconds: 1), () {
//            AppMessage.showOverlayNotification(
//               '',
//               LocaleKeys.somethingWentWrong.tr(),
//                 msgType: 'error',
//             );
//         });
//       }
//     });
//     // AppToast.showToast('${LocaleKeys.downloading.tr()} $name...');
//     // await FileDownloader.downloadFile(
//     //   url: uri.toString(),
//     //   onDownloadCompleted: (s) {

//     //     AppToast.showToast(LocaleKeys.fileDownloaded.tr());
//     //   },
//     //   onDownloadError: (e) {

//     //     AppToast.showToast(LocaleKeys.somethingWentWrong.tr());
//     //   },
//     // );
//   }

  Future<void> launchToCall(String path) async {
    try {
      final Uri launchUri = Uri.parse(path);
      await launchUrl(launchUri);
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      AppMessage.showOverlayNotificationError(message: LocaleKeys.somethingWentWrong.tr());
    }
  }

  Future<void> _onSubmit(String str) async {
    if (str.trim().isNotEmpty) {
      List<String> searchSuggestions = [];
      searchSuggestions = HiveUtilsSettings.getSearchSuggestions();
      if (!searchSuggestions.contains(str)) {
        searchSuggestions.insert(0, str);
      }
      await HiveUtilsSettings.setSearchSuggestions(
        suggestions: searchSuggestions,
      );
      _resetList();

      if (mounted) {
        // if (widget.type != SearchTypes.indicator) {
        //   context.read<SearchBloc>().add(OnSearchDashboardEvent(query: str));
        // }
        context.read<SearchBloc>().add(OnSearchIfpEvent(query: str));
        context.read<SearchBloc>().add(
              OnSearchWebReportsEvent(
                query: str,
                pageNo: 1,
                pageSize: 5,
                authToken: authToken,
              ),
            );
        context.read<SearchBloc>().add(
              OnSearchPublicationsEvent(
                query: str,
                pageNo: 1,
                authToken: authToken,
              ),
            );
      }
    } else {
      AppMessage.showOverlayNotificationError(message: 'Kindly provide a search query to receive results');
    }
  }

  // Widget _searchEmptyWidget() {
  //   return ListView(
  //     padding: EdgeInsets.zero,
  //     children: [
  //       TitleText(
  //         title: LocaleKeys.recentSearches.tr(),
  //       ),
  //       const SizedBox(
  //         height: 18,
  //       ),
  //       Padding(
  //         padding: const EdgeInsets.symmetric(horizontal: 24),
  //         child: Wrap(
  //           spacing: 10,
  //           runSpacing: 10,
  //           children: [
  //             _recentSearchWidget(),
  //           ],
  //         ),
  //       ),
  //       const SizedBox(
  //         height: 18,
  //       ),
  //       TitleText(
  //         title: LocaleKeys.suggestedIndicators.tr(),
  //       ),
  //       Padding(
  //         padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
  //         child: ListView(
  //           shrinkWrap: true,
  //           padding: const EdgeInsets.only(bottom: 10),
  //           physics: const NeverScrollableScrollPhysics(),
  //           children: const [
  //             Padding(
  //               padding: EdgeInsets.only(bottom: 25),
  //               child: IndicatorCardV2(
  //                 id: '',
  //                 contentType: '',
  //               ),
  //             ),
  //           ],
  //         ),
  //       ),
  //     ],
  //   );
  // }

  // Widget _searchResultWidget() {
  //   return ListView(
  //     padding: EdgeInsets.zero,
  //     children: [
  //       TitleText(
  //         title: LocaleKeys.searchResults.tr(),
  //       ),
  //       const SizedBox(
  //         height: 44,
  //       ),
  //       TitleText(
  //         title: LocaleKeys.suggestedIndicators.tr(),
  //       ),
  //       Container(
  //         margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 15),
  //         decoration: BoxDecoration(
  //           color: AppColors.white,
  //           borderRadius: BorderRadius.circular(20),
  //         ),
  //         child: Column(
  //           children: [
  //             _searchResultSuggestedIndicator('National Statistics'),
  //             _searchResultSuggestedIndicator('National Clarification'),
  //           ],
  //         ),
  //       ),
  //     ],
  //   );
  // }

  // Widget _searchResultSuggestedIndicator(String text) {
  //   return Column(
  //     children: [
  //       Container(
  //         padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 15),
  //         child: Row(
  //           mainAxisSize: MainAxisSize.min,
  //           mainAxisAlignment: MainAxisAlignment.center,
  //           crossAxisAlignment: CrossAxisAlignment.start,
  //           children: [
  //             Expanded(
  //               child: Text.rich(
  //                 maxLines: 1,
  //                 overflow: TextOverflow.ellipsis,
  //                 TextSpan(
  //                   children: [
  //                     const TextSpan(
  //                       text: 'Natio',
  //                       style: TextStyle(
  //                         color: Color(0xFF364151),
  //                         fontSize: 14,
  //                         fontWeight: FontWeight.w500,
  //                         height: 0,
  //                       ),
  //                     ),
  //                     TextSpan(
  //                       text: 'nal Clarification',
  //                       style: TextStyle(
  //                         color: AppColors.grey,
  //                         fontSize: 14,
  //                         fontWeight: FontWeight.w300,
  //                         height: 0,
  //                       ),
  //                     ),
  //                   ],
  //                 ),
  //               ),
  //             ),
  //             const SizedBox(width: 224),
  //             SizedBox(
  //               width: 15,
  //               height: 15,
  //               child: SvgPicture.asset(AppImages.icArrowRight),
  //             ),
  //           ],
  //         ),
  //       ),
  //       const Divider(height: 1, color: Color(0xFFF3F4F6)),
  //     ],
  //   );
  // }

  Widget _searchResultTextItem(
    int index,
    String title,
    bool isLightMode,
    VoidCallback onTap,
  ) {
    return InkWell(
      key: Key(index.toString()),
      onTap: onTap,
      child: Container(
        padding:
            const EdgeInsets.only(top: 10, bottom: 10, left: 24, right: 24),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Expanded(
              child: Text(
                title,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  color: isLightMode ? AppColors.grey : AppColors.greyShade4,
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
            const SizedBox(width: 94),
            SizedBox(
              width: 10,
              height: 10,
              child: RotatedBox(
                quarterTurns: HiveUtilsSettings.isLanguageEnglish ? 0 : 3,
                child: SvgPicture.asset(AppImages.icArrowTopRight),
              ),
            ),
          ],
        ),
      ),
    );
  }

// Widget _recentSearchWidget() {
//   return Container(
//     padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
//     decoration: ShapeDecoration(
//       color: const Color(0xFFE7EEFA),
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.circular(133),
//       ),
//     ),
//     child: Row(
//       mainAxisSize: MainAxisSize.min,
//       mainAxisAlignment: MainAxisAlignment.center,
//       children: [
//         SizedBox(
//           width: 12,
//           height: 12,
//           child: SvgPicture.asset(AppImages.icSearch),
//         ),
//         const SizedBox(width: 10),
//         Flexible(
//           child: Text(
//             'Population',
//             style: const TextStyle(
//               color: Color(0xFF4A5662),
//               fontSize: 14,
//               fontWeight: FontWeight.w400,
//             ),
//             textScaler: TextScaler.linear(textScaleFactor.value),
//           ),
//         ),
//       ],
//     ),
//   );
// }
}
