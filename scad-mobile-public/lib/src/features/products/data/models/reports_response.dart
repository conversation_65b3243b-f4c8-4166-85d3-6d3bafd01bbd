class ReportResponse {
  ReportResponse({
    this.domain,
    this.nodes,
  });

  ReportResponse.fromJson(Map<String, dynamic> json)
      : domain = json['domain'] as String?,
        nodes = (json['nodes'] as List?)
            ?.map((dynamic e) => ReportNode.fromJson(e as Map<String, dynamic>))
            .toList();
  final String? domain;
  final List<ReportNode>? nodes;

  Map<String, dynamic> toJson() => {
        'domain': domain,
        'nodes': nodes?.map((e) => e.toJson()).toList(),
      };
}

class ReportNode {
  ReportNode({
    this.id,
    this.title,
    this.subtitle,
    this.newsletterURL,
    this.policyGuide,
    this.searchTags,
    this.updated,
    this.imgSrc,
    this.domain,
    this.publicationDate,
    this.contentClassification,
    this.contentClassificationKey,
  });

  ReportNode.fromJson(Map<String, dynamic> json)
      : id = json['id'] as String?,
        title = json['title'] as String?,
        subtitle = json['subtitle'] as String?,
        newsletterURL = json['newsletter_URL'] as String?,
        policyGuide = json['policy_guide'] as String?,
        searchTags = json['search_tags'] as List?,
        updated = json['updated'] as String?,
        imgSrc = json['imgSrc'] as String?,
        domain = json['domain'] as String?,
        publicationDate = json['publication_date'] as String?,
        contentClassification = json['content_classification'] as String?,
        contentClassificationKey =
            json['content_classification_key'] as String?;
  final String? id;
  final String? title;
  final String? subtitle;
  final String? newsletterURL;
  final String? policyGuide;
  final List<dynamic>? searchTags;
  final String? updated;
  final String? imgSrc;
  final String? domain;
  final String? publicationDate;
  final String? contentClassification;
  final String? contentClassificationKey;

  Map<String, dynamic> toJson() => {
        'id': id,
        'title': title,
        'subtitle': subtitle,
        'newsletter_URL': newsletterURL,
        'policy_guide': policyGuide,
        'search_tags': searchTags,
        'updated': updated,
        'imgSrc': imgSrc,
        'domain': domain,
        'publication_date': publicationDate,
        'content_classification': contentClassification,
        'content_classification_key': contentClassificationKey,
      };
}
