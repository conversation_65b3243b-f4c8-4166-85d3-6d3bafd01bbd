class ForecastResponse {
  ForecastResponse({
    this.domain,
    this.nodes,
  });
  ForecastResponse.fromJson(Map<String, dynamic> json)
      : domain = json['domain'] as String?,
        nodes = (json['nodes'] as List?)
            ?.map(
                (dynamic e) => ForecastNode.fromJson(e as Map<String, dynamic>))
            .toList();
  final String? domain;
  final List<ForecastNode>? nodes;

  Map<String, dynamic> toJson() =>
      {'domain': domain, 'nodes': nodes?.map((e) => e.toJson()).toList()};
}

class ForecastNode {
  ForecastNode({
    this.id,
    this.title,
    this.subtitle,
    this.pageIcon,
    this.pageLightIcon,
    this.type,
    this.domain,
    this.theme,
    this.subtheme,
    this.product,
    this.contentClassification,
    this.contentClassificationKey,
    this.note,
    this.changed,
  });

  ForecastNode.fromJson(Map<String, dynamic> json)
      : id = json['id'] as String?,
        title = json['title'] as String?,
        subtitle = json['subtitle'] as String?,
        pageIcon = json['page_icon'] as String?,
        pageLightIcon = json['page_light_icon'] as String?,
        type = json['type'] as String?,
        domain = json['domain'] as String?,
        theme = json['theme'] as String?,
        subtheme = json['subtheme'] as String?,
        product = json['product'] as String?,
        contentClassification = json['content_classification'] as String?,
        contentClassificationKey =
            json['content_classification_key'] as String?,
        note = json['note'] as String?,
        changed = json['changed'] as String?;
  final String? id;
  final String? title;
  final String? subtitle;
  final String? pageIcon;
  final String? pageLightIcon;
  final String? type;
  final String? domain;
  final String? theme;
  final String? subtheme;
  final String? product;
  final String? contentClassification;
  final String? contentClassificationKey;
  final String? note;
  final String? changed;

  Map<String, dynamic> toJson() => {
        'id': id,
        'title': title,
        'subtitle': subtitle,
        'page_icon': pageIcon,
        'page_light_icon': pageLightIcon,
        'type': type,
        'domain': domain,
        'theme': theme,
        'subtheme': subtheme,
        'product': product,
        'content_classification': contentClassification,
        'content_classification_key': contentClassificationKey,
        'note': note,
        'changed': changed,
      };
}
