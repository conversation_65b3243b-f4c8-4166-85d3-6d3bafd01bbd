class Webreports {
  final Actions? actions;
  final List<dynamic>? facets;
  final List<ItemsWebReport>? items;
  final int? lastPage;
  final int? page;
  final int? pageSize;
  final int? totalCount;

  Webreports({
    this.actions,
    this.facets,
    this.items,
    this.lastPage,
    this.page,
    this.pageSize,
    this.totalCount,
  });

  Webreports.fromJson(Map<String, dynamic> json)
      : actions = (json['actions'] as Map<String, dynamic>?) != null
            ? Actions.fromJson(json['actions'] as Map<String, dynamic>)
            : null,
        facets = json['facets'] as List?,
        items = (json['items'] as List?)
            ?.map((dynamic e) =>
                ItemsWebReport.fromJson(e as Map<String, dynamic>))
            .toList(),
        lastPage = json['lastPage'] as int?,
        page = json['page'] as int?,
        pageSize = json['pageSize'] as int?,
        totalCount = json['totalCount'] as int?;

  Map<String, dynamic> toJson() => {
        'actions': actions?.toJson(),
        'facets': facets,
        'items': items?.map((e) => e.toJson()).toList(),
        'lastPage': lastPage,
        'page': page,
        'pageSize': pageSize,
        'totalCount': totalCount
      };
}

class Actions {
  final Get? get;

  Actions({
    this.get,
  });

  Actions.fromJson(Map<String, dynamic> json)
      : get = (json['get'] as Map<String, dynamic>?) != null
            ? Get.fromJson(json['get'] as Map<String, dynamic>)
            : null;

  Map<String, dynamic> toJson() => {'get': get?.toJson()};
}

class Get {
  final String? method;
  final String? href;

  Get({
    this.method,
    this.href,
  });

  Get.fromJson(Map<String, dynamic> json)
      : method = json['method'] as String?,
        href = json['href'] as String?;

  Map<String, dynamic> toJson() => {'method': method, 'href': href};
}

class ItemsWebReport {
  final List<ContentFields>? contentFields;
  final String? title;
  String darshboardEn = '';
  String darshboardAr = '';
  ItemsWebReport(
      {this.contentFields,
      this.title,
      required this.darshboardEn,
      required this.darshboardAr});

  ItemsWebReport.fromJson(Map<String, dynamic> json)
      : contentFields = (json['contentFields'] as List?)
            ?.map((dynamic e) =>
                ContentFields.fromJson(e as Map<String, dynamic>))
            .toList(),
        title = json['title'] as String?,
        darshboardEn = '',
        darshboardAr = '';
  Map<String, dynamic> toJson() => {
        'contentFields': contentFields?.map((e) => e.toJson()).toList(),
        'title': title,
        'darshboardEn': darshboardEn,
        'darshboardAr': darshboardAr,
      };
}

class ContentFields {
  final ContentFieldValue? contentFieldValue;
  final String? dataType;
  final String? inputControl;
  final String? label;
  final String? name;
  final List<dynamic>? nestedContentFields;
  final bool? repeatable;

  ContentFields({
    this.contentFieldValue,
    this.dataType,
    this.inputControl,
    this.label,
    this.name,
    this.nestedContentFields,
    this.repeatable,
  });

  ContentFields.fromJson(Map<String, dynamic> json)
      : contentFieldValue = (json['contentFieldValue'] != null)
            ? ContentFieldValue.fromJson(
                Map<String, dynamic>.from(json['contentFieldValue'] as Map),
              )
            : null,
        dataType = json['dataType'] as String?,
        inputControl = json['inputControl'] as String?,
        label = json['label'] as String?,
        name = json['name'] as String?,
        nestedContentFields = json['nestedContentFields'] as List?,
        repeatable = json['repeatable'] as bool?;

  Map<String, dynamic> toJson() => {
        'contentFieldValue': contentFieldValue?.toJson(),
        'dataType': dataType,
        'inputControl': inputControl,
        'label': label,
        'name': name,
        'nestedContentFields': nestedContentFields,
        'repeatable': repeatable
      };
}

class ContentFieldValue {
  final String? data;

  ContentFieldValue({
    this.data,
  });

  ContentFieldValue.fromJson(Map<String, dynamic> json)
      : data = json['data'] as String?;

  Map<String, dynamic> toJson() => {'data': data};
}
