class Authentification {
  final String? accessToken;
  final String? tokenType;
  final int? expiresIn;
  final String? scope;

  Authentification({
    this.accessToken,
    this.tokenType,
    this.expiresIn,
    this.scope,
  });

  Authentification.fromJson(Map<String, dynamic> json)
      : accessToken = json['access_token'] as String?,
        tokenType = json['token_type'] as String?,
        expiresIn = json['expires_in'] as int?,
        scope = json['scope'] as String?;

  Map<String, dynamic> toJson() => {
        'access_token': accessToken,
        'token_type': tokenType,
        'expires_in': expiresIn,
        'scope': scope
      };
}
