class ProductResponse {
  String? id;
  String? name;
  String? lightIcon;
  String? darkIcon;
  int? nodeCount;
  bool? isSelected;
  List<DomainsResponse>? domains;

  ProductResponse({
    this.id,
    this.name,
    this.lightIcon,
    this.darkIcon,
    this.nodeCount,
    this.isSelected,
    this.domains,
  });

  ProductResponse.fromJson(Map<String, dynamic> json) {
    id = json['id'] as String?;
    name = json['name'] as String?;
    lightIcon = json['light_icon'] as String?;
    darkIcon = json['dark_icon'] as String?;
    nodeCount = json['node_count'] as int?;
    isSelected = json['isSelected'] as bool?;
    domains = (json['domains'] as List?)?.map((dynamic e) => DomainsResponse.fromJson(e as Map<String,dynamic>)).toList();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['id'] = id;
    json['name'] = name;
    json['light_icon'] = lightIcon;
    json['dark_icon'] = darkIcon;
    json['node_count'] = nodeCount;
    json['isSelected'] = isSelected;
    json['domains'] = domains?.map((e) => e.toJson()).toList();
    return json;
  }
}

class DomainsResponse {
  String? id;
  String? name;
  String? darkIcon;
  String? lightIcon;
  List<DomainItemResponse>? items;

  DomainsResponse({
    this.id,
    this.name,
    this.darkIcon,
    this.lightIcon,
    this.items,
  });

  DomainsResponse.fromJson(Map<String, dynamic> json) {
    id = json['id'] as String?;
    name = json['name'] as String?;
    darkIcon = json['dark_icon'] as String?;
    lightIcon = json['light_icon'] as String?;
    items = (json['items'] as List?)?.map((dynamic e) => DomainItemResponse.fromJson(e as Map<String,dynamic>)).toList();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['id'] = id;
    json['name'] = name;
    json['dark_icon'] = darkIcon;
    json['light_icon'] = lightIcon;
    json['items'] = items?.map((e) => e.toJson()).toList();
    return json;
  }
}

class DomainItemResponse {
  String? id;
  String? title;
  String? contentType;
  dynamic note;
  Category? category;
  String? appType;

  DomainItemResponse({
    this.id,
    this.title,
    this.contentType,
    this.note,
    this.category,
    this.appType,
  });

  DomainItemResponse.fromJson(Map<String, dynamic> json) {
    id = json['id'] as String?;
    title = json['title'] as String?;
    contentType = json['content_type'] as String?;
    note = json['note'];
    // category = (json['category'] as Map<String,dynamic>?) != null ? Category.fromJson(json['category'] as Map<String,dynamic>) : null;
    appType = json['app_type'] as String?;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['id'] = id;
    json['title'] = title;
    json['content_type'] = contentType;
    json['note'] = note;
    json['category'] = category?.toJson();
    json['app_type'] = appType;
    return json;
  }
}

class Category {
  String? id;
  String? name;
  String? darkIcon;
  String? lightIcon;

  Category({
    this.id,
    this.name,
    this.darkIcon,
    this.lightIcon,
  });

  Category.fromJson(Map<String, dynamic> json) {
    id = json['id'] as String?;
    name = json['name'] as String?;
    darkIcon = json['dark_icon'] as String?;
    lightIcon = json['light_icon'] as String?;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['id'] = id;
    json['name'] = name;
    json['dark_icon'] = darkIcon;
    json['light_icon'] = lightIcon;
    return json;
  }
}