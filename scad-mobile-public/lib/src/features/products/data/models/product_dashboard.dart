class TableauDashboardResponseItem {
  TableauDashboardResponseItem({
    this.uuid,
    this.domainId,
    this.domainName,
    this.domainNameAr,
    this.name,
    this.nameAr,
    this.productType,
    this.url,
    this.urlAr,
    this.urlDark,
    this.urlArDark,
  });

  TableauDashboardResponseItem.fromJson(Map<String, dynamic> json) {
    uuid = json['uuid'] as String?;
    domainId = json['domain_id'] as String?;
    domainName = json['domain_name'] as String?;
    domainNameAr = json['domain_name_ar'] as String?;
    name = json['name'] as String?;
    nameAr = json['name_ar'] as String?;
    productType = json['product_type'] as String?;
    url = json['url'] as String?;
    urlAr = json['url_ar'] as String?;
    urlDark = json['url_dark'] as String?;
    urlArDark = json['url_dark_ar'] as String?;
  }

  String? uuid;
  String? domainId;
  String? domainName;
  String? domainNameAr;
  String? name;
  String? nameAr;
  String? productType;
  String? url;
  String? urlAr;
  String? urlDark;
  String? urlArDark;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['uuid'] = uuid;
    json['domain_id'] = domainId;
    json['domain_name'] = domainName;
    json['domain_name_ar'] = domainNameAr;
    json['name'] = name;
    json['name_ar'] = nameAr;
    json['product_type'] = productType;
    json['url'] = url;
    json['url_ar'] = urlAr;
    json['url_dark'] = urlDark;
    json['url_dark_ar'] = urlArDark;

    return json;
  }
}
