import 'package:auto_route/annotations.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:scad_mobile/src/common/widgets/appbar/flat_app_bar.dart';
import 'package:webview_flutter/webview_flutter.dart';

@RoutePage()
class ReportWebViewPage extends StatefulWidget {
  const ReportWebViewPage({
    required this.title,
    required this.url,
    super.key,
  });

  final String title;
  final String url;

  @override
  State<ReportWebViewPage> createState() => _ReportWebViewPageState();
}

class _ReportWebViewPageState extends State<ReportWebViewPage> {
  WebViewController? controller;

  @override
  void initState() {
    super.initState();
    setUpWebView();
  }

  Future<void> setUpWebView() async {
    controller = WebViewController();
    await controller?.setJavaScriptMode(JavaScriptMode.unrestricted);
    await controller?.loadRequest(Uri.parse(widget.url));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          FlatAppBar(
            title: widget.title,
          ),
          Expanded(
            child: WebViewWidget(
              controller: controller!,
            ),
          ),
        ],
      ),
    );
  }
}
