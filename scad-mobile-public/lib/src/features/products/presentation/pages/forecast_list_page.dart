import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/presentation/pages/indicator_card_v2.dart';
import 'package:scad_mobile/src/common/widgets/no_data_placeholder.dart';
import 'package:scad_mobile/src/features/products/data/models/forecast_response.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_api_cache.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';

class ForecastListPage extends StatefulWidget {
  const ForecastListPage({
    required this.scrollController, super.key,
    this.forecast = const [],
  });

  final List<ForecastResponse> forecast;
  final ScrollController scrollController;

  @override
  State<ForecastListPage> createState() => _ForecastListPageState();
}

class _ForecastListPageState extends State<ForecastListPage> {
  final bool isLightMode = HiveUtilsSettings.getThemeMode() == ThemeMode.light;

  ValueNotifier<int> selectedDomainIndex = ValueNotifier<int>(0);

  @override
  Widget build(BuildContext context) {
    final rtl = DeviceType.isDirectionRTL(context);
    return ValueListenableBuilder(
      valueListenable: selectedDomainIndex,
      builder: (context, i, w) {
        return Column(
          children: [
            if (widget.forecast.isNotEmpty)
              Container(
                height: 45,
                margin: const EdgeInsets.only(top: 24, left: 24, right: 24),
                decoration: BoxDecoration(
                  color: isLightMode ? AppColors.white : AppColors.blackShade8,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    IconButton(
                      onPressed: () {
                        if (selectedDomainIndex.value != 0) {
                          selectedDomainIndex.value--;
                        }
                      },
                      icon: Icon(
                        Icons.chevron_left_rounded,
                        size: 26,
                        color: selectedDomainIndex.value != 0
                            ? AppColors.blue
                            : AppColors.greyShade4,
                      ),
                    ),
                    Flexible(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SvgPicture.network(
                            HiveUtilsApiCache.getDomainImageByName(
                              widget.forecast[selectedDomainIndex.value].domain,
                              rtl,
                            ),
                            height: 22,
                            width: 22,
                            colorFilter: ColorFilter.mode(
                              AppColors.blueLight,
                              BlendMode.srcIn,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Flexible(
                            child: Text(
                              widget.forecast[selectedDomainIndex.value]
                                      .domain ??
                                  '',
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: AppTextStyles.s18w5cBlackShade1.copyWith(
                                color: !isLightMode ? AppColors.white : null,
                              ),
                              textScaler:
                                  TextScaler.linear(textScaleFactor.value),
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        if (selectedDomainIndex.value + 1 <
                            widget.forecast.length) {
                          selectedDomainIndex.value++;
                        }
                      },
                      icon: Icon(
                        Icons.chevron_right_rounded,
                        size: 26,
                        color: selectedDomainIndex.value + 1 <
                                widget.forecast.length
                            ? AppColors.blueLight
                            : AppColors.greyShade4,
                      ),
                    ),
                  ],
                ),
              ),
            const SizedBox(height: 8),
            Expanded(child: _forecastList()),
          ],
        );
      },
    );
  }

  Widget _forecastList() {
    if (widget.forecast.isEmpty) {
      return const Center(
        child: NoDataPlaceholder(),
      );
    }

    final List<ForecastNode> items =
        widget.forecast[selectedDomainIndex.value].nodes ?? [];

    return items.isEmpty
        ? const Center(child: NoDataPlaceholder())
        : ListView.separated(
            shrinkWrap: true,
            controller: widget.scrollController,
            // physics: const NeverScrollableScrollPhysics(),
            padding: const EdgeInsets.fromLTRB(20, 15, 20, 100),
            itemCount: items.length,
            separatorBuilder: (context, index) => const SizedBox(height: 25),
            itemBuilder: (context, index) {
              final ForecastNode item = items[index];
              return IndicatorCardV2(
                key: Key(
                  'products.indicators.IndicatorCardV2.${item.id}${DateTime.now().microsecondsSinceEpoch}',
                ),
                id: item.id!,
                contentType: 'statistics-insights',
              );
            },
          );
  }
}
