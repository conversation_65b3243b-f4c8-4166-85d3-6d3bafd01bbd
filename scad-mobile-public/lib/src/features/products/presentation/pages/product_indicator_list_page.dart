import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/presentation/pages/indicator_card_v2.dart';
import 'package:scad_mobile/src/common/widgets/no_data_placeholder.dart';
import 'package:scad_mobile/src/features/products/data/models/forecast_response.dart';
import 'package:scad_mobile/src/features/products/data/models/response/product_response1.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_api_cache.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';

class ProductIndicatorListPage extends StatefulWidget {
  const ProductIndicatorListPage({
    required this.product,
    required this.scrollController,
    super.key,
    this.forecast = const [],
  });

  final ProductResponse product;
  final List<ForecastResponse> forecast;
  final ScrollController scrollController;

  @override
  State<ProductIndicatorListPage> createState() =>
      _ProductIndicatorListPageState();
}

class _ProductIndicatorListPageState extends State<ProductIndicatorListPage> {
  ValueNotifier<int> selectedDomainIndex = ValueNotifier<int>(0);

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return ValueListenableBuilder(
      valueListenable: selectedDomainIndex,
      builder: (context, i, w) {
        return Column(
          children: [
            if((widget.product.domains??[]).isNotEmpty)
            Container(
              height: 45,
              margin: const EdgeInsets.only(top: 24, left: 24, right: 24),
              decoration: BoxDecoration(
                color: isLightMode ? AppColors.white : AppColors.blueShade32,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  IconButton(
                    onPressed: () {
                      if (selectedDomainIndex.value != 0) {
                        selectedDomainIndex.value--;
                        // if (products.length <= 3) {
                        //   _onDomainChanged(
                        //     products[selectedProductIndex.value]
                        //         .domains[selectedDomainIndex.value],
                        //   );
                        // }
                      }
                    },
                    icon: Icon(
                      Icons.chevron_left_rounded,
                      size: 26,
                      color: selectedDomainIndex.value != 0
                          ? AppColors.blue
                          : AppColors.greyShade4,
                    ),
                  ),
                  Flexible(
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SvgPicture.network(
                          HiveUtilsApiCache.getDomainImage(widget
                              .product.domains?.elementAt(selectedDomainIndex.value).id),
                          height: 22,
                          width: 22,
                          colorFilter: isLightMode ? null:  ColorFilter.mode(AppColors.white, BlendMode.srcIn),
                          // colorFilter: ColorFilter.mode(
                          //     AppColors.blueLight, BlendMode.srcIn),
                        ),
                        const SizedBox(width: 12),
                        Flexible(
                          child: Text(
                            widget.product.domains?.elementAt(selectedDomainIndex.value).name??'',
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: AppTextStyles.s18w5cBlackShade1.copyWith(
                              color: isLightMode
                                  ? AppColors.blackShade1
                                  : AppColors.white,
                            ),
                            textScaler:
                                TextScaler.linear(textScaleFactor.value),
                          ),
                        ),
                        const SizedBox(width: 15),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      if (selectedDomainIndex.value + 1 <
                          (widget.product.domains??[]).length) {
                        selectedDomainIndex.value++;

                        // if (products.length <= 3) {
                        //   _onDomainChanged(
                        //     products[selectedProductIndex.value]
                        //         .domains[selectedDomainIndex.value],
                        //   );
                        // }
                      }
                    },
                    icon: Icon(
                      Icons.chevron_right_rounded,
                      size: 26,
                      color: selectedDomainIndex.value + 1 <
                              (widget.product.domains??[]).length
                          ? isLightMode ? 
                              AppColors.blueLight
                              : AppColors.blueLightOld
                          : AppColors.greyShade4,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(
              height: 8,
            ),
            Expanded(
              child: widget.forecast.isNotEmpty
                  ? _forecastList()
                  : _otherProducts(),
            ),
          ],
        );
      },
    );
  }

  Widget _forecastList() {
    if ((widget.product.domains??[]).isEmpty) {
      return const Center(
        child: NoDataPlaceholder(),
      );
    }

    final List<ForecastNode> items =
        widget.forecast[selectedDomainIndex.value].nodes ?? [];

    return items.isEmpty
        ? const Center(child: NoDataPlaceholder())
        : ListView.separated(
            shrinkWrap: true,
            // controller: widget.scrollController,
            // physics: const NeverScrollableScrollPhysics(),
            padding: const EdgeInsets.fromLTRB(20, 15, 20, 150),
            itemCount: items.length,
            separatorBuilder: (context, index) => const SizedBox(height: 25),
            itemBuilder: (context, index) {
              final ForecastNode item = items[index];
              return IndicatorCardV2(
                key: Key(
                  'products.indicators.IndicatorCardV2.${item.id}${DateTime.now().microsecondsSinceEpoch}',
                ),
                id: item.id!,
                contentType: item.contentClassificationKey!, //item.contentType
              );
            },
          );
  }

  Widget _otherProducts() {
    if ((widget.product.domains??[]).isEmpty) {
      return const Center(
        child: NoDataPlaceholder(),
      );
    }

    final List<DomainItemResponse> items =
        widget.product.domains?.elementAt(selectedDomainIndex.value).items??[];
          // these will be web pages. need to show this in  web view.
          // ..removeWhere(
          //   (element) => [
          //     'eci_insights',
          //     'eci-insights',
          //     'basket-insights',
          //     'basket_insights',
          //   ].contains(element.type.toLowerCase()),
          // );

    return items.isEmpty
        ? const Center(child: NoDataPlaceholder())
        : ListView.separated(
            shrinkWrap: true,
            controller: widget.scrollController,
            // physics: const NeverScrollableScrollPhysics(),
            padding: const EdgeInsets.fromLTRB(20, 15, 20, 150),
            itemCount: items.length,
            separatorBuilder: (context, index) => const SizedBox(height: 25),
            itemBuilder: (context, index) {
              final DomainItemResponse item = items[index];
              return IndicatorCardV2(
                key: Key(
                  'products.indicators.IndicatorCardV2.${item.id}${DateTime.now().microsecondsSinceEpoch}',
                ),
                id: item.id??'',

                //TODO: content-type is updated in scad server only.
                contentType: item.contentType??''
              );
            },
          );
  }

  // void _onDomainChanged(DomainsResponse selectedDomainItem) {
  //   List<String> idList =
  //       []; // selectedDomainItem.items.map((e) => e.id).toList();
  //   final type = selectedDomainItem.items.first.contentType;
  //
  //   idList = selectedDomainItem.items
  //       .where((e) => e.contentType == type)
  //       .toList()
  //       .map((e) => e.id)
  //       .toList();
  // }
}
