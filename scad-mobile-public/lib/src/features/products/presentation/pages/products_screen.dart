import 'dart:async';
import 'dart:ui' as ui;

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/src/common/widgets/app_sliding_tab.dart';
import 'package:scad_mobile/src/common/widgets/appbar/flat_app_bar.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/showcaseview.dart';
import 'package:scad_mobile/src/features/home/<USER>/bloc/home_bloc/home_bloc.dart';
import 'package:scad_mobile/src/features/products/presentation/bloc/products_bloc.dart';
import 'package:scad_mobile/src/features/products/presentation/pages/tab_pages/dashboards_page.dart';
import 'package:scad_mobile/src/features/products/presentation/pages/tab_pages/publications_page.dart';
import 'package:scad_mobile/src/features/products/presentation/pages/tab_pages/web_reports_page.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

@RoutePage()
class Products extends StatefulWidget {
  const Products({super.key, this.isFromDetailsScreen = false});

  final bool isFromDetailsScreen;

  @override
  State<Products> createState() => _ProductsState();
}

class _ProductsState extends State<Products>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {

  @override
  bool get wantKeepAlive => true;

  final bool isLightMode = HiveUtilsSettings.isLightMode;
  List<AppSlidingTabItem> slidingTabList = [];
  TabController? tabController;
  PageController pageController = PageController();

  ScrollController scrollControllerPublications = ScrollController();
  ScrollController webReportsScrollController = ScrollController();
  ScrollController scenarioScrollController = ScrollController();
  ScrollController insightsDiscoveryScrollController = ScrollController();
  ScrollController forecastScrollController = ScrollController();

  ProductTabType? preselectTabType;

  List<GlobalKey> steps = [];
  BuildContext? myContext;
  String tabKey = '0';

  ValueNotifier<int> selectedIndex = ValueNotifier(0);

  @override
  void initState() {
    super.initState();

    slidingTabList = [
      AppSlidingTabItem(
        label: LocaleKeys.dashboards.tr(),
        object: ProductTab(
          title: LocaleKeys.dashboards.tr(),
          productTabType: ProductTabType.dashboards,
        ),
      ),
      AppSlidingTabItem(
        label: LocaleKeys.webReports.tr(),
        object: ProductTab(
          title: LocaleKeys.webReports.tr(),
          productTabType: ProductTabType.webReports,
        ),
      ),
      AppSlidingTabItem(
        label: LocaleKeys.publications.tr(),
        object: ProductTab(
          title: LocaleKeys.publications.tr(),
          productTabType: ProductTabType.publications,
        ),
      ),
    ];

    tabController = TabController(
      length: slidingTabList.length,
      vsync: this,
    );

    SchedulerBinding.instance.addPostFrameCallback((_){
      if (HiveUtilsSettings.getUserGuideStatus() == UserGuides.Products) {
        steps = [];
        for (var i = 0; i <  slidingTabList.length; i++) {
          steps.add(GlobalKey(debugLabel: 'steps-slidingTabList-$i'));
        }
        ShowCaseWidget.of(myContext ?? context).startShowCase(steps);
      }
    });
  }

  @override
  void dispose() {
    tabController?.dispose();
    pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return ShowCaseWidget(
      builder: Builder(
        builder: (context) {
          myContext = context;
          return Scaffold(
            body: Column(
              children: [
                ValueListenableBuilder(
                  valueListenable: selectedIndex,
                  builder: (context, index, _) {
                    return FlatAppBar(
                      key: ValueKey(index.toString()),
                      title: LocaleKeys.disseminationProducts.tr(),
                      mainScreen: true,
                    );
                  },
                ),
                Expanded(
                  child: BlocConsumer<ProductsBloc, ProductsState>(
                    listener: (context, state) {
                      if(state is ProductsResetState){
                        tabController?.animateTo(0);
                        pageController.jumpToPage(0);
                        Future.delayed(const Duration(milliseconds: 240), () {
                          setState(() {});
                        });
                      }
                      if (state is ProductsSelectTabState) {
                        if ((tabController?.length ?? 0) > 0) {
                          for (int i = 0; i < slidingTabList.length; i++) {
                            if (slidingTabList[i].object?.productTabType == state.type) {
                              tabController?.animateTo(i);
                              pageController.jumpToPage(i);
                              break;
                            }
                          }
                        } else {
                          preselectTabType = state.type;
                        }
                      }
                    },
                    builder: (context, state) {
                      return Column(
                        children: [
                          AppSlidingTab(
                            key: Key('tab-${slidingTabList.map((e)=>e.label).join('-')}-$tabKey'),
                            userGuideStepsKeyList: steps,
                            horizontalPadding: 24,
                            onTabChange: (i) {
                              try {
                                pageController.jumpToPage(i);
                                selectedIndex.value = i;
                              } catch (e) {
                                // error
                              }
                            },
                            initialTabIndex: selectedIndex.value,
                            tabs: slidingTabList,
                            pageController: pageController,
                            tabController: tabController,
                          ),
                          const SizedBox(height: 20),
                          if (slidingTabList.isNotEmpty)
                            Divider(
                              height: 1,
                              color: isLightMode
                                  ? AppColors.greyShade1
                                  : AppColors.blackShade4,
                            ),
                          if (slidingTabList.isEmpty)
                            const Center(child: CircularProgressIndicator())
                          else
                            Expanded(
                            child: PageView(
                              key: Key(slidingTabList.map((e)=> e.label).toList().join('-')),
                              controller: pageController,
                              onPageChanged: (i) {
                                final tab = slidingTabList.elementAt(i).object;
                                if (tab is ProductTab && tab.productTabType == ProductTabType.dashboards) {
                                  context.read<HomeBloc>().add(const CheckMaintenanceEvent());
                                }

                                  if ((HiveUtilsSettings.getUserGuideStatus() !=
                                        UserGuides.Products) &&
                                    slidingTabList[i].onTap != null) {
                                  int newIndex;
                                  if (selectedIndex.value < i) {
                                    newIndex = i + 1;
                                  } else {
                                    newIndex = i - 1;
                                  }

                                  if(newIndex >= slidingTabList.length) {
                                    newIndex = selectedIndex.value;
                                  }

                                  tabController?.animateTo(
                                    newIndex,
                                    duration: Duration.zero,
                                  );
                                  pageController.jumpToPage(newIndex);
                                } else {
                                  selectedIndex.value = i;
                                  tabController?.animateTo(i);
                                }
                              },
                              children: _productTabPages(),
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  List<Widget> _productTabPages() {
    return [
      ...slidingTabList.map((e) {
        if ((e.object as ProductTab?)?.productTabType == ProductTabType.dashboards) {
          return const DashboardsPage();
        } else if ((e.object as ProductTab?)?.productTabType == ProductTabType.publications) {
          return const PublicationsPage();
        } else if ((e.object as ProductTab?)?.productTabType == ProductTabType.webReports) {
          return const WebReportsPage();
        } else {
          return const SizedBox();
        }
      }),
    ];
  }

  int getLineLength(BuildContext context, String text, bool isArabic) {
    final span = TextSpan(text: text, style: AppTextStyles.s14w4cblackShade4);
    final tp = TextPainter(
      text: span,
      textDirection: isArabic ? ui.TextDirection.rtl : ui.TextDirection.ltr,
    )..layout(maxWidth: MediaQuery.of(context).size.width - 160);
    final numLines = tp.computeLineMetrics().length;
    return numLines;
  }

}
