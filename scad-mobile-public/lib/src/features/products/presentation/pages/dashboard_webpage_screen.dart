import 'dart:developer';

import 'package:auto_route/annotations.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:scad_mobile/src/common/widgets/appbar/flat_app_bar.dart';
import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:webview_flutter/webview_flutter.dart';

@RoutePage()
class DashboardWebViewPage extends StatefulWidget {
  const DashboardWebViewPage({
    required this.title,
    required this.uuid,
    required this.url,
    required this.urlAr,
    required this.urlDark,
    required this.urlArDark,
    super.key,
  });

  final String title;
  final String uuid;
  final String urlAr;
  final String url;
  final String urlDark;
  final String urlArDark;

  @override
  State<DashboardWebViewPage> createState() => _DashboardWebViewPageState();
}

class _DashboardWebViewPageState extends State<DashboardWebViewPage> {
  WebViewController? controller;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final rtl = DeviceType.isDirectionRTL(context);
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;

    String? getEmbedCode() {
      if (isLightMode && !rtl) {
        return widget.url;
      } else if (isLightMode && rtl) {
        return widget.urlAr;
      } else if (!isLightMode && !rtl) {
        return widget.urlDark;
      } else if (!isLightMode && rtl) {
        return widget.urlArDark;
      } else {
        return null;
      }
    }
    controller = WebViewController();
    controller!.setJavaScriptMode(JavaScriptMode.unrestricted);

    controller!.loadRequest(
      Uri.parse(getEmbedCode()!),
    );
    if (controller == null) {
      return const SizedBox();
    }

    return AppDrawer(
      child: Scaffold(
        body: Column(
          children: [
            FlatAppBar(
              title: widget.title,
            ),
            Expanded(
              child: Column(
                children: [
                  Expanded(
                    child: WebViewWidget(
                      controller: controller!,
                    ),
                  ),
                  // Expanded(child: HtmlWidget(state.data.embedCode!)),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
