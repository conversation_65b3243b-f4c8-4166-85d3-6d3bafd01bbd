part of 'products_bloc.dart';

abstract class ProductsState extends Equatable {
  const ProductsState();

  @override
  List<Object> get props => [];
}

class ProductsSelectTabState extends ProductsState {
  const ProductsSelectTabState({required this.type, required this.rnd});

  final ProductTabType type;
  final int rnd;

  @override
  List<Object> get props => [type, rnd];
}

class ProductsLoadingState extends ProductsState {}

// publication
class PublicationBaseState extends ProductsState {
  const PublicationBaseState();
}

class PublicationErrorState extends PublicationBaseState {
  const PublicationErrorState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}

class PublicationsLoadingState extends PublicationBaseState {
  const PublicationsLoadingState();

  @override
  List<Object> get props => [];
}

class PublicationSuccessState extends PublicationBaseState {
  const PublicationSuccessState({
    required this.data,
    required this.apiRequestId,
  });

  final PublicationsResponse data;
  final int apiRequestId;

  @override
  List<Object> get props => [apiRequestId, data];
}

class PublicationsFilterLoadingState extends ProductsState {
  const PublicationsFilterLoadingState();

  @override
  List<Object> get props => [];
}

class PublicationDomainsSuccessState extends ProductsState {
  PublicationDomainsSuccessState({required this.data}) {
    rnd = Random().nextInt(10000);
  }

  final PublicationDomainResponse data;
  late final int rnd;

  @override
  List<Object> get props => [data, rnd];
}

class PublicationDomainsErrorState extends ProductsState {
  const PublicationDomainsErrorState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}

class PublicationThemesSuccessState extends ProductsState {
  PublicationThemesSuccessState({required this.data}) {
    rnd = Random().nextInt(10000);
  }

  final PublicationThemeResponse data;
  late final int rnd;

  @override
  List<Object> get props => [data, rnd];
}

class PublicationThemesErrorState extends ProductsState {
  const PublicationThemesErrorState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}

class PublicationCategoriesSuccessState extends ProductsState {
  PublicationCategoriesSuccessState({required this.data}) {
    rnd = Random().nextInt(10000);
  }

  final PublicationCategoryResponse data;
  late final int rnd;

  @override
  List<Object> get props => [data, rnd];
}

class PublicationCategoriesErrorState extends ProductsState {
  const PublicationCategoriesErrorState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}

class PublicationYearsSuccessState extends ProductsState {
  PublicationYearsSuccessState({required this.data}) {
    rnd = Random().nextInt(10000);
  }

  late final int rnd;
  final PublicationYearResponse data;

  @override
  List<Object> get props => [data, rnd];
}

class PublicationYearsErrorState extends ProductsState {
  const PublicationYearsErrorState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}

abstract class GetWebReportsBaseState extends ProductsState {
  const GetWebReportsBaseState();
}

class WebReportsLoadingState extends GetWebReportsBaseState {
  const WebReportsLoadingState();

  @override
  List<Object> get props => [];
}

class WebReportsErrorState extends GetWebReportsBaseState {
  const WebReportsErrorState({this.error});

  final String? error;

  @override
  List<Object> get props => [error ?? ''];
}

class WebReportsSuccessState extends GetWebReportsBaseState {
  const WebReportsSuccessState({required this.data, this.apiRequestId = 0});

  final WebReports data;
  final int apiRequestId;

  @override
  List<Object> get props => [data, apiRequestId];
}

abstract class GetDashboardsBaseState extends ProductsState {
  const GetDashboardsBaseState();
}

class GetDashboardsLoadingState extends GetDashboardsBaseState {}

class GetDashboardsErrorState extends GetDashboardsBaseState {
  const GetDashboardsErrorState({this.error});

  final String? error;

  @override
  List<Object> get props => [error ?? ''];
}

class GetDashboardsSuccessState extends GetDashboardsBaseState {
  const GetDashboardsSuccessState({required this.data});

  final List<TableauDashboardResponseItem> data;

  @override
  List<Object> get props => [data];
}

class ProductsResetState extends ProductsState {
  ProductsResetState() {
    rnd = Random().nextInt(10000);
  }

  late final int rnd;

  @override
  List<Object> get props => [rnd];
}

class CheckProductAccessLoadingState extends ProductsState {}

class CheckProductAccessErrorState extends ProductsState {
  const CheckProductAccessErrorState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}

class CheckProductAccessSuccessState extends ProductsState {
  CheckProductAccessSuccessState({
    required this.hasAccessToScenarioDrivers,
    required this.hasAccessToForecasts,
    required this.hasAccessToInsightsDiscovery,
    required this.hasAccessToGeoSpatial,
    required this.hasAccessToReports,
    required this.hasAccessToDashboards,
  }) {
    rnd = Random().nextInt(10000);
  }

  late final int rnd;
  final bool hasAccessToScenarioDrivers;
  final bool hasAccessToForecasts;
  final bool hasAccessToInsightsDiscovery;
  final bool hasAccessToGeoSpatial;
  final bool hasAccessToReports;
  final bool hasAccessToDashboards;

  @override
  List<Object> get props => [
        rnd,
        hasAccessToScenarioDrivers,
        hasAccessToForecasts,
        hasAccessToInsightsDiscovery,
        hasAccessToGeoSpatial,
        hasAccessToReports,
        hasAccessToDashboards,
      ];
}
