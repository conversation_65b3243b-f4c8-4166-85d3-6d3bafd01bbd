part of 'products_bloc.dart';

abstract class ProductsState extends Equatable {
  const ProductsState();

  @override
  List<Object> get props => [];
}

class ProductsSelectTabState extends ProductsState {
  const ProductsSelectTabState({required this.type, required this.rnd});

  final String type;
  final int rnd;

  @override
  List<Object> get props => [type, rnd];
}

class ProductsLoadingState extends ProductsState {}

class ProductsFailureState extends ProductsState {
  const ProductsFailureState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}

class ProductsSuccessState extends ProductsState {
  const ProductsSuccessState({
    required this.products,
  });

  final List<ProductDashboard> products;

  @override
  List<Object> get props => [products];
}

class GetTableauDashboardDetailsLoadingState extends ProductsState {}


class GetTableauDashboardDetailsErrorState extends ProductsState {
  const GetTableauDashboardDetailsErrorState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}

class ScenarioSelectionState extends ProductsState {
  const ScenarioSelectionState({this.selectedScenario});

  final String? selectedScenario;

  @override
  List<Object> get props => [selectedScenario!];
}

// publication
class PublicationInitialState extends ProductsState {
  const PublicationInitialState();

  @override
  List<Object> get props => [];
}

class PublicationErrorState extends ProductsState {
  const PublicationErrorState({this.errorText});

  final String? errorText;

  @override
  List<Object> get props => [errorText ?? ''];
}

class PublicationsLoadingState extends ProductsState {
  const PublicationsLoadingState();

  @override
  List<Object> get props => [];
}

class PublicationSetSucessState extends ProductsState {
  const PublicationSetSucessState({this.data});

  final PublicationsResult? data;

  @override
  List<Object> get props => [data ?? ''];
}

// webreports
class WebReportsInitialState extends ProductsState {
  const WebReportsInitialState();

  @override
  List<Object> get props => [];
}

class WebReportsErrorState extends ProductsState {
  const WebReportsErrorState({this.errorText});

  final String? errorText;

  @override
  List<Object> get props => [errorText ?? ''];
}

class WebReportsLoadingState extends ProductsState {
  const WebReportsLoadingState();

  @override
  List<Object> get props => [];
}

class WebReportsSetSucessState extends ProductsState {
  const WebReportsSetSucessState({this.data});

  final Webreports? data;

  @override
  List<Object> get props => [data ?? []];
}

// authentification
class AuthentificationInitialState extends ProductsState {
  const AuthentificationInitialState();

  @override
  List<Object> get props => [];
}

class AuthentificationLoadingState extends ProductsState {
  const AuthentificationLoadingState();

  @override
  List<Object> get props => [];
}

class AuthentificationErrorState extends ProductsState {
  const AuthentificationErrorState({this.errorText});

  final String? errorText;

  @override
  List<Object> get props => [errorText ?? ''];
}

class AuthentificationSetSucessState extends ProductsState {
  const AuthentificationSetSucessState({this.data});

  final Authentification? data;

  @override
  List<Object> get props => [data ?? ''];
}

// // Forecast
// class ForecastErrorState extends ProductsState {
//   const ForecastErrorState({this.errorText});
//
//   final String? errorText;
//
//   @override
//   List<Object> get props => [errorText ?? ''];
// }
//
// class ForecastLoadingState extends ProductsState {
//   const ForecastLoadingState();
//
//   @override
//   List<Object> get props => [];
// }
//
// class ForecastSetSuccessState extends ProductsState {
//   const ForecastSetSuccessState({this.data});
//
//   final List<ForecastResponse>? data;
//
//   @override
//   List<Object> get props => [data ?? []];
// }
//
// // Reports
// class ReportErrorState extends ProductsState {
//   const ReportErrorState({this.errorText});
//
//   final String? errorText;
//
//   @override
//   List<Object> get props => [errorText ?? ''];
// }
//
// class ReportLoadingState extends ProductsState {
//   const ReportLoadingState();
//
//   @override
//   List<Object> get props => [];
// }
//
// class ReportSetSuccessState extends ProductsState {
//   const ReportSetSuccessState({this.data});
//
//   final List<ReportResponse>? data;
//
//   @override
//   List<Object> get props => [data ?? []];
// }
