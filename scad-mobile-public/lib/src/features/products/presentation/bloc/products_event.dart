part of 'products_bloc.dart';

abstract class ProductsEvent extends Equatable {
  const ProductsEvent();

  @override
  List<Object> get props => [];
}

class ProductsGetDashboardsEvent extends ProductsEvent {
  const ProductsGetDashboardsEvent();

  @override
  List<Object> get props => [];
}

class ProductsSelectTabEvent extends ProductsEvent {
  const ProductsSelectTabEvent({required this.type, required this.rnd});

  final String type;
  final int rnd;

  @override
  List<Object> get props => [type, rnd];
}

class ProductsLoadEvent extends ProductsEvent {
  const ProductsLoadEvent();

  @override
  List<Object> get props => [];
}

class GetTableauDashboardDetailsEvent extends ProductsEvent {
  const GetTableauDashboardDetailsEvent({
    required this.uuid,
  });

  final String uuid;

  @override
  List<Object> get props => [uuid];
}

class ScenarioSelectionEvent extends ProductsEvent {
  const ScenarioSelectionEvent({this.selectedScenario});

  final String? selectedScenario;

  @override
  List<Object> get props => [selectedScenario!];
}

class GetPublicationsEvent extends ProductsEvent {
  const GetPublicationsEvent({required this.pageno, required this.token});

  final String pageno;
  final String token;
  List<Object> get props => [pageno, token];
}

class GetWebReportsEvent extends ProductsEvent {
  const GetWebReportsEvent({required this.pageno, required this.token});

  final String pageno;
  final String token;
  List<Object> get props => [pageno, token];
}

class AuthentificationsEvent extends ProductsEvent {
  const AuthentificationsEvent();
  List<Object> get props => [];
}

class GetForecastEvent extends ProductsEvent {
  const GetForecastEvent();

  @override
  List<Object> get props => [];
}
