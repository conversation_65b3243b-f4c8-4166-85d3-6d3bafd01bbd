part of 'products_bloc.dart';

abstract class ProductsEvent extends Equatable {
  const ProductsEvent();

  @override
  List<Object> get props => [];
}

class ProductsGetDashboardsEvent extends ProductsEvent {
  const ProductsGetDashboardsEvent();

  @override
  List<Object> get props => [];
}

class ProductsSelectTabEvent extends ProductsEvent {
  const ProductsSelectTabEvent({required this.type, required this.rnd});

  final ProductTabType type;
  final int rnd;

  @override
  List<Object> get props => [type, rnd];
}

class GetPublicationDomainsEvent extends ProductsEvent {
  const GetPublicationDomainsEvent();

  @override
  List<Object> get props => [];
}

class GetPublicationThemesEvent extends ProductsEvent {
  const GetPublicationThemesEvent( {required this.scadDomainId});

  final String scadDomainId;

  @override
  List<Object> get props => [scadDomainId];
}

class GetPublicationCategoriesEvent extends ProductsEvent {
  const GetPublicationCategoriesEvent( {required this.scadThemeId});

  final String scadThemeId;

  @override
  List<Object> get props => [scadThemeId];
}

class GetPublicationYearsEvent extends ProductsEvent {
  const GetPublicationYearsEvent();

  @override
  List<Object> get props => [];
}

class GetPublicationsEvent extends ProductsEvent {
  const GetPublicationsEvent({
    required this.pageNo,
    this.apiRequestId = 0,
    this.paramList= const [],
  });

  final int apiRequestId;
  final int pageNo;
  final List<String> paramList;

  @override
  List<Object> get props => [apiRequestId, pageNo, paramList];
}

class GetWebReportsEvent extends ProductsEvent {
  const GetWebReportsEvent({required this.pageNo, this.apiRequestId = 0});

  final int pageNo;
  final int apiRequestId;

  @override
  List<Object> get props => [apiRequestId, pageNo];
}

class ProductsResetEvent extends ProductsEvent {
  ProductsResetEvent() {
    rnd = Random().nextInt(10000);
  }

  late final int rnd;

  @override
  List<Object> get props => [rnd];
}
