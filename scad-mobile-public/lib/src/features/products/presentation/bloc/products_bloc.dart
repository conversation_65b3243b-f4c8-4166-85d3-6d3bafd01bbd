import 'dart:async';
import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/config/dependancy_injection/injection_container.dart';
import 'package:scad_mobile/src/features/products/data/models/product_dashboard.dart';
import 'package:scad_mobile/src/features/products/data/models/scad_api/publication_category_response.dart';
import 'package:scad_mobile/src/features/products/data/models/scad_api/publication_domain_response.dart';
import 'package:scad_mobile/src/features/products/data/models/scad_api/publication_theme_response.dart';
import 'package:scad_mobile/src/features/products/data/models/scad_api/publication_year_response.dart';
import 'package:scad_mobile/src/features/products/data/models/scad_api/publications_response.dart';
import 'package:scad_mobile/src/features/products/data/models/scad_api/web_report_response.dart';
import 'package:scad_mobile/src/features/products/domain/repositories/products_repository_imports.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

part 'products_event.dart';
part 'products_state.dart';

enum ProductTabType {
  dashboards,
  publications,
  webReports,
  // insightsDiscovery,
  // scenarioDrivers,
  // forecasts,
  // reports,
  // geoSpatial,
}

class ProductTab {
  ProductTab({
    required this.productTabType,
    required this.title,
    this.data,
    this.errorMessage,
  });

  final ProductTabType productTabType;
  final String title;
  dynamic data;
  String? errorMessage;
}

class ProductsBloc extends Bloc<ProductsEvent, ProductsState> {
  ProductsBloc() : super(ProductsLoadingState()) {
    on<ProductsGetDashboardsEvent>(_onProductsGetDashboards);
    on<ProductsSelectTabEvent>(_onProductsSelectTab);

    on<GetPublicationDomainsEvent>(_onGetPublicationDomains);
    on<GetPublicationThemesEvent>(_onGetPublicationThemes);
    on<GetPublicationCategoriesEvent>(_onGetPublicationCategories);
    on<GetPublicationYearsEvent>(_onGetPublicationYears);

    on<GetPublicationsEvent>(_onGetPublications);
    on<GetWebReportsEvent>(_onGetWebReports);
    on<ProductsResetEvent>(_onProductsReset);
  }

  bool hasAccessToScenarioDrivers = false;
  bool hasAccessToInsightsDiscovery = false;
  bool hasAccessToForecasts = false;
  bool hasAccessToGeoSpatial = false;
  bool hasAccessToReports = false;
  bool hasAccessToDashboards = false;

  Future<void> _onProductsSelectTab(
    ProductsSelectTabEvent event,
    Emitter<ProductsState> emit,
  ) async {
    emit(ProductsSelectTabState(type: event.type, rnd: Random().nextInt(100)));
  }

  Future<void> _onProductsReset(
    ProductsResetEvent event,
    Emitter<ProductsState> emit,
  ) async {
    emit(ProductsResetState());
  }

  Future<void> _onProductsGetDashboards(
    ProductsGetDashboardsEvent event,
    Emitter<ProductsState> emit,
  ) async {
    try {
      emit(GetDashboardsLoadingState());

      final RepoResponse<List<TableauDashboardResponseItem>> response =
          await servicelocator<ProductsRepository>().getDashboards();

      if (response.isSuccess) {
        emit(GetDashboardsSuccessState(data: response.response!));
      } else {
        emit(GetDashboardsErrorState(error: response.errorMessage));
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(
        GetDashboardsErrorState(error: LocaleKeys.somethingWentWrong.tr()),
      );
    }
  }

  Future<void> _onGetPublicationDomains(
    GetPublicationDomainsEvent event,
    Emitter<ProductsState> emit,
  ) async {
    try {
      emit(const PublicationsFilterLoadingState());

      final RepoResponse<PublicationDomainResponse> response =
          await servicelocator<ProductsRepository>().scadApiPublicationsDomains();

      if (response.isSuccess) {
        final PublicationDomainResponse data = response.response!
          ..items?.removeWhere((e) => e.numberOfTaxonomyCategories == 0);
        emit(PublicationDomainsSuccessState(data: data));
      } else {
        emit(
          PublicationDomainsErrorState(error: response.errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(
        PublicationDomainsErrorState(error: LocaleKeys.somethingWentWrong.tr()),
      );
    }
  }

  Future<void> _onGetPublicationThemes(
    GetPublicationThemesEvent event,
    Emitter<ProductsState> emit,
  ) async {
    try {
      emit(const PublicationsFilterLoadingState());

      final RepoResponse<PublicationThemeResponse> response =
          await servicelocator<ProductsRepository>().scadApiPublicationsThemes(scadDomainId: event.scadDomainId);

      if (response.isSuccess) {
        final PublicationThemeResponse data = response.response!;
        emit(PublicationThemesSuccessState(data: data));
      } else {
        emit(
          PublicationThemesErrorState(error: response.errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(
        PublicationThemesErrorState(error: LocaleKeys.somethingWentWrong.tr()),
      );
    }
  }

  Future<void> _onGetPublicationCategories(
    GetPublicationCategoriesEvent event,
    Emitter<ProductsState> emit,
  ) async {
    try {
      emit(const PublicationsFilterLoadingState());

      final RepoResponse<PublicationCategoryResponse> response =
          await servicelocator<ProductsRepository>().scadApiPublicationsCategories(scadThemeId: event.scadThemeId);

      if (response.isSuccess) {
        final PublicationCategoryResponse data = response.response!;
        emit(PublicationCategoriesSuccessState(data: data));
      } else {
        emit(
          PublicationCategoriesErrorState(error: response.errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(
        PublicationCategoriesErrorState(
          error: LocaleKeys.somethingWentWrong.tr(),
        ),
      );
    }
  }

  Future<void> _onGetPublicationYears(
    GetPublicationYearsEvent event,
    Emitter<ProductsState> emit,
  ) async {
    try {
      emit(const PublicationsFilterLoadingState());

      final RepoResponse<PublicationYearResponse> response =
          await servicelocator<ProductsRepository>().scadApiPublicationsYears();

      if (response.isSuccess) {
        final PublicationYearResponse data = response.response!;
        emit(PublicationYearsSuccessState(data: data));
      } else {
        emit(
          PublicationYearsErrorState(error: response.errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(
        PublicationYearsErrorState(error: LocaleKeys.somethingWentWrong.tr()),
      );
    }
  }

  Future<void> _onGetPublications(
    GetPublicationsEvent event,
    Emitter<ProductsState> emit,
  ) async {
    try {
      emit(const PublicationsLoadingState());

      final RepoResponse<PublicationsResponse> response =
          await servicelocator<ProductsRepository>().scadApiPublications(
        pageNo: event.pageNo,
        pageSize: 20,
        paramList: event.paramList,
      );

      if (response.isSuccess) {
        emit(
          PublicationSuccessState(
            apiRequestId: event.apiRequestId,
            data: response.response!,
          ),
        );
      } else {
        emit(PublicationErrorState(error: response.errorMessage));
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(PublicationErrorState(error: LocaleKeys.somethingWentWrong.tr()));
    }
  }

  Future<void> _onGetWebReports(
    GetWebReportsEvent event,
    Emitter<ProductsState> emit,
  ) async {
    try {
      emit(const WebReportsLoadingState());

      final RepoResponse<WebReports> response = await servicelocator<ProductsRepository>().scadApiWebReports(
        pageNo: event.pageNo,
        pageSize: 20,
      );

      if (response.isSuccess) {
        emit(
          WebReportsSuccessState(
            apiRequestId: event.apiRequestId,
            data: response.response!,
          ),
        );
      } else {
        emit(
          WebReportsErrorState(error: response.errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(
        WebReportsErrorState(error: LocaleKeys.somethingWentWrong.tr()),
      );
    }
  }
}
