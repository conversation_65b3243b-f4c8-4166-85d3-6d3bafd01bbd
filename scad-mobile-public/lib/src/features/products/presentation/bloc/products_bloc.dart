import 'dart:async';
import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/config/dependancy_injection/injection_container.dart';
import 'package:scad_mobile/src/features/products/data/models/authntification_response.dart';
import 'package:scad_mobile/src/features/products/data/models/product_dashboard.dart';
import 'package:scad_mobile/src/features/products/data/models/web_report_response.dart';
import 'package:scad_mobile/src/features/products/domain/repositories/products_repository_imports.dart';
import 'package:scad_mobile/src/features/search/data/models/search_publications_response.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_api_cache.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

part 'products_event.dart';

part 'products_state.dart';

class ProductDashboard {
  ProductDashboard({
    required this.title,
    required this.type,
    required this.value,
  });

  final String title;
  final String type;
  final dynamic value;
}

class ProductsBloc extends Bloc<ProductsEvent, ProductsState> {
  ProductsBloc() : super(ProductsLoadingState()) {
    on<ProductsLoadEvent>(_onLoadProducts);
    on<ScenarioSelectionEvent>(_onSelectScenario);
    on<ProductsSelectTabEvent>(_onProductsSelectTab);
    on<AuthentificationsEvent>(_onAuthentification);
    on<GetPublicationsEvent>(_onGetPublications);
    on<GetWebReportsEvent>(_onGetWebReports);
  }

  Future<void> _onProductsSelectTab(ProductsSelectTabEvent event,
      Emitter<ProductsState> emit,) async {
    emit(ProductsSelectTabState(type: event.type, rnd: Random().nextInt(100)));
  }

  Future<void> _onLoadProducts(ProductsLoadEvent event,
      Emitter<ProductsState> emit,) async {
    emit(ProductsLoadingState());
    try {
      /*  //todo implement parallel api call
      final RepoResponse<List<ProductResponse>> responseProducts =
          await servicelocator<ProductsRepository>().productData();
      if (responseProducts.isSuccess) {
        */
      final RepoResponse<List<ProductDashboardResponseItem>>
      responseDashboards =
      await servicelocator<ProductsRepository>().getDashboards();
      if (responseDashboards.isSuccess) {
        final List<ProductDashboard> products = [];

        final List<ProductDashboardResponseItem> tdList = [];
        final List<ProductDashboardResponseItem> pbList = [];
        final List<ProductDashboardResponseItem> wrList = [];

        for (int i = 0; i < responseDashboards.response!.length; i++) {
          if (responseDashboards.response![i].productType == 'td') {
            tdList.add(responseDashboards.response![i]);
          } else if (responseDashboards.response![i].productType == 'pb') {
            pbList.add(responseDashboards.response![i]);
          } else if (responseDashboards.response![i].productType == 'wr') {
            wrList.add(responseDashboards.response![i]);
          }
        }

        products..add(
          ProductDashboard(
            title: LocaleKeys.tableauDashboard.tr(),
            type: 'td',
            value: tdList,
          ),
        )..add(
          ProductDashboard(
            title: LocaleKeys.publications.tr(),
            type: 'pb',
            value: pbList,
          ),
        )..add(
          ProductDashboard(
            title: LocaleKeys.webReports.tr(),
            type: 'wr',
            value: wrList,
          ),
        );

        /* for (int i = 0; i < responseProducts.response!.length; i++) {
            if (['dashboard','لوحة القيادة','الإحصائيات المبتكرة','الجغرافية المكانية'].contains (responseProducts.response?.elementAt(i).name?.toLowerCase() ?? '')) {
              continue;
            }

            // this Forecast is not being in use
            if (responseProducts.response![i].name == 'Forecast') {
              final RepoResponse<List<ForecastResponse>> forecastResponse =
                  await servicelocator<ProductsRepository>().getForecasts();
              if (forecastResponse.isSuccess) {
                products.add(
                  ProductDashboard(
                    title: responseProducts.response![i].name!,
                    type: responseProducts.response![i].name!,
                    value: forecastResponse.response,
                  ),
                );
              }
            } else {
              products.add(
                ProductDashboard(
                  title: responseProducts.response![i].name??'',
                  type: responseProducts.response![i].name??'', //'ifp',
                  value: responseProducts.response![i],
                ),
              );
            }
          }
          final RepoResponse<List<ReportResponse>> reportsResponse =
              await servicelocator<ProductsRepository>().getReports();
          if (reportsResponse.isSuccess) {
            // products.add(
            //   ProductDashboard(
            //     title: LocaleKeys.reports.tr(),
            //     type: 'Reports',
            //     value: reportsResponse.response,
            //   ),
            // );
          }
*/
        emit(ProductsSuccessState(products: products));
      } else {
        emit(
          ProductsFailureState(error: responseDashboards.errorMessage),
        );
      }
      /*} else {
        emit(
          ProductsFailureState(error: responseProducts.errorMessage),
        );
      }*/
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(
        ProductsFailureState(error: LocaleKeys.somethingWentWrong.tr()),
      );
    }
  }


  void _onSelectScenario(ScenarioSelectionEvent event,
      Emitter<ProductsState> emit,) {
    emit(ScenarioSelectionState(selectedScenario: event.selectedScenario));
  }

  Future<void> _onAuthentification(AuthentificationsEvent event,
      Emitter<ProductsState> emit,) async {
    try {
      const String hiveKeyWebReport = 'webReportList';
      const String hiveKeyPublication = 'publicationsList';
      final dynamic resWebReport = HiveUtilsApiCache.get(hiveKeyWebReport);
      final dynamic resPublication = HiveUtilsApiCache.get(hiveKeyPublication);
      if (resWebReport != null && resPublication != null) {
        const String hiveKey = 'authWebReportAndPublication';

        final dynamic res = HiveUtilsApiCache.get(hiveKey);
        if (res != null) {
          emit(
            AuthentificationSetSucessState(
              data: Authentification.fromJson(res as Map<String, dynamic>),
            ),
          );
        }

        return;
      }
      emit(const AuthentificationLoadingState());
      final RepoResponse<Authentification> response =
      await servicelocator<ProductsRepository>().authentification();
      if (response.isSuccess) {
        emit(AuthentificationSetSucessState(data: response.response));
      } else {
        emit(
          AuthentificationErrorState(errorText: response.errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(
        AuthentificationErrorState(
            errorText: LocaleKeys.somethingWentWrong.tr()),
      );
    }
  }

  Future<void> _onGetPublications(GetPublicationsEvent event,
      Emitter<ProductsState> emit,) async {
    try {
      final String hiveKey = 'publicationsList:${event.pageno}';
      final dynamic res = HiveUtilsApiCache.get(hiveKey);
      emit(const PublicationsLoadingState());
      if (res != null) {
        final PublicationsResult data =
        PublicationsResult.fromJson(res as Map<String, dynamic>);
        // for (final (_, item) in (data.items ?? []).indexed) {
        //   for (final (_, content) in (item.contentFields ?? []).indexed) {
        //     if ((content.contentFieldValue?.document?.contentType ?? '')
        //             .toLowerCase() ==
        //         'document') {
        //       final DocumetsPublications document =
        //           DocumetsPublications.fromJson({
        //         'fileextention':
        //             content.contentFieldValue?.document?.fileExtension ?? '',
        //         'url': ApiConfig.scadApiPath +
        //             (content.contentFieldValue?.document?.contentUrl ?? '')
        //       });
        //       item.documents.add(document);
        //     }
        //   }
        // }
        emit(PublicationSetSucessState(data: data));
        return;
      }

      final RepoResponse<PublicationsResult> response =
      await servicelocator<ProductsRepository>().publications(
        {'page': event.pageno, 'pageSize': '20'},
        event.token,
      );

      if (response.isSuccess) {
        final PublicationsResult data = response.response!;
        // for (final (_, item) in (data.items ?? []).indexed) {
        //   for (final (_, content) in (item.contentFields ?? []).indexed) {
        //     if ((content.contentFieldValue?.document?.contentType ?? '')
        //             .toLowerCase() ==
        //         'document') {
        //       final DocumetsPublications document =
        //           DocumetsPublications.fromJson({
        //         'fileextention':
        //             content.contentFieldValue?.document?.fileExtension ?? '',
        //         'url': ApiConfig.scadApiPath +
        //             (content.contentFieldValue?.document?.contentUrl ?? '')
        //       });
        //       item.documents.add(document);
        //     }
        //   }
        // }
        await HiveUtilsApiCache.set(hiveKey, data);
        emit(PublicationSetSucessState(data: data));
      } else {
        emit(
          PublicationErrorState(errorText: response.errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(
        PublicationErrorState(errorText: LocaleKeys.somethingWentWrong.tr()),
      );
    }
  }

  Future<void> _onGetWebReports(GetWebReportsEvent event,
      Emitter<ProductsState> emit,) async {
    try {
      const String hiveKey = 'webReportList';
      final dynamic res = HiveUtilsApiCache.get(hiveKey);
      if (res != null) {
        emit(
          WebReportsSetSucessState(
            data: Webreports.fromJson(res as Map<String, dynamic>),
          ),
        );
        return;
      }
      emit(const WebReportsLoadingState());
      final RepoResponse<Webreports> response =
      await servicelocator<ProductsRepository>().webReport(
        {
          'uuid': event.pageno,
        },
        event.token,
      );

      if (response.isSuccess) {
        await HiveUtilsApiCache.set(hiveKey, response.response?.toJson());
        emit(WebReportsSetSucessState(data: response.response));
      } else {
        emit(
          WebReportsErrorState(errorText: response.errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(
        WebReportsErrorState(errorText: LocaleKeys.somethingWentWrong.tr()),
      );
    }
  }
}
