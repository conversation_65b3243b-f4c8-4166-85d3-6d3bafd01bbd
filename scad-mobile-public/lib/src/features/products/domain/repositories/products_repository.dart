part of 'products_repository_imports.dart';

abstract class ProductsRepository extends CacheableRepository {
  Future<RepoResponse<List<TableauDashboardResponseItem>>> getDashboards();

  Future<RepoResponse<PublicationDomainResponse>> scadApiPublicationsDomains();

  Future<RepoResponse<PublicationThemeResponse>> scadApiPublicationsThemes({
    required String scadDomainId,
  });

  Future<RepoResponse<PublicationCategoryResponse>> scadApiPublicationsCategories({
    required String scadThemeId,
  });

  Future<RepoResponse<PublicationYearResponse>> scadApiPublicationsYears();

  Future<RepoResponse<PublicationsResponse>> scadApiPublications({
    required int pageNo,
    required int pageSize,
    List<String> paramList = const [],
  });

  Future<RepoResponse<WebReports>> scadApiWebReports({
    required int pageNo,
    required int pageSize,
    String query = '',
  });
}
