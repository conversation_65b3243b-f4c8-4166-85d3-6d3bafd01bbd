part of 'products_repository_imports.dart';

abstract class ProductsRepository {
  Future<RepoResponse<List<ProductDashboardResponseItem>>> getDashboards();

  Future<RepoResponse<List<ProductResponse>>> productData();

  Future<RepoResponse<Map<String, IndicatorOverview>>> indicatorDetails(
    RequestParamsMap requestParams,
    Map<String, dynamic> payload,
  );


  Future<RepoResponse<PublicationsResult>> publications(
    RequestParamsMap requestParams,
    String token,
  );
  Future<RepoResponse<Webreports>> webReport(
    RequestParamsMap requestParams,
    String token,
  );
  Future<RepoResponse<Authentification>> authentification();

  Future<RepoResponse<List<ForecastResponse>>> getForecasts();

  Future<RepoResponse<List<ReportResponse>>> getReports();
}
