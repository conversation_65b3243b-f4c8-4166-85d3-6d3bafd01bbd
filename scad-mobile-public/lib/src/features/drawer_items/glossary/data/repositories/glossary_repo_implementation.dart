import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/features/drawer_items/glossary/data/datasources/glossary_end_points.dart';
import 'package:scad_mobile/src/features/drawer_items/glossary/data/models/glossary_filter/glossary_filter_response_model.dart';
import 'package:scad_mobile/src/features/drawer_items/glossary/data/models/glossary_filter_payload_model.dart';
import 'package:scad_mobile/src/features/drawer_items/glossary/data/models/glossary_response_model.dart';
import 'package:scad_mobile/src/features/drawer_items/glossary/domain/repositories/glossary_repository_imports.dart';
import 'package:scad_mobile/src/services/http_service_impl.dart';
import 'package:scad_mobile/src/services/http_services.dart';
import 'package:scad_mobile/src/utils/extentions/string_extentions.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class GlossaryRepoImplementation extends GlossaryRepository {
  GlossaryRepoImplementation() {
    _httpService = HttpServiceRequests();
  }

  late HttpService _httpService;

  @override
  Future<RepoResponse<GlossaryResponseModel>> getGlossarylist({
    required int page,
    List<String>? domainList,
    List<String>? subDomainList,
    List<String>? alphabetList,
    bool? isAscending = true,
    String? term,
  }) async {
    try {
      Map<String, dynamic> fliter = {
        'TOPIC_EN': domainList ?? [],
        'THEME_EN': subDomainList ?? {},
        'TITLE_EN': alphabetList ?? {},
      };
      if (HiveUtilsSettings.getAppLanguage().toLowerCase() == 'ar') {
        fliter = {
          'TOPIC_AR': domainList ?? [],
          'THEME_AR': subDomainList ?? {},
          'TITLE_AR': alphabetList ?? {},
        };
      }
      final GlossaryFilterPayloadModel payload = GlossaryFilterPayloadModel(
        filters: fliter,
        sortBy: {'alphabetical': isAscending == true ? 'ASC' : 'DESC'},
      );

      final response = await _httpService.postJson(
        '${GlossaryEndPoints.glossaryListEndPoint.setUrlParams({
              'page': '$page'
            })}${(term ?? '').isNotEmpty ? '&term=$term' : ''}',
        jsonPayloadMap: payload.toJson(),
      );

      // final response = ApiResponse.success(glossaryListResponseMap);
      if (response.isSuccess) {
        return RepoResponse<GlossaryResponseModel>.success(
          response: GlossaryResponseModel.fromJson(response.response),
        );
      } else {
        return RepoResponse<GlossaryResponseModel>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<GlossaryResponseModel>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<GlossaryFilterResponseModel>> glossaryFilter() async {
    try {
      final response =
          await _httpService.get(GlossaryEndPoints.glossaryFilterEndPoint);

      // final response = ApiResponse.success(glossaryFilterResponseMap);
      if (response.isSuccess) {
        return RepoResponse<GlossaryFilterResponseModel>.success(
          response: GlossaryFilterResponseModel.fromJson(response.response),
        );
      } else {
        return RepoResponse<GlossaryFilterResponseModel>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<GlossaryFilterResponseModel>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }
}
