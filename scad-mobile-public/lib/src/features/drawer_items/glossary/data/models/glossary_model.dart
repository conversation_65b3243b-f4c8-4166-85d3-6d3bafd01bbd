import 'package:json_annotation/json_annotation.dart';

part 'glossary_model.g.dart';

@JsonSerializable()
class GlossaryModel {
  GlossaryModel({
    this.titleEn,
    this.titleAr,
    this.descriptionEn,
    this.descriptionAr,
    this.topicEn,
    this.themeEn,
    this.topicAr,
    this.themeAr,
    this.type,
    this.total,
    this.isArabic = false,
  });

  factory GlossaryModel.fromJson(Map<String, dynamic> json) =>
      _$GlossaryModelFromJson(json);

  @<PERSON><PERSON><PERSON><PERSON>(name: 'TITLE_EN')
  String? titleEn;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'TITLE_AR')
  String? titleAr;
  @J<PERSON><PERSON><PERSON>(name: 'DESCRIPTION_EN')
  String? descriptionEn;
  @J<PERSON><PERSON><PERSON>(name: 'DESCRIPTION_AR')
  String? descriptionAr;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'TOPIC_EN')
  String? topicEn;
  @J<PERSON><PERSON><PERSON>(name: 'THEME_EN')
  String? themeEn;
  @J<PERSON><PERSON><PERSON>(name: 'TOPIC_AR')
  String? topicAr;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'THEME_AR')
  String? themeAr;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'TYPE')
  String? type;
  @J<PERSON><PERSON><PERSON>(name: 'TOTAL')
  int? total;
  @JsonKey(name: 'is_arabic')
  bool isArabic;

  Map<String, dynamic> toJson() => _$GlossaryModelToJson(this);
}
