// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'glossary_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GlossaryModel _$GlossaryModelFromJson(Map<String, dynamic> json) =>
    GlossaryModel(
      titleEn: json['TITLE_EN'] as String?,
      titleAr: json['TITLE_AR'] as String?,
      descriptionEn: json['DESCRIPTION_EN'] as String?,
      descriptionAr: json['DESCRIPTION_AR'] as String?,
      topicEn: json['TOPIC_EN'] as String?,
      themeEn: json['THEME_EN'] as String?,
      topicAr: json['TOPIC_AR'] as String?,
      themeAr: json['THEME_AR'] as String?,
      type: json['TYPE'] as String?,
      total: json['TOTAL'] as int?,
      isArabic: json['is_arabic'] as bool? ?? false,
    );

Map<String, dynamic> _$GlossaryModelToJson(GlossaryModel instance) =>
    <String, dynamic>{
      'TITLE_EN': instance.titleEn,
      'TITLE_AR': instance.titleAr,
      'DESCRIPTION_EN': instance.descriptionEn,
      'DESCRIPTION_AR': instance.descriptionAr,
      'TOPIC_EN': instance.topicEn,
      'THEME_EN': instance.themeEn,
      'TOPIC_AR': instance.topicAr,
      'THEME_AR': instance.themeAr,
      'TYPE': instance.type,
      'TOTAL': instance.total,
      'is_arabic': instance.isArabic,
    };
