// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'glossary_filter_sub_domain_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GlossaryFilterSubDomainModel _$GlossaryFilterSubDomainModelFromJson(
        Map<String, dynamic> json) =>
    GlossaryFilterSubDomainModel(
      name: json['name'] as String?,
      items:
          (json['items'] as List<dynamic>?)?.map((e) => e as String).toList(),
      isSelected: json['is_selected'] as bool? ?? false,
    );

Map<String, dynamic> _$GlossaryFilterSubDomainModelToJson(
        GlossaryFilterSubDomainModel instance) =>
    <String, dynamic>{
      'name': instance.name,
      'items': instance.items,
      'is_selected': instance.isSelected,
    };
