import 'package:json_annotation/json_annotation.dart';

part 'glossary_filter_sub_domain_model.g.dart';

@JsonSerializable()
class GlossaryFilterSubDomainModel {
  GlossaryFilterSubDomainModel({
    this.name,
    this.items,
    this.isSelected = false,
  });

  factory GlossaryFilterSubDomainModel.fromJson(Map<String, dynamic> json) =>
      _$GlossaryFilterSubDomainModelFromJson(json);

  @<PERSON><PERSON><PERSON>ey(name: 'name')
  String? name;
  @<PERSON>son<PERSON>ey(name: 'items')
  List<String>? items;
  @Json<PERSON><PERSON>(name: 'is_selected')
  bool isSelected;

  Map<String, dynamic> toJson() => _$GlossaryFilterSubDomainModelToJson(this);
}
