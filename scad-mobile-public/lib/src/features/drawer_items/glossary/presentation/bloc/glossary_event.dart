part of 'glossary_bloc.dart';

abstract class GlossaryEvent extends Equatable {
  const GlossaryEvent();

  @override
  List<Object> get props => [];
}

/// the event used to load glossary
class GlossaryLoadDataEvent extends GlossaryEvent {
  const GlossaryLoadDataEvent({required this.page});

  final int page;

  @override
  List<Object> get props => [page];
}

class GlossaryLoadFiltersEvent extends GlossaryEvent {
  const GlossaryLoadFiltersEvent();

  @override
  List<Object> get props => [];
}

/// the event used for domain dropdown
// class GlossaryFilterDomainDropdownEvent extends GlossaryEvent {
//   const GlossaryFilterDomainDropdownEvent({
//     required this.index,
//     required this.domainItems,
//   });
//
//   final int index;
//   final List<GlossaryFilterDomainModel> domainItems;
//
//   @override
//   List<Object> get props => [index, domainItems];
// }

/// the event used for sub domain dropdown
// class GlossaryFilterSubDomainDropdownEvent extends GlossaryEvent {
//   const GlossaryFilterSubDomainDropdownEvent({
//     required this.index,
//     required this.subDomainItems,
//   });
//
//   final int index;
//   final List<GlossaryFilterSubDomainModel> subDomainItems;
//
//   @override
//   List<Object> get props => [index, subDomainItems];
// }

/// the event used for domain dropdown
// class GlossaryFilterDomainDropdownValueUpdateEvent extends GlossaryEvent {
//   const GlossaryFilterDomainDropdownValueUpdateEvent({
//     required this.domainItems,
//     required this.selectedDomains,
//     required this.subDomainItems,
//     required this.selectedSubDomains,
//   });
//
//
//   final List<GlossaryFilterDomainModel> domainItems;
//   final String selectedDomains;
//   final List<GlossaryFilterSubDomainModel> subDomainItems;
//   final String selectedSubDomains;
//
//   @override
//   List<Object> get props =>
//       [domainItems, selectedDomains, subDomainItems, selectedSubDomains];
// }

/// the event used for sub domain dropdown
// class GlossaryFilterSubDomainDropdownValueUpdateEvent extends GlossaryEvent {
//   const GlossaryFilterSubDomainDropdownValueUpdateEvent({
//     required this.selected,
//     required this.subDomainItems,
//   });
//
//   final String selected;
//   final List<GlossaryFilterSubDomainModel> subDomainItems;
//
//   @override
//   List<Object> get props => [selected, subDomainItems];
// }

/// the event on done button for both domain, sub domain and also for
/// alphabet selection and ascending/descending
class GlossaryDoneButtonEvent extends GlossaryEvent {
  const GlossaryDoneButtonEvent({
    required this.page,
    this.selectedDomains,
    this.selectedSubDomains,
    this.selectedAlphabet,
    this.isAscending,
    this.term,
  });

  final List<GlossaryFilterDomainModel>? selectedDomains;
  final List<GlossaryFilterSubDomainModel>? selectedSubDomains;
  final String? selectedAlphabet;
  final bool? isAscending;
  final String? term;
  final int page;

  @override
  List<Object> get props => [
        selectedDomains ?? [],
        selectedSubDomains ?? [],
        selectedAlphabet ?? '',
        isAscending ?? true,
        term ?? '',
        page,
      ];
}

/// the event used to selected alphabet
class GlossaryAlphabetSelectedEvent extends GlossaryEvent {
  const GlossaryAlphabetSelectedEvent({required this.selectedAlphabet});

  final String selectedAlphabet;

  @override
  List<Object> get props => [selectedAlphabet];
}

/// the event used to toggle b/w arabic and english
// class GlossaryLanguageToggleEvent extends GlossaryEvent {
//   const GlossaryLanguageToggleEvent({
//     required this.updatedList,
//     required this.index,
//   });
//
//   final List<GlossaryModel> updatedList;
//   final int index;
//
//   @override
//   List<Object> get props => [updatedList, index];
// }

/// the event used to reset the filter
class GlossaryFilterResetEvent extends GlossaryEvent {
  const GlossaryFilterResetEvent(
      {this.selectedDomain,
      this.selectedSubDomain,
      this.selectedAlphabet,
      required this.term});

  final String? selectedDomain;
  final String? selectedSubDomain;
  final String? selectedAlphabet;
  final String term;

  @override
  List<Object> get props => [
        selectedDomain ?? '',
        selectedSubDomain ?? '',
        selectedAlphabet ?? '',
        term
      ];
}
