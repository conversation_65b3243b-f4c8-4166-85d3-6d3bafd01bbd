// import 'package:expandable/expandable.dart';
// import 'package:flutter/material.dart';
// import 'package:scad_mobile/main.dart';
// import 'package:scad_mobile/src/features/drawer_items/glossary/data/models/glossary_model.dart';
// import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
// import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
// import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
//
// class GlossaryItemListView extends StatelessWidget {
//   const GlossaryItemListView({required this.glossaryList, super.key});
//
//   final List<GlossaryModel> glossaryList;
//
//   int getLineLength(BuildContext context, String text, bool isArabic) {
//     final span = TextSpan(text: text, style: AppTextStyles.s14w4cblackShade4);
//     final tp = TextPainter(
//       text: span,
//       textDirection: isArabic ? TextDirection.rtl : TextDirection.ltr,
//     )..layout(maxWidth: MediaQuery.of(context).size.width - 80);
//     final numLines = tp.computeLineMetrics().length;
//     return numLines;
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     final bool isLightMode =
//         HiveUtilsSettings.isLightMode;
//     return ListView.separated(
//       shrinkWrap: true,
//       itemCount: glossaryList.length,
//       padding: const EdgeInsets.symmetric(
//         horizontal: 16,
//         vertical: 16,
//       ),
//       itemBuilder: (context, index) {
//         return Container(
//           decoration: BoxDecoration(
//             color: !isLightMode ? AppColors.blackShade8 : null,
//             borderRadius: BorderRadius.circular(15),
//             border: Border.all(
//               color: isLightMode ? AppColors.greyShade1 : Colors.transparent,
//             ),
//           ),
//           padding: const EdgeInsets.all(16),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//                   Expanded(
//                     child: Text(
//                       HiveUtilsSettings.isLanguageArabic
//                           ? glossaryList[index].titleAr ?? ''
//                           : glossaryList[index].titleEn ?? '',
//                       style: AppTextStyles.s16w5cBlackShade1.copyWith(
//                         color: !isLightMode ? Colors.white : null,
//                       ),
//                       overflow: TextOverflow.ellipsis,
//                       maxLines: 2,
//                       textScaler: TextScaler.linear(textScaleFactor.value),
//                     ),
//                   ),
//                   //TODO: Commented as per the instructions for the demo
//                   // LanguageSwitch(
//                   //   switchValue: !HiveUtilsSettings.isLanguageArabic,
//                   //   onToggle: () {
//                   //     context.read<GlossaryBloc>().add(
//                   //           GlossaryLanguageToggleEvent(
//                   //             updatedList: glossaryList,
//                   //             index: index,
//                   //           ),
//                   //         );
//                   //   },
//                   // ),
//                 ],
//               ),
//               const SizedBox(height: 10),
//               ExpandableNotifier(
//                 child: ScrollOnExpand(
//                   scrollOnCollapse: false,
//                   child: Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       ExpandablePanel(
//                         theme: const ExpandableThemeData(
//                           headerAlignment:
//                               ExpandablePanelHeaderAlignment.center,
//                           tapBodyToCollapse: true,
//                           hasIcon: false,
//                         ),
//                         header: const SizedBox(),
//                         collapsed: Text(
//                           HiveUtilsSettings.isLanguageArabic
//                               ? glossaryList[index].descriptionAr ?? ''
//                               : glossaryList[index].descriptionEn ?? '',
//                           softWrap: true,
//                           maxLines: 2,
//                           overflow: TextOverflow.ellipsis,
//                           style: AppTextStyles.s14w4cblackShade4.copyWith(
//                             color: isLightMode
//                                 ? AppColors.grey
//                                 : AppColors.greyShade4,
//                           ),
//                           textScaler: TextScaler.linear(textScaleFactor.value),
//                         ),
//                         expanded: Text(
//                           HiveUtilsSettings.isLanguageArabic
//                               ? glossaryList[index].descriptionAr ?? ''
//                               : glossaryList[index].descriptionEn ?? '',
//                           maxLines: 10,
//                           style: AppTextStyles.s14w4cblackShade4.copyWith(
//                             color: isLightMode
//                                 ? AppColors.grey
//                                 : AppColors.greyShade4,
//                           ),
//                           overflow: TextOverflow.ellipsis,
//                           textScaler: TextScaler.linear(textScaleFactor.value),
//                         ),
//                       ),
//                       const SizedBox(height: 6),
//                       if (getLineLength(
//                             context,
//                             HiveUtilsSettings.isLanguageArabic
//                                 ? glossaryList[index].descriptionAr ?? ''
//                                 : glossaryList[index].descriptionEn ?? '',
//                             HiveUtilsSettings.isLanguageArabic,
//                           ) >
//                           2)
//                         Builder(
//                           builder: (context) {
//                             final controller = ExpandableController.of(
//                               context,
//                               required: true,
//                             )!;
//                             return InkWell(
//                               onTap: controller.toggle,
//                               child: Row(
//                                 mainAxisSize: MainAxisSize.min,
//                                 children: [
//                                   Text(
//                                     controller.expanded
//                                         ? 'Show less'
//                                         : 'Show more',
//                                     style: AppTextStyles.s14w4cBlue,
//                                     overflow: TextOverflow.ellipsis,
//                                     textScaler: TextScaler.linear(
//                                         textScaleFactor.value),
//                                   ),
//                                   Icon(
//                                     controller.expanded
//                                         ? Icons.keyboard_arrow_up_rounded
//                                         : Icons.keyboard_arrow_down_rounded,
//                                     size: 18,
//                                     color: AppColors.blueLight,
//                                   ),
//                                 ],
//                               ),
//                             );
//                           },
//                         ),
//                     ],
//                   ),
//                 ),
//               ),
//             ],
//           ),
//         );
//       },
//       separatorBuilder: (context, index) => const SizedBox(height: 14),
//     );
//   }
// }
