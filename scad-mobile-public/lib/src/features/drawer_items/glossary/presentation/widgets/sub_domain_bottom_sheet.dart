import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/check_box_text_row.dart';
import 'package:scad_mobile/src/common/widgets/bottom_sheet_top_notch.dart';
import 'package:scad_mobile/src/features/drawer_items/glossary/data/models/glossary_filter/glossary_filter_domain_model.dart';
import 'package:scad_mobile/src/features/drawer_items/glossary/data/models/glossary_filter/glossary_filter_sub_domain_model.dart';
import 'package:scad_mobile/src/features/drawer_items/glossary/presentation/bloc/glossary_bloc.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class SubDomainBottomSheet extends StatefulWidget {

    const SubDomainBottomSheet({required this.domainList,required this.subDomainList, super.key});

  final List<GlossaryFilterSubDomainModel> subDomainList;
  final List<GlossaryFilterDomainModel> domainList;

  @override
  State<SubDomainBottomSheet> createState() => _SubDomainBottomSheetState();

    List<int> initialSelection() {
      final List<int> list = [];
      for (int i = 0; i < subDomainList.length; i++) {
        if (subDomainList[i].isSelected) {
          list.add(i);
        }
      }
      return list;
    }
}

class _SubDomainBottomSheetState extends State<SubDomainBottomSheet> {
  List<int> selectedIndexList = [];

  bool get isSelectionChanged => !listEquals(
      (widget.initialSelection()..sort()), (selectedIndexList..sort()));

  List<GlossaryFilterSubDomainModel> subDomainList = [];

  @override
  void initState() {
    super.initState();

    subDomainList = List.generate(
        widget.subDomainList.length, (index) => widget.subDomainList[index]);

    for (int i = 0; i < subDomainList.length; i++) {
      if (subDomainList[i].isSelected) {
        selectedIndexList.add(i);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    // List<GlossaryFilterDomainModel>? selectedDomains;
    // List<GlossaryFilterSubDomainModel>? selectedSubDomains;
    // String? selectedAlphabet;
    // String? selectedDomainsName;
    // bool isAscending = true;
    return BlocConsumer<GlossaryBloc, GlossaryState>(
      listener: (context, state) {
        // if (state is GlossaryFilterSubDomainDropdownState) {
        //   isSelectionChanged=true;
        // }
      },
      builder: (context, state) {
        // if (state is GlossaryFilterDomainDropdownState) {
        //   selectedDomains = state.domainItems;
        // } else if (state is GlossaryFilterSubDomainDropdownState) {
        //   selectedSubDomains = state.subDomainList;
        //   selectedDomainsName = state.selectedValue;
        // } else if (state is GlossaryFilterDoneButtonState) {
        //   selectedDomains = state.selectedDomains;
        //   selectedSubDomains = state.selectedSubDomains;
        //   selectedAlphabet = state.selectedAlphabet;
        //   isAscending = state.isAscending ?? true;
        // }
        return Container(
          width: double.infinity,
          padding: const EdgeInsets.fromLTRB(
            24,
            0,
            24,
            24,
          ),

          decoration: BoxDecoration(
            color: isLightMode ? AppColors.white : AppColors.blueShade36,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 10),
              const BottomSheetTopNotch(),
              const SizedBox(height: 16),
              Flexible(child:   ListView.builder(
                    shrinkWrap: true,
                                  padding: EdgeInsets.zero,

                                  itemCount:widget.subDomainList.length ?? 0,
                                  itemBuilder: (context, index) {
                                     final subDomain = widget.subDomainList[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 15),
                  child: CheckBoxTextRow(
                    title: subDomain.name ?? '',
                    titleColor: isLightMode
                        ? AppColors.blackTextTile
                        : AppColors.white,
                    isSelected: selectedIndexList.contains(index),
                    onChanged: () {
                      if (selectedIndexList.contains(index)) {
                        selectedIndexList.remove(index);
                      } else {
                        selectedIndexList.add(index);
                      }
                      setState(() {});
                      // context.read<GlossaryBloc>().add(
                      //       GlossaryFilterSubDomainDropdownEvent(
                      //         index: index,
                      //         subDomainItems: widget.subDomainList ?? [],
                      //       ),
                      //     );
                    },
                  ),
                );
                                   } )),


              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  minimumSize: const Size.fromHeight(43),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(
                      10,
                    ),
                  ),
                ),
                onPressed: !isSelectionChanged
                    ? null
                    : () {
                  Navigator.pop(context);

                  final List<GlossaryFilterSubDomainModel> selected = [];

                  for(int i =0;i<subDomainList.length;i++) {
                    if(selectedIndexList.contains(i)){
                      subDomainList[i].isSelected = true;
                    }else{
                      subDomainList[i].isSelected = false;
                    }
                    selected.add(subDomainList[i]);
                  }

                  context.read<GlossaryBloc>().add(
                    GlossaryDoneButtonEvent(
                      page: 1,
                      selectedDomains: widget.domainList,
                      selectedSubDomains: selected,
                      selectedAlphabet: '',
                      isAscending: true,
                      term: '',
                    ),
                  );

                  // context.read<GlossaryBloc>().add(
                  //       GlossaryDoneButtonEvent(
                  //         page:1,
                  //           selectedDomains: selectedDomains,
                  //           selectedSubDomains: selectedSubDomains,
                  //           selectedAlphabet: selectedAlphabet,
                  //           isAscending: isAscending,
                  //           term: ''),
                  //     );
                  //     context.read<GlossaryBloc>().add(
                  //       GlossaryFilterSubDomainDropdownValueUpdateEvent(
                  //         subDomainItems: widget.subDomainList ?? [],
                  //         selected: selectedDomainsName ?? ''
                  //     ));
                },
                child: Text(
                  LocaleKeys.done.tr(),
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                  textScaler: TextScaler.linear(textScaleFactor.value),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
