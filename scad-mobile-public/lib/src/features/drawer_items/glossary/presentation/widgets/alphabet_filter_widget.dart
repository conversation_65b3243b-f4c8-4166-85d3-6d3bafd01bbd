// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:scad_mobile/main.dart';
// import 'package:scad_mobile/src/features/drawer_items/glossary/data/models/glossary_filter/glossary_filter_domain_model.dart';
// import 'package:scad_mobile/src/features/drawer_items/glossary/data/models/glossary_filter/glossary_filter_sub_domain_model.dart';
// import 'package:scad_mobile/src/features/drawer_items/glossary/data/models/glossary_response_model.dart';
// import 'package:scad_mobile/src/features/drawer_items/glossary/presentation/bloc/glossary_bloc.dart';
// import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
// import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
// import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
//
// class AlphabetFilterWidget extends StatelessWidget {
//   const AlphabetFilterWidget({
//     required this.glossaryData,
//     required this.selectedAlphabet,
//     super.key,
//   });
//
//   final GlossaryResponseModel? glossaryData;
//   final String? selectedAlphabet;
//
//   @override
//   Widget build(BuildContext context) {
//     final bool isLightMode =
//         HiveUtilsSettings.getThemeMode() == ThemeMode.light;
//     List<GlossaryFilterDomainModel>? selectedDomains;
//     List<GlossaryFilterSubDomainModel>? selectedSubDomains;
//     bool isAscending = true;
//     // String? selectedAph = selectedAlphabet;
//     return BlocBuilder<GlossaryBloc, GlossaryState>(
//       builder: (context, state) {
//         if (state is GlossaryAlphabetSelectedState) {
//           // selectedAph = state.selectedAlphabet;
//         } else if (state is GlossaryFilterDoneButtonState) {
//           selectedDomains = state.selectedDomains;
//           selectedSubDomains = state.selectedSubDomains;
//           isAscending = state.isAscending ?? true;
//           // selectedAph = state.selectedAlphabet;
//         }
//         return SizedBox(
//           height: 30,
//           child: SingleChildScrollView(
//             scrollDirection: Axis.horizontal,
//             child: Row(
//               children: glossaryData?.alphabets?.keys.map((e) {
//                     final bool isSelectable = glossaryData?.alphabets?[e] != 0;
//                     return Padding(
//                       padding: EdgeInsets.only(
//                         left: 24,
//                         right: e == glossaryData?.alphabets?.keys.last ? 24 : 0,
//                       ),
//                       child: InkWell(
//                         onTap: () {
//                           if (isSelectable) {
//                             // context.read<GlossaryBloc>().add(
//                             //       GlossaryAlphabetSelectedEvent(
//                             //         selectedAlphabet: e,
//                             //       ),
//                             //     );
//                             if (selectedAlphabet == e) {
//                               context.read<GlossaryBloc>().add(
//                                     GlossaryDoneButtonEvent(
//                                       page: 1,
//                                       selectedDomains: selectedDomains,
//                                       selectedSubDomains: selectedSubDomains,
//                                       isAscending: isAscending,
//                                     ),
//                                   );
//                             } else {
//                               context.read<GlossaryBloc>().add(
//                                     GlossaryDoneButtonEvent(
//                                       page: 1,
//                                       selectedDomains: selectedDomains,
//                                       selectedSubDomains: selectedSubDomains,
//                                       selectedAlphabet: e,
//                                       isAscending: isAscending,
//                                     ),
//                                   );
//                             }
//                           }
//                         },
//                         child: Container(
//                           height: 30,
//                           width: 30,
//                           alignment: Alignment.center,
//                           decoration: BoxDecoration(
//                             shape: BoxShape.circle,
//                             color: selectedAlphabet == e
//                                 ? AppColors.blueLight
//                                 : Colors.transparent,
//                           ),
//                           child: Text(
//                             e,
//                             style: AppTextStyles.s12w4cblueGreyShade1.copyWith(
//                               color: selectedAlphabet == e
//                                   ? AppColors.white
//                                   : (isSelectable
//                                       ? isLightMode
//                                           ? AppColors.blueGreyShade1
//                                           : AppColors.white
//                                       : isLightMode
//                                           ? AppColors.greyShade1
//                                           : AppColors.blackShade8),
//                             ),
//                             textScaler:
//                                 TextScaler.linear(textScaleFactor.value),
//                           ),
//                         ),
//                       ),
//                     );
//                   }).toList() ??
//                   [],
//             ),
//           ),
//         );
//       },
//     );
//   }
// }
