import 'package:json_annotation/json_annotation.dart';
import 'package:scad_mobile/src/features/drawer_items/about_this_app/data/models/about_app_model/response/about_app_response.dart';

part 'about_app_list_response.g.dart';

@JsonSerializable()
class AboutAppListResponseModel {
  AboutAppListResponseModel({
    this.aboutAppListResponse,
  });

  factory AboutAppListResponseModel.fromJson(Map<String, dynamic> json) =>
      _$AboutAppListResponseModelFromJson(json);

  @JsonKey(name: 'data')
  List<AboutAppResponseModel>? aboutAppListResponse;

  Map<String, dynamic> toJson() => _$AboutAppListResponseModelToJson(this);
}
