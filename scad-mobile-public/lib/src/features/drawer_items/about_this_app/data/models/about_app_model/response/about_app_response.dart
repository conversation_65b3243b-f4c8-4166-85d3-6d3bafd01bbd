import 'package:json_annotation/json_annotation.dart';

part 'about_app_response.g.dart';

@JsonSerializable()
class AboutAppResponseModel {
  AboutAppResponseModel({
    this.uuid,
    this.title,
    this.aboutContent,
    this.version,
    this.active,
    this.aboutContentAr,
    this.titleAr,
  });

  factory AboutAppResponseModel.fromJson(Map<String, dynamic> json) =>
      _$AboutAppResponseModelFromJson(json);
  String? uuid;
  String? title;
  @JsonKey(name: 'title_ar')
  String? titleAr;
  @JsonKey(name: 'about_content')
  String? aboutContent;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'about_content_ar')
  String? aboutContentAr;
  String? version;
  bool? active;

  Map<String, dynamic> toJson() => _$AboutAppResponseModelToJson(this);
}
