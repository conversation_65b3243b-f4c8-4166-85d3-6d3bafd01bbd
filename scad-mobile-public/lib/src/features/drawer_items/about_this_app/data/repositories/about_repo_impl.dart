import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/features/drawer_items/about_this_app/data/data_sources/api_end_points.dart';
import 'package:scad_mobile/src/features/drawer_items/about_this_app/data/models/about_app_model/response/about_app_list_response.dart';
import 'package:scad_mobile/src/features/drawer_items/about_this_app/domain/repositories/about_app_repository_imports.dart';
import 'package:scad_mobile/src/services/http_service_impl.dart';
import 'package:scad_mobile/src/services/http_services.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class AboutAppRepositoryImpl implements AboutAppRepository {
  AboutAppRepositoryImpl() {
    _httpService = HttpServiceRequests();
  }

  late HttpService _httpService;

  @override
  Future<RepoResponse<AboutAppListResponseModel>> aboutAppData() async {
    try {
      final response = await _httpService.get(
        AboutAppEndPoints.aboutUs,
      );
      if (response.isSuccess) {
        return RepoResponse<AboutAppListResponseModel>.success(
          response: AboutAppListResponseModel.fromJson(response.response),
        );
      } else {
        return RepoResponse<AboutAppListResponseModel>.error(
          errorMessage: response.message,
        );
      }
    }catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<AboutAppListResponseModel>.error(
        errorMessage:  LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }
}
