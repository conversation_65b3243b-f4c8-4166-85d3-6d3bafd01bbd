import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/config/dependancy_injection/injection_container.dart';
import 'package:scad_mobile/src/features/drawer_items/terms_and_conditions/data/models/t_and_c_model/response/t_and_c_list_response.dart';
import 'package:scad_mobile/src/features/drawer_items/terms_and_conditions/data/models/t_and_c_model/response/t_and_c_response.dart';
import 'package:scad_mobile/src/features/drawer_items/terms_and_conditions/domain/repositories/t_and_c_repository_imports.dart';

part 't_and_c_event.dart';
part 't_and_c_state.dart';

class TAndCBloc extends Bloc<TAndCEvent, TAndCState> {
  TAndCBloc(this.aboutAppRepository) : super(TAndCLoadingState()) {
    on<TAndCLoadingEvent>(_onLoadTAndC);
  }

  final TAndCRepository aboutAppRepository;

  Future<void> _onLoadTAndC(
    TAndCLoadingEvent event,
    Emitter<TAndCState> emit,
  ) async {
    emit(TAndCLoadingState());
    try {
      final RepoResponse<TAndCListResponseModel> response =
          await servicelocator<TAndCRepository>().getTAndCList();
      if (response.isSuccess) {
        emit(
          TAndCSuccessState(
            tAndCList: response.response?.tAndCListResponse ?? [],
          ),
        );
      } else {
        emit(
          TAndCFailureState(error: response.errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(
        TAndCFailureState(error: e.toString()),
      );
    }
  }
}
