part of 't_and_c_bloc.dart';

abstract class TAndCState extends Equatable {
  const TAndCState();

  @override
  List<Object> get props => [];
}

class TAndCLoadingState extends TAndCState {}

class TAndCSuccessState extends TAndCState {
  const TAndCSuccessState({
    required this.tAndCList,
  });

  final List<TAndCResponseModel> tAndCList;

  @override
  List<Object> get props => [tAndCList];
}

class TAndCFailureState extends TAndCState {
  const TAndCFailureState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}
