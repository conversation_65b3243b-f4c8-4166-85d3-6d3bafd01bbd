import 'package:json_annotation/json_annotation.dart';

part 't_and_c_response.g.dart';

@JsonSerializable()
class TAndCResponseModel {
  TAndCResponseModel({
    this.uuid,
    this.title,
    this.textContent,
    this.titleAr,
    this.textContentAr,
    this.active,
  });

  factory TAndCResponseModel.fromJson(Map<String, dynamic> json) =>
      _$TAndCResponseModelFromJson(json);
  String? uuid;
  String? title;
  @JsonKey(name: 'text_content')
  String? textContent;
  @JsonKey(name: 'title_ar')
  String? titleAr;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'text_content_ar')
  String? textContentAr;
  bool? active;

  Map<String, dynamic> toJson() => _$TAndCResponseModelToJson(this);
}
