import 'package:json_annotation/json_annotation.dart';
import 'package:scad_mobile/src/features/drawer_items/terms_and_conditions/data/models/t_and_c_model/response/t_and_c_response.dart';

part 't_and_c_list_response.g.dart';

@JsonSerializable()
class TAndCListResponseModel {
  TAndCListResponseModel({
    this.tAndCListResponse,
  });

  factory TAndCListResponseModel.fromJson(Map<String, dynamic> json) =>
      _$TAndCListResponseModelFromJson(json);

  @JsonKey(name: 'data')
  List<TAndCResponseModel>? tAndCListResponse;

  Map<String, dynamic> toJson() => _$TAndCListResponseModelToJson(this);
}
