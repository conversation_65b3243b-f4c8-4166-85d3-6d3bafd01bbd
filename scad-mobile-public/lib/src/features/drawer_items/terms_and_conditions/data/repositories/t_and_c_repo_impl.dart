import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/features/drawer_items/terms_and_conditions/data/data_sources/api_end_points.dart';
import 'package:scad_mobile/src/features/drawer_items/terms_and_conditions/data/models/t_and_c_model/response/t_and_c_list_response.dart';
import 'package:scad_mobile/src/features/drawer_items/terms_and_conditions/domain/repositories/t_and_c_repository_imports.dart';
import 'package:scad_mobile/src/services/http_service_impl.dart';
import 'package:scad_mobile/src/services/http_services.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class TAndCRepositoryImpl implements TAndCRepository {
  TAndCRepositoryImpl() {
    _httpService = HttpServiceRequests();
  }

  late HttpService _httpService;

  @override
  Future<RepoResponse<TAndCListResponseModel>> getTAndCList() async {
    try {
      // if(isDemoMode){
      //   return RepoResponse<TAndCListResponseModel>.success(
      //     response: TAndCListResponseModel.fromJson(demoTermsAndConditionsResponse),
      //   );
      // }

      final response = await _httpService.get(
        TAndCEndPoints.termsAndConditions,
      );
      if (response.isSuccess) {
        return RepoResponse<TAndCListResponseModel>.success(
          response: TAndCListResponseModel.fromJson(response.response),
        );
      } else {
        return RepoResponse<TAndCListResponseModel>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<TAndCListResponseModel>.error(
        errorMessage:  LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }
}
