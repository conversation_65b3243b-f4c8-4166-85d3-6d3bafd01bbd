import 'dart:async';

import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/features/drawer_items/terms_and_conditions/data/data_sources/api_end_points.dart';
import 'package:scad_mobile/src/features/drawer_items/terms_and_conditions/data/models/t_and_c_model/response/t_and_c_list_response.dart';
import 'package:scad_mobile/src/features/drawer_items/terms_and_conditions/domain/repositories/t_and_c_repository_imports.dart';
import 'package:scad_mobile/src/services/http_service_impl.dart';

class TAndCRepositoryImpl extends TAndCRepository {
  final _httpService = HttpServiceRequests();

  @override
  Future<RepoResponse<TAndCListResponseModel>> getTAndCList() async {
    final endpoint = TAndCEndPoints.termsAndConditions;
    final cacheKey = getCacheKey(endpoint);

    return fetchWithCache(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.get(endpoint),
      parseResult: (json) => TAndCListResponseModel.fromJson(json),
    );
  }
}
