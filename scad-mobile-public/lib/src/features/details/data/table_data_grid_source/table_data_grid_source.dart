import 'package:flutter/material.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';

class TableDataGridSource extends DataGridSource {
  TableDataGridSource({
    required List<Map<String, dynamic>> tableData,
    required List<String> columnNames,
  }) {
    /// the below line is to hide the legend names from the table
    columnNames..removeWhere((dt) => dt == 'legend_name')
    ..removeWhere((bt) => bt == 'legend_title');

    dataGridRows = tableData.map<DataGridRow>((employeeData) {
      return DataGridRow(
        cells: columnNames.map<DataGridCell>((columnName) {
          final value = employeeData[columnName];
          return DataGridCell<dynamic>(
            columnName: columnName,
            value: value ?? '-',
          );
        }).toList(),
      );
    }).toList();
  }

  List<DataGridRow> dataGridRows = [];

  @override
  List<DataGridRow> get rows => dataGridRows;

  @override
  DataGridRowAdapter? buildRow(DataGridRow row) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    int index = dataGridRows.indexOf(row);
    return DataGridRowAdapter(
      cells: row.getCells().map<Widget>((dataGridCell) {
        return Container(
          alignment: /*dataGridCell.value.runtimeType == double ||
                  dataGridCell.value == '-'
              ? Alignment.center
              :*/
              Alignment.centerLeft,
          color: (index % 2 != 0) ? AppColors.greyShade7 : Colors.transparent,
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: Text(
            dataGridCell.value.runtimeType == double
                ? (dataGridCell.value as double).toStringAsFixed(2)
                : dataGridCell.value.toString(),
            style: AppTextStyles.s10w5cWhite.copyWith(
              color: isLightMode
                  ? AppColors.black
                  : index.isEven
                      ? AppColors.white
                      : AppColors.black,
            ),
            overflow: TextOverflow.ellipsis,
            textScaler: TextScaler.linear(textScaleFactor.value),
          ),
        );
      }).toList(),
    );
  }
}
