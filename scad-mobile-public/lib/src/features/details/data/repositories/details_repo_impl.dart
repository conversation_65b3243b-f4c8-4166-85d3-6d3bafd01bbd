import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/features/details/data/datasources/details_end_points.dart';
import 'package:scad_mobile/src/features/details/data/models/insight_filter_model.dart';
import 'package:scad_mobile/src/features/details/domain/repositories/details_repository_imports.dart';
import 'package:scad_mobile/src/services/http_service_impl.dart';
import 'package:scad_mobile/src/services/http_services.dart';

import '../../../../../translations/locale_keys.g.dart';

class DetailsRepositoryImpl implements DetailsRepository {
  DetailsRepositoryImpl() {
    _httpService = HttpServiceRequests();
  }

  late HttpService _httpService;

  @override
  Future<RepoResponse<IndicatorDetailsResponse>> computeData(
      {required Map<String, dynamic> dataMap}) async {
    try {
      final response = await _httpService.postJson(
        DetailsEndPoints.computeDataEndPoint,
        server: ApiServer.ifp,
        jsonPayloadMap: dataMap,
      );

      if (response.isSuccess) {
        return RepoResponse<IndicatorDetailsResponse>.success(
          response: IndicatorDetailsResponse.fromJson(response.response),
        );
      } else {
        return RepoResponse<IndicatorDetailsResponse>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<IndicatorDetailsResponse>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<IndicatorDetailsResponse>> compareIndicators({
    required Map<String, dynamic> payload,
  }) async {
    try {
      final response = await _httpService.postJson(
        DetailsEndPoints.compareIndicatorsEndPoint,
        server: ApiServer.ifp,
        jsonPayloadMap: payload,
      );

      if (response.isSuccess) {
        return RepoResponse<IndicatorDetailsResponse>.success(
          response: IndicatorDetailsResponse.fromJson(response.response),
        );
      } else {
        return RepoResponse<IndicatorDetailsResponse>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<IndicatorDetailsResponse>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<InsightFilterModelResponse>> getCompleteDataToFilter({
    required Map<String, dynamic> payload,
  }) async {
    try {
      final response = await _httpService.postJson(
        DetailsEndPoints.insightFilterEndPoint,
        server: ApiServer.ifp,
        jsonPayloadMap: payload,
      );

      if (response.isSuccess) {
        return RepoResponse<InsightFilterModelResponse>.success(
          response: InsightFilterModelResponse.fromJson(response.response),
        );
      } else {
        return RepoResponse<InsightFilterModelResponse>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<InsightFilterModelResponse>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }
}
