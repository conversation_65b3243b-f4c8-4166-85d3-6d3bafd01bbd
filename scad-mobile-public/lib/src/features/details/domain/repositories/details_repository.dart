part of 'details_repository_imports.dart';

abstract class DetailsRepository {
  /// functiom for compute data indicators
  Future<RepoResponse<IndicatorDetailsResponse>> computeData({
    required Map<String, dynamic> dataMap,
  });

  /// function to compare indicators
  Future<RepoResponse<IndicatorDetailsResponse>> compareIndicators({
    required Map<String, dynamic> payload,
  });

  /// function for insight discovery filter
  Future<RepoResponse<InsightFilterModelResponse>> getCompleteDataToFilter({
    required Map<String, dynamic> payload,
  });
}
