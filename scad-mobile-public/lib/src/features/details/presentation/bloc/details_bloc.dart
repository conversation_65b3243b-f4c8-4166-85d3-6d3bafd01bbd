import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:math';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/route_manager/route_imports.dart';
import 'package:scad_mobile/src/common/functions/indicator_date_setting.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_response_helper.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/config/dependancy_injection/injection_container.dart';
import 'package:scad_mobile/src/features/details/data/models/insight_filter_model.dart';
import 'package:scad_mobile/src/features/details/domain/repositories/details_repository_imports.dart';
import 'package:scad_mobile/src/features/details/domain/usecases/frequency_selector_model.dart';
import 'package:scad_mobile/src/utils/extentions/string_extentions.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

part 'details_event.dart';

part 'details_state.dart';

class DetailsBloc extends Bloc<DetailsEvent, DetailsState> {
  DetailsBloc() : super(DetailsInitial()) {
    on<DetailsEvent>((event, emit) {});
    on<ExpandIndicatorTileEvent>(_onExpandIndicatorTile);
    on<PresentationTypeSelectionEvent>(_onPresentationTypeSelection);
    on<ToggleTermsAndConditionCheckEvent>(_onToggleTermsAndCondition);
    on<ComputeBottomSheetValueEvent>(_onSelectComputeType);
    on<SwitchTitleEvent>(_onSwitchTitle);
    on<FirstIndicatorDropdownEvent>(_onFirstIndicatorDropdown);
    on<FirstIndicatorRadioButtonEvent>(_onFirstInidcatorRadio);
    on<SecondIndicatorCheckBoxEvent>(_onSecondIndicatorCheckBox);
    on<ToggleSecondIndicatorExpansionEvent>(_onToggleSecondIndicatorExpansion);
    on<ComputeDataEvent>(_onComputeData);
    on<ChangeDriversValueUpdateEvent>(_onUpdateChangeDriver);
    on<SelectMeasurementEvent>(_onSelectMeasurement);
    on<DefaultFilterValueEvent>(_onSetDefaultFilter);
    on<FilterDropdownExpansionEvent>(_onFilterDropdown);
    on<FilterCheckboxEvent>(_onFilterCheckbox);
    on<FilterRadioEvent>(_onFilterRadio);
    on<FilterApplyEvent>(_onFilterApply);
    on<CompareIndicatorEvent>(_onCompareIndicators);
    on<DetailsPageFilterResetEvent>(_onResetDetailsPage);
    on<ChangeDataFrequencyEvent>(_onChangeFrequency);
    on<ChangeDataFrequencyApplyEvent>(_onApplyChangeFrequency);
    on<ForecastVisibilityUpdateEvent>(_onForecastVisibilityChanged);
    on<MonthYearFilterEvent>(_onMonthYearFilter);
    on<GenerateMetaDataEvent>(_onMetaData);
    on<SelectedIndicatorIdFromSearchEvent>(
      _onSelectedIndicatorIdFromGlobalSearch,
    );
    on<SelectVisualizationEvent>(_onSelectVisualization);
    on<UpdateLegendEvent>(_onUpdateLegend);
    on<SolidStateUpdateEvent>(_onUpdateSolidSeries);
    on<BackClearEvent>(_onBackClear);
    on<ToResetChangeDriversEvent>(_onChangeDriverResetStatus);
    on<PresentationTypeSelectionDoneEvent>(_onPresentationTypeSelectionDone);
  }

  final Map<String, List<String>> filterCondition = {};
  bool isFirstTimeFilter = true;
  String selectedFrequency = '';

  Future<void> _onExpandIndicatorTile(
    ExpandIndicatorTileEvent event,
    Emitter<DetailsState> emit,
  ) async {
    final currentState = state;

    // Check if it's a parent tile
    if (!event.isChild) {
      if (currentState is CompareIndicatorExpandedState &&
          currentState.expandedIndex == event.index) {
        // Toggle the overall state for the parent
        emit(
          CompareIndicatorExpandedState(
            expandedIndex: event.index,
            isChildExpanded: !currentState.isChildExpanded,
          ),
        );
      } else {
        // If it's a new parent tile, expand it
        emit(
          CompareIndicatorExpandedState(
            expandedIndex: event.index,
            isChildExpanded: true,
          ),
        );
      }
    } else {
      // If it's a child tile, update the state for that specific child
      if (currentState is CompareIndicatorExpandedState &&
          currentState.expandedIndex == event.parentIndex &&
          currentState.isChildExpanded != event.isChild) {
        emit(
          CompareIndicatorExpandedState(
            expandedIndex: currentState.expandedIndex,
            isChildExpanded: event.isChild,
          ),
        );
      }
    }
  }

  Future<void> _onPresentationTypeSelection(
    PresentationTypeSelectionEvent event,
    Emitter<DetailsState> emit,
  ) async {
    emit(
      PresentationTypeSelectionState(selectedType: event.selectType),
    );
  }

  Future<void> _onPresentationTypeSelectionDone(
    PresentationTypeSelectionDoneEvent event,
    Emitter<DetailsState> emit,
  ) async {
    // Navigator.pop(servicelocator<AppRouter>().navigatorKey.currentContext!);
    servicelocator<AppRouter>().navigatorKey.currentContext!.popRoute();

    emit(
      const PresentationTypeSelectionDoneState(),
    );
  }

  Future<void> _onToggleTermsAndCondition(
    ToggleTermsAndConditionCheckEvent event,
    Emitter<DetailsState> emit,
  ) async {
    emit(ToggleTermsAndConditionCheckState(isAccepted: event.isAccepted));
  }

  /// for selecting the compute type
  Future<void> _onSelectComputeType(
    ComputeBottomSheetValueEvent event,
    Emitter<DetailsState> emit,
  ) async {
    emit(ComputeBottomSheetValueState(value: event.value));
  }

  /// for switching titles in compute page
  Future<void> _onSwitchTitle(
    SwitchTitleEvent event,
    Emitter<DetailsState> emit,
  ) async {
    final List<String> titles = [
      LocaleKeys.summation.tr(),
      LocaleKeys.subtraction.tr(),
      LocaleKeys.multiplication.tr(),
      LocaleKeys.division.tr(),
    ];

    final int currentIndex = titles.indexOf(event.value);
    final int newIndex = (currentIndex + event.direction) % titles.length;

    emit(ComputePageTitleState(value: titles[newIndex]));
  }

  /// function for handling the first indicator dropdown
  Future<void> _onFirstIndicatorDropdown(
    FirstIndicatorDropdownEvent event,
    Emitter<DetailsState> emit,
  ) async {
    final List<Properties> indicatorList =  IndicatorDateSetting.removeDuplicates((event.indicatorList as List<Properties>))  ?? [];

    final List<String> optionList = [];

    for (final element in indicatorList) {
      if (element == event.selectedIndicator) {
        optionList.addAll(element.options ?? []);
      }
    }

    emit(
      FirstIndicatorDropdownState(
        selectedIndicator: event.selectedIndicator,
        optionList: optionList,
      ),
    );
  }

  /// function for selecting the first indicator expansion tile
  Future<void> _onFirstInidcatorRadio(
    FirstIndicatorRadioButtonEvent event,
    Emitter<DetailsState> emit,
  ) async {
    final String selectedRadioValue = event.groupValue;

    emit(
      FirstIndicatorRadioButtonState(
        selectedIndicator: event.selectedIndicator,
        groupValue: selectedRadioValue,
      ),
    );
  }

  /// function for opening and closing the first indicator expansion tile
  Future<void> _onToggleSecondIndicatorExpansion(
    ToggleSecondIndicatorExpansionEvent event,
    Emitter<DetailsState> emit,
  ) async {
    final List<Properties> updatedList =  List.from(  IndicatorDateSetting.removeDuplicates(event.updatedList));

    // Close all other tiles
    for (int i = 0; i < updatedList.length; i++) {
      if (i != event.index) {
        updatedList[i].isOpened = false;
      }
    }

    updatedList[event.index].isOpened =
        !(updatedList[event.index].isOpened ?? false);

    emit(DetailsInitial());

    emit(
      ToggleSecondIndicatorExpansionState(
        updatedList: updatedList,
      ),
    );
  }

  /// function for selecting the second indicator expansion tile
  Future<void> _onSecondIndicatorCheckBox(
    SecondIndicatorCheckBoxEvent event,
    Emitter<DetailsState> emit,
  ) async {
    final List<OptionList> dataList = event.itemList;

    final List<OptionList> updatedList = [];

    dataList[event.index].isSelected = event.isSelected;
    if (updatedList.contains(dataList[event.index])) {
      updatedList.remove(dataList[event.index]);
    } else {
      updatedList.add(dataList[event.index]);
    }

    emit(DetailsInitial());

    emit(
      SecondIndicatorCheckBoxState(
        selectedItems: updatedList,
        // selectedItems: updatedList.where((item) => item.isSelected).toList(),
      ),
    );
  }

  /// call the compute api
  Future<void> _onComputeData(
    ComputeDataEvent event,
    Emitter<DetailsState> emit,
  ) async {
    emit(ComputeDataLoadingState());
    try {
      final RepoResponse<IndicatorDetailsResponse> response =
          await servicelocator<DetailsRepository>().computeData(
        dataMap: event.dataMap,
      );

      if (response.isSuccess) {
        if (response.response?.status == 'failed') {
          emit(
            ComputeDataErrorState(
              isLoading: false,
              errorMessage: response.response?.message ?? '',
            ),
          );
        } else {
          emit(
            ComputeDataSuccessState(
              isLoading: false,
              indicatorDetails: response.response ?? IndicatorDetailsResponse(),
              selectedMeasurement: event.selectedMeasurement,
              nameOfIndicator: event.nameOfIndicator,
            ),
          );
        }
      } else {
        emit(
          ComputeDataErrorState(
            isLoading: false,
            errorMessage: response.errorMessage,
          ),
        );
      }
    }catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(
        ComputeDataErrorState(
          isLoading: false,
          errorMessage:LocaleKeys.somethingWentWrong.tr(),
        ),
      );
    }
  }

  /// to call the compare indicator api
  Future<void> _onCompareIndicators(
    CompareIndicatorEvent event,
    Emitter<DetailsState> emit,
  ) async {
    try {
      filterCondition.clear();
      emit(CompareIndicatorLoadingState());
      final RepoResponse<IndicatorDetailsResponse> response =
          await servicelocator<DetailsRepository>().compareIndicators(
        payload: event.payload,
      );

      if (response.isSuccess) {
       final List<SeriesMeta> responseValue = response.response?.indicatorVisualizations?.visualizationsMeta?.firstOrNull?.seriesMeta ?? [];
        for (var i = 0; i < responseValue.length; i++) {
           responseValue[i].data?.firstOrNull?['legend_title'] = response.response?.indicatorVisualizations?.visualizationsMeta?.firstOrNull?.seriesTitles?.values .toList() [i];
        }
      
        emit(
          CompareIndicatorSuccessState(
            indicatorDetails: response.response ?? IndicatorDetailsResponse(),
            domainName: event.domainName,
            initialIndicatorDetails: event.indicatorDetails,
          ),
        );
      } else {
        emit(
          CompareIndicatorFailureState(
            errorText: LocaleKeys.indicatorComparisonError.tr(),
          ),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(CompareIndicatorFailureState(errorText: LocaleKeys.somethingWentWrong.tr()));
    }
  }

  /// to update change drivers
  Future<void> _onUpdateChangeDriver(
    ChangeDriversValueUpdateEvent event,
    Emitter<DetailsState> emit,
  ) async {
    emit(
      ChangeDriversValueUpdateState(indicatorDetails: event.indicatorDetails),
    );
  }

  /// to reset change drivers
  Future<void> _onChangeDriverResetStatus(
    ToResetChangeDriversEvent event,
    Emitter<DetailsState> emit,
  ) async {

    for (final meta in event.indicatorVal?.indicatorDetails
            .indicatorVisualizations?.visualizationsMeta ??
        <VisualizationsMeta>[]) {
      for (final series in meta.seriesMeta ?? <SeriesMeta>[]) {
        if ((series.data ?? []).isNotEmpty &&
            !series.id!.contains('-forecast')) {
          for (final dt in series.data ?? <Map<String, dynamic>>[]) {
            if(!dt.containsKey('legend_name'))
            {dt['legend_name'] = series.label.toString().capitalize();}
          }
          // dataToForcast.add(series.data ?? []);
        }

        // dataToFilter = List.from(series.data ?? []);
      }
    }


    emit(
      ResetChangeDriversState(
        toReset: event.toReset,
        scadProjectionValue: event.scadProjectionValue,
        indicatorVal: event.indicatorVal,
      ),
    );
  }

  Future<void> _onResetDetailsPage(
    DetailsPageFilterResetEvent event,
    Emitter<DetailsState> emit,
  ) async {
    emit(DetailsPageFilterResetState(indicatorDetails: event.indicatorDetails));
  }

  /// to update selected measurement for compute data
  Future<void> _onSelectMeasurement(
    SelectMeasurementEvent event,
    Emitter<DetailsState> emit,
  ) async {
    emit(
      SelectMeasurementState(selectedValue: event.selectedValue),
    );
  }

  /// to set the default filter values
  Future<void> _onSetDefaultFilter(
    DefaultFilterValueEvent event,
    Emitter<DetailsState> emit,
  ) async {
    filterCondition.clear();
    final List<Properties> updatedList = List.from(event.propertyList);

    for (final element in updatedList) {
      bool isDefault = false;
      for (final option in element.optionList) {
        if (element.defaultVal?.toLowerCase() == option.title?.toLowerCase()) {
          option.isSelected = true;
          isDefault = true;
          element.selectedOptionItem = option.title;
        }
      }
      if (!isDefault) {
        element.selectedOptionItem = element.optionList.firstOrNull?.title;
        element.optionList.firstOrNull?.isSelected = true;
      }
    }
    for (final element in updatedList) {
      for (final option in element.optionList) {
        if (option.isSelected &&
            !(filterCondition[element.path]
                    ?.contains(option.title?.toLowerCase()) ??
                false)) {
          filterCondition
              .putIfAbsent(element.path ?? '', () => [])
              .add(option.title?.toLowerCase() ?? '');
        }
      }
    }

    for (final element in updatedList) {
      for (final option in element.optionList) {
        if (option.isSelected == false) {
          filterCondition[element.path]?.remove(option.title?.toLowerCase());
        }
      }
    }

    isFirstTimeFilter = false;

    emit(
      FilterCheckboxState(
        propertiesList: updatedList,
      ),
    );
  }

  /// to do the dropdown functionality on filter
  Future<void> _onFilterDropdown(
    FilterDropdownExpansionEvent event,
    Emitter<DetailsState> emit,
  ) async {
    final List<Properties> updatedList = event.propertyList;

    updatedList[event.index].isOpened =
        !(updatedList[event.index].isOpened ?? false);

    emit(EmptyState());

    emit(FilterDropdownExpansionSatte(updatedList: updatedList));
  }

  /// to do the checkbox functionality on filter
  Future<void> _onFilterCheckbox(
    FilterCheckboxEvent event,
    Emitter<DetailsState> emit,
  ) async {
    final List<Properties> dataList = List.from(event.propertiesList);

    dataList[event.index].optionList[event.childIndex].isSelected =
        !dataList[event.index].optionList[event.childIndex].isSelected;

    final int selectedCheckboxCount = dataList[event.index]
        .optionList
        .where(
          (element) =>
              element.isSelected && dataList[event.index].path != 'OBS_DT',
        )
        .length;

    String selectedItems = '';

    for (final element in dataList[event.index].optionList) {
      if (element.isSelected) {
        if (selectedItems.isNotEmpty) {
          selectedItems += ', ';
        }
        selectedItems += element.title ?? '';
      }
    }

    for (final element in dataList) {
      if (!element.optionList.any((option) => option.isSelected)) {
        bool isDefault = false;
        for (final option in element.optionList) {
          if (option.title?.toLowerCase().trim() ==
              element.defaultVal?.toLowerCase().trim()) {
            option.isSelected = true;
            selectedItems = option.title ?? '';
            isDefault = true;
          }
        }
        if (!isDefault) {
          selectedItems = element.optionList.firstOrNull?.title ?? '';
          element.optionList.firstOrNull?.isSelected = true;
        }
      }
    }

    dataList[event.index].selectedOptionItem = selectedItems;

    for (final element in dataList[event.index].optionList) {
      if (element.isSelected &&
          !(filterCondition[dataList[event.index].path]
                  ?.contains(element.title?.toLowerCase()) ??
              false)) {
        filterCondition
            .putIfAbsent(dataList[event.index].path ?? '', () => [])
            .add(element.title?.toLowerCase() ?? '');
      }
    }

    for (final element in dataList[event.index].optionList) {
      if (element.isSelected == false) {
        filterCondition[dataList[event.index].path]
            ?.remove(element.title?.toLowerCase());
      }
    }

    emit(EmptyState());

    if (selectedCheckboxCount > 1) {
      for (int i = 0; i < dataList.length; i++) {
        if (i != event.index && dataList[i].path != 'OBS_DT') {
          dataList[i].type = 'radio';
        } else if (dataList[i].path == 'OBS_DT') {
          dataList[i].type = 'checkbox';
        }
      }
    } else {
      for (final element in dataList) {
        if (element.path == 'OBS_DT' || selectedCheckboxCount == 1) {
          element.type = 'checkbox';
        }
      }
    }

    emit(
      FilterCheckboxState(
        propertiesList: dataList,
      ),
    );
  }

  /// for selecting the radio button during filtering indicators
  Future<void> _onFilterRadio(
    FilterRadioEvent event,
    Emitter<DetailsState> emit,
  ) async {
    final List<Properties> dataList = List.from(event.propertiesList);

    dataList[event.index].optionList[event.childIndex].isSelected = true;

    for (var i = 0; i < dataList[event.index].optionList.length; i++) {
      if (i != event.childIndex) {
        dataList[event.index].optionList[i].isSelected = false;
      }
    }

    String selectedItems = '';

    for (final element in dataList[event.index].optionList) {
      if (element.isSelected) {
        if (selectedItems.isNotEmpty) {
          selectedItems += ', ';
        }
        selectedItems += element.title ?? '';
      }
    }

    dataList[event.index].selectedOptionItem = selectedItems;

    for (final element in dataList[event.index].optionList) {
      if (element.isSelected &&
          !(filterCondition[dataList[event.index].path]
                  ?.contains(element.title?.toLowerCase()) ??
              false)) {
        filterCondition
            .putIfAbsent(dataList[event.index].path ?? '', () => [])
            .add(element.title?.toLowerCase() ?? '');
      }
    }

    for (final element in dataList[event.index].optionList) {
      if (element.isSelected == false) {
        filterCondition[dataList[event.index].path]
            ?.remove(element.title?.toLowerCase());
      }
    }

    emit(EmptyState());

    emit(
      FilterCheckboxState(
        propertiesList: dataList,
      ),
    );
  }

  /// for applying filter to the indicators
  Future<void> _onFilterApply(
    FilterApplyEvent event,
    Emitter<DetailsState> emit,
  ) async {
    // emit(const FilterApplyLoadingState(isLoading: true));
    final List<VisualizationsMeta> visualizationMeta = List.from(
      event.originlIndicatorDetails.indicatorVisualizations
              ?.visualizationsMeta ??
          [],
    );

    List<Map<String, dynamic>> dataToFilter = [];
    List<List<Map<String, dynamic>>> dataToForcast = [];

    if (event.originlIndicatorDetails.type == 'insights-discovery') {
      dataToFilter = List.from(event.insightToFilterData ?? []);

      ///
    } else {
      for (final meta in visualizationMeta) {
        if (event.isForeCast == true) {
          for (final series in meta.seriesMeta ?? <SeriesMeta>[]) {
            if ((series.data ?? []).isNotEmpty &&
                !series.id!.contains('-forecast')) {
                  for (final dt in series.data??<Map<String, dynamic>>[]) {
                    dt['legend_name'] = series.label.toString().capitalize();
                  }
              dataToForcast.add(series.data ?? []);
            }

            // dataToFilter = List.from(series.data ?? []);
          }
        } else {
          final List<Map<String, dynamic>>? a = meta.seriesMeta
              ?.where((e) => !e.id!.contains('-forecast'))
              .first
              .data;

          dataToFilter = List.from(a ?? []);
        }
      }
    }

    emit(EmptyState());
    emit(
      FilterApplyState(
        filteredDataList: event.isForeCast == true
            ? dataToForcast
            : _getFilterResult(
                List.from(dataToFilter),
              ),
        isForecast: event.isForeCast,
      ),
    );
    // emit(const FilterApplyLoadingState(isLoading: false));
  }

  List<List<Map<String, dynamic>>> _getFilterResult(
    List<Map<String, dynamic>>? seriesData,
  ) {
    final List<Map<String, dynamic>> filteredData = [];

    List<List<Map<String, dynamic>>> filteredDataList = [];
    bool isGreaterThanOne = false;

    filterCondition.forEach((key, value) {
      if (value.length > 1 && key != 'OBS_DT') {
       
        for (var i = 0; i < value.length; i++) {
          final List<Map<String, dynamic>> tempData = [];

          
          for (final data in seriesData ?? <Map<String, dynamic>>[]) {
            
            if (data[key].toString().toLowerCase().trim() ==
                value[i].toLowerCase().trim()) {
              bool flag = true;
              filterCondition.forEach((innerKey, innerVal) {
                if (innerKey != key && innerKey != 'OBS_DT') {
                 
                  if (data[innerKey].toString().toLowerCase().trim() !=
                      innerVal.firstOrNull?.toLowerCase().trim()) {
                    flag = false;
                  }
                }
              });
              if (flag) {
                data['legend_name'] = value[i].capitalize();
                tempData.add(data);
              }
            }
          }
          tempData.sort(
            (a, b) => a['OBS_DT'].toString().compareTo(b['OBS_DT'].toString()),
          );
          filteredDataList.add(tempData);
        }
        isGreaterThanOne = true;
      }
    });


    if (!isGreaterThanOne) {
      for (final data in seriesData ?? <Map<String, dynamic>>[]) {
        bool flag = true;
        filterCondition.forEach((innerKey, innerVal) {
   
          if (innerKey != 'OBS_DT') {
            if (data[innerKey].toString().toLowerCase().trim() !=
                innerVal.firstOrNull?.toLowerCase().trim()) {
              flag = false;
            }
          }
        });
    
        if (flag) {
          filteredData.add(data);
        }
      }
      filteredData.sort(
        (a, b) => a['OBS_DT'].toString().compareTo(b['OBS_DT'].toString()),
      );

      filteredDataList.add(filteredData);
    }

    if (filterCondition.containsKey('OBS_DT') &&
        (!(filterCondition['OBS_DT'] ?? []).contains('All') &&
            !(filterCondition['OBS_DT'] ?? []).contains('ALL') &&
            !(filterCondition['OBS_DT'] ?? []).contains('all') &&
            !(filterCondition['OBS_DT'] ?? []).contains('الجميع'))) {
      final List<List<Map<String, dynamic>>> dateFiltered = [];

      for (final element in filteredDataList) {
        final List<Map<String, dynamic>> dateTempData = [];
        for (final data in element) {
          final str = data['OBS_DT'].toString().trim().split('-').first;
          if ((filterCondition['OBS_DT'] ?? []).contains(str)) {
            dateTempData.add(data);
          }
        }
        dateFiltered.add(dateTempData);
      }

      filteredDataList = dateFiltered;
    }


    return filteredDataList;
  }

  /// for changing the selcted frequency for indicators
  Future<void> _onChangeFrequency(
    ChangeDataFrequencyEvent event,
    Emitter<DetailsState> emit,
  ) async {
    final List<FrequencySelectorModel> updatedList = event.frequencyList;

    for (var i = 0; i < updatedList.length; i++) {
      // if (i != event.index) {
      updatedList[i].isSelected = false;
      // }
    }

    updatedList[event.index].isSelected = true;

    emit(EmptyState());

    emit(
      ChangeDataFrequencyState(
        frequencyList: updatedList,
      ),
    );
  }

  /// for applying change frequency
  Future<void> _onApplyChangeFrequency(
    ChangeDataFrequencyApplyEvent event,
    Emitter<DetailsState> emit,
  ) async {
    emit(EmptyState());
    selectedFrequency = event.selectedFrequency;

    String currentSelectedFrequency = '';

    final List<List<Map<String, dynamic>>> forecastSeries = List.generate(
      (event.forecastSeriesList ?? []).length,
      (outerIndex) => List.generate(
        (event.forecastSeriesList?[outerIndex] ?? []).length,
        (innerIndex) => Map<String, dynamic>.from(
          (event.forecastSeriesList ?? [])[outerIndex][innerIndex],
        ),
      ),
    );



    final List<List<Map<String, dynamic>>> toFilterList = List.generate(
      (event.filteredDataList ?? []).length,
      (outerIndex) => List.generate(
        (event.filteredDataList?[outerIndex] ?? []).length,
        (innerIndex) => Map<String, dynamic>.from(
          (event.filteredDataList ?? [])[outerIndex][innerIndex],
        ),
      ),
    );

    final List<FrequencySelectorModel> timeUnit = List.generate(
      (event.freqList ?? []).length,
      (innerIndex) => FrequencySelectorModel.fromJson(
        jsonDecode(jsonEncode((event.freqList ?? [])[innerIndex]))
            as Map<String, dynamic>,
      ),
    );

    String currentDateType = '';

    if (timeUnit.map((e) => e.title).toList().contains('Monthly') ||
        timeUnit.map((e) => e.title).toList().contains('شهري')) {
      currentDateType = 'Monthly';
    } else if (timeUnit.map((e) => e.title).toList().contains('Quarterly') ||
        timeUnit.map((e) => e.title).toList().contains('ربع سنوي')) {
      currentDateType = 'Quarterly';
    } else {
      currentDateType = 'Yearly';
    }

    if (selectedFrequency == 'Monthly' || selectedFrequency == 'شهري') {
      emit(EmptyState());
      currentSelectedFrequency = 'Monthly';

      emit(
        DataFrequencyApplyState(
          filteredData: toFilterList,
          freqList: event.freqList,
          inidicatorData: event.inidicatorData,
          selectedFrequency: currentSelectedFrequency,
          isFromMonthly: event.isFromMonthly,
          forecastData: forecastSeries,
        ),
      );
    }

    ///
    else if (selectedFrequency == 'Quarterly' ||
        selectedFrequency == 'ربع سنوي') {
      currentSelectedFrequency = 'Quarterly';
      if (currentDateType == 'Monthly') {
        emit(EmptyState());

        emit(
          DataFrequencyApplyState(
            filteredData: IndicatorDateSetting.getMonthlyValue(
              data: toFilterList,
            ),
            freqList: event.freqList,
            inidicatorData: event.inidicatorData,
            selectedFrequency: currentSelectedFrequency,
            forecastData: IndicatorDateSetting.getMonthlyValue(
              data: forecastSeries,
              isForecast: true,
            ),
          ),
        );
      } else {
        emit(
          DataFrequencyApplyState(
            filteredData: toFilterList,
            freqList: event.freqList,
            inidicatorData: event.inidicatorData,
            selectedFrequency: currentSelectedFrequency,
            forecastData: forecastSeries,
          ),
        );
      }
    } else {
      currentSelectedFrequency = 'Yearly';
      if (currentDateType == 'Monthly') {
        emit(EmptyState());

        emit(
          DataFrequencyApplyState(
            filteredData: IndicatorDateSetting.getMonthlyValue(
              data: toFilterList,
              type: 'Yearly',
            ),
            freqList: event.freqList,
            inidicatorData: event.inidicatorData,
            selectedFrequency: currentSelectedFrequency,
            forecastData: IndicatorDateSetting.getMonthlyValue(
              data: forecastSeries,
              type: 'Yearly',
              isForecast: true,
            ),
          ),
        );
      } else if (currentDateType == 'Quarterly') {
      
            final List<List<Map<String, dynamic>>>  filteredValue =   IndicatorDateSetting.getQuarterlyValue(
              data: toFilterList,
            );
             final List<List<Map<String, dynamic>>>  forcatValueFiltered =   IndicatorDateSetting.getQuarterlyValue(
             data: forecastSeries,
              isForecast: true,
              joinValue: filteredValue.lastOrNull?.lastOrNull == null ? {} :filteredValue.lastOrNull?.lastOrNull
            );
            
        emit(EmptyState());
        emit(
          DataFrequencyApplyState(
            filteredData:filteredValue,
            freqList: event.freqList,
            inidicatorData: event.inidicatorData,
            selectedFrequency: currentSelectedFrequency,
            forecastData: forcatValueFiltered,
          ),
        );
      } else {
        emit(EmptyState());
        emit(
          DataFrequencyApplyState(
            filteredData: toFilterList,
            freqList: event.freqList,
            inidicatorData: event.inidicatorData,
            selectedFrequency: currentSelectedFrequency,
            forecastData: forecastSeries,
          ),
        );
      }
    }
  }

  /// to update selected measurement for compute data
  Future<void> _onForecastVisibilityChanged(
    ForecastVisibilityUpdateEvent event,
    Emitter<DetailsState> emit,
  ) async {
    emit(
      ForecastVisibilityUpdateState(isVisibilityOn: event.isVisibilityOn),
    );
  }

  /// to update selected MONTH/YEAR filter
  Future<void> _onMonthYearFilter(
    MonthYearFilterEvent event,
    Emitter<DetailsState> emit,
  ) async {
    
    final List<Options> optionList = List.from(event.options);

    final List<List<Map<String, dynamic>>> toFilterList =
        List.from(event.filteredDataList ?? []);

    final Options selectedItem = optionList[event.index]..isSelected = true;

    List<List<Map<String, dynamic>>> filteredList = [];

try {
    SeriesMeta? series = event.indicatorData.indicatorVisualizations
                    ?.visualizationsMeta?.firstOrNull?.seriesMeta?.firstOrNull;
  for (final SeriesMeta element in event.indicatorData.indicatorVisualizations
                    ?.visualizationsMeta?.first.seriesMeta ?? []) {
                        if(!(element.id ?? '').contains('-forecast') && DateTime.tryParse(element.xMax ?? '') != null) {
                      if((DateTime.tryParse(element.xMax ?? '') ?? DateTime.now() )
                      .isAfter(DateTime.tryParse(series?.xMax ?? '') ?? 
                      DateTime.now(),)) {
                        series = element;
                      }
                      }
                     
  }

   final DateTime startDate =  (series?.xMax  ?? '') .toDateTime() ??
        DateTime.now();
    DateTime endDate = DateTime.now();

    if (selectedItem.id?.toLowerCase() == 'all') {
      filteredList = toFilterList;
    } else {
      for (final element in toFilterList) {
        if (selectedItem.unit?.toLowerCase() == 'years') {
          endDate = startDate.subtract(
            Duration(
              days: 365 * (selectedItem.value ?? 0),
            ),
          );
        } else {
          endDate = startDate.subtract(
            Duration(
              days: 30 * (selectedItem.value ?? 0),
            ),
          );
        }
        filteredList.add(_filterDataByDate(element, startDate, endDate));
      }
    }

    /// for updating the UI
    for (int i = 0; i < optionList.length; i++) {
      if (i != event.index) {
        optionList[i].isSelected = false;
      }
    }
} catch (e) {}
    emit(EmptyState());

    emit(
      MonthYearFilterState(
        options: optionList,
        filteredData: filteredList,
        index: event.index,
        isFromVisulization: event.isFromVisulization,
      ),
    );

    if (event.resetPeriodFilter) {
      emit(PeriodFilterApplyAgainState());
    }

  }

  /// filter data from given start and end date
  List<Map<String, dynamic>> _filterDataByDate(
    List<Map<String, dynamic>> data,
    DateTime startDate,
    DateTime endDate,
  ) {
    return data.where((entry) {
      final DateTime obsDate = DateTime.parse(entry['OBS_DT'] as String);
      return (obsDate.isAfter(endDate)) &&
          (obsDate.isAtSameMomentAs(startDate) || obsDate.isBefore(startDate));
    }).toList();
  }

  /// function to generate meta data
  Future<void> _onMetaData(
    GenerateMetaDataEvent event,
    Emitter<DetailsState> emit,
  ) async {
    final List<MetaData?> metaData = List.from(event.metaData);

    final List<String> items = [
      LocaleKeys.indicatorNameMetaInfo.tr(),
      LocaleKeys.indicatorDescriptionMetaInfo.tr(),
      LocaleKeys.dataSourcesMetaInfo.tr(),
      LocaleKeys.statisticalCalculationMethodMetaInfo.tr(),
    ];

    final List<MetaData?> selectedItems =
        metaData.where((entry) => items.contains(entry?.label)).toList();

    emit(
      GenerateMetaDataState(metaData: selectedItems, isOpened: event.isOpened),
    );
  }

  /// to get selected indicator Id from search results
  Future<void> _onSelectedIndicatorIdFromGlobalSearch(
    SelectedIndicatorIdFromSearchEvent event,
    Emitter<DetailsState> emit,
  ) async {
    emit(
      SelectedIndicatorIdFromSearchState(indicatorId: event.indicatorId),
    );
  }

  /// to set the value of selected visualization if type is insight-discovery
  Future<void> _onSelectVisualization(
    SelectVisualizationEvent event,
    Emitter<DetailsState> emit,
  ) async {
    emit(const InsightDiscoveryFullDataLoadState(isLoading: true));

    final Map<String, dynamic> payLoad = {
      'dbColumn': event.selectedVisualization?.indicatorVisualizations
          ?.visualizationsMeta?.firstOrNull?.dbColumn,
      'dbIndicatorId': event.selectedVisualization?.indicatorVisualizations
          ?.visualizationsMeta?.firstOrNull?.seriesMeta?.firstOrNull?.dbIndicatorId,
      'viewName': event.selectedVisualization?.indicatorVisualizations
          ?.visualizationsMeta?.firstOrNull?.viewName,
    };

    if (event.selectedVisualization?.indicatorVisualizations
            ?.visualizationsMeta?.firstOrNull?.yearlyData !=
        null) {
      payLoad['yearlyData'] = event.selectedVisualization
          ?.indicatorVisualizations?.visualizationsMeta?.firstOrNull?.yearlyData
          ?.toJson();
    }

    try {
      final RepoResponse<InsightFilterModelResponse> response =
          await servicelocator<DetailsRepository>().getCompleteDataToFilter(
        payload: {
          'meta': [payLoad],
        },
      );

      if (response.isSuccess) {
        emit(const InsightDiscoveryFullDataLoadState(isLoading: false));

        emit(
          SelectVisualizationState(
            selectedVisualization: event.selectedVisualization,
            fullData: response.response?.dataList ?? [],
          ),
        );
      } else {
        emit(
          InsightDiscoveryFullDataLoadState(
            isLoading: false,
            errorText: response.errorMessage,
          ),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(
        InsightDiscoveryFullDataLoadState(
          isLoading: false,
          errorText: LocaleKeys.somethingWentWrong.tr(),
        ),
      );
    }
  }

  Future<void> _onUpdateLegend(
    UpdateLegendEvent event,
    Emitter<DetailsState> emit,
  ) async {
    emit(
      UpdateLegendState(
        seriesList: event.seriesList,
        filterKey: event.filterKey,
        filterList: event.filterList,
      ),
    );
  }

  Future<void> _onUpdateSolidSeries(
    SolidStateUpdateEvent event,
    Emitter<DetailsState> emit,
  ) async {
    emit(
      SolidStateUpdateState(
        seriesList: event.seriesList,
        isCompareTrue: event.isCompareTrue,
        selectedFrequencyForFilter: event.selectedFrequencyForFilter,
        forecastSeriesList: event.forecastSeries,
      ),
    );
  }

  Future<void> _onBackClear(
    BackClearEvent event,
    Emitter<DetailsState> emit,
  ) async {
    emit(
      BackClearState(),
    );
  }
}
