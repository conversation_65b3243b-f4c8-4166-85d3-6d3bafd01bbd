import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_response_helper.dart';
import 'package:scad_mobile/src/features/details/presentation/bloc/details_bloc.dart';
import 'package:scad_mobile/src/features/details/presentation/widgets/details_page_category_chip.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class ForecastAndInfoWidget extends StatefulWidget {
  const ForecastAndInfoWidget({super.key, this.isForecast = false, required this.indicatorDetails});
  final bool isForecast;
  final IndicatorDetailsResponseHelper indicatorDetails;
  @override
  State<ForecastAndInfoWidget> createState() => _ForecastAndInfoWidgetState();
}

class _ForecastAndInfoWidgetState extends State<ForecastAndInfoWidget> {
  bool forecastVisibility = true;

  @override
  void initState() {
    super.initState();
    context.read<DetailsBloc>().add(
          const ForecastVisibilityUpdateEvent(isVisibilityOn: true),
        );
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        if (widget.isForecast)
          Row(
            children: [
              Text(
                LocaleKeys.forecast.tr(),
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: AppColors.greyShade4,
                  fontSize: 10,
                  fontWeight: FontWeight.w400,
                ),
                textScaler: TextScaler.linear(textScaleFactor.value),
              ),
              SizedBox(
                height: 17,
                width: 41,
                child: FittedBox(
                  fit: BoxFit.fitHeight,
                  child: BlocConsumer<DetailsBloc, DetailsState>(
                    listener: (context, state) {
                      if (state is ForecastVisibilityUpdateState) {
                        forecastVisibility = state.isVisibilityOn;
                      }
                    },
                    builder: (context, state) {
                      return CupertinoSwitch(
                        activeColor: AppColors.blueShade27,
                        trackColor: AppColors.greySwitchOff,
                        value: forecastVisibility,
                        onChanged: (val) {
                          context.read<DetailsBloc>().add(
                                ForecastVisibilityUpdateEvent(
                                  isVisibilityOn: val,
                                ),
                              );
                        },
                      );
                    },
                  ),
                ),
              ),
            ],
          )
        else
          const SizedBox(),
        //todo: check static title
        DetailsPageCategoryChip(
          text: widget.indicatorDetails.indicatorDetails.tagName ?? '',
          icon: 'scenario-driver-icon',
          type: widget.indicatorDetails.indicatorDetails.type  ?? '',
          classificationType: widget.indicatorDetails.indicatorDetails.contentClassificationKey ?? '',
         // onTap: () {
            // context.pushRoute(ProductsRoute(isFromDetailsScreen: true));
            // Future.delayed(
            //   const Duration(seconds: 1),
            //   () {
            //     context.read<ProductsBloc>().add(
            //           ProductsSelectTabEvent(
            //             type: 'Scenario Drivers',
            //             rnd: Random().nextInt(100),
            //           ),
            //         );
            //   },
            // );
  //        },
        ),
      ],
    );
  }
}
