import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart' show textScaleFactor;
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_response_helper.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

class DetailsPageTitle extends StatelessWidget {
  const DetailsPageTitle({
    required this.title,
    this.isOfficial = false,
    super.key,
    this.indicatorDetails,
  });

  final String title;
  final bool? isOfficial;
  final IndicatorDetailsResponseHelper? indicatorDetails;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Center(
      child: Text.rich(
        textScaler: TextScaler.linear(textScaleFactor.value),
        TextSpan(
          children: [
            TextSpan(
              text: title,
              style: TextStyle(
                color: isLightMode ? AppColors.blackShade1 : AppColors.white,
                fontSize: 24,
                fontWeight: FontWeight.w500,
              ),
            ),
            if (isOfficial ?? false) ...[
              WidgetSpan(
                child: Padding(
                  padding: const EdgeInsets.only(left: 14),
                  child: indicatorDetails!
                              .indicatorDetails.contentClassificationKey ==
                          'official_statistics'
                      ? SvgPicture.asset(
                          AppImages.icOfficialActive,
                        )
                      : indicatorDetails!.indicatorDetails
                                  .contentClassificationKey ==
                              'experimental_statistics'
                          ? SvgPicture.asset(
                              AppImages.icExperimentalActive,
                            )
                          : const SizedBox(),
                ),
              ),
            ],
          ],
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}
