import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class DetailsPageCategoryChip extends StatelessWidget {
  const DetailsPageCategoryChip({
    required this.text,
    required this.icon,
      required this.type,
          required this.classificationType,
 //   required this.onTap,
    super.key,
  });

  final String text;
  final String icon;
   final String type;
      final String classificationType;
   
//  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    bool isLightMode = HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return InkWell(
  
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 15,
          vertical: 5,
        ),
        decoration: BoxDecoration(
          color: isLightMode ? AppColors.greyF3F4F6 : AppColors.blueShade36,
          borderRadius: BorderRadius.circular(50),
        ),
        child: Row(
          children: [
            _getSvg(isLightMode),
            const SizedBox(width: 8),
            Text(
              _getLabel(),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                color: isLightMode ? AppColors.grey : Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w400,
              ),
              textScaler: TextScaler.linear(textScaleFactor.value),
            ),
          ],
        ),
      ),
    );
  }

  String _getLabel() {
    if (classificationType == 'official_statistics') {
      return LocaleKeys.officialStatistics.tr();
    } else if (classificationType == 'experimental_statistics') {
      return LocaleKeys.experimentalStatistics.tr();
    } else if (text.toLowerCase() == 'compute data') {
      return LocaleKeys.computeDataBadge.tr();
    } else if (text.toLowerCase() == 'compare statistics') {
      return LocaleKeys.compareStatistics.tr();
    } else if (text.toLowerCase() == 'forecasts') {
      return LocaleKeys.forecasts.tr();
    } else {
      return text;
    }
  }

  String _getIcon() {
     final bool isLightMode = HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    if(type  == 'coi' || text.toLowerCase()=='forecasts' || text.toLowerCase()=='forecast') {
      return 'Forecast';
    } else if(classificationType == 'official_statistics') {
        return isLightMode ?'Official_badge' : 'Official_dark';
    } else if (classificationType == 'experimental_statistics') {
         return  isLightMode ? 'Experimental' : 'experimental_dark';
    } else if (type  == 'insights-discovery') {
       return  isLightMode ? 'Insights' : 'Insights_dark';
    }  else if (type  == 'Internal') {
       return 'scenario-driver-icon';
    } else if (type == 'scad') {
       return isLightMode ?'Official_badge' : 'Official_dark';
    }
    return '';

  }

 Widget _getSvg(bool isLightMode) {
    try {
      return _getIcon() != '' ?  SvgPicture.asset(
              'assets/images/${_getIcon()}.svg',
              colorFilter: isLightMode
                  ? null
                  : const ColorFilter.mode(Colors.white, BlendMode.srcIn,),
            ): const SizedBox();
    } catch (e) {
      return const SizedBox();
    }
    
  }
}
