import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/features/details/presentation/bloc/details_bloc.dart';
import 'package:scad_mobile/src/features/details/presentation/widgets/chart_filter_chip_widget.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class DetailsPageChartFilter extends StatelessWidget {
  const DetailsPageChartFilter({
    required this.options,
    required this.originalIndicatorForFilter,
    required this.filteredDataList,
    super.key,
  });

  final List<Options>? options;
  final IndicatorDetailsResponse? originalIndicatorForFilter;
  final List<List<Map<String, dynamic>>>? filteredDataList;

  @override
  Widget build(BuildContext context) {
      
    final bool isLightMode =
    HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    final List<Options> optionList = options ?? [];
     if(optionList.isNotEmpty) {       
        final bool optionSlected =  optionList.any((element) => element.isSelected);
       
        if(!optionSlected) {
          optionList.last.isSelected = true;
        }
     }

    return Container(
          decoration: BoxDecoration(
            color: !isLightMode ? AppColors.blueShade36 : const Color(0xFFD9D9D9),
            borderRadius: BorderRadius.circular(30),
            border: Border.all(
              color: isLightMode ? AppColors.greyF3F4F6 : Colors.transparent,
            ),
            // boxShadow: [
            //   BoxShadow(
            //     offset: const Offset(0, -5),
            //     blurRadius: 15,
            //     color: isLightMode ? Colors.black26 : Colors.transparent,
            //     inset: true,
            //   ),
            //   BoxShadow(
            //     offset: const Offset(0, 5),
            //     blurRadius: 8,
            //     color: isLightMode ? Colors.black45 : Colors.transparent,
            //     inset: true,
            //   ),
            // ],
          ),
          child: SizedBox(
            height: 28 * textScaleFactor.value,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: List.generate(
                optionList.length,
                (index) {
                  
                  return  (filteredDataList ?? []).every((element) => element.isEmpty) ?   const SizedBox():
                  ChartFilterChipWidget(
                    index: index,
                    isSelected: optionList[index].isSelected,
                    text:  
                      optionList[index].id ?.toLowerCase() == 'all' ? 
                      LocaleKeys.all.tr() : 
                      optionList[index].unit ?.toLowerCase() == 'years'  || optionList[index].unit ?.toLowerCase() == 'year'? 
                      '${optionList[index].value}Y' : 
                      '${optionList[index].value}M',
                    listLength: optionList.length,
                    onTap: () {
                      if (optionList[index].id ==
                          options
                              ?.where((element) => element.isSelected)
                              .elementAtOrNull(0)
                              ?.id) {
                        return;
                      }

                      context.read<DetailsBloc>().add(
                            MonthYearFilterEvent(
                              indicatorData: originalIndicatorForFilter ??
                                  IndicatorDetailsResponse(),
                              options: optionList,
                              index: index,
                              filteredDataList: filteredDataList,
                              resetPeriodFilter: true,
                            ),
                          );
                    },
                  );
               
                },
              ),
            ),
          ),
        );
      }
  }
