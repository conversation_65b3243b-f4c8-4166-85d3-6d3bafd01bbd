import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart' show textScaleFactor;
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

class ChartActionButton extends StatelessWidget {
  const ChartActionButton({
    required this.text,
    required this.icon,
    required this.onTap,
    super.key,
    this.isDisable = false,
  });

  final String text;
  final String icon;
  final Function onTap;
  final bool? isDisable;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return IgnorePointer(
      ignoring: isDisable != null && !isDisable! ? false : true,
      child: Opacity(
        opacity: isDisable != null && !isDisable! ? 1 : 0.5,
        child: InkWell(
          onTap: () => onTap(),
          child: Column(
            children: [
              Container(
                width: 50,
                height: 70,
                padding: const EdgeInsets.symmetric(
                  horizontal: 10,
                  vertical: 20,
                ),
                decoration: BoxDecoration(
                  color: isLightMode
                      ? const Color(0xFF396EA5).withOpacity(0.1)
                      : AppColors.blueLightOld.withOpacity(0.4),
                  borderRadius: BorderRadius.circular(80),
                ),
                child: SvgPicture.asset(
                  icon,
                  colorFilter: ColorFilter.mode(
                    isLightMode ? AppColors.blueLight : AppColors.white,
                    BlendMode.srcIn,
                  ),
                ),
              ),
              const SizedBox(height: 10),
              SizedBox(
                width: (MediaQuery.sizeOf(context).width-48)/4,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 1),
                  child: Text(
                    text,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: isLightMode ? AppColors.grey : AppColors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                    textScaler: TextScaler.linear(textScaleFactor.value),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
