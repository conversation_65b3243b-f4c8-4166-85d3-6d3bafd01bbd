import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart' show textScaleFactor;
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

class DownloadAsChipWidget extends StatelessWidget {
  const DownloadAsChipWidget({
    required this.text,
    required this.icon,
    required this.onTap,
    required this.isTermsAccepted,
    super.key,
  });

  final bool isTermsAccepted;
  final String text;
  final String icon;
  final Function onTap;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return InkWell(
      onTap: () => onTap(),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Opacity(
            opacity: isTermsAccepted ? 1 : 0.5,
            child: SvgPicture.asset(icon),
          ),
          const SizedBox(height: 4),
          Text(
            text,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: isLightMode
                  ? AppColors.grey
                  : AppColors.white.withOpacity(isTermsAccepted ? 1 : 0.5),
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
            textScaler: TextScaler.linear(textScaleFactor.value),
          ),
        ],
      ),
    );
  }
}
