import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart' hide MetaData;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/widgets/custom_animated_expansion_tile.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/features/details/presentation/bloc/details_bloc.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class MetaDataWidget extends StatelessWidget {
  const MetaDataWidget({super.key, this.metaData});

  final List<MetaData?>? metaData;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    List<MetaData?> metaDataList = [];
    bool isOpened = false;
    return BlocBuilder<DetailsBloc, DetailsState>(
      builder: (context, state) {
        if (state is GenerateMetaDataState) {
          metaDataList = state.metaData;
          isOpened = state.isOpened;
        }
        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            color: isLightMode ? AppColors.white : AppColors.blueShade36,
          ),
          child: CustomAnimatedExpansionTile(
            isExpanded: isOpened,
            padding: const EdgeInsets.all(15),
            title: LocaleKeys.metaData.tr(),
            leading: SvgPicture.asset(
              'assets/images/metadata.svg',
              colorFilter: ColorFilter.mode(
                isLightMode ? AppColors.blueLight : AppColors.blueLightOld,
                BlendMode.srcIn,
              ),
            ),
            onExpansionChanged: () {
              context.read<DetailsBloc>().add(
                    GenerateMetaDataEvent(
                      metaData: metaData ?? [],
                      isOpened: !isOpened,
                    ),
                  );
            },
            children: [
              if (isOpened) Divider(color: AppColors.greyShade1),
              ...List.generate(metaDataList.length, (index) {
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 15),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        metaDataList[index]?.label ?? '',
                        style: TextStyle(
                          color: isLightMode
                              ? AppColors.blackTextTile
                              : AppColors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                        textScaler: TextScaler.linear(textScaleFactor.value),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        metaDataList[index]?.value ?? '-',
                        style: TextStyle(
                          color: isLightMode
                              ? AppColors.grey
                              : AppColors.greyShade4,
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                        ),
                        textScaler: TextScaler.linear(textScaleFactor.value),
                      ),
                      const SizedBox(height: 15),
                    ],
                  ),
                );
              }),
            ],
          ),
        );
      },
    );
  }
}
