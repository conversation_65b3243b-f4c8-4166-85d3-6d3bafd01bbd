import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart' show textScaleFactor;
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

class IconAndTitleWidget extends StatelessWidget {
  const IconAndTitleWidget({
    required this.icon,
    required this.title,
    required this.content,
    super.key,
  });

  final String icon;
  final String title;
  final String content;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Row(
      children: [
        SvgPicture.asset(icon),
        const SizedBox(width: 12),
        Expanded(
          child: Text.rich(
            textScaler: TextScaler.linear(textScaleFactor.value),
            TextSpan(
              children: [
                TextSpan(
                  text: '$title ',
                  style: TextStyle(
                    color: AppColors.greyShade4,
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                TextSpan(
                  text: content,
                  style: TextStyle(
                    color:
                        isLightMode ? AppColors.blackTextTile : AppColors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
