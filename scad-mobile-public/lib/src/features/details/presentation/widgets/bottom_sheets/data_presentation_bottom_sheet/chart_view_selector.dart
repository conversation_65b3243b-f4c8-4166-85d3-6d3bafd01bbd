import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

class ChartViewSelector extends StatelessWidget {
  const ChartViewSelector({
    required this.title,
    required this.icon,
    required this.isSelected,
    required this.onTap,
    super.key,
  });

  final String title;
  final String icon;
  final bool isSelected;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return InkWell(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 61,
            height: 61,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: isSelected
                  ? isLightMode
                      ? AppColors.blueShade22
                      : AppColors.blueLightOld
                  : Colors.transparent,
              shape: BoxShape.circle,
              border: Border.all(
                color: isSelected ? Colors.transparent : AppColors.greyShade1,
              ),
            ),
            child: SvgPicture.asset(
              'assets/images/$icon.svg',
              colorFilter: ColorFilter.mode(
                isSelected ? AppColors.white : AppColors.greyShade4,
                BlendMode.srcIn,
              ),
            ),
          ),
          const SizedBox(height: 10),
          Text(
            title,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: isLightMode ? AppColors.grey : AppColors.white,
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
            textScaler: TextScaler.linear(textScaleFactor.value),
          ),
        ],
      ),
    );
  }
}
