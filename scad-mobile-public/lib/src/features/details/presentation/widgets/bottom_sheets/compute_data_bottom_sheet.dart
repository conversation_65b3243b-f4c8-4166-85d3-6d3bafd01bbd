import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/common/widgets/app_radio_tile.dart';
import 'package:scad_mobile/src/common/widgets/bottom_sheet_top_notch.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/features/details/presentation/bloc/details_bloc.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class ComputeDataBottomSheet extends StatelessWidget {
  const ComputeDataBottomSheet({
    required this.indicatorId,
    this.filterPanel,
    super.key,
  });

  final String indicatorId;
  final FilterPanel? filterPanel;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    String selectedItem = LocaleKeys.summation.tr();
    return BlocBuilder<DetailsBloc, DetailsState>(
      builder: (context, state) {
        if (state is ComputeBottomSheetValueState) {
          selectedItem = state.value;
        }
        return Container(
          width: double.infinity,
          padding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
          decoration: BoxDecoration(
            color: isLightMode ? AppColors.white : AppColors.blueShade32,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 10),
              const BottomSheetTopNotch(),
              const SizedBox(height: 16),
              Text(
                LocaleKeys.computeData.tr(),
                style: TextStyle(
                  color: isLightMode ? AppColors.blackShade1 : AppColors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                textScaler: TextScaler.linear(textScaleFactor.value),
              ),
              const SizedBox(height: 20),
              AppRadioTile(
                title: LocaleKeys.summation.tr(),
                isSelected: selectedItem == LocaleKeys.summation.tr(),
                trailingIcon: 'summation',
                onTap: () {
                  context.read<DetailsBloc>().add(
                        ComputeBottomSheetValueEvent(
                          value: LocaleKeys.summation.tr(),
                        ),
                      );
                },
              ),
              const SizedBox(height: 15),
              AppRadioTile(
                title: LocaleKeys.subtraction.tr(),
                isSelected: selectedItem == LocaleKeys.subtraction.tr(),
                trailingIcon: 'subtraction',
                onTap: () {
                  context.read<DetailsBloc>().add(
                        ComputeBottomSheetValueEvent(
                          value: LocaleKeys.subtraction.tr(),
                        ),
                      );
                },
              ),
              const SizedBox(height: 15),
              AppRadioTile(
                title: LocaleKeys.multiplication.tr(),
                isSelected: selectedItem == LocaleKeys.multiplication.tr(),
                trailingIcon: 'multiplication',
                onTap: () {
                  context.read<DetailsBloc>().add(
                        ComputeBottomSheetValueEvent(
                          value: LocaleKeys.multiplication.tr(),
                        ),
                      );
                },
              ),
              const SizedBox(height: 15),
              AppRadioTile(
                title: LocaleKeys.division.tr(),
                isSelected: selectedItem == LocaleKeys.division.tr(),
                trailingIcon: 'division',
                onTap: () {
                  context.read<DetailsBloc>().add(
                        ComputeBottomSheetValueEvent(
                          value: LocaleKeys.division.tr(),
                        ),
                      );
                },
              ),
              const SizedBox(height: 30),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  minimumSize: const Size.fromHeight(43),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                onPressed: () {
                  Navigator.pop(context);
                  context.pushRoute(
                    ComputePageRoute(
                      title: selectedItem,
                      indicatorId: indicatorId,
                      firstIndicatorList: List.from(
                        filterPanel?.properties ?? [],
                      ),
                      secondIndicatorList: List.from(
                        filterPanel?.propertiesForComputation ?? [],
                      ),
                    ),
                  );
                },
                child: Text(
                  LocaleKeys.next.tr(),
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                  textScaler: TextScaler.linear(textScaleFactor.value),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
