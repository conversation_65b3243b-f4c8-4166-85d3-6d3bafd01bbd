import 'dart:convert';
import 'dart:developer';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/widgets/app_radio_tile.dart';
import 'package:scad_mobile/src/common/widgets/bottom_sheet_top_notch.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/features/details/domain/usecases/frequency_selector_model.dart';
import 'package:scad_mobile/src/features/details/presentation/bloc/details_bloc.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class DataFrequencyBottomSheet extends StatelessWidget {
  const DataFrequencyBottomSheet({
    required this.frequencyList,
    required this.originalIndicatorData,
    this.filteredDataList,
    required this.forecastDataList,
    super.key,
  });

  final List<FrequencySelectorModel> frequencyList;
  final IndicatorDetailsResponse? originalIndicatorData;
  final List<List<Map<String, dynamic>>>? filteredDataList;
  final List<List<Map<String, dynamic>>>? forecastDataList;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
       
    List<FrequencySelectorModel> freqList = List.generate(frequencyList.length, (index) => FrequencySelectorModel.fromJson (jsonDecode(jsonEncode(frequencyList[index])) as Map<String,dynamic>)) ;
    return BlocConsumer<DetailsBloc, DetailsState>(
      listener: (context, state) => {
    if (state is ChangeDataFrequencyState) {
          freqList = state.frequencyList,
        },
      },
      builder: (context, state) {
    
        return Container(
          width: double.infinity,
          padding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
          decoration: BoxDecoration(
            color: isLightMode ? AppColors.white : AppColors.blueShade32,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 10),
              const BottomSheetTopNotch(),
              const SizedBox(height: 16),
              Text(
                LocaleKeys.changeDataFrequency.tr(),
                style: TextStyle(
                  color: isLightMode ? AppColors.blackShade1 : AppColors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                textScaler: TextScaler.linear(textScaleFactor.value),
              ),
              const SizedBox(height: 20),
              ...List.generate(freqList.length, (index) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 15),
                  child: AppRadioTile(
                    title: freqList[index].title.tr(),
                    isSelected: freqList[index].isSelected,
                    onTap: () {
                      context.read<DetailsBloc>().add(
                            ChangeDataFrequencyEvent(
                              index: index,
                              frequencyList: freqList,
                            ),
                          );
                    },
                  ),
                );
              }),
              const SizedBox(height: 15),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  minimumSize: const Size.fromHeight(43),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                onPressed: () {
              
                  // if (freqList.any((element) => element.title == 'Monthly')) {
                  context.read<DetailsBloc>().add(
                        ChangeDataFrequencyApplyEvent(
                          selectedFrequency: freqList
                              .where((element) => element.isSelected)
                              .map((e) => e.title)
                              .first,
                          freqList: freqList,
                          inidicatorData: originalIndicatorData ??
                              IndicatorDetailsResponse(),
                          filteredDataList: List.from(filteredDataList ?? []),
                          forecastSeriesList: forecastDataList,
                        ),
                      );
                  // }
                  Navigator.pop(context);
                },
                child: Text(
                  LocaleKeys.done.tr(),
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                  textScaler: TextScaler.linear(textScaleFactor.value),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
