import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';

class CompareIndicatorsChip extends StatelessWidget {
  const CompareIndicatorsChip({
    required this.text,
    required this.icon,
    required this.isSelected,
    required this.onTap,
    super.key,
  });

  final String text;
  final String icon;
  final bool isSelected;
  final Function onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => onTap(),
      borderRadius: BorderRadius.circular(50),
      child: Container(
        height: 28,
        padding: const EdgeInsets.symmetric(
          horizontal: 15,
          vertical: 5,
        ),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.selectedChipBlue : Colors.transparent,
          borderRadius: BorderRadius.circular(50),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            _getSvg(),
            const SizedBox(width: 10),
            Text(
              text,
              style: TextStyle(
                color: isSelected ? AppColors.white : AppColors.greyShade4,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              textScaler: TextScaler.linear(textScaleFactor.value),
            ),
          ],
        ),
      ),
    );
  }
  Widget _getSvg() {
    try {
      return icon != '' ?  SvgPicture.asset(
              'assets/images/$icon.svg',
              colorFilter: ColorFilter.mode(
                isSelected ? AppColors.white : AppColors.greyShade4,
                BlendMode.srcIn,
              ),
            ):  const SizedBox();
    } catch (e) {
      return const SizedBox();
    }
    
  }
}
