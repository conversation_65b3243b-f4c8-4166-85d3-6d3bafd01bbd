import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/main.dart' show textScaleFactor;
import 'package:scad_mobile/src/common/widgets/app_back_button.dart';
import 'package:scad_mobile/src/common/widgets/custom_animated_expansion_tile.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/common/widgets/rounded_dropdown_widget.dart';
import 'package:scad_mobile/src/features/details/presentation/bloc/details_bloc.dart';
import 'package:scad_mobile/src/features/details/presentation/widgets/compute_data/computation_title.dart';
import 'package:scad_mobile/src/features/details/presentation/widgets/details_page_title.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

@RoutePage()
class ComputePage extends StatelessWidget {
  ComputePage({
    required this.title,
    required this.indicatorId,
    this.firstIndicatorList,
    this.secondIndicatorList,
    super.key,
  });

  final String indicatorId;
  final String title;
  final List<Properties>? firstIndicatorList;
  final List<Properties>? secondIndicatorList;

  final TextEditingController textController = TextEditingController();
  final TextEditingController unitController = TextEditingController();
  final Map<String, List<String>> valueMap = {};

  void setDefaultValue() {
    valueMap.clear();
    final List<Properties> updatedList = List.from(
      firstIndicatorList ?? [],
    );

    for (final element in updatedList) {
      bool isDefault = false;
      for (final option in element.optionList) {
        if (element.defaultVal?.toLowerCase() == option.title?.toLowerCase()) {
          option.isSelected = true;
          isDefault = true;
          element.selectedOptionItem = option.title;
        }
      }
      if (!isDefault) {
        element.selectedOptionItem = element.optionList.first.title;
        element.optionList.first.isSelected = true;
      }
    }

    for (final element in updatedList) {
      for (final option in element.optionList) {
        if (option.isSelected &&
            !(valueMap[element.path]?.contains(option.title?.toLowerCase()) ??
                false)) {
          valueMap
              .putIfAbsent(element.path ?? '', () => [])
              .add(option.title?.toLowerCase() ?? '');
        }
      }
    }

    for (final element in updatedList) {
      for (final option in element.optionList) {
        if (option.isSelected == false) {
          valueMap[element.path]?.remove(option.title?.toLowerCase());
        }
      }
    }

    print('valueMap -> $valueMap');
  }

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    String titleValue = title;
    String selectedFirstIndicatorExpansionTileTitle = '';
    String selectedIndicatorDimension = '';
    final List<Properties>? radioIndicatorList = firstIndicatorList;
    Properties? selectedIndicator;
    List<String> firstIndicatorOptionList = [];
    String selectedFirstIndicator = '-1';

    List<Properties>? checkboxIndicatorList = secondIndicatorList;
    final List<OptionList> selectedSecondIndicators = [];
    bool isComputing = false;
    setDefaultValue();

    return Scaffold(
      // appBar: AppBar(
      //   scrolledUnderElevation: 0,
      //   backgroundColor: Colors.transparent,
      //   elevation: 0,
      //   leading: const SizedBox(),
      // ),
      body: BlocConsumer<DetailsBloc, DetailsState>(
        listener: (context, state) {
          if (state is ComputePageTitleState) {
            titleValue = state.value;
          } else if (state is FirstIndicatorDropdownState) {
            selectedIndicator = state.selectedIndicator;
            firstIndicatorOptionList = state.optionList;
            setDefaultValue();
          } else if (state is FirstIndicatorRadioButtonState) {
            selectedFirstIndicator = state.groupValue;
            selectedIndicatorDimension = state.selectedIndicator.path ?? '';
            selectedFirstIndicatorExpansionTileTitle =
                state.selectedIndicator.label ?? '';
            selectedSecondIndicators.clear();

            for (final element in checkboxIndicatorList ?? <Properties>[]) {
              for (final data in element.optionList) {
                data.isSelected = false;
              }
            }
          } else if (state is ToggleSecondIndicatorExpansionState) {
            checkboxIndicatorList = state.updatedList;
          } else if (state is SecondIndicatorCheckBoxState) {
            for (final element in state.selectedItems) {
              if (selectedSecondIndicators.contains(element)) {
                selectedSecondIndicators.remove(element);
              } else {
                selectedSecondIndicators.add(element);
              }
            }

            for (final element in checkboxIndicatorList ?? <Properties>[]) {
              for (final option in selectedSecondIndicators) {
                if (option.isSelected == false) {
                  valueMap[element.path]?.remove(option.title?.toLowerCase());
                }
              }
            }

            if (valueMap.containsKey(selectedIndicatorDimension)) {
              valueMap[selectedIndicatorDimension] = [
                selectedFirstIndicator,
                ...List.generate(
                  selectedSecondIndicators.length,
                  (index) => selectedSecondIndicators[index].title ?? '',
                ),
              ];
            }
            print(valueMap);
          }

          //  else if (state is ComputeDataLoadingState) {
          //   isComputing = true;
          // }
          else if (state is ComputeDataSuccessState) {
            // isComputing = state.isLoading;
            for (final element in radioIndicatorList ?? <Properties>[]) {
              element.isOpened = false;
            }
            for (final element in checkboxIndicatorList ?? <Properties>[]) {
              element.isOpened = false;
              for (final data in element.optionList) {
                data.isSelected = false;
              }
            }
            valueMap.clear();
            isComputing = false;
            context.popRoute();
          } else if (state is ComputeDataErrorState) {
            // isComputing = state.isLoading;
            isComputing = false;
            AppMessage.showOverlayNotification(
              '',
              state.errorMessage,
              msgType: 'error',
            );
          }
        },
        builder: (context, state) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
                  SizedBox(height: MediaQuery.paddingOf(context).top,), 
              Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  
                  Row(
                    children: [
                     const Padding(padding: EdgeInsets.symmetric(horizontal: 8),
                       child: AppBackButton(),
                     ),
                      /// The title of the page
                      Expanded(
                                child: Padding(
                            padding:  DeviceType.isDirectionRTL(context) ? 
                       const  EdgeInsets.only(left: 30):  const  EdgeInsets.only(right: 30),
                          child: DetailsPageTitle(
                            title: LocaleKeys.computation.tr(),
                          ),
                        ),
                      ),
              
                    ],
                  ),
                      const SizedBox(height: 20),
                  /// The computation title eg:- Summation,
                  /// Subtraction etc
                  ///
                  /// 
                  Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                    child: ComputationTitle(titleValue: titleValue)),
                  const SizedBox(height: 30),
                ],
              ),
              Expanded(
                child: Scrollbar(
                   thumbVisibility: true,
                trackVisibility: true,
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        /// first indicator dropdown
                        Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: isLightMode
                                ? AppColors.greyShade7
                                : AppColors.blueShade32,
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.shadow1,
                                blurRadius: 5,
                                offset: const Offset(1, 4),
                              ),
                            ],
                          ),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              RoundedDropDownWidget<Properties>(
                                title: LocaleKeys.selectFirstIndicator.tr(),
                                items: radioIndicatorList,
                                value: selectedIndicator,
                                compute: true,
                                onChanged: (val) {
                                  context.read<DetailsBloc>().add(
                                        FirstIndicatorDropdownEvent(
                                          indicatorList: radioIndicatorList ?? [],
                                          selectedIndicator: val ?? Properties(),
                                        ),
                                      );
                                },
                              ),
                              AnimatedCrossFade(
                                firstChild: Container(),
                                secondChild: Column(
                                  crossAxisAlignment: CrossAxisAlignment.stretch,
                                  children: [
                                    const SizedBox(height: 20),
                                    ...List.generate(
                                        firstIndicatorOptionList.length, (index) {
                                      return RadioListTile<String>(
                                        activeColor: isLightMode
                                            ? AppColors.blueShade22
                                            : AppColors.blueLightOld,
                                        materialTapTargetSize:
                                            MaterialTapTargetSize.shrinkWrap,
                                        value: firstIndicatorOptionList[index],
                                        groupValue: selectedFirstIndicator,
                                        onChanged: (String? val) {
                                          print('val -> $val');
                                          context.read<DetailsBloc>().add(
                                                FirstIndicatorRadioButtonEvent(
                                                  selectedIndicator:
                                                      selectedIndicator ??
                                                          Properties(),
                                                  groupValue: val ?? '-1',
                                                ),
                                              );
                                        },
                                        title: Text(
                                          firstIndicatorOptionList[index],
                                          style: TextStyle(
                                            color: isLightMode
                                                ? AppColors.grey
                                                : AppColors.greyShade4,
                                            fontSize: 14,
                                            fontWeight: FontWeight.w400,
                                          ),
                                          textScaler: TextScaler.linear(
                                            textScaleFactor.value,
                                          ),
                                        ),
                                        contentPadding: EdgeInsets.zero,
                                        visualDensity: VisualDensity.compact,
                                      );
                                    }),
                                  ],
                                ),
                                duration: const Duration(milliseconds: 300),
                                crossFadeState: selectedIndicator != null
                                    ? CrossFadeState.showSecond
                                    : CrossFadeState.showFirst,
                              ),
                            ],
                          ),
                        ),
                  
                        const SizedBox(height: 30),
                  
                        Text(
                          LocaleKeys.selectOtherIndicators.tr(),
                          style: TextStyle(
                            color: isLightMode
                                ? AppColors.grey
                                : AppColors.greyShade4,
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                          ),
                          textScaler: TextScaler.linear(textScaleFactor.value),
                        ),
                        const SizedBox(height: 10),
                  
                        /// second indicator expansion tiles
                        ...List.generate(checkboxIndicatorList?.length ?? 0,
                            (index) {
                          return Container(
                            margin: const EdgeInsets.only(bottom: 20),
                            decoration: BoxDecoration(
                              color: (selectedFirstIndicatorExpansionTileTitle ==
                                      secondIndicatorList?[index].label)
                                  ? isLightMode
                                      ? AppColors.greyShade7
                                      : AppColors.blueShade32
                                  : AppColors.greyShade13,
                              borderRadius: BorderRadius.circular(15),
                              boxShadow: [
                                BoxShadow(
                                  color: isLightMode
                                      ? AppColors.shadow1
                                      : Colors.transparent,
                                  blurRadius: 5,
                                  offset: const Offset(1, 4),
                                ),
                              ],
                            ),
                            child: CustomAnimatedExpansionTile(
                              key: Key('second_indicator_$index'),
                              title: checkboxIndicatorList?[index].label ?? '',
                              isExpanded:
                                  checkboxIndicatorList?[index].isOpened ?? false,
                              onExpansionChanged: () {
                                if (selectedFirstIndicatorExpansionTileTitle ==
                                    checkboxIndicatorList?[index].label) {
                                  context.read<DetailsBloc>().add(
                                        ToggleSecondIndicatorExpansionEvent(
                                          updatedList:
                                              checkboxIndicatorList ?? [],
                                          index: index,
                                        ),
                                      );
                                }
                              },
                              children: [
                                Divider(color: AppColors.greyShade1),
                                ...List.generate(
                                    checkboxIndicatorList?[index]
                                            .options
                                            ?.length ??
                                        0, (childIndex) {
                                  final OptionList? data =
                                      checkboxIndicatorList?[index]
                                          .optionList[childIndex];
                  
                                  return IgnorePointer(
                                    ignoring:
                                        selectedFirstIndicatorExpansionTileTitle !=
                                            checkboxIndicatorList?[index].label,
                                    child: CheckboxListTile(
                                      checkColor: AppColors.white,
                                      activeColor: isLightMode
                                          ? AppColors.blueShade22
                                          : AppColors.blueLightOld,
                                      enabled:
                                          data?.title != selectedFirstIndicator,
                                      value: data?.isSelected,
                                      onChanged: (bool? val) {
                                        context.read<DetailsBloc>().add(
                                              SecondIndicatorCheckBoxEvent(
                                                itemList:
                                                    checkboxIndicatorList?[index]
                                                            .optionList ??
                                                        [],
                                                index: childIndex,
                                                isSelected: val ?? false,
                                              ),
                                            );
                                      },
                                      title: Text(
                                        data?.title ?? '',
                                        style: TextStyle(
                                          color: data?.title !=
                                                  selectedFirstIndicator
                                              ? isLightMode
                                                  ? AppColors.grey
                                                  : AppColors.greyShade1
                                              : isLightMode
                                                  ? AppColors.greyShade1
                                                  : AppColors.grey,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w400,
                                        ),
                                        textScaler: TextScaler.linear(
                                          textScaleFactor.value,
                                        ),
                                      ),
                                      contentPadding: EdgeInsets.zero,
                                      visualDensity: VisualDensity.compact,
                                      controlAffinity:
                                          ListTileControlAffinity.leading,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(2),
                                      ),
                                    ),
                                  );
                                }),
                              ],
                            ),
                          );
                        }),
                      ],
                    ),
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    const SizedBox(height: 16),
                    Text.rich(
                      textScaler: TextScaler.linear(textScaleFactor.value),
                      TextSpan(
                        children: [
                          TextSpan(
                            text: LocaleKeys.enterNewIndicatorName.tr(),
                            style: TextStyle(
                              color: isLightMode
                                  ? AppColors.grey
                                  : AppColors.greyShade4,
                              fontSize: 14 * textScaleFactor.value,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                          const WidgetSpan(
                            child: SizedBox(width: 5),
                          ),
                          const TextSpan(
                            text: '*',
                            style: TextStyle(
                              color: Color(0xFFBA0202),
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 10),

                    /// compute indicator name
                    SizedBox(
                      height: 49,
                      child: TextField(
                        controller: textController,
                        textCapitalization: TextCapitalization.sentences,
                        maxLength: 60,
                        style: TextStyle(

                          color: isLightMode ? AppColors.black :AppColors.white ,
                          fontSize: 16 * textScaleFactor.value,
                        ),
                        decoration: InputDecoration(
                          fillColor: isLightMode ? AppColors.white : AppColors.blueShade32,
                          filled: true,
                          counterText: '',
                          contentPadding: const EdgeInsets.symmetric(
                            vertical: 12,
                            horizontal: 12,
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: AppColors.greyShade1,
                            ),
                          ),
                          disabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: AppColors.greyShade1,
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: AppColors.greyShade1,
                            ),
                          ),
                          hintText: LocaleKeys.enterHere.tr(),
                          hintStyle: TextStyle(
                            color: AppColors.greyShade1,
                            fontSize: 16 * textScaleFactor.value,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        onSubmitted: (_) {
                          FocusManager.instance.primaryFocus?.unfocus();
                        },
                      ),
                    ),
                    const SizedBox(height: 30),
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        minimumSize: const Size.fromHeight(43),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      onPressed: () {
                        if (textController.text.trim().isEmpty) {
                          AppMessage.showOverlayNotification(
                            '',
                            LocaleKeys.computeValidation.tr(),
                            msgType: 'error',
                          );
                        } else if (selectedFirstIndicator == '-1') {
                          AppMessage.showOverlayNotification(
                            '',
                            LocaleKeys.theFirstIndicatorIsrequired.tr(), //المؤشر الأول مطلوب
                            msgType: 'error',
                          );
                        } else if (selectedSecondIndicators.isEmpty) {
                          AppMessage.showOverlayNotification(
                            '',
                            LocaleKeys.atLeastOneSecondIndicatorShouldBeSelected.tr(), //وينبغي اختيار مؤشر ثانٍ واحد على الأقل ""
                            msgType: 'error',
                          );
                        } else {
                          isComputing = true;
                          // if (!isComputing) {
                          // final Map<String, dynamic> dataMap = {
                          //   'id': widget.indicatorId,
                          //   'dimensions': {
                          //     // selectedIndicatorDimension: [
                          //     //   selectedFirstIndicator,
                          //     //   ...List.generate(
                          //     //     selectedSecondIndicators.length,
                          //     //     (index) =>
                          //     //         selectedSecondIndicators[index].title,
                          //     //   ),
                          //     // ],
                          //   },
                          //   'operation': getComputationSymbol(titleValue),
                          // };

                          final Map<String, dynamic> dataMap =
                              convertToPayload(titleValue);

                          context.read<DetailsBloc>().add(
                                ComputeDataEvent(
                                  dataMap: dataMap,
                                  selectedMeasurement: unitController.text,
                                  nameOfIndicator: textController.text,
                                ),
                              );
                          // }
                        }
                      },
                      child: isComputing
                          ? SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                color: AppColors.white,
                              ),
                            )
                          : Text(
                              LocaleKeys.compute.tr(),
                              style: TextStyle(
                                color: AppColors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                              textScaler:
                                  TextScaler.linear(textScaleFactor.value),
                            ),
                    ),
                    const SizedBox(height: 30),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Map<String, dynamic> convertToPayload(String titleValue) {
    final Map<String, dynamic> payload = {
      'id': indicatorId,
      'dimensions': <String, dynamic>{},
      'operation': getComputationSymbol(titleValue),
    };

    // Iterate through each entry of the original map and format it accordingly
    valueMap.forEach((key, value) {
      // Convert the key to upper case and replace underscore with space
      final String formattedKey = key;

      // Add the formatted key and value to the 'dimensions' map in the payload
      payload['dimensions'][formattedKey] = value.length ==1 ?value.first :value ;
    });

    return payload;
  }

  String getComputationSymbol(String symbol) {
    if (symbol == LocaleKeys.summation.tr()) {
      return '+';
    } else if (symbol == LocaleKeys.subtraction.tr()) {
      return '-';
    } else if (symbol == LocaleKeys.multiplication.tr()) {
      return '*';
    } else if (symbol == LocaleKeys.division.tr()) {
      return '/';
    } else {
      return '+';
    }
  }
}
