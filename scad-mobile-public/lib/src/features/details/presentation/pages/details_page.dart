import 'dart:async';
import 'dart:convert';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/route_manager/route_imports.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/common/functions/indicator_date_setting.dart';
import 'package:scad_mobile/src/common/widgets/appbar/flat_app_bar.dart';
import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_response_helper.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/presentation/bloc/indicator_card_bloc.dart';
import 'package:scad_mobile/src/common/widgets/no_data_placeholder.dart';
import 'package:scad_mobile/src/common/widgets/rounded_dropdown_widget.dart';
import 'package:scad_mobile/src/config/dependancy_injection/injection_container.dart';
import 'package:scad_mobile/src/features/details/presentation/bloc/details_bloc.dart';
import 'package:scad_mobile/src/features/details/presentation/widgets/bottom_sheets/filter_bottom_sheet.dart';
import 'package:scad_mobile/src/features/details/presentation/widgets/details_page_theme.dart';
import 'package:scad_mobile/src/features/details/presentation/widgets/expanded_indicator_details_view.dart';
import 'package:scad_mobile/src/features/details/presentation/widgets/filter_driver_button.dart';
import 'package:scad_mobile/src/features/details/presentation/widgets/glossary_container.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_api_cache.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

@RoutePage()
class DetailsPage extends StatefulWidget {
  const DetailsPage({
    this.value = '',
    this.unit = '',
    this.numberUnit = '',
    this.domain = '',
    this.title = '',
    this.indicatorDetails,
    super.key,
    this.negativeArrow = false,
    this.contentType,
    this.originalIndicatorForFilter,
    this.id,
    this.fromNotification = false,
    this.comparedIndicatorName,
  });

  final String domain;
  final String? id;
  final String title;
  final String value;
  final String unit;
  final String numberUnit;
  final String? contentType;
  final IndicatorDetailsResponseHelper? indicatorDetails;
  final IndicatorDetailsResponse? originalIndicatorForFilter;
  final bool negativeArrow;
  final bool fromNotification;
  final String? comparedIndicatorName;

  @override
  State<DetailsPage> createState() => _DetailsPageState();
}

class _DetailsPageState extends State<DetailsPage> {
  String selectedType = 'line-chart';
  ValueNotifier<bool> isTermsAccepted = ValueNotifier(false);

  late String domainName;
  late String title;
  late String value;
  late String unit;
  late String numberUnit;
  late bool negativeArrow;
  IndicatorDetailsResponseHelper? indicatorDetailsInitial;
  IndicatorDetailsResponseHelper? indicatorDetails;
  IndicatorDetailsResponse? originalIndicatorForFilter;
  late String contentType;

  // To use this ids to reset filters details
  String? currentOverviewContentType;
  String? currentContentType;
  String? nodeId;
  bool isComparisonActive = false;

  String? selectedMeasurement = '';
  String? nameOfIndicator = '';

  Visualizations? selectedvisualizationComponent;

  ScrollController scrollController = ScrollController();

  bool? isDataLoading;
  bool? isFirstTimeDataLoading;

  List<Map<String, dynamic>> insightToFilterData = [];

  String? duplicateData;
  List<Properties>? properties = [];

  bool toResetChangeDrivers = true;
  bool? scadProjectionValue;

  @override
  void initState() {
    originalIndicatorForFilter = null;
    if (widget.id != null) {
      context.read<IndicatorCardBloc>().add(
            GetIndicatorDetailsEvent(
              id: widget.id!,
              contentType: widget.contentType!,
              overviewContentType: '',
            ),
          );
    } else {
      if (widget.indicatorDetails != null) {
        indicatorDetailsInitial = widget.indicatorDetails;
        currentContentType =
            widget.indicatorDetails!.indicatorDetails.indicatorType ??
                widget.contentType ??
                '';
        nodeId = widget.indicatorDetails!.indicatorDetails.id;
        currentOverviewContentType = widget
                .indicatorDetails!.indicatorDetails.contentClassificationKey ??
            currentContentType;

        indicatorDetails = widget.indicatorDetails;
        isComparisonActive = indicatorDetails?.indicatorDetails
                .indicatorVisualizations?.visualizationsMeta?.firstOrNull?.id ==
            'compare-chart';
        domainName = indicatorDetails!.domainName;
        title = indicatorDetails!.title;
        value = indicatorDetails!.value;
        unit = indicatorDetails!.unit;
        numberUnit = indicatorDetails!.numberUnit;
        negativeArrow = indicatorDetails!.negativeArrow;
        contentType = indicatorDetails!.indicatorDetails.indicatorType ??
            widget.contentType ??
            '';
        originalIndicatorForFilter = IndicatorDetailsResponse.fromJson(
            jsonDecode(
                    jsonEncode(widget.indicatorDetails?.indicatorDetails ?? {}))
                as Map<String, dynamic>);
      } else {
        domainName = widget.domain;
        title = widget.title;
        value = widget.value;
        unit = widget.unit;
        numberUnit = widget.numberUnit;
        negativeArrow = widget.negativeArrow;
        contentType = widget.contentType ?? '';
      }
      if (widget.id == null && widget.originalIndicatorForFilter != null) {
        originalIndicatorForFilter = widget.originalIndicatorForFilter;
      }
      duplicateData = json.encode(originalIndicatorForFilter);

      setFilter();
    }

    if (widget.originalIndicatorForFilter?.type == 'insights-discovery') {
      final String defaultVisualization =
          widget.originalIndicatorForFilter?.defaultVisualisation ?? '';

      for (final element in widget.originalIndicatorForFilter?.visualizations ??
          <Visualizations>[]) {
        if (element.id == defaultVisualization) {
          selectedvisualizationComponent = element;
          context.read<DetailsBloc>().add(
                SelectVisualizationEvent(
                  selectedVisualization: element,
                ),
              );
          break;
        }
      }
      // setFilter();
    }

    super.initState();
  }

  void setFilter() {
    // if (widget.id != null) {
    //   if (originalIndicatorForFilter?.type != 'insights-discovery') {
    //     if ((originalIndicatorForFilter?.filterPanel != null &&
    //             originalIndicatorForFilter?.filterPanel != false) ||
    //         (selectedvisualizationComponent?.filterPanel != null)) {
    //       context.read<DetailsBloc>().add(
    //             FilterApplyEvent(
    //               propertyList: const [],
    //               originlIndicatorDetails:
    //                   originalIndicatorForFilter ?? IndicatorDetailsResponse(),
    //             ),
    //           );
    //     } else {
    //       final a = originalIndicatorForFilter?.indicatorVisualizations
    //           ?.visualizationsMeta?.firstOrNull?.seriesMeta
    //           ?.where((e) => e.id!.contains('-forecast'));

    //       context.read<DetailsBloc>().add(
    //             FilterApplyEvent(
    //               propertyList: const [],
    //               originlIndicatorDetails:
    //                   originalIndicatorForFilter ?? IndicatorDetailsResponse(),
    //               // insightToFilterData: a,
    //               isForeCast: a != null,
    //             ),
    //           );
    //     }
    //   }
    // } else

    if ((originalIndicatorForFilter?.filterPanel != null &&
            originalIndicatorForFilter?.filterPanel != false) ||
        (selectedvisualizationComponent?.filterPanel != null)) {
      // if (context.read<DetailsBloc>().isFirstTimeFilter) {
      List<Properties>? propertiesBuffer =
          (originalIndicatorForFilter?.type == 'insights-discovery'
              ? selectedvisualizationComponent?.filterPanel?.properties ?? []
              : (originalIndicatorForFilter?.filterPanel as FilterPanel?)
                      ?.properties ??
                  []);
      properties = List.generate(
          propertiesBuffer.length,
          (index) => Properties.fromJson(
              jsonDecode(jsonEncode(propertiesBuffer?[index]))
                  as Map<String, dynamic>));
      properties = IndicatorDateSetting.removeDuplicates(properties ?? []);
      context.read<DetailsBloc>().add(
            DefaultFilterValueEvent(propertyList: properties ?? []),
          );
      // }

      context.read<DetailsBloc>().add(
            FilterApplyEvent(
              propertyList: const [],
              originlIndicatorDetails:
                  originalIndicatorForFilter ?? IndicatorDetailsResponse(),
              insightToFilterData: selectedvisualizationComponent
                      ?.indicatorVisualizations
                      ?.visualizationsMeta
                      ?.first
                      .seriesMeta
                      ?.first
                      .data ??
                  [],
              selectedVisualization: selectedvisualizationComponent,
            ),
          );
    } else {
      final a = widget.indicatorDetails?.indicatorDetails
          .indicatorVisualizations?.visualizationsMeta?.firstOrNull?.seriesMeta
          ?.where((e) => e.id!.contains('-forecast'));

      context.read<DetailsBloc>().add(
            FilterApplyEvent(
              propertyList: const [],
              originlIndicatorDetails:
                  originalIndicatorForFilter ?? IndicatorDetailsResponse(),
              // insightToFilterData: a,
              isForeCast: a != null,
            ),
          );
    }
  }

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return PopScope(
      canPop: widget.fromNotification ? false : true,
      onPopInvoked: (didPop) {
        context.read<DetailsBloc>().add(BackClearEvent());
        if (widget.fromNotification) {
          servicelocator<AppRouter>().replace(
            HomeNavigationRoute(),
          );
        }
      },
      child: AppDrawer(
        child: Scaffold(
          // appBar: AppBar(
          //   backgroundColor: Colors.transparent,
          //   elevation: 0,
          //   leading: const SizedBox(),
          //   scrolledUnderElevation: 0,
          // ),

          body: BlocConsumer<IndicatorCardBloc, IndicatorCardState>(
            listener: (context, indState) {
              if (indState is IndicatorDetailsSuccessState) {
                SchedulerBinding.instance.addPostFrameCallback((_) {
                  // if (indicatorDetails?.indicatorDetails.type == 'Internal' &&
                  //     indicatorDetails!.indicatorDetails.multiDrivers == true) {
                  //   context.router.removeWhere(
                  //       (route) => route.name == WhatIfDetailsPageRoute.name);
                  //   context.replaceRoute(
                  //     WhatIfDetailsPageRoute(
                  //       nodeId: widget.id ?? '',
                  //       indicatorDetails: indicatorDetails,
                  //       originalIndicatorData: originalIndicatorForFilter,
                  //     ),
                  //   );
                  //   return;
                  // }

                  // if (indState.isFromChangeDriverCallInitOnce != true) {
                  //   indicatorDetailsInitial = IndicatorDetailsResponseHelper(
                  //       indState.indicatorDetails);
                  // }

                  if (indState.isFromChangeDriverCallInitOnce != true) {
                    indicatorDetails = IndicatorDetailsResponseHelper(
                        indState.indicatorDetails);
                  }

                  isComparisonActive = indicatorDetails
                          ?.indicatorDetails
                          .indicatorVisualizations
                          ?.visualizationsMeta
                          ?.first
                          .id ==
                      'compare-chart';
                  domainName = indicatorDetails!.domainName;
                  title = indicatorDetails!.title;
                  value = indicatorDetails!.value;
                  unit = indicatorDetails!.unit;
                  numberUnit = indicatorDetails!.numberUnit;
                  negativeArrow = indicatorDetails!.negativeArrow;
                  contentType =
                      indicatorDetails!.indicatorDetails.indicatorType ??
                          widget.contentType ??
                          '';

                  originalIndicatorForFilter = indState.indicatorDetails;
                  nodeId = indState.indicatorDetails.id;

                  if (originalIndicatorForFilter?.type ==
                      'insights-discovery') {
                    final String defaultVisualization =
                        originalIndicatorForFilter?.defaultVisualisation ?? '';

                    for (final element
                        in originalIndicatorForFilter?.visualizations ??
                            <Visualizations>[]) {
                      if (element.id == defaultVisualization) {
                        selectedvisualizationComponent = element;
                        context.read<DetailsBloc>().add(
                              SelectVisualizationEvent(
                                selectedVisualization: element,
                              ),
                            );
                        break;
                      }
                    }
                  }

                  setFilter();
                });
              }
            },
            builder: (context, indState) {
              return BlocConsumer<DetailsBloc, DetailsState>(
                listener: (context, state) {
                  if (state is FilterCheckboxState) {
                    properties = state.propertiesList;
                  }
                  //  else if (state is ChangeDriversValueUpdateState) {
                  //   indicatorDetails = state.indicatorDetails;
                  // }
                  else if (state is ComputeDataSuccessState) {
                    indicatorDetails =
                        IndicatorDetailsResponseHelper(state.indicatorDetails);
                    selectedMeasurement = state.selectedMeasurement;
                    nameOfIndicator = state.nameOfIndicator;
                  } else if (state is CompareIndicatorSuccessState) {
                    indicatorDetails =
                        IndicatorDetailsResponseHelper(state.indicatorDetails);
                    isComparisonActive = indicatorDetails
                            ?.indicatorDetails
                            .indicatorVisualizations
                            ?.visualizationsMeta
                            ?.first
                            .id ==
                        'compare-chart';
                  } else if (state is SelectVisualizationState) {
                    selectedvisualizationComponent =
                        state.selectedVisualization;
                    insightToFilterData = List.from(
                      state.fullData?.first.data ?? [],
                    );
                    setFilter();

                    value = double.parse(
                      valUnit(
                            selectedvisualizationComponent?.indicatorValues
                                    ?.valuesMeta?.firstOrNull?.value ??
                                '',
                          ).firstOrNull ??
                          '',
                    ).toStringAsFixed(2);
                    // unit = indicatorDetails!.unit;
                    numberUnit = valUnit(
                      selectedvisualizationComponent?.indicatorValues
                              ?.valuesMeta?.firstOrNull?.value ??
                          '',
                    )[1];
                    // negativeArrow = indicatorDetails!.negativeArrow;

                    // this fixes chart no data issue on changing the visualization in some cases
                    context.read<DetailsBloc>().add(
                          FilterApplyEvent(
                            originlIndicatorDetails:
                                widget.originalIndicatorForFilter ??
                                    IndicatorDetailsResponse(),
                            propertyList: properties ?? [],
                            selectedVisualization:
                                selectedvisualizationComponent,
                            insightToFilterData: insightToFilterData,
                          ),
                        );
                  } else if (state is InsightDiscoveryFullDataLoadState) {
                    isDataLoading = state.isLoading;
                    if (isFirstTimeDataLoading != false) {
                      isFirstTimeDataLoading = true;
                    }
                  } else if (state is ResetChangeDriversState) {
                    scadProjectionValue = state.scadProjectionValue;
                    if (toResetChangeDrivers) {
                      toResetChangeDrivers = state.toReset;
                    }
                    if (state.toReset == false) {
                      indicatorDetails = state.indicatorVal;
                    }
                  }
                },
                builder: (context, state) {
                  final bool disableAppbarSlide = HiveUtilsSettings.getUserGuideStatus() == UserGuides.Home;
                  return indicatorDetails != null
                      ? Column(
                          children: [
                            FlatAppBar(
                              title: isComparisonActive
                                  ? widget.comparedIndicatorName ??
                                      LocaleKeys.compareIndicatorsResult.tr()
                                  : title,
                              isOfficial: !isComparisonActive,
                              bottomPadding: 0,
                              usingForDetailsPage: disableAppbarSlide ? false : true,
                              scrollController: disableAppbarSlide ? null : scrollController,
                              indicatorDetails: indicatorDetails,
                              onBack: () {
                                if (widget.fromNotification) {
                                  servicelocator<AppRouter>().replace(
                                    HomeNavigationRoute(),
                                  );
                                }
                                context.read<DetailsBloc>().selectedFrequency =
                                    '';
                                context.read<DetailsBloc>().isFirstTimeFilter =
                                    true;
                                for (final element in originalIndicatorForFilter
                                        ?.indicatorFilters?.first.options ??
                                    <Options>[]) {
                                  element.isSelected = false;
                                }
                                originalIndicatorForFilter = null;
                                if (duplicateData != null) {
                                  try {
                                    originalIndicatorForFilter =
                                        IndicatorDetailsResponse.fromJson(
                                      jsonDecode(duplicateData ?? '')
                                          as Map<String, dynamic>,
                                    );
                                  } catch (e, s) {
                                    Completer<dynamic>().completeError(e, s);
                                  }
                                }

                                setState(() {});
                              },
                            ),
                            Expanded(
                              child:
                                  isDataLoading == true &&
                                          isFirstTimeDataLoading == true
                                      ? const Center(
                                          child: CircularProgressIndicator(),
                                        )
                                      : Stack(
                                          children: [
                                            IgnorePointer(
                                              ignoring: isDataLoading == true,
                                              child: Padding(
                                                padding: const EdgeInsets.only(
                                                    top: 8),
                                                child: SingleChildScrollView(
                                                  controller: scrollController,
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      /// The title of the page
                                                      // DetailsPageTitle(
                                                      //   title: isComparisonActive
                                                      //       ? widget.comparedIndicatorName ??
                                                      //           LocaleKeys.compareIndicatorsResult
                                                      //               .tr()
                                                      //       : title,
                                                      //   isOfficial: !isComparisonActive,
                                                      // ),
                                                      // const SizedBox(height: 20),

                                                      Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                .symmetric(
                                                          horizontal: 24,
                                                        ),
                                                        child: Row(
                                                          mainAxisAlignment:
                                                              MainAxisAlignment
                                                                  .spaceBetween,
                                                          children: [
                                                            /// The theme(whether economy or anything else)
                                                            /// of the page

                                                            Expanded(
                                                              child: Visibility(
                                                                visible:
                                                                    !isComparisonActive,
                                                                child:
                                                                    DetailsPageTheme(
                                                                  themeText:
                                                                      domainName,
                                                                  icon: HiveUtilsApiCache
                                                                      .getDomainImage(
                                                                    indicatorDetails
                                                                        ?.indicatorDetails
                                                                        .domainId,
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                      const SizedBox(
                                                          height: 12),
                                                      if (originalIndicatorForFilter
                                                              ?.type ==
                                                          'insights-discovery') ...[
                                                        Padding(
                                                          padding:
                                                              const EdgeInsets
                                                                  .symmetric(
                                                            horizontal: 24,
                                                          ),
                                                          child:
                                                              RoundedDropDownWidget<
                                                                  Visualizations>(
                                                            items: originalIndicatorForFilter
                                                                    ?.visualizations ??
                                                                [],
                                                            compute: true,
                                                            onChanged: (val) {
                                                              isFirstTimeDataLoading =
                                                                  false;
                                                              context
                                                                  .read<
                                                                      DetailsBloc>()
                                                                  .add(
                                                                    SelectVisualizationEvent(
                                                                      selectedVisualization:
                                                                          val,
                                                                    ),
                                                                  );
                                                            },
                                                            value:
                                                                selectedvisualizationComponent,
                                                          ),
                                                        ),
                                                        const SizedBox(
                                                            height: 12),
                                                      ],
                                                      Container(
                                                        padding: EdgeInsets
                                                            .symmetric(
                                                          horizontal: 10,
                                                          vertical:
                                                              isComparisonActive
                                                                  ? 4
                                                                  : 18,
                                                        ),
                                                        decoration:
                                                            BoxDecoration(
                                                          color: isLightMode
                                                              ? AppColors
                                                                  .blueShade29
                                                              : AppColors
                                                                  .blueShade32,
                                                          borderRadius:
                                                              const BorderRadius
                                                                  .only(
                                                            topLeft:
                                                                Radius.circular(
                                                                    30),
                                                            topRight:
                                                                Radius.circular(
                                                                    30),
                                                          ),
                                                        ),
                                                        child: Column(
                                                          mainAxisSize:
                                                              MainAxisSize.min,
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .stretch,
                                                          children: [
                                                            if (!isComparisonActive)
                                                              Row(
                                                                mainAxisAlignment:
                                                                    MainAxisAlignment
                                                                        .spaceBetween,
                                                                children: [
                                                                  if (widget
                                                                          .originalIndicatorForFilter
                                                                          ?.type ==
                                                                      'insights-discovery') ...[
                                                                    Flexible(
                                                                      child:
                                                                          Padding(
                                                                        padding:
                                                                            const EdgeInsets.only(
                                                                          left:
                                                                              16,
                                                                        ),
                                                                        child:
                                                                            Text(
                                                                          selectedvisualizationComponent?.componentTitle ??
                                                                              '',
                                                                          textScaler:
                                                                              TextScaler.linear(textScaleFactor.value),
                                                                          style:
                                                                              TextStyle(
                                                                            color: isLightMode
                                                                                ? AppColors.black
                                                                                : AppColors.white,
                                                                            fontSize:
                                                                                20,
                                                                            fontWeight:
                                                                                FontWeight.w500,
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  ],
                                                                  if (indicatorDetails
                                                                              ?.indicatorDetails
                                                                              .filterPanel !=
                                                                          null ||
                                                                      selectedvisualizationComponent
                                                                              ?.filterPanel !=
                                                                          null) // &&  isDataLoading == false
                                                                    Visibility(
                                                                      visible: (indicatorDetails?.indicatorDetails.filterPanel
                                                                              is bool)
                                                                          ? (indicatorDetails?.indicatorDetails.filterPanel as bool) !=
                                                                              false
                                                                          : true,
                                                                      child:
                                                                          FilterDriverButton(
                                                                        title: LocaleKeys
                                                                            .filters
                                                                            .tr(),
                                                                        icon:
                                                                            'filters',
                                                                        onPressed:
                                                                            () {
                                                                          showModalBottomSheet<
                                                                              dynamic>(
                                                                            isScrollControlled:
                                                                                true,
                                                                            backgroundColor:
                                                                                Colors.transparent,
                                                                            useSafeArea:
                                                                                true,
                                                                            context:
                                                                                context,
                                                                            builder:
                                                                                (
                                                                              BuildContext context,
                                                                            ) {
                                                                              return FilterBottomSheet(
                                                                                properties: properties,
                                                                                filterData: originalIndicatorForFilter?.type == 'insights-discovery' ? selectedvisualizationComponent?.filterPanel : originalIndicatorForFilter?.filterPanel as FilterPanel?,
                                                                                indicatorData: indicatorDetails,
                                                                                originalIndicatorForFilter: originalIndicatorForFilter,
                                                                                selectedVisualization: selectedvisualizationComponent,
                                                                                insightToFilterData: insightToFilterData,
                                                                              );
                                                                            },
                                                                          );
                                                                        },
                                                                      ),
                                                                    )
                                                                  else
                                                                    const SizedBox
                                                                        .shrink(),
                                                                  if ((indicatorDetails
                                                                              ?.indicatorDetails
                                                                              .indicatorDrivers ??
                                                                          [])
                                                                      .isNotEmpty)
                                                                    FilterDriverButton(
                                                                      title: LocaleKeys
                                                                          .changeDrivers
                                                                          .tr(),
                                                                      icon:
                                                                          'driver-icon-blue',
                                                                      onPressed:
                                                                          () {
                                                                        AutoRouter
                                                                            .of(
                                                                          context,
                                                                        ).push(
                                                                          ChangeDriversFullScreenDialogRoute(
                                                                            scadProjection:
                                                                                scadProjectionValue,
                                                                            // indicatorDetails: toResetChangeDrivers
                                                                            //     ? IndicatorDetailsResponseHelper(indicatorDetailsInitial!.indicatorDetails)
                                                                            //     : IndicatorDetailsResponseHelper(indicatorDetails!.indicatorDetails),
                                                                            indicatorDetails:
                                                                                IndicatorDetailsResponseHelper(IndicatorDetailsResponse.fromJson(jsonDecode(jsonEncode(indicatorDetails?.indicatorDetails ?? {})) as Map<String, dynamic>)),
                                                                            contentType:
                                                                                widget.contentType,
                                                                          ),
                                                                        );
                                                                      },
                                                                    ),
                                                                ],
                                                              ),
                                                            //const SizedBox(height: 15),
                                                            ExpandedIndicatorDetailsView(
                                                              scrollController:
                                                                  scrollController,
                                                              isWhatIfDetails:
                                                                  false,
                                                              id: indicatorDetails!
                                                                  .indicatorDetails
                                                                  .id!,
                                                              indicatorDetails:
                                                                  indicatorDetails,
                                                              contentType:
                                                                  contentType,
                                                              nodeIdForResetFilter:
                                                                  nodeId,
                                                              contentTypeForResetFilter:
                                                                  currentContentType,
                                                              overviewContentTypeForResetFilter:
                                                                  currentOverviewContentType,
                                                              originalIndicatorForFilter:
                                                                  originalIndicatorForFilter,
                                                              comparedIndicatorName:
                                                                  widget
                                                                      .comparedIndicatorName,
                                                              selectedVisualization:
                                                                  selectedvisualizationComponent,
                                                              computedIndicatorName:
                                                                  nameOfIndicator,
                                                              isSearch:
                                                                  widget.id !=
                                                                      null,
                                                            ),

                                                            /// A container to navigate to glossary
                                                            // Padding(
                                                            //   padding:
                                                            //       const EdgeInsets
                                                            //           .symmetric(
                                                            //     horizontal: 16,
                                                            //   ),
                                                            //   child:
                                                            //       GlossaryContiner(
                                                            //     theme: indicatorDetails
                                                            //             ?.indicatorDetails
                                                            //             .theme ??
                                                            //         '',
                                                            //   ),
                                                            // ),
                                                            const SizedBox(
                                                                height: 10),
                                                          ],
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            ),
                                            if (isDataLoading == true) ...[
                                              Center(
                                                child: Container(
                                                  width: 50,
                                                  height: 50,
                                                  padding:
                                                      const EdgeInsets.all(10),
                                                  decoration: BoxDecoration(
                                                    color: Colors.transparent
                                                        .withOpacity(0.2),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            10),
                                                  ),
                                                  child:
                                                      CircularProgressIndicator(
                                                    color: AppColors
                                                        .selectedChipBlue,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ],
                                        ),
                            ),
                          ],
                        )
                      : indState is IndicatorDetailsErrorState
                          ? Center(
                              child: NoDataPlaceholder(
                                msg: indState.error,
                              ),
                            )
                          : const Center(child: CircularProgressIndicator());
                },
              );
            },
          ),
        ),
      ),
    );
  }

  List<String> valUnit(String originalValue) {
    final num val = num.tryParse(originalValue) ?? 0;

    num returnValue = val;
    String returnValueUnit = '';

    if (val > 1000000000000) {
      returnValue = val / 1000000000000;
      returnValueUnit = 'T';
    } else if (val > 1000000000) {
      returnValue = val / 1000000000;
      returnValueUnit = 'B';
    } else if (val > 1000000) {
      returnValue = val / 1000000;
      returnValueUnit = 'M';
    } else if (val > 10000) {
      returnValue = val / 10000;
      returnValueUnit = 'k';
    }

    return ['$returnValue', returnValueUnit];
  }
}

class TableText extends StatelessWidget {
  const TableText({
    required this.text,
    super.key,
    this.isHeader = false,
    this.padding,
    this.fontColor,
    this.alignment,
  });

  final String text;
  final bool isHeader;
  final EdgeInsetsGeometry? padding;
  final Color? fontColor;
  final AlignmentGeometry? alignment;

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: alignment ?? Alignment.centerLeft,
      padding: padding ?? const EdgeInsets.only(left: 24),
      height: 30,
      child: Text(
        text,
        style: AppTextStyles.s10w5cWhite
            .copyWith(color: isHeader ? Colors.white : fontColor),
        textScaler: TextScaler.linear(textScaleFactor.value),
      ),
    );
  }
}
