import 'dart:async';
import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/config/dependancy_injection/injection_container.dart';
import 'package:scad_mobile/src/features/home/<USER>/models/key_indicator_list/key_indicator_list_response.dart';
import 'package:scad_mobile/src/features/home/<USER>/models/recommended_indicator_list_item/recommended_indicator_list_item.dart';
import 'package:scad_mobile/src/features/home/<USER>/repositories/home_repository_imports.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

part 'home_event.dart';

part 'home_state.dart';

class HomeBloc extends Bloc<HomeEvent, HomeState> {
  HomeBloc(this.homeRepository) : super(HomeInitial()) {
    on<HomeEvent>((event, emit) {});
    on<NavToHomeEvent>(_onNavToHome);
    on<KeyIndicatorsEvent>(keyIndicatorEventHandler);
  }

  final HomeRepository homeRepository;

  FutureOr<void> _onNavToHome(
    NavToHomeEvent event,
    Emitter<HomeState> emit,
  ) {
    emit(NavToHomeState());
  }

  FutureOr<void> keyIndicatorEventHandler(
    KeyIndicatorsEvent event,
    Emitter<HomeState> emit,
  ) async {
    try {
      emit(KeyIndicatorLoadingState());

      final RepoResponse<List<KeyIndicatorListResponseItem>> response =
          await servicelocator<HomeRepository>().keyIndicatorList();

      if (response.isSuccess) {
        emit(KeyIndicatorListState(list: response.response ?? []));
      } else {
        emit(
          KeyIndicatorErrorState(error: response.errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(KeyIndicatorErrorState(error:  LocaleKeys.somethingWentWrong.tr()));
    } finally {
      // emit(KeyIndicatorCompleteState());
    }
  }

}
