part of 'home_bloc.dart';

abstract class HomeState extends Equatable {
  const HomeState();

  @override
  List<Object> get props => [];
}

class HomeInitial extends HomeState {}

class KeyIndicatorListState extends HomeState {
  const KeyIndicatorListState({
    required this.list,
  });

  final List<KeyIndicatorListResponseItem> list;

  @override
  List<Object> get props => [list];
}

class RecommendedIndicatorListState extends HomeState {
  const RecommendedIndicatorListState({
    required this.list,
  });

  final List<RecommendedIndicatorListResponseItem> list;

  @override
  List<Object> get props => [list];
}

class NavToHomeState extends HomeState {
  NavToHomeState() {
    rnd = Random().nextInt(10000);
  }

  late final int rnd;

  @override
  List<Object> get props => [rnd];
}

class KeyIndicatorLoadingState extends HomeState {}

class KeyIndicatorCompleteState extends HomeState {}

class KeyIndicatorErrorState extends HomeState {
  const KeyIndicatorErrorState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}

class CheckMaintenanceSuccessState extends HomeState {
  const CheckMaintenanceSuccessState(this.data);

  final MaintenanceStatus data;

  @override
  List<Object> get props => [data];
}