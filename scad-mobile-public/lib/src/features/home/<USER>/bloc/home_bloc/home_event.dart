part of 'home_bloc.dart';

abstract class HomeEvent extends Equatable {
  const HomeEvent();

  @override
  List<Object> get props => [];
}

class NavToHomeEvent extends HomeEvent {
  NavToHomeEvent() {
    rnd = Random().nextInt(10000);
  }

  late final int rnd;

  @override
  List<Object> get props => [rnd];
}

class KeyIndicatorsEvent extends HomeEvent {
  const KeyIndicatorsEvent();

  @override
  List<Object> get props => [];
}

