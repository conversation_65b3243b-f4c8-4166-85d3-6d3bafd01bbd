class RecommendedIndicatorListResponseItem {
  RecommendedIndicatorListResponseItem({
    this.indicatorId,
    this.title,
    this.contentType,
    this.appType,
    this.type,
    this.domain,
  });

  RecommendedIndicatorListResponseItem.fromJson(Map<String, dynamic> json) {
    indicatorId = json['indicatorId'] as String?;
    title = json['title'] as String?;
    contentType = json['contentType'] as String?;
    appType = json['appType'];
    type = json['type'] as String?;
    domain = json['domain'] as String?;
  }

  String? indicatorId;
  String? title;
  String? contentType;
  dynamic appType;
  String? type;
  String? domain;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['indicatorId'] = indicatorId;
    json['title'] = title;
    json['contentType'] = contentType;
    json['appType'] = appType;
    json['type'] = type;
    json['domain'] = domain;
    return json;
  }
}
