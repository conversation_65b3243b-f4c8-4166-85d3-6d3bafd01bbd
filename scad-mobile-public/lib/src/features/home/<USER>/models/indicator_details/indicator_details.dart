class IndicatorOverview {
  IndicatorOverview({
    this.id,
    this.compareFilters,
    this.valueFormat,
    this.templateFormat,
    this.baseDate,
    this.value,
    this.yearlyCompareValue,
    this.yearlyChangeValue,
    this.quarterlyCompareValue,
    this.quarterlyChangeValue,
    this.monthlyCompareValue,
    this.monthlyChangeValue,
  });

  IndicatorOverview.fromJson(Map<String, dynamic> json) {
    id = json['id'] as String?;
    compareFilters = (json['compareFilters'] as List?)
        ?.map((dynamic e) => e as String)
        .toList();
    valueFormat = json['valueFormat'] as String?;
    templateFormat = json['templateFormat'] as String?;
    baseDate = json['baseDate'] as String?;
    value = double.parse(json['value'].toString());
    yearlyCompareValue = double.parse(json['yearlyCompareValue'].toString());
    yearlyChangeValue = json['yearlyChangeValue'] as int?;
    quarterlyCompareValue =
        double.parse(json['quarterlyCompareValue'].toString());
    quarterlyChangeValue = json['quarterlyChangeValue'] as int?;
    monthlyCompareValue = double.parse(json['monthlyCompareValue'].toString());
    monthlyChangeValue = json['monthlyChangeValue'] as int?;
  }

  String? id;
  List<String>? compareFilters;
  String? valueFormat;
  String? templateFormat;
  String? baseDate;
  double? value;
  double? yearlyCompareValue;
  int? yearlyChangeValue;
  double? quarterlyCompareValue;
  int? quarterlyChangeValue;
  double? monthlyCompareValue;
  int? monthlyChangeValue;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['id'] = id;
    json['compareFilters'] = compareFilters;
    json['valueFormat'] = valueFormat;
    json['templateFormat'] = templateFormat;
    json['baseDate'] = baseDate;
    json['value'] = value;
    json['yearlyCompareValue'] = yearlyCompareValue;
    json['yearlyChangeValue'] = yearlyChangeValue;
    json['quarterlyCompareValue'] = quarterlyCompareValue;
    json['quarterlyChangeValue'] = quarterlyChangeValue;
    json['monthlyCompareValue'] = monthlyCompareValue;
    json['monthlyChangeValue'] = monthlyChangeValue;
    return json;
  }
}
