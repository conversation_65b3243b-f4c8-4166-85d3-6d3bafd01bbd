// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'key_indicator_list_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

KeyIndicatorListResponseItem _$KeyIndicatorListResponseItemFromJson(
        Map<String, dynamic> json) =>
    KeyIndicatorListResponseItem(
      json['uuid'] as String?,
      json['added_by'] == null
          ? null
          : KeyIndicatorListResponseAddedBy.fromJson(
              json['added_by'] as Map<String, dynamic>),
      json['edited_by'] == null
          ? null
          : KeyIndicatorListResponseEditedBy.fromJson(
              json['edited_by'] as Map<String, dynamic>),
      json['node_id'] as String?,
      json['app_type'] as String?,
      json['content_type'] as String?,
      json['key'] as String?,
      json['title'] as String?,
      json['domain_name'] as String?,
      json['active'] as bool?,
      json['created_time'] as String?,
      json['updated_time'] as String?,
    );

Map<String, dynamic> _$KeyIndicatorListResponseItemToJson(
        KeyIndicatorListResponseItem instance) =>
    <String, dynamic>{
      'uuid': instance.uuid,
      'added_by': instance.addedBy,
      'edited_by': instance.editedBy,
      'node_id': instance.nodeId,
      'app_type': instance.appType,
      'content_type': instance.contentType,
      'key': instance.key,
      'title': instance.title,
      'domain_name': instance.domainName,
      'active': instance.active,
      'created_time': instance.createdTime,
      'updated_time': instance.updatedTime,
    };

KeyIndicatorListResponseAddedBy _$KeyIndicatorListResponseAddedByFromJson(
        Map<String, dynamic> json) =>
    KeyIndicatorListResponseAddedBy(
      json['uuid'] as String?,
      json['name'] as String?,
    );

Map<String, dynamic> _$KeyIndicatorListResponseAddedByToJson(
        KeyIndicatorListResponseAddedBy instance) =>
    <String, dynamic>{
      'uuid': instance.uuid,
      'name': instance.name,
    };

KeyIndicatorListResponseEditedBy _$KeyIndicatorListResponseEditedByFromJson(
        Map<String, dynamic> json) =>
    KeyIndicatorListResponseEditedBy(
      json['uuid'] as String?,
      json['name'] as String?,
    );

Map<String, dynamic> _$KeyIndicatorListResponseEditedByToJson(
        KeyIndicatorListResponseEditedBy instance) =>
    <String, dynamic>{
      'uuid': instance.uuid,
      'name': instance.name,
    };
