import 'package:json_annotation/json_annotation.dart';

part 'key_indicator_list_response.g.dart';

@JsonSerializable()
class KeyIndicatorListResponseItem {
  String? uuid;
  // @J<PERSON><PERSON><PERSON>(name: 'added_by')
  // KeyIndicatorListResponseAddedBy? addedBy;
  // @<PERSON><PERSON><PERSON><PERSON>(name: 'edited_by')
  // KeyIndicatorListResponseEditedBy? editedBy;
  @Json<PERSON>ey(name: 'node_id')
  String? nodeId;
  @Json<PERSON>ey(name: 'app_type')
  String? appType;
  @Json<PERSON>ey(name: 'content_type')
  String? contentType;
  String? key;
  String? title;
  @Json<PERSON>ey(name: 'domain_name')
  String? domainName;
  bool? active;
  @Json<PERSON>ey(name: 'created_time')
  String? createdTime;
  @Json<PERSON>ey(name: 'updated_time')
  String? updatedTime;

  KeyIndicatorListResponseItem(
      this.uuid,
      // this.addedBy,
      // this.editedBy,
      this.nodeId,
      this.appType,
      this.contentType,
      this.key,
      this.title,
      this.domainName,
      this.active,
      this.createdTime,
      this.updatedTime);

  factory KeyIndicatorListResponseItem.fromJson(Map<String, dynamic> json) =>
      _$KeyIndicatorListResponseItemFromJson(json);

  Map<String, dynamic> toJson() => _$KeyIndicatorListResponseItemToJson(this);
}

// @JsonSerializable()
// class KeyIndicatorListResponseAddedBy {
//   String? uuid;
//   String? name;
//
//   KeyIndicatorListResponseAddedBy(this.uuid, this.name);
//
//   factory KeyIndicatorListResponseAddedBy.fromJson(Map<String, dynamic> json) =>
//       _$KeyIndicatorListResponseAddedByFromJson(json);
//
//   Map<String, dynamic> toJson() =>
//       _$KeyIndicatorListResponseAddedByToJson(this);
// }
//
// @JsonSerializable()
// class KeyIndicatorListResponseEditedBy {
//   String? uuid;
//   String? name;
//
//   KeyIndicatorListResponseEditedBy(this.uuid, this.name);
//
//   factory KeyIndicatorListResponseEditedBy.fromJson(
//           Map<String, dynamic> json) =>
//       _$KeyIndicatorListResponseEditedByFromJson(json);
//
//   Map<String, dynamic> toJson() =>
//       _$KeyIndicatorListResponseEditedByToJson(this);
// }
