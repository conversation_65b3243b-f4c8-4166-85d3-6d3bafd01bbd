// import 'package:json_annotation/json_annotation.dart';
//
// part 'key_indicator_response.g.dart';
//
//
// @JsonSerializable()
// class KeyIndicatorResponse {
//   String? id;
//   @Json<PERSON>ey(name: 'component_title')
//   String? componentTitle;
//   @Json<PERSON>ey(name: 'component_subtitle')
//   String? componentSubtitle;
//   @Json<PERSON>ey(name: 'page_icon')
//   String? pageIcon;
//   @J<PERSON><PERSON><PERSON>(name: 'page_light_icon')
//   String? pageLightIcon;
//   List<String?>? domains;
//   String? domain;
//   String? theme;
//   String? subtheme;
//   String? product;
//   String? type;
//   String? note;
//   String? narrative;
//   @Json<PERSON>ey(name: 'search_tags')
//   List<String?>? searchTags;
//   String? attachment;
//   @J<PERSON><PERSON>ey(name: 'Indicator')
//   String? Indicator;
//   @<PERSON><PERSON><PERSON><PERSON>(name: 'policy_guide')
//   String? policyGuide;
//   bool? enableCompare;
//   @J<PERSON><PERSON><PERSON>(name: 'compare_data')
//   String? compareData;
//   String? insights;
//   bool? enablePointToggle;
//   String? maxPointLimit;
//   String? minLimitYAxis;
//   bool? enableVisualizationTypes;
//   bool? enableUploadAndCompare;
//   String? showInsights;
//   @JsonKey(name: 'content_classification')
//   String? contentClassification;
//   @JsonKey(name: 'content_classification_key')
//   String? contentClassificationKey;
//   @JsonKey(name: 'domain_id')
//   String? domainId;
//   @JsonKey(name: 'theme_id')
//   int? themeId;
//   @JsonKey(name: 'subtheme_id')
//   int? subthemeId;
//   @JsonKey(name: 'product_id')
//   int? productId;
//   String? tagName;
//   @JsonKey(name: 'data_source')
//   String? dataSource;
//   @JsonKey(name: 'publication_date')
//   String? publicationDate;
//   String? updated;
//   String? language;
//   List<KeyIndicatorResponseIndicatorTools?>? indicatorTools;
//   List<KeyIndicatorResponseIndicatorFilters?>? indicatorFilters;
//   List<dynamic>? indicatorDrivers;
//   KeyIndicatorResponseIndicatorValues? indicatorValues;
//   KeyIndicatorResponseIndicatorVisualizations? indicatorVisualizations;
//   bool? isUploadCompareData;
//
//   // KeyIndicatorResponseMetaData? metaData;
//   String? unit;
//   String? viewName;
//   String? indicatorId;
//   bool? isMultiDimension;
//   KeyIndicatorResponseFilterPanel? filterPanel;
//   List<KeyIndicatorResponseTableFields?>? tableFields;
//
//   KeyIndicatorResponse(
//       this.id,
//       this.componentTitle,
//       this.componentSubtitle,
//       this.pageIcon,
//       this.pageLightIcon,
//       this.domains,
//       this.domain,
//       this.theme,
//       this.subtheme,
//       this.product,
//       this.type,
//       this.note,
//       this.narrative,
//       this.searchTags,
//       this.attachment,
//       this.Indicator,
//       this.policyGuide,
//       this.enableCompare,
//       this.compareData,
//       this.insights,
//       this.enablePointToggle,
//       this.maxPointLimit,
//       this.minLimitYAxis,
//       this.enableVisualizationTypes,
//       this.enableUploadAndCompare,
//       this.showInsights,
//       this.contentClassification,
//       this.contentClassificationKey,
//       this.domainId,
//       this.themeId,
//       this.subthemeId,
//       this.productId,
//       this.tagName,
//       this.dataSource,
//       this.publicationDate,
//       this.updated,
//       this.language,
//       this.indicatorTools,
//       this.indicatorFilters,
//       this.indicatorDrivers,
//       this.indicatorValues,
//       this.indicatorVisualizations,
//       this.isUploadCompareData,
//       // this.metaData,
//       this.unit,
//       this.viewName,
//       this.indicatorId,
//       this.isMultiDimension,
//       this.filterPanel,
//       this.tableFields);
//
//   factory KeyIndicatorResponse.fromJson(Map<String, dynamic> json) =>
//       _$KeyIndicatorResponseFromJson(json);
//
//   Map<String, dynamic> toJson() => _$KeyIndicatorResponseToJson(this);
// }
//
//
// @JsonSerializable()
// class KeyIndicatorResponseIndicatorTools {
//   String? id;
//   bool? disabled;
//   String? label;
//
//   KeyIndicatorResponseIndicatorTools(this.id, this.disabled, this.label);
//
//   factory KeyIndicatorResponseIndicatorTools.fromJson(
//           Map<String, dynamic> json) =>
//       _$KeyIndicatorResponseIndicatorToolsFromJson(json);
//
//   Map<String, dynamic> toJson() =>
//       _$KeyIndicatorResponseIndicatorToolsToJson(this);
// }
//
//
// @JsonSerializable()
// class KeyIndicatorResponseIndicatorFilters {
//   String? id;
//   List<KeyIndicatorResponseIndicatorFiltersOptions?>? options;
//
//   KeyIndicatorResponseIndicatorFilters(this.id, this.options);
//
//   factory KeyIndicatorResponseIndicatorFilters.fromJson(
//           Map<String, dynamic> json) =>
//       _$KeyIndicatorResponseIndicatorFiltersFromJson(json);
//
//   Map<String, dynamic> toJson() =>
//       _$KeyIndicatorResponseIndicatorFiltersToJson(this);
// }
//
//
// @JsonSerializable()
// class KeyIndicatorResponseIndicatorFiltersOptions {
//   String? id;
//   String? label;
//   String? unit;
//   int? value;
//
//   KeyIndicatorResponseIndicatorFiltersOptions(
//       this.id, this.label, this.unit, this.value);
//
//   factory KeyIndicatorResponseIndicatorFiltersOptions.fromJson(
//           Map<String, dynamic> json) =>
//       _$KeyIndicatorResponseIndicatorFiltersOptionsFromJson(json);
//
//   Map<String, dynamic> toJson() =>
//       _$KeyIndicatorResponseIndicatorFiltersOptionsToJson(this);
// }
//
//
// @JsonSerializable()
// class KeyIndicatorResponseIndicatorValues {
//   KeyIndicatorResponseIndicatorValues();
//
//   factory KeyIndicatorResponseIndicatorValues.fromJson(
//           Map<String, dynamic> json) =>
//       _$KeyIndicatorResponseIndicatorValuesFromJson(json);
//
//   Map<String, dynamic> toJson() =>
//       _$KeyIndicatorResponseIndicatorValuesToJson(this);
// }
//
//
// @JsonSerializable()
// class KeyIndicatorResponseIndicatorVisualizations {
//   List<KeyIndicatorResponseIndicatorVisualizationsVisualizationsMeta?>?
//       visualizationsMeta;
//   String? visualizationDefault;
//
//   KeyIndicatorResponseIndicatorVisualizations(
//       this.visualizationsMeta, this.visualizationDefault);
//
//   factory KeyIndicatorResponseIndicatorVisualizations.fromJson(
//           Map<String, dynamic> json) =>
//       _$KeyIndicatorResponseIndicatorVisualizationsFromJson(json);
//
//   Map<String, dynamic> toJson() =>
//       _$KeyIndicatorResponseIndicatorVisualizationsToJson(this);
// }
//
//
// @JsonSerializable()
// class KeyIndicatorResponseIndicatorVisualizationsVisualizationsMeta {
//   String? id;
//   String? type;
//   String? indicatorId;
//   List<KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaSeriesMeta?>?
//       seriesMeta;
//   String? tooltipTitleFormat;
//   String? tooltipValueFormat;
//   String? xAxisFormat;
//   String? yAxisFormat;
//   String? xAxisLabel;
//   String? yAxisLabel;
//   String? viewName;
//   List<dynamic>? markersMeta;
//   String? dbColumn;
//   @JsonKey(name: 'componentTitle_ar')
//   String? componentTitleAr;
//   @JsonKey(name: 'data_source')
//   String? dataSource;
//   @JsonKey(name: 'componentSubtitle_ar')
//   String? componentSubtitleAr;
//   @JsonKey(name: 'unit_ar')
//   String? unitAr;
//   String? unit;
//   @JsonKey(name: 'yAxisLabel_ar')
//   String? yAxisLabelAr;
//   List<String?>? timeUnit;
//   String? componentTitle;
//   @JsonKey(name: 'data_source_ar')
//   String? dataSourceAr;
//   String? note;
//   bool? dimension;
//   @JsonKey(name: 'note_ar')
//   String? noteAr;
//   String? componentSubtitle;
//   bool? isMultiDimension;
//   List<KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaIndicatorFilters?>?
//       indicatorFilters;
//   bool? showLegend;
//   String? indicatorIdValue;
//   KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaFilterPanelData?
//       filterPanelData;
//   bool? showPointLabels;
//
//   KeyIndicatorResponseIndicatorVisualizationsVisualizationsMeta(
//       this.id,
//       this.type,
//       this.indicatorId,
//       this.seriesMeta,
//       this.tooltipTitleFormat,
//       this.tooltipValueFormat,
//       this.xAxisFormat,
//       this.yAxisFormat,
//       this.xAxisLabel,
//       this.yAxisLabel,
//       this.viewName,
//       this.markersMeta,
//       this.dbColumn,
//       this.componentTitleAr,
//       this.dataSource,
//       this.componentSubtitleAr,
//       this.unitAr,
//       this.unit,
//       this.yAxisLabelAr,
//       this.timeUnit,
//       this.componentTitle,
//       this.dataSourceAr,
//       this.note,
//       this.dimension,
//       this.noteAr,
//       this.componentSubtitle,
//       this.isMultiDimension,
//       this.indicatorFilters,
//       this.showLegend,
//       this.indicatorIdValue,
//       this.filterPanelData,
//       this.showPointLabels);
//
//   factory KeyIndicatorResponseIndicatorVisualizationsVisualizationsMeta.fromJson(
//           Map<String, dynamic> json) =>
//       _$KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaFromJson(
//           json);
//
//   Map<String, dynamic> toJson() =>
//       _$KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaToJson(
//           this);
// }
//
//
// @JsonSerializable()
// class KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaSeriesMeta {
//   String? type;
//   KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaSeriesMetaXAccessor?
//       xAccessor;
//   KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaSeriesMetaYAccessor?
//       yAccessor;
//   String? id;
//   String? dbIndicatorId;
//   dynamic label;
//   String? color;
//   List<Map<String, dynamic>>? data;
//   int? yMax;
//   int? yMin;
//   String? xMin;
//   String? xMax;
//
//   KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaSeriesMeta(
//       this.type,
//       this.xAccessor,
//       this.yAccessor,
//       this.id,
//       this.dbIndicatorId,
//       this.label,
//       this.color,
//       this.data,
//       this.yMax,
//       this.yMin,
//       this.xMin,
//       this.xMax);
//
//   factory KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaSeriesMeta.fromJson(
//           Map<String, dynamic> json) =>
//       _$KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaSeriesMetaFromJson(
//           json);
//
//   Map<String, dynamic> toJson() =>
//       _$KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaSeriesMetaToJson(
//           this);
// }
//
//
// @JsonSerializable()
// class KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaSeriesMetaXAccessor {
//   String? type;
//   String? path;
//   String? specifier;
//
//   KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaSeriesMetaXAccessor(
//       this.type, this.path, this.specifier);
//
//   factory KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaSeriesMetaXAccessor.fromJson(
//           Map<String, dynamic> json) =>
//       _$KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaSeriesMetaXAccessorFromJson(
//           json);
//
//   Map<String, dynamic> toJson() =>
//       _$KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaSeriesMetaXAccessorToJson(
//           this);
// }
//
//
// @JsonSerializable()
// class KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaSeriesMetaYAccessor {
//   String? type;
//   String? path;
//
//   KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaSeriesMetaYAccessor(
//       this.type, this.path);
//
//   factory KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaSeriesMetaYAccessor.fromJson(
//           Map<String, dynamic> json) =>
//       _$KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaSeriesMetaYAccessorFromJson(
//           json);
//
//   Map<String, dynamic> toJson() =>
//       _$KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaSeriesMetaYAccessorToJson(
//           this);
// }
//
//
// @JsonSerializable()
// class KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaIndicatorFilters {
//   String? id;
//   List<KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaIndicatorFiltersOptions?>?
//       options;
//
//   KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaIndicatorFilters(
//       this.id, this.options);
//
//   factory KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaIndicatorFilters.fromJson(
//           Map<String, dynamic> json) =>
//       _$KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaIndicatorFiltersFromJson(
//           json);
//
//   Map<String, dynamic> toJson() =>
//       _$KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaIndicatorFiltersToJson(
//           this);
// }
//
//
// @JsonSerializable()
// class KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaIndicatorFiltersOptions {
//   String? id;
//   String? label;
//   String? unit;
//   int? value;
//
//   KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaIndicatorFiltersOptions(
//       this.id, this.label, this.unit, this.value);
//
//   factory KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaIndicatorFiltersOptions.fromJson(
//           Map<String, dynamic> json) =>
//       _$KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaIndicatorFiltersOptionsFromJson(
//           json);
//
//   Map<String, dynamic> toJson() =>
//       _$KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaIndicatorFiltersOptionsToJson(
//           this);
// }
//
//
// @JsonSerializable()
// class KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaFilterPanelData {
//   String? id;
//   bool? isEnabled;
//   String? label;
//   String? viewName;
//   KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaFilterPanelDataDimension?
//       dimension;
//   List<KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaFilterPanelDataProperties?>?
//       properties;
//
//   KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaFilterPanelData(
//       this.id,
//       this.isEnabled,
//       this.label,
//       this.viewName,
//       this.dimension,
//       this.properties);
//
//   factory KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaFilterPanelData.fromJson(
//           Map<String, dynamic> json) =>
//       _$KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaFilterPanelDataFromJson(
//           json);
//
//   Map<String, dynamic> toJson() =>
//       _$KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaFilterPanelDataToJson(
//           this);
// }
//
//
// @JsonSerializable()
// class KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaFilterPanelDataDimension {
//   @JsonKey(name: 'INDICATOR_ID')
//   String? INDICATORID;
//
//   KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaFilterPanelDataDimension(
//       this.INDICATORID);
//
//   factory KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaFilterPanelDataDimension.fromJson(
//           Map<String, dynamic> json) =>
//       _$KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaFilterPanelDataDimensionFromJson(
//           json);
//
//   Map<String, dynamic> toJson() =>
//       _$KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaFilterPanelDataDimensionToJson(
//           this);
// }
//
//
// @JsonSerializable()
// class KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaFilterPanelDataProperties {
//   String? label;
//   String? path;
//   bool? isMultiSeries;
//   bool? isRanked;
//   @JsonKey(name: 'default')
//   String? defaultVal;
//
//   String? type;
//   String? lang;
//   List<String?>? options;
//
//   KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaFilterPanelDataProperties(
//       this.label,
//       this.path,
//       this.isMultiSeries,
//       this.isRanked,
//       this.defaultVal,
//       this.type,
//       this.lang,
//       this.options);
//
//   factory KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaFilterPanelDataProperties.fromJson(
//           Map<String, dynamic> json) =>
//       _$KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaFilterPanelDataPropertiesFromJson(
//           json);
//
//   Map<String, dynamic> toJson() =>
//       _$KeyIndicatorResponseIndicatorVisualizationsVisualizationsMetaFilterPanelDataPropertiesToJson(
//           this);
// }
//
//
// @JsonSerializable()
// class KeyIndicatorResponseMetaData {
//   KeyIndicatorResponseMetaData();
//
//   factory KeyIndicatorResponseMetaData.fromJson(Map<String, dynamic> json) =>
//       _$KeyIndicatorResponseMetaDataFromJson(json);
//
//   Map<String, dynamic> toJson() => _$KeyIndicatorResponseMetaDataToJson(this);
// }
//
//
// @JsonSerializable()
// class KeyIndicatorResponseFilterPanel {
//   String? id;
//   bool? isEnabled;
//   String? label;
//   String? viewName;
//   KeyIndicatorResponseFilterPanelDimension? dimension;
//   List<KeyIndicatorResponseFilterPanelProperties?>? properties;
//
//   KeyIndicatorResponseFilterPanel(this.id, this.isEnabled, this.label,
//       this.viewName, this.dimension, this.properties);
//
//   factory KeyIndicatorResponseFilterPanel.fromJson(Map<String, dynamic> json) =>
//       _$KeyIndicatorResponseFilterPanelFromJson(json);
//
//   Map<String, dynamic> toJson() =>
//       _$KeyIndicatorResponseFilterPanelToJson(this);
// }
//
//
// @JsonSerializable()
// class KeyIndicatorResponseFilterPanelDimension {
//   @JsonKey(name: 'INDICATOR_ID')
//   String? INDICATORID;
//
//   KeyIndicatorResponseFilterPanelDimension(this.INDICATORID);
//
//   factory KeyIndicatorResponseFilterPanelDimension.fromJson(
//           Map<String, dynamic> json) =>
//       _$KeyIndicatorResponseFilterPanelDimensionFromJson(json);
//
//   Map<String, dynamic> toJson() =>
//       _$KeyIndicatorResponseFilterPanelDimensionToJson(this);
// }
//
//
// @JsonSerializable()
// class KeyIndicatorResponseFilterPanelProperties {
//   String? label;
//   String? path;
//   bool? isMultiSeries;
//   bool? isRanked;
//
//   @JsonKey(name: 'default')
//   String? defaultVal;
//
//   String? type;
//   String? lang;
//   List<String?>? options;
//
//   KeyIndicatorResponseFilterPanelProperties(
//       this.label,
//       this.path,
//       this.isMultiSeries,
//       this.isRanked,
//       this.defaultVal,
//       this.type,
//       this.lang,
//       this.options);
//
//   factory KeyIndicatorResponseFilterPanelProperties.fromJson(
//           Map<String, dynamic> json) =>
//       _$KeyIndicatorResponseFilterPanelPropertiesFromJson(json);
//
//   Map<String, dynamic> toJson() =>
//       _$KeyIndicatorResponseFilterPanelPropertiesToJson(this);
// }
//
//
// @JsonSerializable()
// class KeyIndicatorResponseTableFields {
//   String? label;
//   String? path;
//
//   KeyIndicatorResponseTableFields(this.label, this.path);
//
//   factory KeyIndicatorResponseTableFields.fromJson(Map<String, dynamic> json) =>
//       _$KeyIndicatorResponseTableFieldsFromJson(json);
//
//   Map<String, dynamic> toJson() =>
//       _$KeyIndicatorResponseTableFieldsToJson(this);
// }
