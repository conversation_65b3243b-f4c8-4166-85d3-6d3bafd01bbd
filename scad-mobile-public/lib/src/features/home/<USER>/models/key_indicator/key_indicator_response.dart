class KeyIndicatorResponse {
  String? id;
  String? componentTitle;
  String? componentSubtitle;
  String? domain;
  String? type;
  String? note;
  String? maxPointLimit;
  String? minLimitYAxis;
  String? contentClassification;
  String? domainId;
  String? tagName;
  String? language;
  List<IndicatorTools>? indicatorTools;
  List<IndicatorFilters>? indicatorFilters;
  List<dynamic>? indicatorDrivers;

  // IndicatorValues? indicatorValues;
  IndicatorVisualizations? indicatorVisualizations;
  String? domainLightIcon;
  String? domainDarkIcon;
  String? subdomain;
  String? subdomainId;
  String? viewName;
  String? contentClassificationKey;
  OverView? overView;
  String? dataSource;
  String? unit;
  String? publicationDate;
  List<TableFields>? tableFields;
  bool? isMultiDimension;

  KeyIndicatorResponse({
    this.id,
    this.componentTitle,
    this.componentSubtitle,
    this.domain,
    this.type,
    this.note,
    this.maxPointLimit,
    this.minLimitYAxis,
    this.contentClassification,
    this.domainId,
    this.tagName,
    this.language,
    this.indicatorTools,
    this.indicatorFilters,
    this.indicatorDrivers,
    // this.indicatorValues,
    this.indicatorVisualizations,
    this.domainLightIcon,
    this.domainDarkIcon,
    this.subdomain,
    this.subdomainId,
    this.viewName,
    this.contentClassificationKey,
    this.overView,
    this.dataSource,
    this.unit,
    this.publicationDate,
    this.tableFields,
    this.isMultiDimension,
  });

  KeyIndicatorResponse.fromJson(Map<String, dynamic> json) {
    id = json['id'] as String?;
    componentTitle = json['component_title'] as String?;
    componentSubtitle = json['component_subtitle'] as String?;
    domain = json['domain'] as String?;
    type = json['type'] as String?;
    note = json['note'] as String?;
    maxPointLimit = json['maxPointLimit'] as String?;
    minLimitYAxis = json['minLimitYAxis'] as String?;
    contentClassification = json['content_classification'] as String?;
    domainId = json['domain_id'].toString();
    tagName = json['tagName'] as String?;
    language = json['language'] as String?;
    indicatorTools = (json['indicatorTools'] as List?)
        ?.map((dynamic e) => IndicatorTools.fromJson(e as Map<String, dynamic>))
        .toList();
    indicatorFilters = (json['indicatorFilters'] as List?)
        ?.map(
            (dynamic e) => IndicatorFilters.fromJson(e as Map<String, dynamic>),)
        .toList();
    indicatorDrivers = json['indicatorDrivers'] as List?;
    // indicatorValues = (json['indicatorValues'] as Map<String,dynamic>?) != null ? IndicatorValues.fromJson(json['indicatorValues'] as Map<String,dynamic>) : null;
    indicatorVisualizations =
        (json['indicatorVisualizations'] as Map<String, dynamic>?) != null
            ? IndicatorVisualizations.fromJson(
                json['indicatorVisualizations'] as Map<String, dynamic>,)
            : null;
    domainLightIcon = json['domain_light_icon'] as String?;
    domainDarkIcon = json['domain_dark_icon'] as String?;
    subdomain = json['subdomain'] as String?;
    subdomainId = json['subdomain_id'] as String?;
    viewName = json['viewName'] as String?;
    contentClassificationKey = json['content_classification_key'] as String?;
    overView = (json['overView'] as Map<String, dynamic>?) != null
        ? OverView.fromJson(json['overView'] as Map<String, dynamic>)
        : null;
    dataSource = json['data_source'] as String?;
    unit = json['unit'] as String?;
    publicationDate = json['publication_date'] as String?;
    tableFields = (json['tableFields'] as List?)
        ?.map((dynamic e) => TableFields.fromJson(e as Map<String, dynamic>))
        .toList();
    isMultiDimension = json['isMultiDimension'] as bool?;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['id'] = id;
    json['component_title'] = componentTitle;
    json['component_subtitle'] = componentSubtitle;
    json['domain'] = domain;
    json['type'] = type;
    json['note'] = note;
    json['maxPointLimit'] = maxPointLimit;
    json['minLimitYAxis'] = minLimitYAxis;
    json['content_classification'] = contentClassification;
    json['domain_id'] = domainId;
    json['tagName'] = tagName;
    json['language'] = language;
    json['indicatorTools'] = indicatorTools?.map((e) => e.toJson()).toList();
    json['indicatorFilters'] =
        indicatorFilters?.map((e) => e.toJson()).toList();
    json['indicatorDrivers'] = indicatorDrivers;
    // json['indicatorValues'] = indicatorValues?.toJson();
    json['indicatorVisualizations'] = indicatorVisualizations?.toJson();
    json['domain_light_icon'] = domainLightIcon;
    json['domain_dark_icon'] = domainDarkIcon;
    json['subdomain'] = subdomain;
    json['subdomain_id'] = subdomainId;
    json['viewName'] = viewName;
    json['content_classification_key'] = contentClassificationKey;
    json['overView'] = overView?.toJson();
    json['data_source'] = dataSource;
    json['unit'] = unit;
    json['publication_date'] = publicationDate;
    json['tableFields'] = tableFields?.map((e) => e.toJson()).toList();
    json['isMultiDimension'] = isMultiDimension;
    return json;
  }
}

class IndicatorTools {
  String? id;
  bool? disabled;
  String? label;

  IndicatorTools({
    this.id,
    this.disabled,
    this.label,
  });

  IndicatorTools.fromJson(Map<String, dynamic> json) {
    id = json['id'] as String?;
    disabled = json['disabled'] as bool?;
    label = json['label'] as String?;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['id'] = id;
    json['disabled'] = disabled;
    json['label'] = label;
    return json;
  }
}

class IndicatorFilters {
  String? id;
  List<Options>? options;

  IndicatorFilters({
    this.id,
    this.options,
  });

  IndicatorFilters.fromJson(Map<String, dynamic> json) {
    id = json['id'] as String?;
    options = (json['options'] as List?)
        ?.map((dynamic e) => Options.fromJson(e as Map<String, dynamic>))
        .toList();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['id'] = id;
    json['options'] = options?.map((e) => e.toJson()).toList();
    return json;
  }
}

class Options {
  String? id;
  String? label;
  String? unit;
  int? value;
  bool? isSelected;

  Options({
    this.id,
    this.label,
    this.unit,
    this.value,
    this.isSelected,
  });

  Options.fromJson(Map<String, dynamic> json) {
    id = json['id'] as String?;
    label = json['label'] as String?;
    unit = json['unit'] as String?;
    value = json['value'] as int?;
    isSelected = json['isSelected'] as bool?;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['id'] = id;
    json['label'] = label;
    json['unit'] = unit;
    json['value'] = value;
    json['isSelected'] = isSelected;
    return json;
  }
}

// class IndicatorValues {
//
//   IndicatorValues({});
//
// IndicatorValues.fromJson(Map<String, dynamic> json) {
//
// }
// Map<String, dynamic> toJson() {
// final Map<String, dynamic> json = <String, dynamic>{};
//
// return json;
// }
// }

class IndicatorVisualizations {
  List<VisualizationsMeta>? visualizationsMeta;
  String? visualizationDefault;

  IndicatorVisualizations({
    this.visualizationsMeta,
    this.visualizationDefault,
  });

  IndicatorVisualizations.fromJson(Map<String, dynamic> json) {
    visualizationsMeta = (json['visualizationsMeta'] as List?)
        ?.map((dynamic e) =>
            VisualizationsMeta.fromJson(e as Map<String, dynamic>),)
        .toList();
    visualizationDefault = json['visualizationDefault'] as String?;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['visualizationsMeta'] =
        visualizationsMeta?.map((e) => e.toJson()).toList();
    json['visualizationDefault'] = visualizationDefault;
    return json;
  }
}

class VisualizationsMeta {
  String? id;
  String? type;
  List<SeriesMeta>? seriesMeta;
  String? tooltipTitleFormat;
  String? tooltipValueFormat;
  List<String>? timeUnit;
  String? xAxisFormat;
  String? yAxisFormat;
  String? xAxisLabel;
  String? yAxisLabel;
  String? unit;

  VisualizationsMeta({
    this.id,
    this.type,
    this.seriesMeta,
    this.tooltipTitleFormat,
    this.tooltipValueFormat,
    this.timeUnit,
    this.xAxisFormat,
    this.yAxisFormat,
    this.xAxisLabel,
    this.yAxisLabel,
    this.unit,
  });

  VisualizationsMeta.fromJson(Map<String, dynamic> json) {
    id = json['id'] as String?;
    type = json['type'] as String?;
    seriesMeta = (json['seriesMeta'] as List?)
        ?.map((dynamic e) => SeriesMeta.fromJson(e as Map<String, dynamic>))
        .toList();
    tooltipTitleFormat = json['tooltipTitleFormat'] as String?;
    tooltipValueFormat = json['tooltipValueFormat'] as String?;
    timeUnit =
        (json['timeUnit'] as List?)?.map((dynamic e) => e as String).toList();
    xAxisFormat = json['xAxisFormat'] as String?;
    yAxisFormat = json['yAxisFormat'] as String?;
    xAxisLabel = json['xAxisLabel'] as String?;
    yAxisLabel = json['yAxisLabel'] as String?;
    unit = json['unit'] as String?;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['id'] = id;
    json['type'] = type;
    json['seriesMeta'] = seriesMeta?.map((e) => e.toJson()).toList();
    json['tooltipTitleFormat'] = tooltipTitleFormat;
    json['tooltipValueFormat'] = tooltipValueFormat;
    json['timeUnit'] = timeUnit;
    json['xAxisFormat'] = xAxisFormat;
    json['yAxisFormat'] = yAxisFormat;
    json['xAxisLabel'] = xAxisLabel;
    json['yAxisLabel'] = yAxisLabel;
    json['unit'] = unit;
    return json;
  }
}

class SeriesMeta {
  String? type;
  XAccessor? xAccessor;
  YAccessor? yAccessor;
  List<Map<String, dynamic>>? data;
  String? id;
  String? dbIndicatorId;
  dynamic label;
  String? color;
  dynamic yMax;
  dynamic yMin;
  String? xMin;
  String? xMax;

  SeriesMeta({
    this.type,
    this.xAccessor,
    this.yAccessor,
    this.data,
    this.id,
    this.dbIndicatorId,
    this.label,
    this.color,
    this.yMax,
    this.yMin,
    this.xMin,
    this.xMax,
  });

  SeriesMeta.fromJson(Map<String, dynamic> json) {
    type = json['type'] as String?;
    xAccessor = (json['xAccessor'] as Map<String, dynamic>?) != null
        ? XAccessor.fromJson(json['xAccessor'] as Map<String, dynamic>)
        : null;
    yAccessor = (json['yAccessor'] as Map<String, dynamic>?) != null
        ? YAccessor.fromJson(json['yAccessor'] as Map<String, dynamic>)
        : null;
    data = List<Map<String, dynamic>>.from(json['data']
        as List,); //(json['data'] as List<Map<String, dynamic>>?); //?.map((dynamic e) => SeriesMetaData.fromJson(e as Map<String,dynamic>)).toList();
    id = json['id'] as String?;
    dbIndicatorId = json['dbIndicatorId'] as String?;
    label = json['label'];
    color = json['color'] as String?;
    yMax = json['yMax'];
    yMin = json['yMin'];
    xMin = json['xMin'] as String?;
    xMax = json['xMax'] as String?;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['type'] = type;
    json['xAccessor'] = xAccessor?.toJson();
    json['yAccessor'] = yAccessor?.toJson();
// json['data'] = data?.map((e) => e.toJson()).toList();
    json['id'] = id;
    json['dbIndicatorId'] = dbIndicatorId;
    json['label'] = label;
    json['color'] = color;
    json['yMax'] = yMax;
    json['yMin'] = yMin;
    json['xMin'] = xMin;
    json['xMax'] = xMax;
    return json;
  }
}

class XAccessor {
  String? type;
  String? path;
  String? specifier;

  XAccessor({
    this.type,
    this.path,
    this.specifier,
  });

  XAccessor.fromJson(Map<String, dynamic> json) {
    type = json['type'] as String?;
    path = json['path'] as String?;
    specifier = json['specifier'] as String?;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['type'] = type;
    json['path'] = path;
    json['specifier'] = specifier;
    return json;
  }
}

class YAccessor {
  String? type;
  String? path;

  YAccessor({
    this.type,
    this.path,
  });

  YAccessor.fromJson(Map<String, dynamic> json) {
    type = json['type'] as String?;
    path = json['path'] as String?;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['type'] = type;
    json['path'] = path;
    return json;
  }
}

class SeriesMetaData {
  double? vALUE;
  String? uNIT;
  String? iNDICATORNAME;
  double? mAXVALUE;
  double? mINVALUE;
  String? mAXOBSDT;
  String? mINOBSDT;
  String? oBSDT;
  String? yEAR;
  String? oBSDTLATEST;
  double? vALUELATEST;
  double? mONTHLYCHANGEVALUE;
  double? mONTHLYCOMPAREVALUE;
  double? qUARTERLYCHANGEVALUE;
  double? qUARTERLYCOMPAREVALUE;
  dynamic yEARLYCHANGEVALUE;
  dynamic yEARLYCOMPAREVALUE;
  int? mONTHLY;
  int? qUARTERLY;
  int? yEARLY;
  String? dATASOURCE;
  String? tOPIC;
  String? tHEME;

  SeriesMetaData({
    this.vALUE,
    this.uNIT,
    this.iNDICATORNAME,
    this.mAXVALUE,
    this.mINVALUE,
    this.mAXOBSDT,
    this.mINOBSDT,
    this.oBSDT,
    this.yEAR,
    this.oBSDTLATEST,
    this.vALUELATEST,
    this.mONTHLYCHANGEVALUE,
    this.mONTHLYCOMPAREVALUE,
    this.qUARTERLYCHANGEVALUE,
    this.qUARTERLYCOMPAREVALUE,
    this.yEARLYCHANGEVALUE,
    this.yEARLYCOMPAREVALUE,
    this.mONTHLY,
    this.qUARTERLY,
    this.yEARLY,
    this.dATASOURCE,
    this.tOPIC,
    this.tHEME,
  });

  SeriesMetaData.fromJson(Map<String, dynamic> json) {
    vALUE = json['VALUE'] as double?;
    uNIT = json['UNIT'] as String?;
    iNDICATORNAME = json['INDICATOR_NAME'] as String?;
    mAXVALUE = json['MAX_VALUE'] as double?;
    mINVALUE = json['MIN_VALUE'] as double?;
    mAXOBSDT = json['MAX_OBS_DT'] as String?;
    mINOBSDT = json['MIN_OBS_DT'] as String?;
    oBSDT = json['OBS_DT'] as String?;
    yEAR = json['YEAR'] as String?;
    oBSDTLATEST = json['OBS_DT_LATEST'] as String?;
    vALUELATEST = json['VALUE_LATEST'] as double?;
    mONTHLYCHANGEVALUE = json['MONTHLY_CHANGE_VALUE'] as double?;
    mONTHLYCOMPAREVALUE = json['MONTHLY_COMPARE_VALUE'] as double?;
    qUARTERLYCHANGEVALUE = json['QUARTERLY_CHANGE_VALUE'] as double?;
    qUARTERLYCOMPAREVALUE = json['QUARTERLY_COMPARE_VALUE'] as double?;
    yEARLYCHANGEVALUE = json['YEARLY_CHANGE_VALUE'];
    yEARLYCOMPAREVALUE = json['YEARLY_COMPARE_VALUE'];
    mONTHLY = json['MONTHLY'] as int?;
    qUARTERLY = json['QUARTERLY'] as int?;
    yEARLY = json['YEARLY'] as int?;
    dATASOURCE = json['DATA_SOURCE'] as String?;
    tOPIC = json['TOPIC'] as String?;
    tHEME = json['THEME'] as String?;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['VALUE'] = vALUE;
    json['UNIT'] = uNIT;
    json['INDICATOR_NAME'] = iNDICATORNAME;
    json['MAX_VALUE'] = mAXVALUE;
    json['MIN_VALUE'] = mINVALUE;
    json['MAX_OBS_DT'] = mAXOBSDT;
    json['MIN_OBS_DT'] = mINOBSDT;
    json['OBS_DT'] = oBSDT;
    json['YEAR'] = yEAR;
    json['OBS_DT_LATEST'] = oBSDTLATEST;
    json['VALUE_LATEST'] = vALUELATEST;
    json['MONTHLY_CHANGE_VALUE'] = mONTHLYCHANGEVALUE;
    json['MONTHLY_COMPARE_VALUE'] = mONTHLYCOMPAREVALUE;
    json['QUARTERLY_CHANGE_VALUE'] = qUARTERLYCHANGEVALUE;
    json['QUARTERLY_COMPARE_VALUE'] = qUARTERLYCOMPAREVALUE;
    json['YEARLY_CHANGE_VALUE'] = yEARLYCHANGEVALUE;
    json['YEARLY_COMPARE_VALUE'] = yEARLYCOMPAREVALUE;
    json['MONTHLY'] = mONTHLY;
    json['QUARTERLY'] = qUARTERLY;
    json['YEARLY'] = yEARLY;
    json['DATA_SOURCE'] = dATASOURCE;
    json['TOPIC'] = tOPIC;
    json['THEME'] = tHEME;
    return json;
  }
}

class OverView {
  List<String>? compareFilters;
  String? valueFormat;
  String? templateFormat;
  String? baseDate;
  double? value;
  dynamic yearlyCompareValue;
  dynamic yearlyChangeValue;
  double? quarterlyCompareValue;
  double? quarterlyChangeValue;
  double? monthlyCompareValue;
  double? monthlyChangeValue;

  OverView({
    this.compareFilters,
    this.valueFormat,
    this.templateFormat,
    this.baseDate,
    this.value,
    this.yearlyCompareValue,
    this.yearlyChangeValue,
    this.quarterlyCompareValue,
    this.quarterlyChangeValue,
    this.monthlyCompareValue,
    this.monthlyChangeValue,
  });

  OverView.fromJson(Map<String, dynamic> json) {
    compareFilters = (json['compareFilters'] as List?)
        ?.map((dynamic e) => e as String)
        .toList();
    valueFormat = json['valueFormat'] as String?;
    templateFormat = json['templateFormat'] as String?;
    baseDate = json['baseDate'] as String?;
    value = json['value'] as double?;
    yearlyCompareValue = json['yearlyCompareValue'];
    yearlyChangeValue = json['yearlyChangeValue'];
    quarterlyCompareValue = json['quarterlyCompareValue'] as double?;
    quarterlyChangeValue = json['quarterlyChangeValue'] as double?;
    monthlyCompareValue = json['monthlyCompareValue'] as double?;
    monthlyChangeValue = json['monthlyChangeValue'] as double?;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['compareFilters'] = compareFilters;
    json['valueFormat'] = valueFormat;
    json['templateFormat'] = templateFormat;
    json['baseDate'] = baseDate;
    json['value'] = value;
    json['yearlyCompareValue'] = yearlyCompareValue;
    json['yearlyChangeValue'] = yearlyChangeValue;
    json['quarterlyCompareValue'] = quarterlyCompareValue;
    json['quarterlyChangeValue'] = quarterlyChangeValue;
    json['monthlyCompareValue'] = monthlyCompareValue;
    json['monthlyChangeValue'] = monthlyChangeValue;
    return json;
  }
}

class TableFields {
  String? label;
  String? path;

  TableFields({
    this.label,
    this.path,
  });

  TableFields.fromJson(Map<String, dynamic> json) {
    label = json['label'] as String?;
    path = json['path'] as String?;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['label'] = label;
    json['path'] = path;
    return json;
  }
}
