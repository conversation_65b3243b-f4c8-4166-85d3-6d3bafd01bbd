import 'dart:math' as math;

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/features/home/<USER>/widgets/awesome_bottom_bar/awesome_bottom_bar.dart';
import 'package:scad_mobile/src/features/home/<USER>/widgets/awesome_bottom_bar/widgets/build_icon.dart';
import 'package:scad_mobile/src/features/home/<USER>/widgets/awesome_bottom_bar/widgets/inspired/painter.dart';
import 'package:scad_mobile/src/features/home/<USER>/widgets/awesome_bottom_bar/widgets/inspired/stack.dart'
    as extend;
import 'package:scad_mobile/src/features/home/<USER>/widgets/awesome_bottom_bar/widgets/inspired/transition_container.dart';

// /// Default size of the curve line.
// const double converSize = 70;
//
// enum ItemStyle { circle }
//
// class Inspired extends StatefulWidget {
//   const Inspired({
//     required this.background,
//     required this.items,
//     required this.color,
//     required this.colorSelected,
//     super.key,
//     this.fixed = false,
//     this.height = 40,
//     this.initialActive,
//     this.curve = Curves.easeInOut,
//     this.cornerRadius,
//     this.onTap,
//     this.chipStyle,
//     this.elevation,
//     this.top = -18,
//     this.curveSize,
//     this.containerSize,
//     this.itemStyle = ItemStyle.circle,
//     this.animated = true,
//     this.isAnimated = true,
//     this.shadowColor,
//     this.padTop = 12,
//     this.padbottom = 12,
//     this.pad = 4,
//     this.radius = 0,
//     this.fixedIndex = 0,
//     this.iconSize = 22,
//     this.countStyle,
//     this.titleStyle,
//     this.sizeInside = 48,
//     this.duration,
//     this.animateStyle = 'flip',
//   });
//   final bool fixed;
//   final double height;
//   final Color background;
//   final int? initialActive;
//   final Curve curve;
//   final double? cornerRadius;
//   final void Function(int index)? onTap;
//   final ChipStyle? chipStyle;
//   final double? elevation;
//   final double? top;
//   final double? curveSize;
//   final double? containerSize;
//   final ItemStyle? itemStyle;
//   final bool animated;
//   final bool isAnimated;
//   final Color? shadowColor;
//   final double? padTop;
//   final double? padbottom;
//   final double? pad;
//   final double? radius;
//   final int? fixedIndex;
//   final Color color;
//   final Color colorSelected;
//   final double iconSize;
//   final CountStyle? countStyle;
//   final TextStyle? titleStyle;
//   final double? sizeInside;
//   final Duration? duration;
//   final String animateStyle;
//   final List<TabItem<dynamic>> items;
//
//   @override
//   _InspiredState createState() => _InspiredState();
// }
//
// class _InspiredState extends State<Inspired> with TickerProviderStateMixin {
//   int? _currentIndex;
//   Animation<double>? _animation;
//   AnimationController? _animationController;
//   TabController? _controller;
//
//   static const _transitionDuration = 100;
//
//   int count = 5;
//
//   @override
//   void initState() {
//     count = widget.items.length;
//     if (widget.cornerRadius != null &&
//         widget.cornerRadius! > 0 &&
//         !widget.fixed) {
//       throw FlutterError.fromParts(<DiagnosticsNode>[
//         ErrorSummary('ConvexAppBar is configured with cornerRadius'),
//         ErrorDescription(
//             'Currently the corner only work for fixed style, if you are using '
//             'other styles, the convex shape can be broken on the first and '
//             'last tab item '),
//         ErrorHint(
//             'You should use TabStyle.fixed or TabStyle.fixedCircle to make the'
//             ' background display with topLeft/topRight corner'),
//       ]);
//     }
//     _resetState();
//     super.initState();
//   }
//
//   /// change active tab index; can be used with [PageView].
//   Future<void> animateTo(int index, {int? from}) async {
//     _updateAnimation(
//       from: from ?? _currentIndex,
//       to: index,
//       duration: widget.animated == true
//           ? const Duration(milliseconds: _transitionDuration)
//           : Duration.zero,
//     );
//     // ignore: unawaited_futures
//     _animationController?.forward();
//     if (mounted) {
//       setState(() {
//         _currentIndex = index;
//       });
//     }
//   }
//
//   Animation<double> _updateAnimation({
//     int? from,
//     int? to,
//     Duration duration = const Duration(milliseconds: _transitionDuration),
//   }) {
//     if (from != null && (from == to) && _animation != null) {
//       return _animation!;
//     }
//     from ??= widget.fixed
//         ? widget.fixedIndex
//         : _controller?.index ?? widget.initialActive ?? 0;
//     to ??= from;
//     final lower = (2 * from! + 1) / (2 * count);
//     final upper = (2 * to! + 1) / (2 * count);
//     _animationController?.dispose();
//     final controller = AnimationController(duration: duration, vsync: this);
//     final curve = CurvedAnimation(
//       parent: controller,
//       curve: widget.curve,
//     );
//     _animationController = controller;
//     return _animation = Tween(begin: lower, end: upper).animate(curve);
//   }
//
//   @override
//   void dispose() {
//     _controller = null;
//
//     _animationController?.dispose();
//     super.dispose();
//   }
//
//   void _resetState() {
//     final index = _controller?.index ?? widget.initialActive;
//     // when both initialActiveIndex and controller are not configured
//     _currentIndex = index ?? 0;
//
//     if (!widget.fixed && _controller != null) {
//       // when controller is not defined, the default index can rollback to 0
//       // https://github.com/hacktons/convex_bottom_bar/issues/67
//       _updateAnimation();
//     }
//   }
//
//   void _onTabClick(int i) {
//     animateTo(i);
//     _controller?.animateTo(i);
//     widget.onTap?.call(i);
//   }
//
//   @override
//   void didUpdateWidget(Inspired oldWidget) {
//     super.didUpdateWidget(oldWidget);
//     if (widget.animated == oldWidget.animated) {
//       _resetState();
//     }
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     // take care of iPhoneX' safe area at bottom edge
//     final double additionalBottomPadding =
//         math.max(MediaQuery.of(context).padding.bottom, 0);
//
//     final int convexIndex =
//         widget.fixed ? (widget.fixedIndex ?? (count ~/ 2)) : _currentIndex ?? 0;
//
//     final bool active = widget.fixed ? convexIndex == _currentIndex : true;
//
//     final double height = widget.height +
//         additionalBottomPadding +
//         widget.pad! +
//         widget.padTop! +
//         widget.padbottom!;
//     final double width = MediaQuery.of(context).size.width;
//
//     final Animation<double> percent = widget.fixed
//         ? _updateAnimation()
//         : widget.isAnimated == true
//             ? _updateAnimation()
//             : _updateAnimation();
//     final double factor = 1 / count;
//     final TextDirection textDirection = Directionality.of(context);
//     double dx = convexIndex / (count - 1);
//     if (textDirection == TextDirection.rtl) {
//       dx = 1 - dx;
//     }
//
//     final bool isHexagon = widget.chipStyle?.isHexagon ?? false;
//
//     final bool drawHexagon = widget.chipStyle?.drawHexagon ?? false;
//
//     final bool convexBridge = widget.chipStyle?.convexBridge ?? false;
//
//     final NotchSmoothness notchSmoothness =
//         widget.chipStyle?.notchSmoothness ?? NotchSmoothness.defaultEdge;
//
//     final offset = FractionalOffset(count > 1 ? dx : 0.0, 0);
//
//     return extend.Stack(
//       clipBehavior: Clip.none,
//       alignment: Alignment.bottomCenter,
//       children: <Widget>[
//         SizedBox(
//           height: height,
//           width: width,
//           child: CustomPaint(
//             painter: ConvexPainter(
//               top: drawHexagon || !convexBridge ? -38 : -22,
//               width: widget.curveSize ?? converSize,
//               height: 81,
//               color: widget.background,
//               shadowColor: widget.shadowColor ?? Colors.black26,
//               sigma: widget.elevation ?? 2,
//               leftPercent: percent,
//               textDirection: textDirection,
//               isHexagon: isHexagon,
//               drawHexagon: drawHexagon,
//               notchSmoothness: notchSmoothness,
//               convexBridge: convexBridge,
//               leftCornerRadius: widget.fixed && widget.fixedIndex == 0
//                   ? 0
//                   : (widget.initialActive == 0 && !widget.fixed
//                       ? 0
//                       : widget.radius!),
//               rightCornerRadius: widget.fixed && widget.fixedIndex == count - 1
//                   ? 0
//                   : (widget.initialActive == count - 1 && !widget.fixed
//                       ? 0
//                       : widget.radius!),
//             ),
//           ),
//         ),
//         _barContent(height, additionalBottomPadding, convexIndex),
//         Positioned.fill(
//           top: widget.top! - widget.pad! - widget.padTop! - widget.padbottom!,
//           bottom: additionalBottomPadding,
//           child: FractionallySizedBox(
//             widthFactor: factor,
//             alignment: offset,
//             child: GestureDetector(
//               key: ValueKey(widget.items[convexIndex].key ?? ''),
//               onTap: () => _onTabClick(convexIndex),
//               child: buildItem(
//                 context,
//                 item: widget.items[convexIndex],
//                 index: convexIndex,
//                 active: active,
//               ),
//             ),
//           ),
//         ),
//       ],
//     );
//   }
//
//   Widget _barContent(double height, double paddingBottom, int curveTabIndex) {
//     final children = <Widget>[];
//     for (var i = 0; i < count; i++) {
//       final String value = widget.items[i].key ?? '';
//       if (i == curveTabIndex) {
//         children.add(Expanded(child: Container()));
//         continue;
//       }
//       final active = _currentIndex == i;
//
//       children.add(
//         Expanded(
//           child: GestureDetector(
//             key: ValueKey(value),
//             behavior: HitTestBehavior.opaque,
//             onTap: () => _onTabClick(i),
//             child: buildItem(
//               context,
//               item: widget.items[i],
//               index: i,
//               active: active,
//             ),
//           ),
//         ),
//       );
//     }
//     return Container(
//       height: height,
//       padding: EdgeInsets.only(bottom: paddingBottom),
//       child: Row(
//         children: children,
//       ),
//     );
//   }
//
//   Widget buildItem(BuildContext context,
//       {required TabItem item, required int index, bool active = false}) {
//     final bool isLightMode =
//         HiveUtilsSettings.isLightMode;
//     Color itemColor() {
//       if (widget.fixed) {
//         return active ? widget.chipStyle!.background! : widget.color;
//       }
//       return active ? widget.colorSelected : widget.color;
//     }
//
//     if (widget.fixed ? widget.fixedIndex == index : active) {
//       if (widget.animated) {
//         if (widget.animateStyle == 'flip') {
//           return TransitionContainer.flip(
//             data: index,
//             duration: widget.duration ?? const Duration(milliseconds: 350),
//             height: 80,
//             curve: widget.curve,
//             bottomChild: buildContentItem(
//               item,
//               itemColor(),
//               widget.iconSize,
//               widget.sizeInside!,
//               active,
//             ),
//           );
//         } else {
//           return TransitionContainer.scale(
//             data: index,
//             duration: widget.duration ?? const Duration(milliseconds: 350),
//             curve: widget.curve,
//             child: buildContentItem(
//               item,
//               itemColor(),
//               widget.iconSize,
//               widget.sizeInside!,
//               active,
//             ),
//           );
//         }
//       }
//       return buildContentItem(
//         item,
//         itemColor(),
//         widget.iconSize,
//         widget.sizeInside!,
//         active,
//       );
//     }
//     return Container(
//       padding: EdgeInsets.only(bottom: widget.padbottom!, top: widget.padTop!),
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           BuildIcon(
//             item: item,
//             iconColor: itemColor(),
//             iconSize: widget.iconSize,
//             countStyle: widget.countStyle,
//             active: active,
//           ),
//           if (item.title is String && item.title != '') ...[
//             SizedBox(height: widget.pad),
//             Expanded(
//               child: Text(
//                 item.title!,
//                 style: Theme.of(context)
//                     .textTheme
//                     .labelMedium
//                     ?.merge(widget.titleStyle)
//                     .copyWith(
//                       color: isLightMode ? itemColor() : AppColors.greyShade4,
//                       letterSpacing: 0,
//                     ),
//                 maxLines: 1,
//                 overflow: TextOverflow.ellipsis,
//                 textAlign: TextAlign.center,
//                 textScaler: TextScaler.linear(textScaleFactor.value),
//               ),
//             ),
//           ],
//         ],
//       ),
//     );
//   }
//
//   Widget buildContentItem(
//     TabItem item,
//     Color itemColor,
//     double iconSize,
//     double sizeInside,
//     bool active,
//   ) {
//     final bool isLightMode =
//         HiveUtilsSettings.isLightMode;
//     return Column(
//       mainAxisAlignment: MainAxisAlignment.center,
//       children: [
//         if (widget.itemStyle == ItemStyle.circle)
//           Column(
//             children: [
//               const SizedBox(height: 20),
//               Container(
//                 decoration: BoxDecoration(
//                   border: isLightMode
//                       ? Border.all(color: AppColors.white, width: 2)
//                       : null,
//                   shape: BoxShape.circle,
//                   boxShadow: [
//                     BoxShadow(
//                       color: AppColors.blue.withOpacity(.22),
//                       blurRadius: 7,
//                       offset: const Offset(0, 2),
//                     ),
//                   ],
//                 ),
//                 child: Container(
//                   width: sizeInside,
//                   height: sizeInside,
//                   decoration: BoxDecoration(
//                     color: widget.chipStyle?.background,
//                     gradient: LinearGradient(
//                       colors: [
//                         AppColors.blueShade11,
//                         AppColors.blueShade12,
//                       ],
//                       begin: Alignment.topRight,
//                       end: Alignment.bottomLeft,
//                     ),
//                     // shape: BoxShape.circle,
//                     borderRadius: BorderRadius.circular(100),
//                   ),
//                   alignment: Alignment.center,
//                   child: BuildIcon(
//                     item: item,
//                     iconColor: widget.fixed ? widget.colorSelected : itemColor,
//                     iconSize: iconSize,
//                     countStyle: widget.countStyle,
//                     active: active,
//                   ),
//                 ),
//               ),
//               if (item.title is String && item.title != '') ...[
//                 SizedBox(height: widget.pad),
//                 Text(
//                   item.title!,
//                   style: Theme.of(context)
//                       .textTheme
//                       .labelMedium
//                       ?.merge(widget.titleStyle)
//                       .copyWith(
//                         color: itemColor,
//                         letterSpacing: 0,
//                         fontSize: 11,
//                       ),
//                   textAlign: TextAlign.center,
//                   overflow: TextOverflow.ellipsis,
//                   maxLines: 1,
//                   textScaler: TextScaler.linear(textScaleFactor.value),
//                 ),
//               ],
//             ],
//           ),
//       ],
//     );
//   }
// }
//
// enum NotchSmoothness {
//   sharpEdge,
//   defaultEdge,
//   softEdge,
//   smoothEdge,
//   verySmoothEdge,
// }

/// Default size of the curve line.
const double converSize = 70;

const double curveHeight = 90;

enum ItemStyle { hexagon, circle }

class Inspired extends StatefulWidget {
  final bool fixed;
  final double height;
  final Color background;
  final int? initialActive;
  final Curve curve;
  final double? cornerRadius;
  final void Function(int index)? onTap;
  final ChipStyle? chipStyle;
  final double? elevation;
  final double? top;
  final double? curveSize;
  final double? containerSize;
  final ItemStyle? itemStyle;
  final bool animated;
  final bool isAnimated;
  final Color? shadowColor;
  final double? padTop;
  final double? padbottom;
  final double? pad;
  final double? radius;
  final int? fixedIndex;
  final Color color;
  final Color colorSelected;
  final double iconSize;
  final CountStyle? countStyle;
  final TextStyle? titleStyle;
  final double? sizeInside;
  final Duration? duration;
  final String animateStyle;
  final List<TabItem> items;

  const Inspired({
    Key? key,
    required this.background,
    required this.items,
    required this.color,
    required this.colorSelected,
    this.fixed = false,
    this.height = 40,
    this.initialActive,
    this.curve = Curves.easeInOut,
    this.cornerRadius,
    this.onTap,
    this.chipStyle,
    this.elevation,
    this.top = -18,
    this.curveSize,
    this.containerSize,
    this.itemStyle = ItemStyle.circle,
    this.animated = true,
    this.isAnimated = true,
    this.shadowColor,
    this.padTop = 12,
    this.padbottom = 12,
    this.pad = 4,
    this.radius = 0,
    this.fixedIndex = 0,
    this.iconSize = 22,
    this.countStyle,
    this.titleStyle,
    this.sizeInside = 48,
    this.duration,
    this.animateStyle = 'flip',
  }) : super(key: key);

  @override
  _InspiredState createState() => _InspiredState();
}

class _InspiredState extends State<Inspired> with TickerProviderStateMixin {
  int? _currentIndex;
  Animation<double>? _animation;
  AnimationController? _animationController;
  TabController? _controller;

  static const _transitionDuration = 100;

  int count = 5;

  @override
  void initState() {
    count = widget.items.length;
    if (widget.cornerRadius != null && widget.cornerRadius! > 0 && !widget.fixed) {
      throw FlutterError.fromParts(<DiagnosticsNode>[
        ErrorSummary('ConvexAppBar is configured with cornerRadius'),
        ErrorDescription('Currently the corner only work for fixed style, if you are using '
            'other styles, the convex shape can be broken on the first and last tab item '),
        ErrorHint('You should use TabStyle.fixed or TabStyle.fixedCircle to make the'
            ' background display with topLeft/topRight corner'),
      ]);
    }
    _resetState();
    super.initState();
  }

  /// change active tab index; can be used with [PageView].
  Future<void> animateTo(int index, {int? from}) async {
    _updateAnimation(
      from: from ?? _currentIndex,
      to: index,
      duration: widget.animated == true ? const Duration(milliseconds: _transitionDuration) : const Duration(),
    );
    // ignore: unawaited_futures
    _animationController?.forward();
    if (mounted) {
      setState(() {
        _currentIndex = index;
      });
    }
  }

  Animation<double> _updateAnimation({
    int? from,
    int? to,
    Duration duration = const Duration(milliseconds: _transitionDuration),
  }) {
    if (from != null && (from == to) && _animation != null) {
      return _animation!;
    }
    from ??= widget.fixed ? widget.fixedIndex : _controller?.index ?? widget.initialActive ?? 0;
    to ??= from;
    final lower = (2 * from! + 1) / (2 * count);
    final upper = (2 * to! + 1) / (2 * count);
    _animationController?.dispose();
    final controller = AnimationController(duration: duration, vsync: this);
    final curve = CurvedAnimation(
      parent: controller,
      curve: widget.curve,
    );
    _animationController = controller;
    return _animation = Tween(begin: lower, end: upper).animate(curve);
  }

  @override
  void dispose() {
    _controller = null;

    _animationController?.dispose();
    super.dispose();
  }

  void _resetState() {
    final index = _controller?.index ?? widget.initialActive;
    // when both initialActiveIndex and controller are not configured
    _currentIndex = index ?? 0;

    if (!widget.fixed && _controller != null) {
      // when controller is not defined, the default index can rollback to 0
      // https://github.com/hacktons/convex_bottom_bar/issues/67
      _updateAnimation();
    }
  }

  void _onTabClick(int i) {
    animateTo(i);
    _controller?.animateTo(i);
    widget.onTap?.call(i);
  }

  @override
  void didUpdateWidget(Inspired oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.animated == oldWidget.animated) {
      _resetState();
    }
  }

  @override
  Widget build(BuildContext context) {
    // take care of iPhoneX' safe area at bottom edge
    final double additionalBottomPadding = math.max(MediaQuery.of(context).padding.bottom, 0.0);

    final int convexIndex = widget.fixed ? (widget.fixedIndex ?? (count ~/ 2)) : _currentIndex ?? 0;

    final bool active = widget.fixed ? convexIndex == _currentIndex : true;

    final double height = widget.height + additionalBottomPadding + widget.pad! + widget.padTop! + widget.padbottom!;
    final double width = MediaQuery.of(context).size.width;

    final Animation<double> percent = widget.fixed
        ? _updateAnimation()
        : widget.isAnimated == true
            ? _updateAnimation()
            : _updateAnimation();
    final double factor = 1 / count;
    final TextDirection textDirection = Directionality.of(context);
    double dx = convexIndex / (count - 1);
    if (textDirection == TextDirection.rtl) {
      dx = 1 - dx;
    }

    final bool isHexagon = widget.chipStyle?.isHexagon ?? false;

    final bool drawHexagon = widget.chipStyle?.drawHexagon ?? false;

    final bool convexBridge = widget.chipStyle?.convexBridge ?? false;

    final NotchSmoothness notchSmoothness = widget.chipStyle?.notchSmoothness ?? NotchSmoothness.defaultEdge;

    final offset = FractionalOffset(count > 1 ? dx : 0.0, 0);

    return extend.Stack(
      clipBehavior: Clip.none,
      alignment: Alignment.bottomCenter,
      children: <Widget>[
        SizedBox(
          height: height,
          width: width,
          child: CustomPaint(
            painter: ConvexPainter(
              top: drawHexagon || !convexBridge ? -38 : -22,
              width: widget.curveSize ?? converSize,
              height: curveHeight,
              color: widget.background,
              shadowColor: widget.shadowColor ?? const Color.fromRGBO(0, 0, 0, 0.06),
              sigma: widget.elevation ?? 2,
              leftPercent: percent,
              textDirection: textDirection,
              isHexagon: isHexagon,
              drawHexagon: drawHexagon,
              notchSmoothness: notchSmoothness,
              convexBridge: convexBridge,
              leftCornerRadius: widget.fixed && widget.fixedIndex == 0
                  ? 0
                  : (widget.initialActive == 0 && !widget.fixed ? 0 : widget.radius!),
              rightCornerRadius: widget.fixed && widget.fixedIndex == count - 1
                  ? 0
                  : (widget.initialActive == count - 1 && !widget.fixed ? 0 : widget.radius!),
            ),
          ),
        ),
        _barContent(height, additionalBottomPadding, convexIndex),
        Positioned.fill(
          top: widget.top! - widget.pad! - widget.padTop! - widget.padbottom!,
          bottom: additionalBottomPadding,
          child: FractionallySizedBox(
            widthFactor: factor,
            alignment: offset,
            child: GestureDetector(
              key: ValueKey(widget.items[convexIndex].key ?? ''),
              behavior: HitTestBehavior.opaque,
              onTap: () => _onTabClick(convexIndex),
              child: buildItem(
                context,
                item: widget.items[convexIndex],
                index: convexIndex,
                active: active,
                paddingBottom: additionalBottomPadding,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _barContent(double height, double paddingBottom, int curveTabIndex) {
    final children = <Widget>[];
    for (var i = 0; i < count; i++) {
      final String value = widget.items[i].key ?? '';
      if (i == curveTabIndex) {
        children.add(Expanded(child: Container()));
        continue;
      }
      final active = _currentIndex == i;

      children.add(
        Expanded(
          child: GestureDetector(
            key: ValueKey(value),
            behavior: HitTestBehavior.opaque,
            onTap: () => _onTabClick(i),
            child: buildItem(
              context,
              item: widget.items[i],
              index: i,
              active: active,
              paddingBottom: paddingBottom,
            ),
          ),
        ),
      );
    }
    return Container(
      height: height,
      padding: EdgeInsets.only(bottom: paddingBottom),
      child: Row(children: children),
    );
  }

  Widget buildItem(
    BuildContext context, {
    required TabItem item,
    required int index,
    bool active = false,
    required double paddingBottom,
  }) {
    Color itemColor() {
      if (widget.fixed) {
        return active ? widget.chipStyle!.background! : widget.color;
      }
      return active ? widget.colorSelected : widget.color;
    }

    if (widget.fixed ? widget.fixedIndex == index : active) {
      if (widget.animated) {
        if (widget.animateStyle == 'flip') {
          return TransitionContainer.flip(
            data: index,
            duration: widget.duration ?? const Duration(milliseconds: 350),
            height: 80,
            curve: widget.curve,
            bottomChild: buildContentItem(
              item,
              itemColor(),
              widget.iconSize,
              widget.sizeInside!,
            ),
          );
        } else {
          return TransitionContainer.scale(
            data: index,
            duration: widget.duration ?? const Duration(milliseconds: 350),
            curve: widget.curve,
            child: buildContentItem(
              item,
              itemColor(),
              widget.iconSize,
              widget.sizeInside!,
            ),
          );
        }
      }
      return buildContentItem(
        item,
        itemColor(),
        widget.iconSize,
        widget.sizeInside!,
      );
    }
    return Container(
      padding: EdgeInsets.only(bottom: widget.padbottom!, top: widget.padTop!),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          BuildIcon(
            active: false,
            item: item,
            iconColor: itemColor(),
            iconSize: widget.iconSize,
            countStyle: widget.countStyle,
          ),
          if (item.title is String && item.title != '') ...[
            SizedBox(height: widget.pad),
            Text(
              item.title!,
              style: Theme.of(context).textTheme.labelSmall?.merge(widget.titleStyle).copyWith(
                    color: itemColor(),
                    letterSpacing: -0.2,
                  ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  Widget buildContentItem(TabItem item, Color itemColor, double iconSize, double sizeInside,
      {double paddingBottom = 10}) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        if (widget.itemStyle == ItemStyle.circle)
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Container(
              width: sizeInside,
              height: sizeInside,
              decoration: BoxDecoration(color: widget.chipStyle?.background, shape: BoxShape.circle),
              alignment: Alignment.center,
              child: BuildIcon(
                active: true,
                item: item,
                iconColor: widget.fixed ? widget.colorSelected : itemColor,
                iconSize: iconSize + 4,
                countStyle: widget.countStyle,
              ),
            ),
          ),
        if (item.title is String && item.title != '') ...[
          SizedBox(height: widget.pad ?? 0),
          Text(
            item.title!,
            style: Theme.of(context).textTheme.labelSmall?.merge(widget.titleStyle).copyWith(
                  color: itemColor,
                  letterSpacing: -0.2,
                ),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
            textAlign: TextAlign.center,
          ),
          SizedBox(height: paddingBottom),
        ],
      ],
    );
  }
}

enum NotchSmoothness {
  sharpEdge,
  defaultEdge,
  softEdge,
  smoothEdge,
  verySmoothEdge,
}
