import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';

import '../tab_item.dart';
import '../count_style.dart';

class BuildIcon extends StatelessWidget {
  final TabItem item;
  final double iconSize;
  final Color iconColor;
  final CountStyle? countStyle;
  final bool active;

  const BuildIcon({
    required this.active,
    required this.item,
    required this.iconColor,
    Key? key,
    this.iconSize = 22,
    this.countStyle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final Widget icon = SizedBox(
      width: iconSize,
      height: iconSize,
      child: SvgPicture.asset(
        item.icon,
        colorFilter: ColorFilter.mode(
          active ? AppColors.white : iconColor,
          BlendMode.srcIn,
        ),
      ),
    );
    if (item.count is Widget) {
      double sizeBadge = countStyle?.size ?? 18;

      return Stack(
        clipBehavior: Clip.none,
        children: [
          SizedBox(
            width: iconSize,
            height: iconSize,
            child: SvgPicture.asset(
              item.icon,
              colorFilter: ColorFilter.mode(
                active ? AppColors.white : iconColor,
                BlendMode.srcIn,
              ),
            ),
          ),
          PositionedDirectional(
            start: iconSize - sizeBadge / 2,
            top: -sizeBadge / 2,
            child: item.count!,
          ),
        ],
      );
    }
    return icon;
  }
}
