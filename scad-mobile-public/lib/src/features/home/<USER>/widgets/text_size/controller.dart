//
// import 'package:flutter/material.dart';
//
// class TextSizeValueController extends ValueNotifier<TextSizeValue> {
//   TextSizeValueController([TextSizeValue? value])
//       : super(value ?? TextSizeValue.medium());
//
//   void setSmall() {
//     value = TextSizeValue.small();
//     notifyListeners();
//   }
//   void setMedium() {
//     value = TextSizeValue.medium();
//     notifyListeners();
//   }
//   void setLarge() {
//     value = TextSizeValue.large();
//     notifyListeners();
//   }
//
// }
//
//
// class TextSizeValue {
//
//     TextSizeValue({this.textScaleFactor = 1});
//
//   factory TextSizeValue.medium() {
//     return   TextSizeValue.medium(this.textScaleFactor = 1);
//   }
//
//   factory TextSizeValue.small() {
//     return   TextSizeValue();
//   }
//
//   factory TextSizeValue.large() {
//     return   TextSizeValue();
//   }
//
//   final double textScaleFactor;
//
//   const TextSizeValue({
//     this.textScaleFactor = 1,
//   });
//
//   factory AdvancedDrawerValue.hidden() {
//     return const AdvancedDrawerValue();
//   }
// }
