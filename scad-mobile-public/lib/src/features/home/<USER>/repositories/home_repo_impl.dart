import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:scad_mobile/src/common/models/response_models/api_response.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/features/home/<USER>/data_sources/api_end_points.dart';
import 'package:scad_mobile/src/features/home/<USER>/models/key_indicator_list/key_indicator_list_response.dart';
import 'package:scad_mobile/src/features/home/<USER>/models/recommended_indicator_list_item/recommended_indicator_list_item.dart';
import 'package:scad_mobile/src/features/home/<USER>/repositories/home_repository_imports.dart';
import 'package:scad_mobile/src/services/http_service_impl.dart';
import 'package:scad_mobile/src/services/http_services.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class HomeRepositoryImpl implements HomeRepository {
  HomeRepositoryImpl() {
    _httpService = HttpServiceRequests();
  }

  late HttpService _httpService;

  /// get key indicator list from app server
  @override
  Future<RepoResponse<List<KeyIndicatorListResponseItem>>>
      keyIndicatorList() async {
    try {
      final String endpoint = HomeEndPoints.keyIndicatorList;
      final ApiResponse response = await _httpService.get(endpoint);

      if (response.isSuccess) {
        final List<KeyIndicatorListResponseItem> list =
            (response.response['data'] as List<dynamic>)
                .map((e) => KeyIndicatorListResponseItem.fromJson(
                    e as Map<String, dynamic>))
                .toList();
        return RepoResponse<List<KeyIndicatorListResponseItem>>.success(
          response: list,
        );
      } else {
        return RepoResponse<List<KeyIndicatorListResponseItem>>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<List<KeyIndicatorListResponseItem>>.error(
        errorMessage:  LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<List<RecommendedIndicatorListResponseItem>>>
      recommendedIndicatorList(List<String> domainNameList) async {
    try {
      final String endpoint = HomeEndPoints.recommendedIndicatorList;
      final ApiResponse response = await _httpService.postJson(
        endpoint,
        jsonPayloadList: domainNameList,
      );

      if (response.isSuccess) {
        final List<RecommendedIndicatorListResponseItem> list =
            (response.response['data'] as List<dynamic>)
                .map((e) => RecommendedIndicatorListResponseItem.fromJson(
                    e as Map<String, dynamic>))
                .toList();
        return RepoResponse<List<RecommendedIndicatorListResponseItem>>.success(
          response: list,
        );
      } else {
        return RepoResponse<List<RecommendedIndicatorListResponseItem>>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<List<RecommendedIndicatorListResponseItem>>.error(
        errorMessage:  LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

}
