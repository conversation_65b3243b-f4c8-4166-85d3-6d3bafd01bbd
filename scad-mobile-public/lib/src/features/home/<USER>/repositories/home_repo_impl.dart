import 'dart:async';

import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/common/types.dart';
import 'package:scad_mobile/src/features/home/<USER>/data_sources/api_end_points.dart';
import 'package:scad_mobile/src/features/home/<USER>/models/key_indicator_list/key_indicator_list_response.dart';
import 'package:scad_mobile/src/features/home/<USER>/repositories/home_repository_imports.dart';
import 'package:scad_mobile/src/services/http_service_impl.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_keys.dart';

class HomeRepositoryImpl extends HomeRepository {
  final _httpService = HttpServiceRequests();

  @override
  Future<RepoResponse<List<KeyIndicatorListResponseItem>>> keyIndicatorList() async {
    final cacheKey = getStaticCacheKey(HiveKeys.keyIndicatorList);
    return fetchWithCache(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.get(
        HomeEndPoints.keyIndicatorList,
      ),
      parseResult: (json) {
        final data = json['data'] as List<dynamic>;
        return data
            .map(
              (e) => KeyIndicatorListResponseItem.fromJson(
                e as JSONObject,
              ),
            )
            .toList();
      },
    );
  }
}
