import 'package:auto_route/auto_route.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/presentation/pages/indicator_card_v2.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/intro_widget.dart';
import 'package:scad_mobile/src/common/widgets/no_data_placeholder.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/showcaseview.dart';
import 'package:scad_mobile/src/features/domains/data/models/domain_model/domain_model.dart';
import 'package:scad_mobile/src/features/domains/presentation/bloc/domains_bloc.dart';
import 'package:scad_mobile/src/features/home/<USER>/models/indicator_tab.dart';
import 'package:scad_mobile/src/features/home/<USER>/models/key_indicator_list/key_indicator_list_response.dart';
import 'package:scad_mobile/src/features/home/<USER>/bloc/home_bloc/home_bloc.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

@RoutePage()
class HomePage extends StatefulWidget {
  const HomePage({
    required this.navToDomainsPage,
    required this.navToProductsPage,
    super.key,
  });

  final VoidCallback navToDomainsPage;
  final void Function(String type) navToProductsPage;

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage>
    with AutomaticKeepAliveClientMixin {
  ValueNotifier<int> currentTabIndex = ValueNotifier(0);
  final CarouselController _carouselController = CarouselController();

  final List<IndicatorTab> tabItems = [
    IndicatorTab(
      title: LocaleKeys.tableauDashboards.tr(),
      icon: AppImages.icDashboard,
      color: AppColors.blueShade23,
      productType: 'td',
    ),
    IndicatorTab(
      title: LocaleKeys.publications.tr(),
      icon: AppImages.icPublications,
      color: AppColors.blueShade25,
      productType: 'pb',
    ),
    IndicatorTab(
      title: LocaleKeys.webReports.tr(),
      icon: AppImages.icWebReports,
      color: AppColors.blueShade25,
      productType: 'wr',
    ),
  ];

  final GlobalKey step1 = GlobalKey();
  final GlobalKey step4 = GlobalKey();
  List<GlobalKey> steps = [];

  List<KeyIndicatorListResponseItem> keyIndicatorList = [];

  num indicatorsRefreshedAt = 0;
  List<DomainModel> domainList = [];

  bool isPreviousFromDetailsScreen = false;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    steps = [step1, step4];
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (mounted) {
        await _loadIndicators();
        if (HiveUtilsSettings.getUserGuideStatus() == UserGuides.Home) {
          ShowCaseWidget.of(context).startShowCase(steps);
        }
      }

      // if (widget.homeCarouselInitialIndex != null) {
      //  Future.delayed(const Duration(milliseconds: 300,),(){
      //    _carouselController.animateToPage(widget.homeCarouselInitialIndex!);
      //  currentTabIndex.value=widget.homeCarouselInitialIndex!;});
      // }
    });
  }

  @override
  void dispose() {
    super.dispose();

    // ShowCaseWidget.of(context).dispose();
  }

  Future<void> _loadIndicators() async {
    keyIndicatorList = [];

    context.read<DomainsBloc>().add(const DomainsInitEvent());
    context.read<HomeBloc>().add(const KeyIndicatorsEvent());
    indicatorsRefreshedAt = DateTime.now().microsecondsSinceEpoch;
  }

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    super.build(context);

    return Stack(
      alignment: Alignment.topCenter,
      children: [
        // Image.asset(
        //   AppImages.animatedBg,
        //   height: MediaQuery.sizeOf(context).height * 0.3,
        //   width: MediaQuery.sizeOf(context).width,
        //   fit: BoxFit.cover,
        // ),
        Container(
          height: MediaQuery.sizeOf(context).height * 0.3,
          width: MediaQuery.sizeOf(context).width,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                AppColors.blueGradientShade1,
                AppColors.blueGradientShade2,
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            BlocListener<DomainsBloc, DomainsState>(
              listener: (context, state) {
                if (state is DomainShowResponseState) {
                  domainList = state.list;
                }
              },
              child: const SizedBox(),
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 20),
              child: Column(
                children: [
                  SizedBox(
                    height: MediaQuery.paddingOf(context).top,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 10,
                      vertical: 10,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        IntroWidget(
                          stepKey: step1,
                          stepIndex: 1,
                          totalSteps: 7,
                          title: LocaleKeys.hamburgerMenu.tr(),
                          description: LocaleKeys.hamburgerMenuDesc.tr(),
                          arrowAlignment: Alignment.bottomLeft,
                          arrowPadding: const EdgeInsets.only(
                            left: 14,
                          ),
                          child: appDrawerController.drawerButton(
                            isArabic:
                                HiveUtilsSettings.getAppLanguage() == 'ar',
                          ),
                        ),
                        Expanded(
                          child: GestureDetector(
                            onTap: () {
                              context.router.removeWhere(
                                (route) => route.name == SearchScreenRoute.name,
                              );
                              context.pushRoute(SearchScreenRoute());
                            },
                            child: Hero(
                              tag: 'search_box',
                              child: Material(
                                type: MaterialType.transparency,
                                child: Container(
                                  margin: const EdgeInsets.symmetric(
                                    horizontal: 10,
                                  ),
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 10,
                                    vertical: 9,
                                  ),
                                  // /  clipBehavior: Clip.antiAlias,
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    border: Border.all(color: Colors.white),
                                    borderRadius: BorderRadius.circular(60),
                                  ),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Padding(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 12,
                                        ),
                                        child: Text(
                                          LocaleKeys.search.tr(),
                                          style: AppTextStyles.s14w3cGrey,
                                          textScaler: TextScaler.linear(
                                            textScaleFactor.value,
                                          ),
                                        ),
                                      ),
                                      SizedBox(
                                        width: 12,
                                        height: 12,
                                        child: SvgPicture.asset(
                                          AppImages.icSearchWhite,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  // const SizedBox(height: 10),
                  // GestureDetector(
                  //   onTap: () {
                  //     context.router.removeWhere((route) => route.name == SearchScreenRoute.name);
                  //     context.pushRoute(SearchScreenRoute());
                  //   },
                  //   child: Hero(
                  //     tag: 'search_box',
                  //     child: Material(
                  //       type: MaterialType.transparency,
                  //       child: Container(
                  //         margin: const EdgeInsets.symmetric(horizontal: 36),
                  //         padding: const EdgeInsets.symmetric(
                  //           horizontal: 10,
                  //           vertical: 9,
                  //         ),
                  //         clipBehavior: Clip.antiAlias,
                  //         decoration: ShapeDecoration(
                  //           color: Colors.white,
                  //           shape: RoundedRectangleBorder(
                  //             borderRadius: BorderRadius.circular(60),
                  //           ),
                  //         ),
                  //         child: Row(
                  //           children: [
                  //             Expanded(
                  //               child: Padding(
                  //                 padding: const EdgeInsets.symmetric(
                  //                   horizontal: 12,
                  //                 ),
                  //                 child: Text(
                  //                   LocaleKeys.search.tr(),
                  //                   style: AppTextStyles.s14w3cHintColor,
                  //                   textScaler: TextScaler.linear(
                  //                     textScaleFactor.value,
                  //                   ),
                  //                 ),
                  //               ),
                  //             ),
                  //             SizedBox(
                  //               width: 27,
                  //               height: 27,
                  //               child: SvgPicture.asset(
                  //                 AppImages.icSearchGreenRound,
                  //               ),
                  //             ),
                  //           ],
                  //         ),
                  //       ),
                  //     ),
                  //   ),
                  // ),
                  // const SizedBox(height: 16),
                ],
              ),
            ),
            Expanded(
              child: RefreshIndicator(
                onRefresh: _loadIndicators,
                child: ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(40),
                    topRight: Radius.circular(40),
                  ),
                  child: Container(
                    decoration: ShapeDecoration(
                      color: isLightMode
                          ? AppColors.scaffoldBackgroundLight
                          : AppColors.scaffoldBackgroundDark,
                      shape: const RoundedRectangleBorder(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(40),
                          topRight: Radius.circular(40),
                        ),
                      ),
                    ),
                    child: ListView(
                      padding: const EdgeInsets.only(bottom: 150),
                      children: [
                        slidingTabView(context, isLightMode),
                        const SizedBox(height: 16),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 24),
                          child: Align(
                            child: Text(
                              LocaleKeys.keyFiguresAtAGlance.tr(),
                              style: AppTextStyles.s16w5cBlueTitleText.copyWith(
                                color: !isLightMode ? AppColors.white : null,
                              ),
                              textScaler:
                                  TextScaler.linear(textScaleFactor.value),
                            ),
                          ),
                        ),
                        const SizedBox(
                          height: 16,
                        ),
                        BlocConsumer<HomeBloc, HomeState>(
                          listener: (context, state) {
                            if (state is KeyIndicatorListState) {
                              keyIndicatorList = state.list;
                            }
                          },
                          builder: (context, state) {
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [
                                _buildKeyIndicatorList(
                                  state,
                                  isLightMode,
                                ),
                              ],
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Column slidingTabView(BuildContext context, bool isLightMode) {
    int currentCount = 0;

    currentCount =
        MediaQuery.sizeOf(context).width ~/ (112 * textScaleFactor.value);

    return Column(
      children: [
        Container(
          color:
              isLightMode ? AppColors.greyShade15_1 : AppColors.blueTitleText,
          // borderColor: isLightMode ? AppColors.white : AppColors.greyBorder,
          width: MediaQuery.sizeOf(context).width,
          height: 114,
          child: Padding(
            padding: const EdgeInsets.fromLTRB(5, 14, 5, 14),
            child: CarouselSlider.builder(
              carouselController: _carouselController,
              options: CarouselOptions(
                onPageChanged: (index, reason) {
                  currentTabIndex.value = index;
                },
                viewportFraction: 1,
                enableInfiniteScroll: false,
              ),
              itemCount: (tabItems.length / currentCount).ceil(),
              itemBuilder: (context, index, realIdx) {
                final int currentStartingValue = index * currentCount;
                final list = List<int>.generate(
                  currentCount,
                  (i) => currentStartingValue + i,
                );
                return Row(
                  mainAxisAlignment: textScaleFactor.value < 1.0
                      ? MainAxisAlignment.center
                      : MainAxisAlignment.spaceAround,
                  children: [
                    if (textScaleFactor.value < 1.0) ...[
                      Container(
                        constraints: BoxConstraints(
                          minWidth: (MediaQuery.sizeOf(context).width - 112) /
                              currentCount,
                        ),
                        margin: const EdgeInsets.symmetric(horizontal: 2),
                      ),
                    ],
                    ...list.map((idx) {
                      if (idx >= tabItems.length &&
                          !(textScaleFactor.value < 1.0)) {
                        return Container(
                          constraints: BoxConstraints(
                            minWidth: (MediaQuery.sizeOf(context).width - 112) /
                                currentCount,
                          ),
                          margin: const EdgeInsets.symmetric(horizontal: 2),
                        );
                      }

                      return idx < tabItems.length
                          ? Padding(
                              padding: EdgeInsets.symmetric(
                                  horizontal:
                                      textScaleFactor.value < 1.0 ? 10 : 0),
                              child: _indicator(
                                tabItems[idx].title,
                                tabItems[idx].icon,
                                tabItems[idx].color,
                                onTap: () {
                                  if (tabItems[idx].productType != null) {
                                    widget.navToProductsPage(
                                      tabItems[idx].productType!,
                                    );
                                  }
                                },
                                count: currentCount,
                                isLightMode: isLightMode,
                              ),
                            )
                          : const SizedBox();
                    }),
                    if (textScaleFactor.value < 1.0) ...[
                      Container(
                        constraints: BoxConstraints(
                          minWidth: (MediaQuery.sizeOf(context).width - 112) /
                              currentCount,
                        ),
                        margin: const EdgeInsets.symmetric(horizontal: 2),
                      ),
                    ],
                  ],
                );
              },
            ),
          ),
        ),
        Container(
          color:
              isLightMode ? AppColors.greyShade15_1 : AppColors.blueTitleText,
          padding: const EdgeInsets.only(bottom: 10),
          child: ValueListenableBuilder(
            valueListenable: currentTabIndex,
            builder: (context, currentIndex, _) {
              return Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(
                  (tabItems.length / currentCount).ceil(),
                  (index) => AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    margin: const EdgeInsets.only(right: 5),
                    height: 5,
                    // width: currentIndex == index ? 14 : 5,
                    width: 5,
                    decoration: BoxDecoration(
                      color: currentIndex == index
                          ? isLightMode
                              ? AppColors.blueLightOld
                              : AppColors.greyShade4
                          : (isLightMode
                                  ? AppColors.blueLightOld
                                  : AppColors.greyShade4)
                              .withOpacity(0.4),
                      borderRadius: BorderRadius.circular(3),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _indicator(
    String label,
    String icon,
    Color color, {
    required VoidCallback onTap,
    int count = 4,
    required bool isLightMode,
  }) {
    return Container(
      constraints: BoxConstraints(
        minWidth: (MediaQuery.sizeOf(context).width - 112) / count,
      ),
      margin: const EdgeInsets.symmetric(horizontal: 2),
      // width: (MediaQuery.sizeOf(context).width-48)/4,
      child: InkWell(
        onTap: onTap,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Spacer(flex: 3),
            ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: Container(
                width: 50,
                height: 50,
                decoration: ShapeDecoration(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                  ),
                ),
                child: SvgPicture.asset(icon),
              ),
            ),
            const SizedBox(height: 6),
            Text(
              label,
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                color: isLightMode
                    ? AppColors.blueTitleText
                    : AppColors.greyShade1,
                fontSize: 12,
                fontWeight: FontWeight.w400,
                height: 0,
              ),
              textScaler: TextScaler.linear(textScaleFactor.value),
            ),
            const Spacer(
              flex: 2,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildKeyIndicatorList(HomeState state, bool isLightMode) {
    if (keyIndicatorList.isEmpty) {
      if (state is KeyIndicatorLoadingState) {
        return const Center(
          child: Padding(
            padding: EdgeInsets.symmetric(
              vertical: 100,
            ),
            child: CircularProgressIndicator(),
          ),
        );
      }
      return const Center(
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 100),
          child: NoDataPlaceholder(),
        ),
      );
    }
    return ListView.separated(
      key: const Key('keyIndicatorList'),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.fromLTRB(20, 15, 20, 0),
      itemCount: keyIndicatorList.length,
      separatorBuilder: (context, index) => const SizedBox(
        height: 25,
      ),
      itemBuilder: (context, index) {
        final KeyIndicatorListResponseItem item = keyIndicatorList[index];
        return IndicatorCardV2(
          key: Key(
            'home.keyIndicators.IndicatorCardV2-${item.nodeId}-$indicatorsRefreshedAt',
          ),
          id: item.nodeId!,
          contentType: item.contentType!,
          openButtonKey: index == 0 ? step4 : null,
          onUserGuideBackFromDetailsPage: (isPreviousActionTriggered) {
            isPreviousFromDetailsScreen = isPreviousActionTriggered;
            if (isPreviousActionTriggered) {
              if (HiveUtilsSettings.getUserGuideStatus() == UserGuides.Home) {
                ShowCaseWidget.of(context).startShowCase(steps);
                ShowCaseWidget.of(context).jumpToId(steps.length - 1);
              }
            }
          },
        );
      },
    );
  }
}
