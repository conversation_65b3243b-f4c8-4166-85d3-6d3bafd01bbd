import 'dart:math';

import 'package:auto_route/auto_route.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/intro_widget.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/showcaseview.dart';
import 'package:scad_mobile/src/features/home/<USER>/models/indicator_tab.dart';
import 'package:scad_mobile/src/features/home/<USER>/widgets/key_indicator_list/key_indicator_list.dart';
import 'package:scad_mobile/src/features/products/presentation/bloc/products_bloc.dart';
import 'package:scad_mobile/src/utils/app_utils/app_update.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

@RoutePage()
class HomePage extends StatefulWidget {
  const HomePage({
    required this.navToDomainsPage,
    required this.navToProductsPage,
    super.key,
  });

  final VoidCallback navToDomainsPage;
  final void Function(ProductTabType type) navToProductsPage;

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with AutomaticKeepAliveClientMixin {
  ValueNotifier<int> currentTabIndex = ValueNotifier(0);

  final CarouselSliderController _carouselController = CarouselSliderController();

  final steps = [
    GlobalKey(debugLabel: 'home-step1'),
    // GlobalKey(debugLabel: 'home-step2'),
    // GlobalKey(debugLabel: 'home-step3'),
    GlobalKey(debugLabel: 'home-step4'),
  ];

  GlobalKey get step1 => steps.elementAt(0);

  // GlobalKey get step2 => steps.elementAt(1);
  //
  // GlobalKey get step3 => steps.elementAt(2);

  // GlobalKey get step4 => steps.elementAt(3);

  List<IndicatorTab> tabItems = [
    IndicatorTab(
      title: LocaleKeys.dashboards.tr(),
      icon: AppImages.icDashboard,
      productType: ProductTabType.dashboards,
    ),
    IndicatorTab(
      title: LocaleKeys.webReports.tr(),
      icon: AppImages.icWebReports,
      productType: ProductTabType.webReports,
    ),
    IndicatorTab(
      title: LocaleKeys.publications.tr(),
      icon: AppImages.icPublications,
      productType: ProductTabType.publications,
    ),
  ];

  @override
  bool get wantKeepAlive => true;

  ScrollController scrollController = ScrollController();

  int get _productItemCount =>
      DeviceType.isTabDevice() ? MediaQuery.sizeOf(context).width ~/ (90 * HiveUtilsSettings.textSizeFactor) : 3;

  BoxConstraints get _productItemWidthConstraint =>
      BoxConstraints(minWidth: (MediaQuery.sizeOf(context).width - 112) / _productItemCount);

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _checkAndShowGuide();
        AppUpdate.check(context);
      }
    });
  }

  void _checkAndShowGuide() {
    if (HiveUtilsSettings.getUserGuideStatus() == UserGuides.Home) {
      ShowCaseWidget.of(context).startShowCase(steps);
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bool isLightMode = HiveUtilsSettings.isLightMode;
    super.build(context);

    return Stack(
      alignment: Alignment.topCenter,
      children: [
        Container(
          height: MediaQuery.sizeOf(context).height * 0.3,
          width: MediaQuery.sizeOf(context).width,
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [
                AppColors.blueGradientShade1,
                AppColors.blueGradientShade2,
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Padding(
              padding: const EdgeInsets.only(bottom: 20),
              child: Column(
                children: [
                  SizedBox(
                    height: MediaQuery.paddingOf(context).top,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 10,
                      vertical: 10,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        IntroWidget(
                          stepKey: step1,
                          stepIndex: 1,
                          totalSteps: 7,
                          title: LocaleKeys.hamburgerMenu.tr(),
                          description: LocaleKeys.hamburgerMenuDesc.tr(),
                          arrowAlignment: Alignment.bottomLeft,
                          targetPadding: const EdgeInsets.all(3),
                          arrowPadding: const EdgeInsets.only(
                            left: 14,
                            bottom: 10,
                          ),
                          child: appDrawerController.drawerButton(
                            isArabic: HiveUtilsSettings.isLanguageArabic,
                          ),
                        ),
                        Expanded(
                          child: GestureDetector(
                            onTap: () {
                              context.router.removeWhere(
                                (route) => route.name == SearchScreenRoute.name,
                              );
                              context.pushRoute(SearchScreenRoute());
                            },
                            child: Hero(
                              tag: 'search_box',
                              child: Material(
                                type: MaterialType.transparency,
                                child: Container(
                                  margin: const EdgeInsets.symmetric(
                                    horizontal: 10,
                                  ),
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 10,
                                    vertical: 9,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    border: Border.all(color: Colors.white),
                                    borderRadius: BorderRadius.circular(60),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      Padding(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 12,
                                        ),
                                        child: Text(
                                          LocaleKeys.search.tr(),
                                          style: AppTextStyles.s14w3cGrey,
                                        ),
                                      ),
                                      SizedBox.square(
                                        dimension: 12,
                                        child: SvgPicture.asset(
                                          AppImages.icSearchWhite,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(40),
                  topRight: Radius.circular(40),
                ),
                child: Container(
                  decoration: ShapeDecoration(
                    color: isLightMode ? AppColors.scaffoldBackgroundLight : AppColors.scaffoldBackgroundDark,
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(40),
                        topRight: Radius.circular(40),
                      ),
                    ),
                  ),
                  child: ListView(
                    controller: scrollController,
                    padding: const EdgeInsets.only(bottom: 150),
                    children: [
                      _buildSlidingTabView(context, isLightMode),
                      const SizedBox(height: 16),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 24),
                        child: Align(
                          child: Text(
                            LocaleKeys.keyFiguresAtAGlance.tr(),
                            style: AppTextStyles.s16w5cBlueTitleText.copyWith(
                              color: !isLightMode ? AppColors.white : null,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 16,
                      ),
                      KeyIndicatorList(
                        key: const Key(
                          'key-indicator-list',
                        ),
                        stepKeys: steps,
                        parentScrollController: scrollController,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSlidingTabView(BuildContext context, bool isLightMode) {
    final perPageCount = _productItemCount;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 500),
      curve: Curves.ease,
      height: 130,
      color: isLightMode ? AppColors.greyShade15_1 : AppColors.blueTitleText,
      child: FittedBox(
        fit: BoxFit.scaleDown,
        child: Column(
          children: [
            Container(
              width: MediaQuery.sizeOf(context).width,
              height: 114,
              padding: const EdgeInsets.fromLTRB(5, 14, 5, 14),
              child: CarouselSlider.builder(
                carouselController: _carouselController,
                options: CarouselOptions(
                  onPageChanged: (index, reason) {
                    currentTabIndex.value = index;
                  },
                  viewportFraction: 1,
                  enableInfiniteScroll: false,
                ),
                itemCount: (tabItems.length / perPageCount).ceil(),
                itemBuilder: (context, index, realIdx) {
                  final int currentStartingValue = index * perPageCount;
                  final list = List<int>.generate(
                    min(tabItems.length, perPageCount),
                    (i) => currentStartingValue + i,
                  );
                  return Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: list.map((idx) {
                      void onTap() {
                        if (tabItems[idx].productType != null) {
                          widget.navToProductsPage(
                            tabItems[idx].productType!,
                          );
                        }
                      }

                      return idx < tabItems.length
                          ? _indicator(
                              tabItems[idx].title,
                              tabItems[idx].icon,
                              onTap: onTap,
                              count: perPageCount,
                              isLightMode: isLightMode,
                            )
                          : const SizedBox();
                    }).toList(),
                  );
                },
              ),
            ),
            if ((tabItems.length / perPageCount).ceil() > 1)
              Container(
                width: MediaQuery.sizeOf(context).width,
                color: isLightMode ? AppColors.greyShade15_1 : AppColors.blueTitleText,
                padding: const EdgeInsets.only(bottom: 10),
                child: ValueListenableBuilder(
                  valueListenable: currentTabIndex,
                  builder: (context, currentIndex, _) {
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: List.generate(
                        (tabItems.length / perPageCount).ceil(),
                        (index) => AnimatedContainer(
                          duration: const Duration(milliseconds: 200),
                          margin: const EdgeInsets.symmetric(horizontal: 2.5),
                          height: 5,
                          width: 5,
                          decoration: BoxDecoration(
                            color: (isLightMode ? AppColors.blueLightOld : AppColors.greyShade4)
                                .withValues(alpha: currentIndex == index ? 1 : 0.4),
                            borderRadius: BorderRadius.circular(3),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _indicator(
    String label,
    String icon, {
    required VoidCallback onTap,
    required bool isLightMode,
    int count = 4,
  }) {
    return Container(
      width: DeviceType.isTabDevice() ? null : MediaQuery.sizeOf(context).width * .3,
      constraints: _productItemWidthConstraint,
      margin: const EdgeInsets.symmetric(horizontal: 2),
      child: InkWell(
        onTap: onTap,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Spacer(flex: 3),
            ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: Container(
                width: 50,
                height: 50,
                decoration: ShapeDecoration(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                  ),
                ),
                child: SvgPicture.asset(icon),
              ),
            ),
            const SizedBox(height: 6),
            Text(
              label,
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                color: isLightMode ? AppColors.blueTitleText : AppColors.greyShade1,
                fontSize: 12,
                fontWeight: FontWeight.w400,
                height: 0,
              ),
            ),
            const Spacer(
              flex: 2,
            ),
          ],
        ),
      ),
    );
  }
}
