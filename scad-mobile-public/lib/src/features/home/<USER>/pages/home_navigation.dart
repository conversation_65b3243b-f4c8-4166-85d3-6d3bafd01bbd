import 'dart:async';
import 'dart:math';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/route_manager/analytics_route.observer.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/common/widgets/debug/debug_overlay_button.dart';
import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/showcaseview.dart';
import 'package:scad_mobile/src/features/domains/presentation/pages/themes_page.dart';
import 'package:scad_mobile/src/features/home/<USER>/bloc/home_bloc/home_bloc.dart';
import 'package:scad_mobile/src/features/home/<USER>/pages/home/<USER>';
import 'package:scad_mobile/src/features/home/<USER>/widgets/awesome_bottom_bar/awesome_bottom_bar.dart';
import 'package:scad_mobile/src/features/home/<USER>/widgets/awesome_bottom_bar/src/bottom_bar_inspired_outside.dart';
import 'package:scad_mobile/src/features/home/<USER>/widgets/awesome_bottom_bar/widgets/inspired/inspired.dart';
import 'package:scad_mobile/src/features/home/<USER>/widgets/persistent_bottom_nav_bar/persistent_bottom_nav_bar_v2.dart';
import 'package:scad_mobile/src/features/products/presentation/bloc/products_bloc.dart';
import 'package:scad_mobile/src/features/products/presentation/pages/products_screen.dart';
import 'package:scad_mobile/src/utils/app_utils/app_update.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

@RoutePage()
class HomeNavigation extends StatefulWidget {
  const HomeNavigation({
    super.key,
    this.screenTabIndex,
  });

  final int? screenTabIndex;

  @override
  State<HomeNavigation> createState() => _HomeNavigationState();
}

class _HomeNavigationState extends State<HomeNavigation> with AutomaticKeepAliveClientMixin {
  final PageController _pageController = PageController();
  final PersistentTabController _persistentTabController = PersistentTabController();

  List<Widget> bottomBarPages = [];
  ValueNotifier<int> visit = ValueNotifier(0);

  List<PersistentTabConfig> _tabs() => [
        PersistentTabConfig(
          screen: HomePage(
            navToDomainsPage: _onNavToDomainsPage,
            navToProductsPage: _onNavToProducts,
          ),
          item: ItemConfig(
            icon: const Icon(Icons.home),
          ),
        ),
        PersistentTabConfig(
          screen: const ThemesPage(),
          item: ItemConfig(
            icon: const Icon(Icons.search),
          ),
        ),
        PersistentTabConfig(
          screen: const Products(),
          item: ItemConfig(
            icon: const Icon(Icons.message),
          ),
        ),
      ];

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();

    _persistentTabController.addListener(_homeNavigationListener);
    _homeNavigationListener();

    bottomBarPages = [
      HomePage(
        navToDomainsPage: _onNavToDomainsPage,
        navToProductsPage: _onNavToProducts,
      ),
      const ThemesPage(),
      const Products(),
    ];
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      if (widget.screenTabIndex != null) {
        _navigateToIndexedScreen();
      }
    });
  }

  void _homeNavigationListener() => AnalyticsRouteObserver.homeNavigationListener(_persistentTabController);

  void _onNavToHomePage() {
    visit.value = 0;
    // _pageController.jumpToPage(0);
    _persistentTabController.jumpToTab(0);
  }

  void _onNavToDomainsPage() {
    visit.value = 1;
    _pageController.jumpToPage(1);
  }

  void _onNavToProducts(ProductTabType type) {
    visit.value = 2;
    AppUpdate.check(context);
    _persistentTabController.jumpToTab(2);
    Future.delayed(const Duration(milliseconds: 200), () {
      context.read<ProductsBloc>().add(ProductsSelectTabEvent(type: type, rnd: Random().nextInt(100)));
    });
  }

  void _navigateToIndexedScreen() {
    _persistentTabController.jumpToTab(widget.screenTabIndex!);
    visit.value = widget.screenTabIndex!;
  }

  @override
  void dispose() {
    _pageController.dispose();
    _persistentTabController
      ..removeListener(_homeNavigationListener)
      ..dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Stack(
      children: [
        Positioned.fill(
          child: AppDrawer(
            initSetting: true,
            child: Column(
              children: [
                AppUpdate.listenerWidget(),
                Expanded(
                  child: ShowCaseWidget(
                    builder: Builder(
                      builder: (context) {
                        return Scaffold(
                          resizeToAvoidBottomInset: false,
                          body: BlocListener<HomeBloc, HomeState>(
                            listener: (context, state) {
                              if (state is NavToHomeState) {
                                _onNavToHomePage();
                              }
                            },
                            child: PersistentTabView(
                              resizeToAvoidBottomInset: false,
                              controller: _persistentTabController,
                              tabs: _tabs(),
                              navBarBuilder: (navBarConfig) => PersistentBottomNavBar(
                                visit: visit,
                                persistentTabController: _persistentTabController,
                                navBarConfig: navBarConfig,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        if (kDebugMode) const DebugOverlayButton(),
      ],
    );
  }
}

class PersistentBottomNavBar extends StatefulWidget {
  const PersistentBottomNavBar({
    required this.visit,
    required this.persistentTabController,
    required this.navBarConfig,
    super.key,
  });

  final ValueNotifier<int> visit;
  final PersistentTabController persistentTabController;
  final NavBarConfig navBarConfig;

  @override
  State<PersistentBottomNavBar> createState() => _PersistentBottomNavBarState();
}

class _PersistentBottomNavBarState extends State<PersistentBottomNavBar> {
  List<TabItem> tabs = [
    TabItem(
      title: LocaleKeys.home.tr(),
      icon: AppImages.icHomeMenu,
      analyticsPage: 'Home',
    ),
    TabItem(
      title: LocaleKeys.domains.tr(),
      icon: AppImages.icDomainMenu,
      analyticsPage: 'Domains',
    ),
    TabItem(
      title: LocaleKeys.products.tr(),
      icon: AppImages.icProductsMenu,
      analyticsPage: 'Products',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final bool isLightMode = HiveUtilsSettings.isLightMode;
    return BlocListener<HomeBloc, HomeState>(
      listener: (context, state) {
        if (state is CheckMaintenanceSuccessState) {
          if (state.data.isFullMaintenance) {
            AutoRouter.of(context).replaceAll([
              MaintenanceScreenRoute(data: state.data),
            ]);
          }
        }
      },
      child: ValueListenableBuilder(
        valueListenable: widget.visit,
        builder: (context, currentIndex, _) {
          return BottomBarInspiredOutside(
            items: tabs,
            elevation: 10,
            shadowColor: isLightMode ? AppColors.shadow4.withOpacity(0.4) : AppColors.shadow3,
            backgroundColor: isLightMode ? AppColors.scaffoldBackgroundLight : AppColors.scaffoldBackgroundDark,
            color: isLightMode ? AppColors.blueGreyShade2 : AppColors.greyShade4,
            colorSelected: isLightMode ? AppColors.blueShade22 : AppColors.white,
            iconSize: 24,
            padbottom: 4,
            height: 50,
            pad: 8,
            radius: 20,
            top: -30,
            chipStyle: ChipStyle(
              notchSmoothness: NotchSmoothness.softEdge,
              background: isLightMode ? AppColors.blueShade22 : AppColors.blueShade27,
            ),
            curve: Curves.decelerate,
            titleStyle: const TextStyle(fontSize: 11),
            indexSelected: currentIndex,
            onTap: (index) {
              widget.visit.value = index;
              widget.navBarConfig.onItemSelected(index);

              AppUpdate.check(context);
            },
            itemStyle: ItemStyle.circle,
          );
        },
      ),
    );
  }
}
