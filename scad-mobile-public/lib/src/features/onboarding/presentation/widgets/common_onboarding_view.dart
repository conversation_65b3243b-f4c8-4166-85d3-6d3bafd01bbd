import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/features/onboarding/presentation/widgets/onboarding_header.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class CommonOnboardingView extends StatelessWidget {
  const CommonOnboardingView({
    required this.index,
    required this.widget,
    this.onNextButtonPressed,
    this.isLastScreen = false,
    super.key,
  });
  final Widget widget;
  final VoidCallback? onNextButtonPressed;
  final bool isLastScreen;
  final int index;

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Stack(
      children: [
        Stack(
          children: [
            SvgPicture.asset(
              AppImages.bgOnboarding,
              width: MediaQuery.sizeOf(context).width,
            ),
            Container(
              height: size.height * 0.46,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColors.white.withOpacity(0.4),
                    AppColors.white.withOpacity(0.1),
                    Colors.transparent,
                    Colors.transparent,
                  ],
                  begin: Alignment.topRight,
                  end: Alignment.bottomLeft,
                ),
              ),
            ),
          ],
        ),
        Padding(
          padding:
              EdgeInsets.only(top: size.height * 0.17, left: 26, right: 26),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 24),
                child: widget,
              ),
              const Spacer(),
              OnBoardingHeader(
                title: 'How to use',
                description: getDescription(),
              ),
              const SizedBox(height: 24),
              TextButton(
                style: TextButton.styleFrom(
                  backgroundColor: isLightMode
                      ? AppColors.blueLight
                      : AppColors.blueLightOld,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                onPressed: onNextButtonPressed,
                child: Text(
                  isLastScreen
                      ? LocaleKeys.exploreNow.tr()
                      : LocaleKeys.next.tr(),
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: AppColors.white,
                  ),
                  textScaler: TextScaler.linear(textScaleFactor.value),
                ),
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ],
    );
  }

  String getDescription() {
    switch (index) {
      case 1:
        return LocaleKeys.onboarding1.tr();
      case 2:
        return LocaleKeys.onboarding2.tr();
      case 3:
        return LocaleKeys.onboarding3.tr();
      case 4:
        return LocaleKeys.onboarding4.tr();
      default:
        return '';
    }
  }
}
