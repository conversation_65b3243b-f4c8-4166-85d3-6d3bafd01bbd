import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class OnboardingPageIndicators extends StatelessWidget {
  const OnboardingPageIndicators({
    required this.currentPageIndex,
    required this.pageController,
    required this.pageLength,
    super.key,
    this.isLastScreen = false,
  });

  final ValueNotifier<int> currentPageIndex;
  final PageController pageController;
  final bool isLastScreen;
  final int pageLength;

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Container(
      alignment: Alignment.center,
      height: 100,
      width: size.width,
      child: ValueListenableBuilder(
        valueListenable: currentPageIndex,
        builder: (context, index, w) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildDot(
                    color: index >= 1 ? AppColors.white :
                    isLightMode?
                     AppColors.blueLight
                            : AppColors.blueLightOld,
                  ),
                  _buildLine(
                    color: index >= 1 ? AppColors.white : AppColors.skyBlue,
                  ),
                  _buildDot(
                      color: index >= 1 ? AppColors.white : AppColors.skyBlue),
                  _buildLine(
                    color: index >= 2
                        ? AppColors.white
                        : index >= 1
                            ? AppColors.skyBlue.withOpacity(0.6)
                            : AppColors.skyBlue,
                  ),
                  _buildDot(
                    color: index >= 2
                        ? AppColors.white
                        : index >= 1
                            ? AppColors.skyBlue.withOpacity(0.6)
                            : AppColors.skyBlue,
                  ),
                  _buildLine(
                    color: index >= 3
                        ? AppColors.white
                        : index >= 1
                            ? AppColors.skyBlue.withOpacity(0.6)
                            : AppColors.skyBlue,
                  ),
                  _buildDot(
                    color: index >= 3
                        ? AppColors.white
                        : index >= 1
                            ? AppColors.skyBlue.withOpacity(0.6)
                            : AppColors.skyBlue,
                  ),
                  _buildLine(
                    color: index >= 4
                        ? AppColors.white
                        : index >= 1
                            ? AppColors.skyBlue.withOpacity(0.6)
                            : AppColors.skyBlue,
                  ),
                  _buildDot(
                    color: index >= 4
                        ? AppColors.white
                        : index >= 1
                            ? AppColors.skyBlue.withOpacity(0.6)
                            : AppColors.skyBlue,
                  ),
                  _buildLine(
                    color: Colors.transparent,
                    isVisible: false,
                  ),
                ],
              ),
              SizedBox(
                width: size.width,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Visibility(
                      visible: index != 0,
                      child: InkWell(
                        onTap: () {
                          pageController.animateToPage(
                            index - 1,
                            duration: const Duration(milliseconds: 200),
                            curve: Curves.easeIn,
                          );
                        },
                        borderRadius: BorderRadius.circular(8),
                        child: Padding(
                          padding: const EdgeInsets.all(14),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              SvgPicture.asset(
                                AppImages.icArrowLeft,
                                colorFilter: ColorFilter.mode(
                                  index >= 1 ? AppColors.white : AppColors.blue,
                                  BlendMode.srcIn,
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(left: 8),
                                child: Text(
                                  LocaleKeys.back.tr(),
                                  style: AppTextStyles.s14w6cBlue.copyWith(
                                    color: index >= 1
                                        ? AppColors.white
                                        : AppColors.blue,
                                  ),
                                  textScaler:
                                      TextScaler.linear(textScaleFactor.value),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 50,
                      child: Visibility(
                        visible: index > 0 && !isLastScreen,
                        child: InkWell(
                          onTap: () {
                            pageController.animateToPage(
                              4,
                              duration: const Duration(milliseconds: 200),
                              curve: Curves.easeIn,
                            );
                          },
                          borderRadius: BorderRadius.circular(8),
                          child: Padding(
                            padding: const EdgeInsets.all(14),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(right: 8),
                                  child: Text(
                                    LocaleKeys.skip.tr(),
                                    style: AppTextStyles.s14w6cBlue.copyWith(
                                      color: index >= 1
                                          ? AppColors.white
                                          : AppColors.blue,
                                    ),
                                    textScaler: TextScaler.linear(
                                        textScaleFactor.value),
                                  ),
                                ),
                                SvgPicture.asset(
                                  AppImages.icSkip,
                                  colorFilter: ColorFilter.mode(
                                    index >= 1
                                        ? AppColors.white
                                        : AppColors.blue,
                                    BlendMode.srcIn,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildDot({required Color color}) {
    return Container(
      width: 10,
      height: 10,
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: color,
      ),
    );
  }

  Widget _buildLine({
    required Color color,
    bool isVisible = true,
  }) {
    return Flexible(
      child: Visibility(
        visible: isVisible,
        child: Container(
          height: 1,
          color: color,
        ),
      ),
    );
  }
}
