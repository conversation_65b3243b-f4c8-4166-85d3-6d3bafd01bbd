import 'package:flutter/material.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';

class OnBoardingHeader extends StatelessWidget {
  const OnBoardingHeader({
    required this.title,
    required this.description,
    super.key,
  });

  final String title;
  final String description;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          title,
          textAlign: TextAlign.center,
          style: AppTextStyles.s24w5cBlackOrWhiteShade,
          textScaler: TextScaler.linear(textScaleFactor.value),
        ),
        const SizedBox(height: 10),
        Text(
          description,
          textAlign: TextAlign.center,
          style: AppTextStyles.s14w4cblackShade4
              .copyWith(color: AppColors.greyShade4),
          textScaler: TextScaler.linear(textScaleFactor.value),
        ),
      ],
    );
  }
}
