import 'package:flutter/material.dart';
import 'package:scad_mobile/src/features/onboarding/presentation/widgets/common_onboarding_view.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';

class OnBoardingScreen4 extends StatelessWidget {
  const OnBoardingScreen4({required this.pageController, super.key});
  final PageController pageController;

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;
    return CommonOnboardingView(
      index: 3,
      widget: Image.asset(
        AppImages.imgOnboarding3,
        height: size.height * .55,
      ),
      onNextButtonPressed: () {
        pageController.animateToPage(
          4,
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeIn,
        );
      },
    );
  }
}
