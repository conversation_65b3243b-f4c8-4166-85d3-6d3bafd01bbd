import 'dart:io';

import 'package:auto_route/annotations.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:scad_mobile/src/features/onboarding/presentation/pages/onboarding_screen_2.dart';
import 'package:scad_mobile/src/features/onboarding/presentation/pages/onboarding_screen_3.dart';
import 'package:scad_mobile/src/features/onboarding/presentation/pages/onboarding_screen_4.dart';
import 'package:scad_mobile/src/features/onboarding/presentation/pages/onboarding_screen_5.dart';
import 'package:scad_mobile/src/features/onboarding/presentation/widgets/onboarding_page_indicators.dart';

@RoutePage()
class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();

  ValueNotifier<int> currentPageIndex = ValueNotifier(0);
  List<Widget> onboardingPages = [];
  @override
  void initState() {
    super.initState();
    _pageController.addListener(() {
      currentPageIndex.value = _pageController.page!.round();
    });

    onboardingPages = [
      OnBoardingScreen2(pageController: _pageController),
      OnBoardingScreen3(pageController: _pageController),
      OnBoardingScreen4(pageController: _pageController),
      OnBoardingScreen5(pageController: _pageController),
    ];
  }

  @override
  void dispose() {
    super.dispose();
    _pageController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;
    return PopScope(
      canPop: false,
      onPopInvoked: (bool didPop) {
        if(currentPageIndex.value > 0) {
          _pageController.animateToPage(
            currentPageIndex.value - 1,
            duration: const Duration(milliseconds: 200),
            curve: Curves.easeIn,
          );
        } else {
          if(!Platform.isIOS) {
            SystemNavigator.pop();
          }
        }
      },
      child: Scaffold(
        body: Stack(
          children: [
            PageView(
              controller: _pageController,
              physics: const NeverScrollableScrollPhysics(),
              children: onboardingPages,
            ),
            Positioned(
              top: size.height * .06,
              child: ValueListenableBuilder(
                valueListenable: currentPageIndex,
                builder: (context, index, w) {
                  return OnboardingPageIndicators(
                    pageController: _pageController,
                    currentPageIndex: currentPageIndex,
                    pageLength: onboardingPages.length,
                    isLastScreen: index == 4,
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
