import 'package:flutter/material.dart';
import 'package:scad_mobile/src/features/onboarding/presentation/widgets/common_onboarding_view.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';

class OnBoardingScreen2 extends StatelessWidget {
  const OnBoardingScreen2({
    required this.pageController,
    super.key,
  });
  final PageController pageController;

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;
    return CommonOnboardingView(
      index: 1,
      widget: Image.asset(
        AppImages.imgOnboarding1,
        height: size.height * .55,
      ),
      onNextButtonPressed: () {
        pageController.animateToPage(
          2,
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeIn,
        );
      },
    );
  }
}
