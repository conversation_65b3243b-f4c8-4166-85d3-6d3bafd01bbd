import 'package:flutter/material.dart';
import 'package:scad_mobile/src/features/onboarding/presentation/widgets/common_onboarding_view.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';

class OnBoardingScreen3 extends StatelessWidget {
  const OnBoardingScreen3({
    required this.pageController,
    super.key,
  });
  final PageController pageController;
  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;
    return CommonOnboardingView(
      index: 2,
      widget: Padding(
        padding: const EdgeInsets.only(left: 22),
        child: Image.asset(
          AppImages.imgOnboarding2,
          height: size.height * .55,
        ),
      ),
      onNextButtonPressed: () {
        pageController.animateToPage(
          3,
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeIn,
        );
      },
    );
  }
}
