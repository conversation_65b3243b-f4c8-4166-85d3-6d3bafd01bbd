import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/features/onboarding/presentation/widgets/common_onboarding_view.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';

class OnBoardingScreen5 extends StatelessWidget {
  const OnBoardingScreen5({required this.pageController, super.key});
  final PageController pageController;

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;
    return CommonOnboardingView(
      index: 4,
      isLastScreen: true,
      widget: Image.asset(
        AppImages.imgOnboarding4,
        height: size.height * .55,
      ),
      onNextButtonPressed: () {
        AutoRouter.of(context).replaceAll([HomeNavigationRoute()]);
      },
    );
  }
}
