import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:overlay_support/overlay_support.dart';
import 'package:scad_mobile/route_manager/route_imports.dart';
import 'package:scad_mobile/src/config/bloc_config/bloc_providers.dart';
import 'package:scad_mobile/src/config/dependancy_injection/injection_container.dart';
import 'package:scad_mobile/src/config/theme_config/theme_constants.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  final _appRoutes = servicelocator<AppRouter>();

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: providers,
      child: OverlaySupport.global(
        child: ValueListenableBuilder(
        valueListenable: HiveUtilsSettings.box().listenable(),
        builder: (c, box, child) {
          return MaterialApp.router(
                key: Key(HiveUtilsSettings.boxStatus()),
                localizationsDelegates: context.localizationDelegates,
                supportedLocales: const [Locale('en', ''), Locale('ar', '')],
                //context.supportedLocales,
                locale: context.locale,
                routerConfig: _appRoutes.config(),
                debugShowCheckedModeBanner: false,
                scaffoldMessengerKey: snackBarKey,
                theme: AppThemeData.lightTheme,
                darkTheme: AppThemeData.darkTheme,
                themeMode: HiveUtilsSettings.getThemeMode(),
          );
        },
        ),
      ),
    );
  }
}
