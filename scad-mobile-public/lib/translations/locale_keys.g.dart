// DO NOT EDIT. This is code generated via package:easy_localization/generate.dart

abstract class  LocaleKeys {
  static const all = 'all';
  static const clearAll = 'clearAll';
  static const language = 'language';
  static const recentSearches = 'recentSearches';
  static const searchHere = 'searchHere';
  static const suggestedIndicators = 'suggestedIndicators';
  static const password = 'password';
  static const searchResults = 'searchResults';
  static const settings = 'settings';
  static const notification = 'notification';
  static const keyIndicators = 'keyIndicators';
  static const keyFiguresAtAGlance = 'keyFiguresAtAGlance';
  static const statisticalDomains = 'statisticalDomains';
  static const somethingWentWrong = 'somethingWentWrong';
  static const chatThreads = 'chatThreads';
  static const mostPopularFAQ = 'mostPopularFAQ';
  static const domains = 'domains';
  static const general = 'general';
  static const selectDomain = 'selectDomain';
  static const noDataAvailable = 'noDataAvailable';
  static const noData = 'noData';
  static const send = 'send';
  static const enter = 'enter';
  static const forgotPassword = 'forgotPassword';
  static const emailId = 'emailId';
  static const hamburgerMenu = 'hamburgerMenu';
  static const hamburgerMenuDesc = 'hamburgerMenuDesc';
  static const recommendedForYou = 'recommendedForYou';
  static const recommendedForYouDesc = 'recommendedForYouDesc';
  static const addIcons = 'addIcons';
  static const addIconsDesc = 'addIconsDesc';
  static const expand = 'expand';
  static const expandDesc = 'expandDesc';
  static const backToUserGuide = 'backToUserGuide';
  static const of = 'of';
  static const steps = 'steps';
  static const next = 'next';
  static const search = 'search';
  static const forecast = 'forecast';
  static const scenarioDrivers = 'scenarioDrivers';
  static const downloadAs = 'downloadAs';
  static const changeDataFrequency = 'changeDataFrequency';
  static const changeDataPresentation = 'changeDataPresentation';
  static const compareIndicators = 'compareIndicators';
  static const computeData = 'computeData';
  static const termsAndConditions = 'termsAndConditions';
  static const iAgreeTo = 'iAgreeTo';
  static const updatedOn = 'updatedOn';
  static const source = 'source';
  static const glossary = 'glossary';
  static const nationalAccounts = 'nationalAccounts';
  static const whatIfScenario = 'whatIfScenario';
  static const changeDrivers = 'changeDrivers';
  static const back = 'back';
  static const filters = 'filters';
  static const searchApps = 'searchApps';
  static const addApps = 'addApps';
  static const myApps = 'myApps';
  static const youDontHaveAnyAppsAdded = 'youDontHaveAnyAppsAdded';
  static const chooseYourDomains = 'chooseYourDomains';
  static const createStartNewCollection = 'createStartNewCollection';
  static const saveTo = 'saveTo';
  static const done = 'done';
  static const startANewChat = 'startANewChat';
  static const startAddingApps = 'startAddingApps';
  static const youDontHaveAnyConversationsStartedYet = 'youDontHaveAnyConversationsStartedYet';
  static const contactSCAD = 'contactSCAD';
  static const whatINeedToHighlight = 'whatINeedToHighlight';
  static const whatIsItRegarding = 'whatIsItRegarding';
  static const pleaseExplain = 'pleaseExplain';
  static const filePickerPlaceholder = 'filePickerPlaceholder';
  static const attachments = 'attachments';
  static const submit = 'submit';
  static const feedback = 'feedback';
  static const feedbackOrComments = 'feedbackOrComments';
  static const feedbackSuccess = 'feedbackSuccess';
  static const feedbackSuccessThanks = 'feedbackSuccessThanks';
  static const backToHome = 'backToHome';
  static const retypePassword = 'retypePassword';
  static const currentPassword = 'currentPassword';
  static const newPassword = 'newPassword';
  static const cancel = 'cancel';
  static const inAppNotifications = 'inAppNotifications';
  static const notificationByEmail = 'notificationByEmail';
  static const notifications = 'notifications';
  static const clear = 'clear';
  static const disseminationProducts = 'disseminationProducts';
  static const signedInAs = 'signedInAs';
  static const submitANewQuestion = 'submitANewQuestion';
  static const questionThreads = 'questionThreads';
  static const ticketID = 'ticketID';
  static const you = 'you';
  static const SME = 'SME';
  static const reference = 'reference';
  static const changeDomain = 'changeDomain';
  static const changeTheme = 'changeTheme';
  static const changeSubTheme = 'changeSubTheme';
  static const addSubjectLine = 'addSubjectLine';
  static const selectCategoryYouWantToTalkAbout = 'selectCategoryYouWantToTalkAbout';
  static const submitYourQuery = 'submitYourQuery';
  static const select = 'select';
  static const download = 'download';
  static const loginTagLine = 'loginTagLine';
  static const loginWithEmail = 'loginWithEmail';
  static const governmentEmail = 'governmentEmail';
  static const forgotPasswordQue = 'forgotPasswordQue';
  static const privacyPolicy = 'privacyPolicy';
  static const otherLoginOptions = 'otherLoginOptions';
  static const login = 'login';
  static const chooseYourInterests = 'chooseYourInterests';
  static const interestDesc = 'interestDesc';
  static const userGuide = 'userGuide';
  static const giveYourFeedback = 'giveYourFeedback';
  static const termsOfUseEMPPrivacyPolicy = 'termsOfUseEMPPrivacyPolicy';
  static const aboutThisApp = 'aboutThisApp';
  static const dark = 'dark';
  static const light = 'light';
  static const skip = 'skip';
  static const continueButton = 'continueButton';
  static const logout = 'logout';
  static const newCollection = 'newCollection';
  static const myDashboards = 'myDashboards';
  static const products = 'products';
  static const home = 'home';
  static const textSize = 'textSize';
  static const myDashboard = 'myDashboard';
  static const lineChart = 'lineChart';
  static const barChart = 'barChart';
  static const tableView = 'tableView';
  static const treeMap = 'treeMap';
  static const summation = 'summation';
  static const subtraction = 'subtraction';
  static const multiplication = 'multiplication';
  static const division = 'division';
  static const compute = 'compute';
  static const apply = 'apply';
  static const areYouSure = 'areYouSure';
  static const clearDataWarning = 'clearDataWarning';
  static const proceed = 'proceed';
  static const downloadPDF = 'downloadPDF';
  static const downloadImage = 'downloadImage';
  static const modifyDrivers = 'modifyDrivers';
  static const continueAction = 'continueAction';
  static const selectDomains = 'selectDomains';
  static const exploreNow = 'exploreNow';
  static const compare = 'compare';
  static const updateCollection = 'updateCollection';
  static const save = 'save';
  static const update = 'update';
  static const collectionUpdated = 'collectionUpdated';
  static const collectionCreated = 'collectionCreated';
  static const enterCollectionName = 'enterCollectionName';
  static const duplicateCollectionName = 'duplicateCollectionName';
  static const pleaseWait = 'pleaseWait';
  static const user = 'user';
  static const noDataFound = 'noDataFound';
  static const inputOfficialEmail = 'inputOfficialEmail';
  static const inputPassword = 'inputPassword';
  static const errorLoadingData = 'errorLoadingData';
  static const forYou = 'forYou';
  static const edit = 'edit';
  static const delete = 'delete';
  static const passwordReset = 'passwordReset';
  static const passwordResetSuccess = 'passwordResetSuccess';
  static const statisticsInterest = 'statisticsInterest';
  static const selectSuggestion = 'selectSuggestion';
  static const selectTopic = 'selectTopic';
  static const enterExplanation = 'enterExplanation';
  static const selectFileSize = 'selectFileSize';
  static const writeFeedback = 'writeFeedback';
  static const unitOfMeasurement = 'unitOfMeasurement';
  static const enterHere = 'enterHere';
  static const enterNewIndicatorName = 'enterNewIndicatorName';
  static const selectOtherIndicators = 'selectOtherIndicators';
  static const setUpPassword = 'setUpPassword';
  static const setUpPasswordDesc = 'setUpPasswordDesc';
  static const forgotPasswordDesc = 'forgotPasswordDesc';
  static const askForHelp = 'askForHelp';
  static const metaData = 'metaData';
  static const reset = 'reset';
  static const reload = 'reload';
  static const computation = 'computation';
  static const selectFirstIndicator = 'selectFirstIndicator';
  static const invalidEmail = 'invalidEmail';
  static const create = 'create';
  static const resendOTP = 'resendOTP';
  static const enterPassKeyTo = 'enterPassKeyTo';
  static const reEnterPassword = 'reEnterPassword';
  static const passwordAlreadySet = 'passwordAlreadySet';
  static const otpSentForPasswordSetting = 'otpSentForPasswordSetting';
  static const appUser = 'appUser';
  static const upTo = 'upTo';
  static const downTo = 'downTo';
  static const takeATour = 'takeATour';
  static const dashboards = 'dashboards';
  static const onboarding1 = 'onboarding1';
  static const onboarding2 = 'onboarding2';
  static const onboarding3 = 'onboarding3';
  static const onboarding4 = 'onboarding4';
  static const contactUsSuccess = 'contactUsSuccess';
  static const indicators = 'indicators';
  static const publications = 'publications';
  static const downloading = 'downloading';
  static const tableauDashboard = 'tableauDashboard';
  static const webReports = 'webReports';
  static const insightsDiscovery = 'insightsDiscovery';
  static const reports = 'reports';
  static const geoSpatial = 'geoSpatial';
  static const forecasts = 'forecasts';
  static const fileDownloaded = 'fileDownloaded';
  static const fileIsSavedTo = 'fileIsSavedTo';
  static const sensitiveInformation = 'sensitiveInformation';
  static const copyright = 'copyright';
  static const compareIndicatorsResult = 'compareIndicatorsResult';
  static const awaitingResponseFromSme = 'awaitingResponseFromSme';
  static const chatEnded = 'chatEnded';
  static const enterYourFeedback = 'enterYourFeedback';
  static const feedbackFieldError = 'feedbackFieldError';
  static const seeMore = 'seeMore';
  static const seeLess = 'seeLess';
  static const collectionError = 'collectionError';
  static const emptyCollectionNameError = 'emptyCollectionNameError';
  static const computeValidation = 'computeValidation';
  static const inactiveProfileStatusMessage = 'inactiveProfileStatusMessage';
  static const chatThreadsGuideDesc = 'chatThreadsGuideDesc';
  static const startChatGuideDesc = 'startChatGuideDesc';
  static const mostPopularFAQGuideDesc = 'mostPopularFAQGuideDesc';
  static const addAppsGuideDesc = 'addAppsGuideDesc';
  static const createNewCollection = 'createNewCollection';
  static const createNewCollectionGuideDesc = 'createNewCollectionGuideDesc';
  static const changeDataFrequencyGuideDesc = 'changeDataFrequencyGuideDesc';
  static const changeDataPresentationGuideDesc = 'changeDataPresentationGuideDesc';
  static const compareDataGuideDesc = 'compareDataGuideDesc';
  static const computeDataGuideDesc = 'computeDataGuideDesc';
  static const chatOption = 'chatOption';
  static const chatOptionGuideDesc = 'chatOptionGuideDesc';
  static const downloadAsGuideDesc = 'downloadAsGuideDesc';
  static const compareIndicatorLabel = 'compareIndicatorLabel';
  static const compareIndicatorError = 'compareIndicatorError';
  static const searchQueryError = 'searchQueryError';
  static const indicatorComparisonError = 'indicatorComparisonError';
  static const globalSearch = 'globalSearch';
  static const globalSearchGuideDesc = 'globalSearchGuideDesc';
  static const notificationsGuideDesc = 'notificationsGuideDesc';
  static const tableauDashboardGuideDesc = 'tableauDashboardGuideDesc';
  static const webReportGuideDesc = 'webReportGuideDesc';
  static const insightsDiscoveryGuideDesc = 'insightsDiscoveryGuideDesc';
  static const scenarioDriversGuideDesc = 'scenarioDriversGuideDesc';
  static const forecastGuideDesc = 'forecastGuideDesc';
  static const reportsGuideDesc = 'reportsGuideDesc';
  static const publicationsGuideDesc = 'publicationsGuideDesc';
  static const indicatorNameMetaInfo = 'indicatorNameMetaInfo';
  static const indicatorDescriptionMetaInfo = 'indicatorDescriptionMetaInfo';
  static const dataSourcesMetaInfo = 'dataSourcesMetaInfo';
  static const statisticalCalculationMethodMetaInfo = 'statisticalCalculationMethodMetaInfo';
  static const unknownErrorOccurred = 'unknownErrorOccurred';
  static const read = 'read';
  static const unread = 'unread';
  static const last10Days = 'last10Days';
  static const lastMonth = 'lastMonth';
  static const sameIndicatorComparisonError = 'sameIndicatorComparisonError';
  static const filterEmptyMessage = 'filterEmptyMessage';
  static const termsAndConditionsWarning = 'termsAndConditionsWarning';
  static const image = 'image';
  static const pdf = 'pdf';
  static const excel = 'excel';
  static const helpUsHint = 'helpUsHint';
  static const experimentalStatistics = 'experimentalStatistics';
  static const themeRequired = 'themeRequired';
  static const domainRequired = 'domainRequired';
  static const subThemeRequired = 'subThemeRequired';
  static const subjectRequired = 'subjectRequired';
  static const officialStatistics = 'officialStatistics';
  static const INDICATOR_ID = 'INDICATOR_ID';
  static const RUN_SEQ_ID = 'RUN_SEQ_ID';
  static const RUN_DT = 'RUN_DT';
  static const VALUE = 'VALUE';
  static const VALUE_LL = 'VALUE_LL';
  static const VALUE_UL = 'VALUE_UL';
  static const UNIT = 'UNIT';
  static const OBS_DT = 'OBS_DT';
  static const OPT = 'OPT';
  static const TYPE = 'TYPE';
  static const OIL_NONOIL = 'OIL_NONOIL';
  static const SECTOR = 'SECTOR';
  static const INDUSTRY = 'INDUSTRY';
  static const PARAMETER_COMBO_ID = 'PARAMETER_COMBO_ID';
  static const SECTOR_AR = 'SECTOR_AR';
  static const VALUE_FORECAST = 'VALUE_FORECAST';
  static const OBS_DT_CUR = 'OBS_DT_CUR';
  static const VALUE_PERC_ECO = 'VALUE_PERC_ECO';
  static const VALUE_CURRENT = 'VALUE_CURRENT';
  static const CHANGE = 'CHANGE';
  static const CHANGE_PY = 'CHANGE_PY';
  static const LANGUAGE_CD = 'LANGUAGE_CD';
  static const uaePassButtonText = 'uaePassButtonText';
  static const feedbackSubmittedSuccessfully = 'feedbackSubmittedSuccessfully';
  static const changeProfilePicture = 'changeProfilePicture';
  static const deleteCurrentPicture = 'deleteCurrentPicture';
  static const showLess = 'showLess';
  static const showMore = 'showMore';
  static const permissionDenied = 'permissionDenied';
  static const openAppSettingsForPermission = 'openAppSettingsForPermission';
  static const tableauDashboards = 'tableauDashboards';
  static const scenarioDriver = 'scenarioDriver';
  static const forcastHome = 'forcastHome';
  static const insightsDiscoverys = 'insightsDiscoverys';
  static const emirati = 'emirati';
  static const nonEmirati = 'nonEmirati';
  static const totalPopulation = 'totalPopulation';
  static const byNationality = 'byNationality';
  static const byGender = 'byGender';
  static const gender = 'gender';
  static const nationality = 'nationality';
  static const byNationals = 'byNationals';
  static const byDistricts = 'byDistricts';
  static const genderMale = 'genderMale';
  static const genderFemale = 'genderFemale';
  static const populationOverview = 'populationOverview';
  static const householdPopulation = 'householdPopulation';
  static const populationDotRatio = 'populationDotRatio';
  static const pointOfInterest = 'pointOfInterest';
  static const distanceMeasurement = 'distanceMeasurement';
  static const areaMeasurement = 'areaMeasurement';
  static const distance = 'distance';
  static const theme = 'theme';
  static const subTheme = 'subTheme';
  static const methodology = 'methodology';
  static const district = 'district';
  static const population = 'population';
  static const area = 'area';
  static const byEthnicity = 'byEthnicity';
  static const byBedrooms = 'byBedrooms';
  static const realEstate = 'realEstate';
  static const flatTransactions = 'flatTransactions';
  static const monthWiseAvgSum = 'monthWiseAvgSum';
  static const perimeter = 'perimeter';
  static const newMeasurement = 'newMeasurement';
  static const smallAreaSelection = 'smallAreaSelection';
  static const sumSellingPrice = 'sumSellingPrice';
  static const avgSellingPrice = 'avgSellingPrice';
  static const spatialAnalysis = 'spatialAnalysis';
  static const bySpecialization = 'bySpecialization';
  static const byJobVsVacancies = 'byJobVsVacancies';
  static const byVacancies = 'byVacancies';
  static const workExperience = 'workExperience';
  static const jobSeekers = 'jobSeekers';
  static const experience = 'experience';
  static const totalNumbers = 'totalNumbers';
  static const averageSale = 'averageSale';
  static const transactionsValue = 'transactionsValue';
  static const jobSeekersByYearsOfExperience = 'jobSeekersByYearsOfExperience';
  static const jobVacanciesByHighDegreeSpecialization = 'jobVacanciesByHighDegreeSpecialization';
  static const jobSeekersVsJobVacancies = 'jobSeekersVsJobVacancies';
  static const jobSeekersHighDegreeSpecialization = 'jobSeekersHighDegreeSpecialization';
  static const averageSellingPriceByBedroomType = 'averageSellingPriceByBedroomType';
  static const monthlyAverageOrSumOfTransactions = 'monthlyAverageOrSumOfTransactions';
  static const averageSaleByDistrict = 'averageSaleByDistrict';
  static const abuDhabi = 'abuDhabi';
  static const experimental = 'experimental';
  static const experimentalDescription = 'experimentalDescription';
  static const official = 'official';
  static const officialDescription = 'officialDescription';
  static const myPanel = 'myPanel';
  static const Ab = 'Ab';
  static const theFirstIndicatorIsrequired = 'theFirstIndicatorIsrequired';
  static const atLeastOneSecondIndicatorShouldBeSelected = 'atLeastOneSecondIndicatorShouldBeSelected';
  static const computeDataBadge = 'computeDataBadge';
  static const compareStatistics = 'compareStatistics';
  static const unableToProcessTheRequest = 'unableToProcessTheRequest';
  static const unknownResponse = 'unknownResponse';
  static const requestedResourceIsMissing = 'requestedResourceIsMissing';
  static const sessionExpired = 'sessionExpired';
  static const unauthorized = 'unauthorized';
  static const badRequest = 'badRequest';
  static const badResponse = 'badResponse';
  static const networkError = 'networkError';
  static const requestHasBeenTimedOut = 'requestHasBeenTimedOut';
  static const Monthly = 'Monthly';
  static const Quarterly = 'Quarterly';
  static const Yearly = 'Yearly';
  static const unableToDownloadFile = 'unableToDownloadFile';
  static const faq = 'faq';

}
