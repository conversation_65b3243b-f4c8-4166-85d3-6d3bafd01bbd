PODS:
  - audioplayers_darwin (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - FMDB (2.7.11):
    - FMDB/standard (= 2.7.11)
  - FMDB/standard (2.7.11)
  - IOSSecuritySuite (2.1.0)
  - open_filex (0.0.2):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - root_jailbreak_sniffer (1.0.6):
    - Flutter
    - IOSSecuritySuite
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite (0.0.3):
    - Flutter
    - FMDB (>= 2.7.5)
  - Toaster (2.3.0)
  - url_launcher_ios (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter

DEPENDENCIES:
  - audioplayers_darwin (from `.symlinks/plugins/audioplayers_darwin/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - Flutter (from `Flutter`)
  - open_filex (from `.symlinks/plugins/open_filex/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - root_jailbreak_sniffer (from `.symlinks/plugins/root_jailbreak_sniffer/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite (from `.symlinks/plugins/sqflite/ios`)
  - Toaster
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)

SPEC REPOS:
  trunk:
    - FMDB
    - IOSSecuritySuite
    - Toaster

EXTERNAL SOURCES:
  audioplayers_darwin:
    :path: ".symlinks/plugins/audioplayers_darwin/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  Flutter:
    :path: Flutter
  open_filex:
    :path: ".symlinks/plugins/open_filex/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  root_jailbreak_sniffer:
    :path: ".symlinks/plugins/root_jailbreak_sniffer/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite:
    :path: ".symlinks/plugins/sqflite/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"

SPEC CHECKSUMS:
  audioplayers_darwin: 877d9a4d06331c5c374595e46e16453ac7eafa40
  device_info_plus: c6fb39579d0f423935b0c9ce7ee2f44b71b9fce6
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  FMDB: 57486c1117fd8e0e6b947b2f54c3f42bf8e57a4e
  IOSSecuritySuite: 45e8531b05ffa72b5661cbdb5b5b5648a8de1a84
  open_filex: 6e26e659846ec990262224a12ef1c528bb4edbe4
  package_info_plus: 115f4ad11e0698c8c1c5d8a689390df880f47e85
  path_provider_foundation: 29f094ae23ebbca9d3d0cec13889cd9060c0e943
  permission_handler_apple: 036b856153a2b1f61f21030ff725f3e6fece2b78
  root_jailbreak_sniffer: e0be4831c81d09059597eeff6804122d22eb9e6a
  shared_preferences_foundation: 5b919d13b803cadd15ed2dc053125c68730e5126
  sqflite: 31f7eba61e3074736dff8807a9b41581e4f7f15a
  Toaster: c3473963c78e8cabbf6ea6f11ad0fdaae6f54987
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  webview_flutter_wkwebview: 2e2d318f21a5e036e2c3f26171342e95908bd60a

PODFILE CHECKSUM: 78be3a9c799e6d98efefff1c4919d46de1a49668

COCOAPODS: 1.15.2
