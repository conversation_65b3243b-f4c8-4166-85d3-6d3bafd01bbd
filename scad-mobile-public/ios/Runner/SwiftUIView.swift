////
////  SwiftUIView.swift
////  Runner
////
////  Created by SHARIS on 22/02/24.
////
//
//import SwiftUI
//import ArcGIS
//import Toaster
//import Combine
//import Flutter
//
//@available(iOS 15.0, *)
//struct SwiftUIView: View {
//    
//    // MARK: - variables
//    private let messageKey = "message_key"
//    private let outMessageKeyValueMapReady = "map_ready"
//    private let outMessageKeyValueApiResponse = "api_response"
//    private let outMessageKeyValueDistrictSelected = "district_selected"
//    private let inMessageKeyValueInitMap = "init_map"
//    private let inMessageKeyValueCallApi = "call_api"
//    private let inMessageKeyValueModuleChanged = "module_changed"
//    private let inMessageKeyValueUpdateLayer = "update_layer"
//    private let inMessageKeyValuePOI = "point_of_interest"
//    private let eventChannelName = "arcgisMapEvent"
//    private let methodChannelName = "arcgisMapMethod"
//    private var eventHandler: EventHandler = EventHandler()
//    private var activeMapLayerList: [String] = []
//    var cancellables: Set<AnyCancellable> = []
//    private var mapViewProxy: MapViewProxy?
//    
//    // MARK: - function for showing toast messages
//    private func toast(message: String) {
//        Toast(text: message, delay: Delay.short, duration: Delay.short).show()
//    }
//    
//    // MARK: - function for adding map layer
//    private mutating func addMapLayer(title: String, element: Layer) {
//        activeMapLayerList.append(title)
//        map().addOperationalLayer(element)
//        
//        Task {
//            //            mapView.setViewpointScale(mapView.mapScale.value + 5)
//            //            mapView.requestFocus()
//        }
//    }
//    
//    // MARK: - function for removing map layer
//    private mutating func removeMapLayer(title: String) {
//        guard var index = activeMapLayerList.firstIndex(of: title) else {
//            return print("index is null \(String(describing: index))")
//        }
//        activeMapLayerList.remove(atOffsets: IndexSet(integer: index))
//        map().removeOperationalLayer(map().operationalLayers[index])
//    }
//    
//    // MARK: - function for clearing map layers
//    private mutating func clearMapLayers() {
//        if map().operationalLayers.isEmpty == false {
//            guard let index = activeMapLayerList.firstIndex(of: "district") else {
//                return print("index is null")
//            }
//            let layer = map().operationalLayers[index]
//            map().removeAllOperationalLayers()
//            activeMapLayerList.removeAll()
//            addMapLayer(title: "district", element: layer)
//        }
//    }
//    
//    // MARK: - district layer load function
//    private mutating func loadLayerDistrict(url: String) {
//        var localSelf = self
//        
//        let layerRenderer = SimpleRenderer(
//            symbol: SimpleFillSymbol(
//                style: .solid,
//                color: UIColor(red: 0.2, green: 0.5, blue: 0.5, alpha: 0.5),
//                outline: SimpleLineSymbol(
//                    style: .solid,
//                    color: UIColor(red: 0.5, green: 0.5, blue: 0.5, alpha: 1.0),
//                    width: 1.0
//                )
//            )
//        )
//        
//        DispatchQueue.global(qos: .background).async {
//            Task {
//                do {
//                    let serviceFeatureTable = try ServiceFeatureTable(url: URL(string: url)!)
//                    
//                    var queryParameters = QueryParameters()
//                    queryParameters.whereClause = "1=1"
//                    
//                    guard let featureQueryResult = try? await serviceFeatureTable.queryFeatures(
//                        using: queryParameters, queryFeatureFields: .loadAll
//                    ) else {
//                        toast(message: "Unable to load map data")
//                        print("loadDistricts: featureQueryResult == null")
//                        return
//                    }
//                    
//                    var feature = Array(featureQueryResult.features()).first
//                    
//                    var featureTable: FeatureTable? = feature?.table
//                    if (featureTable == nil) {
//                        toast(message: "Unable to load map data")
//                        print("loadDistricts: featureQueryResult == null")
//                    } else {
//                        districtFeatureLayer = FeatureLayer(featureTable: featureTable!)
//                        districtFeatureLayer?.renderer = layerRenderer
//                        localSelf.addMapLayer(title: "District",element: districtFeatureLayer!)
//                        
//                        let attributesArray = Array(featureQueryResult.features()).map { $0.attributes }
//                        let attributesJSONData = try? JSONSerialization.data(withJSONObject: attributesArray, options: [])
//                        let attributesJSONString = String(data: attributesJSONData ?? Data(), encoding: .utf8) ?? ""
//                        
//                        let messageDict: [String: Any] = [
//                            "key": "district_list",
//                            "url": url,
//                            "attributes": attributesJSONString
//                        ]
//                        
//                        self.sendMessageToFlutter(messageType: outMessageKeyValueApiResponse, dataHashMap: messageDict)
//                        
//                        
//                        
////                        map().onSingleTapConfirmed.sink { tapEvent in
////                            let screenCoordinate = tapEvent.screenCoordinate
////                            getSelectedFeatureLayer(identifyPoint)
////                        }.store(in: &cancellables)
//                             
////                        mapViewProxy?.onSingleTapConformed.sink {
////                            
////                        }
//                        
//                    }
//                } catch {
//                    toast(message: "Unable to load map data")
//                    print("loadDistricts: featureQueryResult == null")
//                }
//            }
//        }
//    }
//    
//    // MARK: - function for query
//    func query(url: String) async -> FeatureTable? {
//        var whereCondition = "1=1"
//        let serviceFeatureTable = ServiceFeatureTable(url: URL(string: url)!)
//        let queryParameters = QueryParameters()
//        queryParameters.whereClause = whereCondition
//        
//        guard let featureQueryResult = try? await serviceFeatureTable.queryFeatures(
//            using: queryParameters, queryFeatureFields: .loadAll
//        ) else {
//            print("query: featureQueryResult == nil")
//            return nil
//        }
//        
//        let feature = Array(featureQueryResult.features()).first
//        
//        let attributeDictionaries = Array(featureQueryResult.features()).map { $0.attributes }
//        
//        do {
//            // Convert the array of dictionaries to JSON data
//            let jsonData = try JSONSerialization.data(withJSONObject: attributeDictionaries, options: [])
//            
//            // Convert JSON data to a string
//            if let jsonString = String(data: jsonData, encoding: .utf8) {
//                
//                sendMessageToFlutter(
//                    messageType: outMessageKeyValueApiResponse,
//                    dataHashMap: [
//                        "url": url,
//                        "attributes": jsonString
//                    ]
//                )
//            } else {
//                print("Error converting JSON data to string.")
//            }
//        } catch {
//            print("Error converting to JSON data: \(error)")
//        }
//        
//        return feature?.table
//    }
//    
//    // MARK: - function for loading POI layer
//    func loadPOILayer(title: String, url: String, imgUrl: String) {
//        if !activeMapLayerList.contains(title) {
//            Task {
//                do {
//                    let featureTable = try await ServiceFeatureTable(url: URL(string: url)!)
//                    let featureLayer = FeatureLayer(featureTable: featureTable)
//                    
//                    //                    addMapLayer(title, featureLayer)
//                } catch {
//                    print("Error creating FeatureLayer: \(error)")
//                }
//            }
//        }
//    }
//    
//    // MARK: - function to send messages to flutter
//    private func sendMessageToFlutter(messageType: String, dataHashMap: [String: Any]?) {
//        var hashMap: [String: Any] = ["message_key": messageType]
//        
//        if let dataHashMap = dataHashMap {
//            hashMap.merge(dataHashMap) { (_, new) in new }
//        }
//        
//        print("sendMessageToFlutter: \(hashMap)")
//        
//        DispatchQueue.main.async {
//            eventHandler.eventSink?(hashMap)
//        }
//    }
//    
//    // MARK: - function to handle incoming messages from flutter
//    private mutating func handleIncomingMessage(call: FlutterMethodCall) {
//        print("handleIncomingMessage: method:\(call.method)\narguments:\(String(describing: call.arguments))")
//        
//        switch call.method {
//        case inMessageKeyValueInitMap:
//            if let arguments = call.arguments as? [String: Any],
//               let districtLayer = arguments["district_layer"] as? String {
//                loadLayerDistrict(url: districtLayer)
//            }
//        case inMessageKeyValueModuleChanged:
//            districtFeatureLayer?.clearSelection()
//            if let arguments = call.arguments as? [String: Any],
//               let module = arguments["module"] as? String,
//               let layerUrls = arguments["layerUrls"] as? [String: String] {
//                
//                switch module {
//                case "Population Overview":
//                    if let dotDensityLayerUrl = layerUrls["dotDensityLayerUrl"] {
//                        loadPopulationOverviewLayer(populationLayerUrl: dotDensityLayerUrl)
//                    }
//                case "Household Population":
//                    if let populationLayerUrl = layerUrls["populationLayerUrl"] {
//                        loadHouseholdPopulationLayer(populationLayerUrl: populationLayerUrl)
//                    }
//                case "Job Seekers":
//                    if let jobSeekersLayerUrl = layerUrls["jobSeekersLayerUrl"],
//                       let jobSeekersPlaceNameLayerUrl = layerUrls["jobSeekersPlaceNameLayerUrl"],
//                       let jobSeekersGreyShadeLayerUrl = layerUrls["jobSeekersGreyShadeLayerUrl"] {
//                        loadJobSeekersLayer(
//                            jobSeekersLayerUrl: jobSeekersLayerUrl,
//                            jobSeekersPlaceNameLayerUrl: jobSeekersPlaceNameLayerUrl,
//                            jobSeekersGreyShadeLayerUrl: jobSeekersGreyShadeLayerUrl
//                        )
//                    }
//                case "Flat Transaction":
//                    if let realEstateDataLayerUrl = layerUrls["realEstateDataLayerUrl"] {
//                        loadRealEstateDataLayer(realEstateDataLayerUrl: realEstateDataLayerUrl)
//                    }
//                default:
//                    break
//                }
//            }
//        case inMessageKeyValueUpdateLayer:
//            if let arguments = call.arguments as? [String: Any],
//               let module = arguments["module"] as? String,
//               module == "Household Population",
//               let data = arguments["data"] as? [String: Any],
//               let showNonUae = data["showNonUae"] as? Bool,
//               let showUae = data["showUae"] as? Bool {
//                householdPopulationLayerUpdate(showNonUae: showNonUae, showUae: showUae)
//            }
//        case inMessageKeyValuePOI:
//            guard let arguments = call.arguments as? [String: Any],
//                  let data = arguments["poi"] as? [[String: Any]] else { return }
//
//            let tempList = activeMapLayerList
//
//            for layer in tempList {
//                if layer.hasPrefix("poi-") {
//                    removeMapLayer(title: layer)
//                }
//            }
//
//            for poi in data {
//                guard let title = poi["title"] as? String,
//                      let url = poi["url"] as? String,
//                      let imgUrl = poi["imgUrl"] as? String else {
//                    continue
//                }
//
//                loadPOILayer(title: title, url: url, imgUrl: imgUrl)
//            }
//        default:
//            print("invalid")
//        }
//    }
//    
//    // MARK: - function to load population overview layer
//    private mutating func loadPopulationOverviewLayer(populationLayerUrl: String?) {
//        var localSelf = self
//        DispatchQueue.global(qos: .background).async {
//            Task {
//                do {
//                    localSelf.clearMapLayers()
//                    
//                    guard let populationLayerUrl = populationLayerUrl,
//                          let featureQueryResult = try? await localSelf.getFeatureQueryResult(url: populationLayerUrl),
//                          let feature = Array(featureQueryResult.features()).first,
//                          let featureTable = feature.table else {
//                        print("loadPopulationOverviewLayer: featureTable is null")
//                        return
//                    }
//                    
//                    let featureLayer = FeatureLayer(featureTable: featureTable)
//                    localSelf.populationOverviewLayerAdd(featureLayer: featureLayer, showNonUae: true, showUae: true)
//                } catch {
//                    localSelf.toast(message: error.localizedDescription)
//                    print("Unable to load layer: \(error.localizedDescription)")
//                }
//            }
//        }
//        self = localSelf
//    }
//    
//    // MARK: - function to load household population layer
//    private mutating func loadHouseholdPopulationLayer(populationLayerUrl: String?) {
//        var localSelf = self
//        DispatchQueue.global(qos: .background).async {
//            Task {
//                do {
//                    localSelf.clearMapLayers()
//                    
//                    guard let populationLayerUrl = populationLayerUrl,
//                          let featureQueryResult = try? await localSelf.getFeatureQueryResult(url: populationLayerUrl),
//                          let feature = Array(featureQueryResult.features()).first,
//                          let featureTable = feature.table else {
//                        print("loadHouseholdPopulationLayer: featureTable is null")
//                        return
//                    }
//                    
//                    let featureLayer = FeatureLayer(featureTable: featureTable)
//                    localSelf.householdPopulationLayerAdd(featureLayer: featureLayer, showNonUae: true, showUae: true)
//                } catch {
//                    localSelf.toast(message: error.localizedDescription)
//                    print("Unable to load layer: \(error.localizedDescription)")
//                }
//            }
//        }
//        self = localSelf
//    }
//    
//    // MARK: - function to load job seekers layer
//    private mutating func loadJobSeekersLayer(
//        jobSeekersLayerUrl: String?,
//        jobSeekersPlaceNameLayerUrl: String?,
//        jobSeekersGreyShadeLayerUrl: String?
//    ) {
//        var localSelf = self
//        DispatchQueue.global(qos: .background).async {
//            Task {
//                do {
//                    localSelf.clearMapLayers()
//
//                    guard let jobSeekersLayerUrl = jobSeekersLayerUrl,
//                          let featureQueryResultJobSeekersLayer = try? await localSelf.getFeatureQueryResult(url: jobSeekersLayerUrl) else {
//                        print("loadJobSeekersLayer: featureQueryResultJobSeekersLayer is null")
//                        return
//                    }
//                    
//                    var a = Array(featureQueryResultJobSeekersLayer.features()).first?.table;
//
//                    if let featureTable = a {
//                        localSelf.addMapLayer(
//                            title: "JobSeekers",
//                            element: FeatureLayer(featureTable: featureTable)
//                        )
//                    }
//                } catch {
//                    localSelf.toast(message: error.localizedDescription)
//                    print("Unable to load layer: \(error.localizedDescription)")
//                }
//            }
//        }
//        self = localSelf
//    }
//    
//    // MARK: - function to load real estate layer
//    private mutating func loadRealEstateDataLayer(realEstateDataLayerUrl: String?) {
//        var localSelf = self
//        DispatchQueue.global().async {
//            Task {
//                do {
//                    localSelf.clearMapLayers()
//                    
//                    let realEstateDataLayerResult = try await localSelf.getFeatureQueryResult(url: realEstateDataLayerUrl!)
//                    
//                    guard let feature = Array(realEstateDataLayerResult.features()).first,
//                          let featureTable = feature.table else {
//                        print("Real Estate Data layer feature table is nil")
//                        return
//                    }
//                    
//                    let featureLayer = FeatureLayer(featureTable: featureTable)
//                    localSelf.addMapLayer(title: "RealEstateData", element: featureLayer)
//                    
//                } catch {
//                    localSelf.toast(message: error.localizedDescription)
//                    print("Unable to load Real Estate Data layer: \(error.localizedDescription)")
//                }
//            }
//        }
//        self = localSelf
//    }
//    
//    // MARK: - function to update household population layer
//    private mutating func householdPopulationLayerUpdate(showNonUae: Bool, showUae: Bool) {
//        guard let index = activeMapLayerList.firstIndex(of: "householdPopulation"),
//              let featureLayer = map().operationalLayers[index] as? FeatureLayer else {
//            return
//        }
//        
//        removeMapLayer(title: activeMapLayerList[index])
//        
//        householdPopulationLayerAdd(featureLayer: featureLayer, showNonUae: showNonUae, showUae: showUae)
//    }
//
//    // MARK: - function getFeatureQueryResult
//    private func getFeatureQueryResult(url: String, whereCondition: String = "1=1") async throws -> FeatureQueryResult {
//        let serviceFeatureTable = ServiceFeatureTable(url: URL(string: url)!)
//        var queryParameters = QueryParameters()
//        queryParameters.whereClause = whereCondition
//        
//        do {
//            let featureQueryResult = try await serviceFeatureTable.queryFeatures(
//                using: queryParameters,
//                queryFeatureFields: .loadAll
//            )
//            return featureQueryResult
//        } catch {
//            throw error
//        }
//    }
//    
//    // MARK: - function populationOverviewLayerAdd
//    private mutating func populationOverviewLayerAdd(featureLayer: FeatureLayer, showNonUae: Bool, showUae: Bool) {
//        let symbolNonUae = SimpleMarkerSymbol(style: .circle, color: showNonUae ? .blue : .clear, size: 4)
//        let symbolUae = SimpleMarkerSymbol(style: .circle, color: showUae ? .red : .clear, size: 4)
//        
//        let uniqueValue1 = UniqueValue(
//            description: "total_non_citizen",
//            label: "total_non_citizen",
//            symbol: symbolNonUae,
//            values: ["15260"]
//        )
//        let uniqueValue2 = UniqueValue(
//            description: "total_citizen",
//            label: "total_citizen",
//            symbol: symbolUae,
//            values: ["44016"]
//        )
//        
//        let uniqueValueRenderer = UniqueValueRenderer(
//            fieldNames: ["total_non_citizen", "total_citizen"],
//            uniqueValues: [uniqueValue1, uniqueValue2]
//        )
//        
//        featureLayer.renderer = uniqueValueRenderer
//        
//        addMapLayer(title: "populationOverview", element: featureLayer)
//    }
//    
//    // MARK: - function householdPopulationLayerAdd
//    private mutating func householdPopulationLayerAdd(featureLayer: FeatureLayer, showNonUae: Bool, showUae: Bool) {
//        let symbolNonUae = SimpleMarkerSymbol(
//            style: .circle, color: showNonUae
//            ? .init(red: 0.349, green: 0.306, blue: 0.870, alpha: 1.0)
//            : .clear, size: 4.0
//        )
//        let symbolUae = SimpleMarkerSymbol(
//            style: .circle, color: showUae
//            ? .init(red: 0.843, green: 0.369, blue: 0.365, alpha: 1.0)
//            : .clear, size: 4.0
//        )
//        
//        let nonUaeUniqueValue = UniqueValue(
//            description: "premise_citizenship",
//            label: "premise_citizenship",
//            symbol: symbolNonUae,
//            values: ["NON_UAE"]
//        )
//        let uaeUniqueValue = UniqueValue(
//            description: "premise_citizenship",
//            label: "premise_citizenship",
//            symbol: symbolUae,
//            values: ["UAE"]
//        )
//    
//        let uniqueValueRenderer = UniqueValueRenderer(
//            fieldNames: ["premise_citizenship"],
//            uniqueValues: [nonUaeUniqueValue, uaeUniqueValue]
//        )
//        
//        featureLayer.renderer = uniqueValueRenderer
//        
//        addMapLayer(title: "householdPopulation", element: featureLayer)
//    }
//    
//    // MARK: - map view
//    @State private var map = {
//        
//        ArcGISEnvironment.apiKey = ArcGIS.APIKey(
//            rawValue: "AAPKdac197f8650a484aa13408fff3689970Or1lQ3e-m2s3BSQMBVnDBj8N4SFpAH5KEzKlN8rNNnZZKQjnt_kp-FmN-IYfGrJJ"
//        )
//        
//        let map = Map(basemapStyle: .arcGISNavigation)
//        
//        map.initialViewpoint = Viewpoint(
//            center: Point(x: -93.258133, y: 44.986656, spatialReference: .wgs84),
//            scale: 1_000_000
//        )
//        
//        return map
//    }
//    
//    @State private var viewpoint: Viewpoint?
//    
//    @State private var districtFeatureLayer: FeatureLayer? = nil
//    
//    // The point indicating where to identify features.
//    @State private var identifyPoint: CGPoint?
//    
//    @State var task: Task<Void, Never>? = nil
//    
//    
////    // MARK: - init
////    init(topController: UIViewController) {
////        let eventHandler = EventHandler()
////        
////        FlutterEventChannel(
////            name: eventChannelName, binaryMessenger: topController as! FlutterBinaryMessenger
////        ).setStreamHandler(eventHandler)
//////        
//////        var a = FlutterMethodChannel(name: methodChannelName, binaryMessenger: topController as! FlutterBinaryMessenger)
//////        a.setMethodCallHandler(<#FlutterMethodCallHandler?#>)
////        
////        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, channelName: methodChannelName)
////            .setMethodCallHandler { [weak self] call, result in
////                self?.handleIncomingMessage(call)
////                result.success(true)
////            }
////        
////        guard let baseMapPortalId = creationParams?["baseMapPortalId"] as? String,
////              let accessToken = creationParams?["token"] as? String else {
////            return
////        }
////        
////        ArcGISEnvironment.authenticationManager.arcGISAuthenticationChallengeHandler = { challenge in
////            let tokenInfo = TokenInfo(token: accessToken, expiresAt: Date().addingTimeInterval(8 * 60 * 60), isPersistent: true)
////            let preGeneratedTokenCredential = PregeneratedTokenCredential(requestURL: challenge.requestURL, tokenInfo: tokenInfo)
////            return ArcGISAuthenticationChallengeResponse.continue(with: preGeneratedTokenCredential)
////        }
////        
////        guard let inflater = context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as? LayoutInflater else {
////            return
////        }
////        view = inflater.inflate(R.layout.activity_main, nil)
////        
////        guard let mapView = view.findViewById(R.id.mapView) as? MapView else {
////            return
////        }
////        self.mapView = mapView
////        
////        mapView.map = ArcGISMap(portalItem: PortalItem(url: "https://www.arcgis.com", itemID: baseMapPortalId))
////        mapView.selectionProperties.color = .green
////        
////        if let mainActivity = MainActivity.getInstance() {
////            mainActivity.lifecycle.addObserver(mapView)
////        }
////        
////        Timer.scheduledTimer(withTimeInterval: 10, repeats: false) { [weak self] _ in
////            self?.sendMessageToFlutter(outMessageKeyValueMapReady, data: nil)
////        }
////        
////        mapView.setViewpoint(Viewpoint(center: Point(x: 24.485, y: 54.39, spatialReference: .wgs84()), scale: 179757.5))
////    }
////
////    
//    
//
//    
//
//    // MARK: - map view
//    var body: some View {
//        MapViewReader { mapViewProxy in
//            MapView(map: map())
//                .task(id: identifyPoint) {
//                    
//                    guard let identifyPoint = identifyPoint else { return }
//                    
//                    do {
//                        // clear the previous selection
//                        districtFeatureLayer?.clearSelection()
//                        
//                        // set a tolerance for accuracy of returned selections from point tapped
//                        let tolerance: Double = 0.0
//                        
//                        // Saves the results from the identify method on the map view proxy.
//                        let identifyLayerResult = try await mapViewProxy.identify(
//                            on: districtFeatureLayer!,
//                            screenPoint: identifyPoint,
//                            tolerance: 12,
//                            maximumResults: 10
//                        )
//                        
//                        // Updates the selected features to the geo elements from the results.
//                        let selectedFeatures = identifyLayerResult.geoElements as! [Feature]
//                        
//                        districtFeatureLayer?.selectFeatures(selectedFeatures)
//                        
//                        await mapViewProxy.setViewpointScale((map().referenceScale ?? 0.0) + 0.0000000000000001)
//                        
//                        let attributes = selectedFeatures.first?.attributes ?? [:]
//                        
//                        sendMessageToFlutter(
//                            messageType: outMessageKeyValueDistrictSelected,
//                            dataHashMap: ["attributes": attributes]
//                        )
//                    } catch {
//                        // Updates the error and shows an alert.
//                        toast(message: "Select feature failed: \(error.localizedDescription)")
//                    }
//                }
//        }
//    }
//}
//
//// MARK: - EventHandler class
//class EventHandler: NSObject, FlutterStreamHandler {
//    var eventSink: FlutterEventSink?
//    
//    func onListen(withArguments arguments: Any?, eventSink: @escaping FlutterEventSink) -> FlutterError? {
//        self.eventSink = eventSink
//        return nil
//    }
//    
//    func onCancel(withArguments arguments: Any?) -> FlutterError? {
//        eventSink = nil
//        return nil
//    }
//}
