name: scad_mobile
description: Bayaan App

publish_to: 'none'

version: 1.0.10+26

environment:
  sdk: '>=3.1.5 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  flutter_mute:
    path: ./libraries/flutter_mute
  root_jailbreak_sniffer:
    path: ./libraries/root_jailbreak_sniffer-1.0.6
  sound_mode:
    path: ./libraries/sound_mode

  dio: ^5.7.0
  get_it: ^8.0.3
  equatable: ^2.0.5
  local_auth: ^2.2.0
  jwt_decoder: ^2.0.1
  flutter_svg: ^2.0.10+1
  flutter_bloc: ^8.1.6
  google_fonts: ^6.2.1
  hive_flutter: ^1.1.0
  flutter_xlider: ^3.5.0
  path_provider: ^2.1.4
  easy_localization: ^3.0.7
  firebase_core: ^3.3.0
  firebase_analytics: ^11.2.1
  flutter_local_notifications: ^17.2.1+2
  shimmer: ^3.0.0
  flutter_rating_bar: ^4.0.1
  flutter_widget_from_html_core: ^0.15.1
  pinput: ^5.0.0
  dotted_border: ^2.1.0
  expandable: ^5.0.1
  dropdown_button2: ^2.3.9
  sliding_up_panel2: ^3.3.0+1
  timeline_tile: ^2.0.0
  flutter_chat_bubble: ^2.0.2
  overlay_support: ^2.1.0
  carousel_slider: ^5.0.0
  smooth_page_indicator: ^1.2.0+3
  flutter_inset_box_shadow: ^1.0.8
  scrollable_positioned_list: ^0.3.8
  syncfusion_flutter_sliders: ^29.1.38
  syncfusion_flutter_xlsio: ^29.1.38
  syncfusion_flutter_charts: ^29.1.38
  syncfusion_flutter_pdf: ^29.1.38
  syncfusion_flutter_core: ^29.1.38
  syncfusion_flutter_treemap: ^29.1.38
  syncfusion_flutter_datagrid: ^29.1.38
  flutter_pdfview: ^1.3.2
  image: ^4.2.0
  screenshot: ^3.0.0
  url_launcher: ^6.3.0
  package_info_plus: ^8.0.1
  cart_stepper: ^4.3.0
  webview_flutter: ^4.8.0
  lottie: ^3.1.2
  permission_handler: ^11.3.1
  device_info_plus: ^10.1.1
  flutter_typeahead: ^5.2.0
  go_router: ^14.2.2
  flutter_sliding_up_panel: ^2.1.1
  open_filex: ^4.4.0
  flutter_udid: ^3.0.0
  http_parser: ^4.0.2
  http_certificate_pinning: ^3.0.1
  real_volume: ^1.0.9
  audioplayers: ^6.0.0
  excel: ^4.0.6
  firebase_remote_config: ^5.4.2
  intl: ^0.20.2
  auto_route: ^10.1.0

dev_dependencies:
  auto_route_generator: ^10.1.0
  flutter_test:
    sdk: flutter
  build_runner: ^2.4.11
  flutter_lints: ^5.0.0
  very_good_analysis: ^6.0.0

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/images/products/
    - assets/images/animation/
    - assets/translations/
    - assets/images/temp/
    - assets/images/png/
    - assets/images/icons/
    - assets/temp_image/
    - assets/audio/
    - assets/images/animation/dark_theme/

