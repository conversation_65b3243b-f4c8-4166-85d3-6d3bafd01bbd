# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _fe_analyzer_shared:
    dependency: transitive
    description:
      name: _fe_analyzer_shared
      sha256: e55636ed79578b9abca5fecf9437947798f5ef7456308b5cb85720b793eac92f
      url: "https://pub.dev"
    source: hosted
    version: "82.0.0"
  _flutterfire_internals:
    dependency: transitive
    description:
      name: _flutterfire_internals
      sha256: "214e6f07e2a44f45972e0365c7b537eaeaddb4598db0778dd4ac64b4acd3f5b1"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.55"
  analyzer:
    dependency: transitive
    description:
      name: analyzer
      sha256: "904ae5bb474d32c38fb9482e2d925d5454cda04ddd0e55d2e6826bc72f6ba8c0"
      url: "https://pub.dev"
    source: hosted
    version: "7.4.5"
  ansicolor:
    dependency: transitive
    description:
      name: ansicolor
      sha256: "50e982d500bc863e1d703448afdbf9e5a72eb48840a4f766fa361ffd6877055f"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  archive:
    dependency: transitive
    description:
      name: archive
      sha256: cb6a278ef2dbb298455e1a713bda08524a175630ec643a242c399c932a0a1f7d
      url: "https://pub.dev"
    source: hosted
    version: "3.6.1"
  args:
    dependency: transitive
    description:
      name: args
      sha256: d0481093c50b1da8910eb0bb301626d4d8eb7284aa739614d2b394ee09e3ea04
      url: "https://pub.dev"
    source: hosted
    version: "2.7.0"
  async:
    dependency: transitive
    description:
      name: async
      sha256: "758e6d74e971c3e5aceb4110bfd6698efc7f501675bcfe0c775459a8140750eb"
      url: "https://pub.dev"
    source: hosted
    version: "2.13.0"
  audioplayers:
    dependency: "direct main"
    description:
      name: audioplayers
      sha256: a5341380a4f1d3a10a4edde5bb75de5127fe31e0faa8c4d860e64d2f91ad84c7
      url: "https://pub.dev"
    source: hosted
    version: "6.4.0"
  audioplayers_android:
    dependency: transitive
    description:
      name: audioplayers_android
      sha256: f8c90823a45b475d2c129f85bbda9c029c8d4450b172f62e066564c6e170f69a
      url: "https://pub.dev"
    source: hosted
    version: "5.2.0"
  audioplayers_darwin:
    dependency: transitive
    description:
      name: audioplayers_darwin
      sha256: "405cdbd53ebdb4623f1c5af69f275dad4f930ce895512d5261c07cd95d23e778"
      url: "https://pub.dev"
    source: hosted
    version: "6.2.0"
  audioplayers_linux:
    dependency: transitive
    description:
      name: audioplayers_linux
      sha256: "7e0d081a6a527c53aef9539691258a08ff69a7dc15ef6335fbea1b4b03ebbef0"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.0"
  audioplayers_platform_interface:
    dependency: transitive
    description:
      name: audioplayers_platform_interface
      sha256: "77e5fa20fb4a64709158391c75c1cca69a481d35dc879b519e350a05ff520373"
      url: "https://pub.dev"
    source: hosted
    version: "7.1.0"
  audioplayers_web:
    dependency: transitive
    description:
      name: audioplayers_web
      sha256: bd99d8821114747682a2be0adcdb70233d4697af989b549d3a20a0f49f6c9b13
      url: "https://pub.dev"
    source: hosted
    version: "5.1.0"
  audioplayers_windows:
    dependency: transitive
    description:
      name: audioplayers_windows
      sha256: "871d3831c25cd2408ddc552600fd4b32fba675943e319a41284704ee038ad563"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.0"
  auto_route:
    dependency: "direct main"
    description:
      name: auto_route
      sha256: b8c036fa613a98a759cf0fdcba26e62f4985dcbff01a5e760ab411e8554bbaf0
      url: "https://pub.dev"
    source: hosted
    version: "10.1.0+1"
  auto_route_generator:
    dependency: "direct dev"
    description:
      name: auto_route_generator
      sha256: "2a5b5bf9c55d4a2098931037dac90921a4663808aed494bb4f134d82d46cb8ec"
      url: "https://pub.dev"
    source: hosted
    version: "10.2.3"
  bloc:
    dependency: transitive
    description:
      name: bloc
      sha256: "106842ad6569f0b60297619e9e0b1885c2fb9bf84812935490e6c5275777804e"
      url: "https://pub.dev"
    source: hosted
    version: "8.1.4"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "8aab1771e1243a5063b8b0ff68042d67334e3feab9e95b9490f9a6ebf73b42ea"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  build:
    dependency: transitive
    description:
      name: build
      sha256: cef23f1eda9b57566c81e2133d196f8e3df48f244b317368d65c5943d91148f0
      url: "https://pub.dev"
    source: hosted
    version: "2.4.2"
  build_config:
    dependency: transitive
    description:
      name: build_config
      sha256: "4ae2de3e1e67ea270081eaee972e1bd8f027d459f249e0f1186730784c2e7e33"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  build_daemon:
    dependency: transitive
    description:
      name: build_daemon
      sha256: "8e928697a82be082206edb0b9c99c5a4ad6bc31c9e9b8b2f291ae65cd4a25daa"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.4"
  build_resolvers:
    dependency: transitive
    description:
      name: build_resolvers
      sha256: b9e4fda21d846e192628e7a4f6deda6888c36b5b69ba02ff291a01fd529140f0
      url: "https://pub.dev"
    source: hosted
    version: "2.4.4"
  build_runner:
    dependency: "direct dev"
    description:
      name: build_runner
      sha256: "058fe9dce1de7d69c4b84fada934df3e0153dd000758c4d65964d0166779aa99"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.15"
  build_runner_core:
    dependency: transitive
    description:
      name: build_runner_core
      sha256: "22e3aa1c80e0ada3722fe5b63fd43d9c8990759d0a2cf489c8c5d7b2bdebc021"
      url: "https://pub.dev"
    source: hosted
    version: "8.0.0"
  built_collection:
    dependency: transitive
    description:
      name: built_collection
      sha256: "376e3dd27b51ea877c28d525560790aee2e6fbb5f20e2f85d5081027d94e2100"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  built_value:
    dependency: transitive
    description:
      name: built_value
      sha256: "082001b5c3dc495d4a42f1d5789990505df20d8547d42507c29050af6933ee27"
      url: "https://pub.dev"
    source: hosted
    version: "8.10.1"
  carousel_slider:
    dependency: "direct main"
    description:
      name: carousel_slider
      sha256: "7b006ec356205054af5beaef62e2221160ea36b90fb70a35e4deacd49d0349ae"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  cart_stepper:
    dependency: "direct main"
    description:
      name: cart_stepper
      sha256: ae895502e082ce74f1a181acd6aad8240279c8e3cc534c126e7f6bfcd0c1842e
      url: "https://pub.dev"
    source: hosted
    version: "4.3.0"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: f71061c654a3380576a52b451dd5532377954cf9dbd272a78fc8479606670803
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  checked_yaml:
    dependency: transitive
    description:
      name: checked_yaml
      sha256: feb6bed21949061731a7a75fc5d2aa727cf160b91af9a3e464c5e3a32e28b5ff
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  cli_config:
    dependency: transitive
    description:
      name: cli_config
      sha256: ac20a183a07002b700f0c25e61b7ee46b23c309d76ab7b7640a028f18e4d99ec
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: fddb70d9b5277016c77a80201021d40a2247104d9f4aa7bab7157b7e3f05b84b
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  code_builder:
    dependency: transitive
    description:
      name: code_builder
      sha256: "0ec10bf4a89e4c613960bf1e8b42c64127021740fb21640c29c909826a5eea3e"
      url: "https://pub.dev"
    source: hosted
    version: "4.10.1"
  collection:
    dependency: transitive
    description:
      name: collection
      sha256: "2f5709ae4d3d59dd8f7cd309b4e023046b57d8a6c82130785d2b0e5868084e76"
      url: "https://pub.dev"
    source: hosted
    version: "1.19.1"
  convert:
    dependency: transitive
    description:
      name: convert
      sha256: b30acd5944035672bc15c6b7a8b47d773e41e2f17de064350988c5d02adb1c68
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  coverage:
    dependency: transitive
    description:
      name: coverage
      sha256: "4b8701e48a58f7712492c9b1f7ba0bb9d525644dd66d023b62e1fc8cdb560c8a"
      url: "https://pub.dev"
    source: hosted
    version: "1.14.0"
  crypto:
    dependency: transitive
    description:
      name: crypto
      sha256: "1e445881f28f22d6140f181e07737b22f1e099a5e1ff94b0af2f9e4a463f4855"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.6"
  csslib:
    dependency: transitive
    description:
      name: csslib
      sha256: "09bad715f418841f976c77db72d5398dc1253c21fb9c0c7f0b0b985860b2d58e"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  dart_style:
    dependency: transitive
    description:
      name: dart_style
      sha256: "5b236382b47ee411741447c1f1e111459c941ea1b3f2b540dde54c210a3662af"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  dbus:
    dependency: transitive
    description:
      name: dbus
      sha256: "79e0c23480ff85dc68de79e2cd6334add97e48f7f4865d17686dd6ea81a47e8c"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.11"
  device_info_plus:
    dependency: "direct main"
    description:
      name: device_info_plus
      sha256: a7fd703482b391a87d60b6061d04dfdeab07826b96f9abd8f5ed98068acc0074
      url: "https://pub.dev"
    source: hosted
    version: "10.1.2"
  device_info_plus_platform_interface:
    dependency: transitive
    description:
      name: device_info_plus_platform_interface
      sha256: "0b04e02b30791224b31969eb1b50d723498f402971bff3630bca2ba839bd1ed2"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.2"
  dio:
    dependency: "direct main"
    description:
      name: dio
      sha256: "253a18bbd4851fecba42f7343a1df3a9a4c1d31a2c1b37e221086b4fa8c8dbc9"
      url: "https://pub.dev"
    source: hosted
    version: "5.8.0+1"
  dio_web_adapter:
    dependency: transitive
    description:
      name: dio_web_adapter
      sha256: "7586e476d70caecaf1686d21eee7247ea43ef5c345eab9e0cc3583ff13378d78"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  dotted_border:
    dependency: "direct main"
    description:
      name: dotted_border
      sha256: "108837e11848ca776c53b30bc870086f84b62ed6e01c503ed976e8f8c7df9c04"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  dropdown_button2:
    dependency: "direct main"
    description:
      name: dropdown_button2
      sha256: b0fe8d49a030315e9eef6c7ac84ca964250155a6224d491c1365061bc974a9e1
      url: "https://pub.dev"
    source: hosted
    version: "2.3.9"
  easy_localization:
    dependency: "direct main"
    description:
      name: easy_localization
      sha256: "0f5239c7b8ab06c66440cfb0e9aa4b4640429c6668d5a42fe389c5de42220b12"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.7+1"
  easy_logger:
    dependency: transitive
    description:
      name: easy_logger
      sha256: c764a6e024846f33405a2342caf91c62e357c24b02c04dbc712ef232bf30ffb7
      url: "https://pub.dev"
    source: hosted
    version: "0.0.2"
  equatable:
    dependency: "direct main"
    description:
      name: equatable
      sha256: "567c64b3cb4cf82397aac55f4f0cbd3ca20d77c6c03bedbc4ceaddc08904aef7"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.7"
  excel:
    dependency: "direct main"
    description:
      name: excel
      sha256: "1a15327dcad260d5db21d1f6e04f04838109b39a2f6a84ea486ceda36e468780"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.6"
  expandable:
    dependency: "direct main"
    description:
      name: expandable
      sha256: "9604d612d4d1146dafa96c6d8eec9c2ff0994658d6d09fed720ab788c7f5afc2"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.1"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "5368f224a74523e8d2e7399ea1638b37aecfca824a3cc4dfdf77bf1fa905ac44"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.3"
  ffi:
    dependency: transitive
    description:
      name: ffi
      sha256: "289279317b4b16eb2bb7e271abccd4bf84ec9bdcbe999e278a94b804f5630418"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  file:
    dependency: transitive
    description:
      name: file
      sha256: a3b4f84adafef897088c160faf7dfffb7696046cb13ae90b508c2cbc95d3b8d4
      url: "https://pub.dev"
    source: hosted
    version: "7.0.1"
  firebase_analytics:
    dependency: "direct main"
    description:
      name: firebase_analytics
      sha256: b5b31727ad7c7f41e139637d1d582e435fd14054abcbd162c64476f90737105c
      url: "https://pub.dev"
    source: hosted
    version: "11.4.6"
  firebase_analytics_platform_interface:
    dependency: transitive
    description:
      name: firebase_analytics_platform_interface
      sha256: "86817ee7db6377e09830467932c31644713d8e8915cefff64610ad735612bc7d"
      url: "https://pub.dev"
    source: hosted
    version: "4.3.6"
  firebase_analytics_web:
    dependency: transitive
    description:
      name: firebase_analytics_web
      sha256: "4914e184a6aa37811d976db59f95fa8fda1d58db49ab7f73d4d8aac9dd9b19b7"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.10+12"
  firebase_core:
    dependency: "direct main"
    description:
      name: firebase_core
      sha256: "8cfe3c900512399ce8d50fcc817e5758ff8615eeb6fa5c846a4cc47bbf6353b6"
      url: "https://pub.dev"
    source: hosted
    version: "3.13.1"
  firebase_core_platform_interface:
    dependency: transitive
    description:
      name: firebase_core_platform_interface
      sha256: d7253d255ff10f85cfd2adaba9ac17bae878fa3ba577462451163bd9f1d1f0bf
      url: "https://pub.dev"
    source: hosted
    version: "5.4.0"
  firebase_core_web:
    dependency: transitive
    description:
      name: firebase_core_web
      sha256: ddd72baa6f727e5b23f32d9af23d7d453d67946f380bd9c21daf474ee0f7326e
      url: "https://pub.dev"
    source: hosted
    version: "2.23.0"
  firebase_remote_config:
    dependency: "direct main"
    description:
      name: firebase_remote_config
      sha256: e65062f7435c8be13826d38c75218b30251a096ca742e7da55342b33ff8b4749
      url: "https://pub.dev"
    source: hosted
    version: "5.4.4"
  firebase_remote_config_platform_interface:
    dependency: transitive
    description:
      name: firebase_remote_config_platform_interface
      sha256: "6d2f94570edbea0b1fb47e623af576f1896d14f839b1e3ba9b393a5e7c93c84d"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.4"
  firebase_remote_config_web:
    dependency: transitive
    description:
      name: firebase_remote_config_web
      sha256: "5cf6eff2748193b64e9eb9965d5d727585f562d2946a53239385ad667e1c22e4"
      url: "https://pub.dev"
    source: hosted
    version: "1.8.4"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      sha256: b6dc7065e46c974bc7c5f143080a6764ec7a4be6da1285ececdc37be96de53be
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_bloc:
    dependency: "direct main"
    description:
      name: flutter_bloc
      sha256: b594505eac31a0518bdcb4b5b79573b8d9117b193cc80cc12e17d639b10aa27a
      url: "https://pub.dev"
    source: hosted
    version: "8.1.6"
  flutter_chat_bubble:
    dependency: "direct main"
    description:
      name: flutter_chat_bubble
      sha256: ede33df7237fea3d89464c8493bfa0bee2f94f50fd097ebd30c517c3c0d4fcf9
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  flutter_inset_box_shadow:
    dependency: "direct main"
    description:
      name: flutter_inset_box_shadow
      sha256: "0b1930ed5f4ad84b11b41dda907fbc5d4c12929a4c51455c626e5937b47ffb93"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.8"
  flutter_keyboard_visibility:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility
      sha256: "98664be7be0e3ffca00de50f7f6a287ab62c763fc8c762e0a21584584a3ff4f8"
      url: "https://pub.dev"
    source: hosted
    version: "6.0.0"
  flutter_keyboard_visibility_linux:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_linux
      sha256: "6fba7cd9bb033b6ddd8c2beb4c99ad02d728f1e6e6d9b9446667398b2ac39f08"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  flutter_keyboard_visibility_macos:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_macos
      sha256: c5c49b16fff453dfdafdc16f26bdd8fb8d55812a1d50b0ce25fc8d9f2e53d086
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  flutter_keyboard_visibility_platform_interface:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_platform_interface
      sha256: e43a89845873f7be10cb3884345ceb9aebf00a659f479d1c8f4293fcb37022a4
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  flutter_keyboard_visibility_web:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_web
      sha256: d3771a2e752880c79203f8d80658401d0c998e4183edca05a149f5098ce6e3d1
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  flutter_keyboard_visibility_windows:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_windows
      sha256: fc4b0f0b6be9b93ae527f3d527fb56ee2d918cd88bbca438c478af7bcfd0ef73
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  flutter_lints:
    dependency: "direct dev"
    description:
      name: flutter_lints
      sha256: "5398f14efa795ffb7a33e9b6a08798b26a180edac4ad7db3f231e40f82ce11e1"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  flutter_local_notifications:
    dependency: "direct main"
    description:
      name: flutter_local_notifications
      sha256: "674173fd3c9eda9d4c8528da2ce0ea69f161577495a9cc835a2a4ecd7eadeb35"
      url: "https://pub.dev"
    source: hosted
    version: "17.2.4"
  flutter_local_notifications_linux:
    dependency: transitive
    description:
      name: flutter_local_notifications_linux
      sha256: c49bd06165cad9beeb79090b18cd1eb0296f4bf4b23b84426e37dd7c027fc3af
      url: "https://pub.dev"
    source: hosted
    version: "4.0.1"
  flutter_local_notifications_platform_interface:
    dependency: transitive
    description:
      name: flutter_local_notifications_platform_interface
      sha256: "85f8d07fe708c1bdcf45037f2c0109753b26ae077e9d9e899d55971711a4ea66"
      url: "https://pub.dev"
    source: hosted
    version: "7.2.0"
  flutter_localizations:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_mute:
    dependency: "direct main"
    description:
      path: "libraries/flutter_mute"
      relative: true
    source: path
    version: "0.0.4"
  flutter_pdfview:
    dependency: "direct main"
    description:
      name: flutter_pdfview
      sha256: "51413e36ab3f1a2fe0edf97ebfa770e20182ea4a066bc9f292920330d9245c9d"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0+1"
  flutter_plugin_android_lifecycle:
    dependency: transitive
    description:
      name: flutter_plugin_android_lifecycle
      sha256: f948e346c12f8d5480d2825e03de228d0eb8c3a737e4cdaa122267b89c022b5e
      url: "https://pub.dev"
    source: hosted
    version: "2.0.28"
  flutter_rating_bar:
    dependency: "direct main"
    description:
      name: flutter_rating_bar
      sha256: d2af03469eac832c591a1eba47c91ecc871fe5708e69967073c043b2d775ed93
      url: "https://pub.dev"
    source: hosted
    version: "4.0.1"
  flutter_sliding_up_panel:
    dependency: "direct main"
    description:
      name: flutter_sliding_up_panel
      sha256: "94f928973d83e146bbc52051e2d9f2a7ed7a5c9e7f04b54d835fff2e41d6cb99"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  flutter_svg:
    dependency: "direct main"
    description:
      name: flutter_svg
      sha256: d44bf546b13025ec7353091516f6881f1d4c633993cb109c3916c3a0159dadf1
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_typeahead:
    dependency: "direct main"
    description:
      name: flutter_typeahead
      sha256: d64712c65db240b1057559b952398ebb6e498077baeebf9b0731dade62438a6d
      url: "https://pub.dev"
    source: hosted
    version: "5.2.0"
  flutter_udid:
    dependency: "direct main"
    description:
      name: flutter_udid
      sha256: be464dc5b1fb7ee894f6a32d65c086ca5e177fdcf9375ac08d77495b98150f84
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_widget_from_html_core:
    dependency: "direct main"
    description:
      name: flutter_widget_from_html_core
      sha256: b1048fd119a14762e2361bd057da608148a895477846d6149109b2151d2f7abf
      url: "https://pub.dev"
    source: hosted
    version: "0.15.2"
  flutter_xlider:
    dependency: "direct main"
    description:
      name: flutter_xlider
      sha256: b83da229b8a2153adeefc5d9e08e0060689c8dc2187b30e3502cf67c1a6495be
      url: "https://pub.dev"
    source: hosted
    version: "3.5.0"
  frontend_server_client:
    dependency: transitive
    description:
      name: frontend_server_client
      sha256: f64a0333a82f30b0cca061bc3d143813a486dc086b574bfb233b7c1372427694
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  get_it:
    dependency: "direct main"
    description:
      name: get_it
      sha256: f126a3e286b7f5b578bf436d5592968706c4c1de28a228b870ce375d9f743103
      url: "https://pub.dev"
    source: hosted
    version: "8.0.3"
  glob:
    dependency: transitive
    description:
      name: glob
      sha256: c3f1ee72c96f8f78935e18aa8cecced9ab132419e8625dc187e1c2408efc20de
      url: "https://pub.dev"
    source: hosted
    version: "2.1.3"
  go_router:
    dependency: "direct main"
    description:
      name: go_router
      sha256: f02fd7d2a4dc512fec615529824fdd217fecb3a3d3de68360293a551f21634b3
      url: "https://pub.dev"
    source: hosted
    version: "14.8.1"
  google_fonts:
    dependency: "direct main"
    description:
      name: google_fonts
      sha256: b1ac0fe2832c9cc95e5e88b57d627c5e68c223b9657f4b96e1487aa9098c7b82
      url: "https://pub.dev"
    source: hosted
    version: "6.2.1"
  graphs:
    dependency: transitive
    description:
      name: graphs
      sha256: "741bbf84165310a68ff28fe9e727332eef1407342fca52759cb21ad8177bb8d0"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  hive:
    dependency: transitive
    description:
      name: hive
      sha256: "8dcf6db979d7933da8217edcec84e9df1bdb4e4edc7fc77dbd5aa74356d6d941"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.3"
  hive_flutter:
    dependency: "direct main"
    description:
      name: hive_flutter
      sha256: dca1da446b1d808a51689fb5d0c6c9510c0a2ba01e22805d492c73b68e33eecc
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  hotreloader:
    dependency: transitive
    description:
      name: hotreloader
      sha256: bc167a1163807b03bada490bfe2df25b0d744df359227880220a5cbd04e5734b
      url: "https://pub.dev"
    source: hosted
    version: "4.3.0"
  html:
    dependency: transitive
    description:
      name: html
      sha256: "6d1264f2dffa1b1101c25a91dff0dc2daee4c18e87cd8538729773c073dbf602"
      url: "https://pub.dev"
    source: hosted
    version: "0.15.6"
  http:
    dependency: transitive
    description:
      name: http
      sha256: "2c11f3f94c687ee9bad77c171151672986360b2b001d109814ee7140b2cf261b"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  http_certificate_pinning:
    dependency: "direct main"
    description:
      name: http_certificate_pinning
      sha256: "1501c69142a3906a5ad4876d7e71696fa6a6a187fc6f4a746d7a5ba32f9f8fcf"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  http_multi_server:
    dependency: transitive
    description:
      name: http_multi_server
      sha256: aa6199f908078bb1c5efb8d8638d4ae191aac11b311132c3ef48ce352fb52ef8
      url: "https://pub.dev"
    source: hosted
    version: "3.2.2"
  http_parser:
    dependency: "direct main"
    description:
      name: http_parser
      sha256: "178d74305e7866013777bab2c3d8726205dc5a4dd935297175b19a23a2e66571"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.2"
  image:
    dependency: "direct main"
    description:
      name: image
      sha256: f31d52537dc417fdcde36088fdf11d191026fd5e4fae742491ebd40e5a8bea7d
      url: "https://pub.dev"
    source: hosted
    version: "4.3.0"
  intl:
    dependency: "direct main"
    description:
      name: intl
      sha256: "3df61194eb431efc39c4ceba583b95633a403f46c9fd341e550ce0bfa50e9aa5"
      url: "https://pub.dev"
    source: hosted
    version: "0.20.2"
  io:
    dependency: transitive
    description:
      name: io
      sha256: dfd5a80599cf0165756e3181807ed3e77daf6dd4137caaad72d0b7931597650b
      url: "https://pub.dev"
    source: hosted
    version: "1.0.5"
  jiffy:
    dependency: transitive
    description:
      name: jiffy
      sha256: "9bafbfe6d97587048bf449165e050029e716a12438f54a3d39e7e3a256decdac"
      url: "https://pub.dev"
    source: hosted
    version: "6.4.3"
  js:
    dependency: transitive
    description:
      name: js
      sha256: "53385261521cc4a0c4658fd0ad07a7d14591cf8fc33abbceae306ddb974888dc"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.2"
  json_annotation:
    dependency: transitive
    description:
      name: json_annotation
      sha256: "1ce844379ca14835a50d2f019a3099f419082cfdd231cd86a142af94dd5c6bb1"
      url: "https://pub.dev"
    source: hosted
    version: "4.9.0"
  jwt_decoder:
    dependency: "direct main"
    description:
      name: jwt_decoder
      sha256: "54774aebf83f2923b99e6416b4ea915d47af3bde56884eb622de85feabbc559f"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  leak_tracker:
    dependency: transitive
    description:
      name: leak_tracker
      sha256: "6bb818ecbdffe216e81182c2f0714a2e62b593f4a4f13098713ff1685dfb6ab0"
      url: "https://pub.dev"
    source: hosted
    version: "10.0.9"
  leak_tracker_flutter_testing:
    dependency: transitive
    description:
      name: leak_tracker_flutter_testing
      sha256: f8b613e7e6a13ec79cfdc0e97638fddb3ab848452eff057653abd3edba760573
      url: "https://pub.dev"
    source: hosted
    version: "3.0.9"
  leak_tracker_testing:
    dependency: transitive
    description:
      name: leak_tracker_testing
      sha256: "6ba465d5d76e67ddf503e1161d1f4a6bc42306f9d66ca1e8f079a47290fb06d3"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  lean_builder:
    dependency: transitive
    description:
      name: lean_builder
      sha256: c45eadd4dd260a3fc14cd6a114f5302080a94ddc448cddb28adca69b9571b99a
      url: "https://pub.dev"
    source: hosted
    version: "0.1.0-alpha.10"
  lints:
    dependency: transitive
    description:
      name: lints
      sha256: c35bb79562d980e9a453fc715854e1ed39e24e7d0297a880ef54e17f9874a9d7
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  local_auth:
    dependency: "direct main"
    description:
      name: local_auth
      sha256: "434d854cf478f17f12ab29a76a02b3067f86a63a6d6c4eb8fbfdcfe4879c1b7b"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  local_auth_android:
    dependency: transitive
    description:
      name: local_auth_android
      sha256: "63ad7ca6396290626dc0cb34725a939e4cfe965d80d36112f08d49cf13a8136e"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.49"
  local_auth_darwin:
    dependency: transitive
    description:
      name: local_auth_darwin
      sha256: "630996cd7b7f28f5ab92432c4b35d055dd03a747bc319e5ffbb3c4806a3e50d2"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.3"
  local_auth_platform_interface:
    dependency: transitive
    description:
      name: local_auth_platform_interface
      sha256: "1b842ff177a7068442eae093b64abe3592f816afd2a533c0ebcdbe40f9d2075a"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.10"
  local_auth_windows:
    dependency: transitive
    description:
      name: local_auth_windows
      sha256: bc4e66a29b0fdf751aafbec923b5bed7ad6ed3614875d8151afe2578520b2ab5
      url: "https://pub.dev"
    source: hosted
    version: "1.0.11"
  logging:
    dependency: transitive
    description:
      name: logging
      sha256: c8245ada5f1717ed44271ed1c26b8ce85ca3228fd2ffdb75468ab01979309d61
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  lottie:
    dependency: "direct main"
    description:
      name: lottie
      sha256: "377d87b8dcef640c04717e93afb86a510f0e1117a399ab94dc4b3f39c85eaa87"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.0"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: dc58c723c3c24bf8d3e2d3ad3f2f9d7bd9cf43ec6feaa64181775e60190153f2
      url: "https://pub.dev"
    source: hosted
    version: "0.12.17"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: f7142bb1154231d7ea5f96bc7bde4bda2a0945d2806bb11670e30b850d56bdec
      url: "https://pub.dev"
    source: hosted
    version: "0.11.1"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: e3641ec5d63ebf0d9b41bd43201a66e3fc79a65db5f61fc181f04cd27aab950c
      url: "https://pub.dev"
    source: hosted
    version: "1.16.0"
  mime:
    dependency: transitive
    description:
      name: mime
      sha256: "41a20518f0cb1256669420fdba0cd90d21561e560ac240f26ef8322e45bb7ed6"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  nested:
    dependency: transitive
    description:
      name: nested
      sha256: "03bac4c528c64c95c722ec99280375a6f2fc708eec17c7b3f07253b626cd2a20"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  node_preamble:
    dependency: transitive
    description:
      name: node_preamble
      sha256: "6e7eac89047ab8a8d26cf16127b5ed26de65209847630400f9aefd7cd5c730db"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  open_filex:
    dependency: "direct main"
    description:
      name: open_filex
      sha256: "9976da61b6a72302cf3b1efbce259200cd40232643a467aac7370addf94d6900"
      url: "https://pub.dev"
    source: hosted
    version: "4.7.0"
  overlay_support:
    dependency: "direct main"
    description:
      name: overlay_support
      sha256: fc39389bfd94e6985e1e13b2a88a125fc4027608485d2d4e2847afe1b2bb339c
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  package_config:
    dependency: transitive
    description:
      name: package_config
      sha256: f096c55ebb7deb7e384101542bfba8c52696c1b56fca2eb62827989ef2353bbc
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  package_info_plus:
    dependency: "direct main"
    description:
      name: package_info_plus
      sha256: "7976bfe4c583170d6cdc7077e3237560b364149fcd268b5f53d95a991963b191"
      url: "https://pub.dev"
    source: hosted
    version: "8.3.0"
  package_info_plus_platform_interface:
    dependency: transitive
    description:
      name: package_info_plus_platform_interface
      sha256: "6c935fb612dff8e3cc9632c2b301720c77450a126114126ffaafe28d2e87956c"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  path:
    dependency: transitive
    description:
      name: path
      sha256: "75cca69d1490965be98c73ceaea117e8a04dd21217b37b292c9ddbec0d955bc5"
      url: "https://pub.dev"
    source: hosted
    version: "1.9.1"
  path_drawing:
    dependency: transitive
    description:
      name: path_drawing
      sha256: bbb1934c0cbb03091af082a6389ca2080345291ef07a5fa6d6e078ba8682f977
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      sha256: "883402936929eac138ee0a45da5b0f2c80f89913e6dc3bf77eb65b84b409c6ca"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  path_provider:
    dependency: "direct main"
    description:
      name: path_provider
      sha256: "50c5dd5b6e1aaf6fb3a78b33f6aa3afca52bf903a8a5298f53101fdaee55bbcd"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.5"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: d0d310befe2c8ab9e7f393288ccbb11b60c019c6b5afc21973eeee4dda2b35e9
      url: "https://pub.dev"
    source: hosted
    version: "2.2.17"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: "4843174df4d288f5e29185bd6e72a6fbdf5a4a4602717eed565497429f179942"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: f7a1fe3a634fe7734c8d3f2766ad746ae2a2884abe22e241a8b301bf5cac3279
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "88f5779f72ba699763fa3a3b06aa4bf6de76c8e5de842cf6f29e2e06476c2334"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: bd6f00dbd873bfb70d0761682da2b3a2c2fccc2b9e84c495821639601d81afe7
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  permission_handler:
    dependency: "direct main"
    description:
      name: permission_handler
      sha256: "59adad729136f01ea9e35a48f5d1395e25cba6cea552249ddbe9cf950f5d7849"
      url: "https://pub.dev"
    source: hosted
    version: "11.4.0"
  permission_handler_android:
    dependency: transitive
    description:
      name: permission_handler_android
      sha256: d3971dcdd76182a0c198c096b5db2f0884b0d4196723d21a866fc4cdea057ebc
      url: "https://pub.dev"
    source: hosted
    version: "12.1.0"
  permission_handler_apple:
    dependency: transitive
    description:
      name: permission_handler_apple
      sha256: f000131e755c54cf4d84a5d8bd6e4149e262cc31c5a8b1d698de1ac85fa41023
      url: "https://pub.dev"
    source: hosted
    version: "9.4.7"
  permission_handler_html:
    dependency: transitive
    description:
      name: permission_handler_html
      sha256: "38f000e83355abb3392140f6bc3030660cfaef189e1f87824facb76300b4ff24"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.3+5"
  permission_handler_platform_interface:
    dependency: transitive
    description:
      name: permission_handler_platform_interface
      sha256: eb99b295153abce5d683cac8c02e22faab63e50679b937fa1bf67d58bb282878
      url: "https://pub.dev"
    source: hosted
    version: "4.3.0"
  permission_handler_windows:
    dependency: transitive
    description:
      name: permission_handler_windows
      sha256: "1a790728016f79a41216d88672dbc5df30e686e811ad4e698bfc51f76ad91f1e"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: "07c8f0b1913bcde1ff0d26e57ace2f3012ccbf2b204e070290dad3bb22797646"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.0"
  pinput:
    dependency: "direct main"
    description:
      name: pinput
      sha256: "8a73be426a91fefec90a7f130763ca39772d547e92f19a827cf4aa02e323d35a"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.1"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "5d6b1b0036a5f331ebc77c850ebc8506cbc1e9416c27e59b439f917a902a4984"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.6"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: "4820fbfdb9478b1ebae27888254d445073732dae3d6ea81f0b7e06d5dedc3f02"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.8"
  pointer_interceptor:
    dependency: transitive
    description:
      name: pointer_interceptor
      sha256: "57210410680379aea8b1b7ed6ae0c3ad349bfd56fe845b8ea934a53344b9d523"
      url: "https://pub.dev"
    source: hosted
    version: "0.10.1+2"
  pointer_interceptor_ios:
    dependency: transitive
    description:
      name: pointer_interceptor_ios
      sha256: a6906772b3205b42c44614fcea28f818b1e5fdad73a4ca742a7bd49818d9c917
      url: "https://pub.dev"
    source: hosted
    version: "0.10.1"
  pointer_interceptor_platform_interface:
    dependency: transitive
    description:
      name: pointer_interceptor_platform_interface
      sha256: "0597b0560e14354baeb23f8375cd612e8bd4841bf8306ecb71fcd0bb78552506"
      url: "https://pub.dev"
    source: hosted
    version: "0.10.0+1"
  pointer_interceptor_web:
    dependency: transitive
    description:
      name: pointer_interceptor_web
      sha256: "7a7087782110f8c1827170660b09f8aa893e0e9a61431dbbe2ac3fc482e8c044"
      url: "https://pub.dev"
    source: hosted
    version: "0.10.2+1"
  pool:
    dependency: transitive
    description:
      name: pool
      sha256: "20fe868b6314b322ea036ba325e6fc0711a22948856475e2c2b6306e8ab39c2a"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.1"
  provider:
    dependency: transitive
    description:
      name: provider
      sha256: "4abbd070a04e9ddc287673bf5a030c7ca8b685ff70218720abab8b092f53dd84"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.5"
  pub_semver:
    dependency: transitive
    description:
      name: pub_semver
      sha256: "5bfcf68ca79ef689f8990d1160781b4bad40a3bd5e5218ad4076ddb7f4081585"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  pubspec_parse:
    dependency: transitive
    description:
      name: pubspec_parse
      sha256: "0560ba233314abbed0a48a2956f7f022cce7c3e1e73df540277da7544cad4082"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.0"
  quiver:
    dependency: transitive
    description:
      name: quiver
      sha256: ea0b925899e64ecdfbf9c7becb60d5b50e706ade44a85b2363be2a22d88117d2
      url: "https://pub.dev"
    source: hosted
    version: "3.2.2"
  real_volume:
    dependency: "direct main"
    description:
      name: real_volume
      sha256: "77e7dfb900e1cb73f97b0cdbfd494b43a336dce5547245a9a827701079b09709"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.9"
  root_jailbreak_sniffer:
    dependency: "direct main"
    description:
      path: "libraries/root_jailbreak_sniffer-1.0.6"
      relative: true
    source: path
    version: "1.0.6"
  screenshot:
    dependency: "direct main"
    description:
      name: screenshot
      sha256: "63817697a7835e6ce82add4228e15d233b74d42975c143ad8cfe07009fab866b"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  scrollable_positioned_list:
    dependency: "direct main"
    description:
      name: scrollable_positioned_list
      sha256: "1b54d5f1329a1e263269abc9e2543d90806131aa14fe7c6062a8054d57249287"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.8"
  shared_preferences:
    dependency: transitive
    description:
      name: shared_preferences
      sha256: "6e8bf70b7fef813df4e9a36f658ac46d107db4b4cfe1048b477d4e453a8159f5"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.3"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      sha256: "20cbd561f743a342c76c151d6ddb93a9ce6005751e7aa458baad3858bfbfb6ac"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.10"
  shared_preferences_foundation:
    dependency: transitive
    description:
      name: shared_preferences_foundation
      sha256: "6a52cfcdaeac77cad8c97b539ff688ccfc458c007b4db12be584fbe5c0e49e03"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.4"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      sha256: "580abfd40f415611503cae30adf626e6656dfb2f0cee8f465ece7b6defb40f2f"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      sha256: "57cbf196c486bc2cf1f02b85784932c6094376284b3ad5779d1b1c6c6a816b80"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      sha256: c49bd060261c9a3f0ff445892695d6212ff603ef3115edbb448509d407600019
      url: "https://pub.dev"
    source: hosted
    version: "2.4.3"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      sha256: "94ef0f72b2d71bc3e700e025db3710911bd51a71cefb65cc609dd0d9a982e3c1"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  shelf:
    dependency: transitive
    description:
      name: shelf
      sha256: e7dd780a7ffb623c57850b33f43309312fc863fb6aa3d276a754bb299839ef12
      url: "https://pub.dev"
    source: hosted
    version: "1.4.2"
  shelf_packages_handler:
    dependency: transitive
    description:
      name: shelf_packages_handler
      sha256: "89f967eca29607c933ba9571d838be31d67f53f6e4ee15147d5dc2934fee1b1e"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.2"
  shelf_static:
    dependency: transitive
    description:
      name: shelf_static
      sha256: c87c3875f91262785dade62d135760c2c69cb217ac759485334c5857ad89f6e3
      url: "https://pub.dev"
    source: hosted
    version: "1.1.3"
  shelf_web_socket:
    dependency: transitive
    description:
      name: shelf_web_socket
      sha256: "3632775c8e90d6c9712f883e633716432a27758216dfb61bd86a8321c0580925"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  shimmer:
    dependency: "direct main"
    description:
      name: shimmer
      sha256: "5f88c883a22e9f9f299e5ba0e4f7e6054857224976a5d9f839d4ebdc94a14ac9"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  sliding_up_panel2:
    dependency: "direct main"
    description:
      name: sliding_up_panel2
      sha256: "7c2aac81c03e74fcd070799c5e2011f1c5de7026bd22a76164e81e23a49f2bdb"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.0+1"
  smooth_page_indicator:
    dependency: "direct main"
    description:
      name: smooth_page_indicator
      sha256: b21ebb8bc39cf72d11c7cfd809162a48c3800668ced1c9da3aade13a32cf6c1c
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  sound_mode:
    dependency: "direct main"
    description:
      path: "libraries/sound_mode"
      relative: true
    source: path
    version: "3.0.0"
  source_gen:
    dependency: transitive
    description:
      name: source_gen
      sha256: "35c8150ece9e8c8d263337a265153c3329667640850b9304861faea59fc98f6b"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  source_map_stack_trace:
    dependency: transitive
    description:
      name: source_map_stack_trace
      sha256: c0713a43e323c3302c2abe2a1cc89aa057a387101ebd280371d6a6c9fa68516b
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  source_maps:
    dependency: transitive
    description:
      name: source_maps
      sha256: "190222579a448b03896e0ca6eca5998fa810fda630c1d65e2f78b3f638f54812"
      url: "https://pub.dev"
    source: hosted
    version: "0.10.13"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: "254ee5351d6cb365c859e20ee823c3bb479bf4a293c22d17a9f1bf144ce86f7c"
      url: "https://pub.dev"
    source: hosted
    version: "1.10.1"
  sprintf:
    dependency: transitive
    description:
      name: sprintf
      sha256: "1fc9ffe69d4df602376b52949af107d8f5703b77cda567c4d7d86a0693120f23"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: "8b27215b45d22309b5cddda1aa2b19bdfec9df0e765f2de506401c071d38d1b1"
      url: "https://pub.dev"
    source: hosted
    version: "1.12.1"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: "969e04c80b8bcdf826f8f16579c7b14d780458bd97f56d107d3950fdbeef059d"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      sha256: ad47125e588cfd37a9a7f86c7d6356dde8dfe89d071d293f80ca9e9273a33871
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "921cd31725b72fe181906c6a94d987c78e3b98c2e205b397ea399d4054872b43"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.1"
  syncfusion_flutter_charts:
    dependency: "direct main"
    description:
      name: syncfusion_flutter_charts
      sha256: "8c55049105f3939adfd8a913a5421154999f0dde4fd5472ab66f3d0f49d3a9a3"
      url: "https://pub.dev"
    source: hosted
    version: "29.2.7"
  syncfusion_flutter_core:
    dependency: "direct main"
    description:
      name: syncfusion_flutter_core
      sha256: "6898c5dc81f73ac4e1156a075e4dd4591ea8927f26dcc77f6ba0e9b1b1d6c8c5"
      url: "https://pub.dev"
    source: hosted
    version: "29.2.7"
  syncfusion_flutter_datagrid:
    dependency: "direct main"
    description:
      name: syncfusion_flutter_datagrid
      sha256: "6b9fdbfaf0893069e3609fd8264311060e993667463acd179e92a6c5ed13ad65"
      url: "https://pub.dev"
    source: hosted
    version: "29.2.7+1"
  syncfusion_flutter_pdf:
    dependency: "direct main"
    description:
      name: syncfusion_flutter_pdf
      sha256: "8f21ee8f3da89bf47ecf901fb33176deac452bff20b34b554340cfdf91214669"
      url: "https://pub.dev"
    source: hosted
    version: "29.2.7+1"
  syncfusion_flutter_sliders:
    dependency: "direct main"
    description:
      name: syncfusion_flutter_sliders
      sha256: b0094c8f864a6e6716e773edcd149c2736452ab6e282e7aae867412b9948f6c9
      url: "https://pub.dev"
    source: hosted
    version: "29.2.7"
  syncfusion_flutter_treemap:
    dependency: "direct main"
    description:
      name: syncfusion_flutter_treemap
      sha256: d674b29c3c05432898f7fd5088d1db7a330258cc7f9ac2b3198de8a4e91cf924
      url: "https://pub.dev"
    source: hosted
    version: "29.2.7"
  syncfusion_flutter_xlsio:
    dependency: "direct main"
    description:
      name: syncfusion_flutter_xlsio
      sha256: "1ea8715d41ddb5570aa6a9376a3013df45ae5d135a09979bf8a97a2b5ae9d1d4"
      url: "https://pub.dev"
    source: hosted
    version: "29.2.7"
  syncfusion_officecore:
    dependency: transitive
    description:
      name: syncfusion_officecore
      sha256: "07c66cf1655205e24f0c476e31e4a8c985bb929a60c19bcbbc608644127c7854"
      url: "https://pub.dev"
    source: hosted
    version: "29.2.7"
  synchronized:
    dependency: transitive
    description:
      name: synchronized
      sha256: "0669c70faae6270521ee4f05bffd2919892d42d1276e6c495be80174b6bc0ef6"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.1"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: "7f554798625ea768a7518313e58f83891c7f5024f88e46e7182a4558850a4b8e"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.2"
  test:
    dependency: transitive
    description:
      name: test
      sha256: "301b213cd241ca982e9ba50266bd3f5bd1ea33f1455554c5abb85d1be0e2d87e"
      url: "https://pub.dev"
    source: hosted
    version: "1.25.15"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: fb31f383e2ee25fbbfe06b40fe21e1e458d14080e3c67e7ba0acfde4df4e0bbd
      url: "https://pub.dev"
    source: hosted
    version: "0.7.4"
  test_core:
    dependency: transitive
    description:
      name: test_core
      sha256: "84d17c3486c8dfdbe5e12a50c8ae176d15e2a771b96909a9442b40173649ccaa"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.8"
  timeline_tile:
    dependency: "direct main"
    description:
      name: timeline_tile
      sha256: "85ec2023c67137397c2812e3e848b2fb20b410b67cd9aff304bb5480c376fc0c"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  timezone:
    dependency: transitive
    description:
      name: timezone
      sha256: "2236ec079a174ce07434e89fcd3fcda430025eb7692244139a9cf54fdcf1fc7d"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.4"
  timing:
    dependency: transitive
    description:
      name: timing
      sha256: "62ee18aca144e4a9f29d212f5a4c6a053be252b895ab14b5821996cff4ed90fe"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: f9049c039ebfeb4cf7a7104a675823cd72dba8297f264b6637062516699fa006
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  universal_platform:
    dependency: transitive
    description:
      name: universal_platform
      sha256: "64e16458a0ea9b99260ceb5467a214c1f298d647c659af1bff6d3bf82536b1ec"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  url_launcher:
    dependency: "direct main"
    description:
      name: url_launcher
      sha256: "9d06212b1362abc2f0f0d78e6f09f726608c74e3b9462e8368bb03314aa8d603"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.1"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      sha256: "8582d7f6fe14d2652b4c45c9b6c14c0b678c2af2d083a11b604caeba51930d79"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.16"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      sha256: "7f2022359d4c099eea7df3fdf739f7d3d3b9faf3166fb1dd390775176e0b76cb"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.3"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      sha256: "4e9ba368772369e3e08f231d2301b4ef72b9ff87c31192ef471b380ef29a4935"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.1"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      sha256: "17ba2000b847f334f16626a574c702b196723af2a289e7a93ffcb79acff855c2"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.2"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      sha256: "552f8a1e663569be95a8190206a38187b531910283c3e982193e4f2733f01029"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      sha256: "4bd2b7b4dc4d4d0b94e5babfffbca8eac1a126c7f3d6ecbc1a11013faa3abba2"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      sha256: "3284b6d2ac454cf34f114e1d3319866fdd1e19cdc329999057e44ffe936cfa77"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.4"
  uuid:
    dependency: transitive
    description:
      name: uuid
      sha256: a5be9ef6618a7ac1e964353ef476418026db906c4facdedaa299b7a2e71690ff
      url: "https://pub.dev"
    source: hosted
    version: "4.5.1"
  vector_graphics:
    dependency: transitive
    description:
      name: vector_graphics
      sha256: "44cc7104ff32563122a929e4620cf3efd584194eec6d1d913eb5ba593dbcf6de"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.18"
  vector_graphics_codec:
    dependency: transitive
    description:
      name: vector_graphics_codec
      sha256: "99fd9fbd34d9f9a32efd7b6a6aae14125d8237b10403b422a6a6dfeac2806146"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.13"
  vector_graphics_compiler:
    dependency: transitive
    description:
      name: vector_graphics_compiler
      sha256: "557a315b7d2a6dbb0aaaff84d857967ce6bdc96a63dc6ee2a57ce5a6ee5d3331"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.17"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  very_good_analysis:
    dependency: "direct dev"
    description:
      name: very_good_analysis
      sha256: "1fb637c0022034b1f19ea2acb42a3603cbd8314a470646a59a2fb01f5f3a8629"
      url: "https://pub.dev"
    source: hosted
    version: "6.0.0"
  vm_service:
    dependency: transitive
    description:
      name: vm_service
      sha256: ddfa8d30d89985b96407efce8acbdd124701f96741f2d981ca860662f1c0dc02
      url: "https://pub.dev"
    source: hosted
    version: "15.0.0"
  watcher:
    dependency: transitive
    description:
      name: watcher
      sha256: "69da27e49efa56a15f8afe8f4438c4ec02eff0a117df1b22ea4aad194fe1c104"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  web:
    dependency: transitive
    description:
      name: web
      sha256: "868d88a33d8a87b18ffc05f9f030ba328ffefba92d6c127917a2ba740f9cfe4a"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  web_socket:
    dependency: transitive
    description:
      name: web_socket
      sha256: "34d64019aa8e36bf9842ac014bb5d2f5586ca73df5e4d9bf5c936975cae6982c"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  web_socket_channel:
    dependency: transitive
    description:
      name: web_socket_channel
      sha256: d645757fb0f4773d602444000a8131ff5d48c9e47adfe9772652dd1a4f2d45c8
      url: "https://pub.dev"
    source: hosted
    version: "3.0.3"
  webkit_inspection_protocol:
    dependency: transitive
    description:
      name: webkit_inspection_protocol
      sha256: "87d3f2333bb240704cd3f1c6b5b7acd8a10e7f0bc28c28dcf14e782014f4a572"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  webview_flutter:
    dependency: "direct main"
    description:
      name: webview_flutter
      sha256: c3e4fe614b1c814950ad07186007eff2f2e5dd2935eba7b9a9a1af8e5885f1ba
      url: "https://pub.dev"
    source: hosted
    version: "4.13.0"
  webview_flutter_android:
    dependency: transitive
    description:
      name: webview_flutter_android
      sha256: f6e6afef6e234801da77170f7a1847ded8450778caf2fe13979d140484be3678
      url: "https://pub.dev"
    source: hosted
    version: "4.7.0"
  webview_flutter_platform_interface:
    dependency: transitive
    description:
      name: webview_flutter_platform_interface
      sha256: "7cb32b21825bd65569665c32bb00a34ded5779786d6201f5350979d2d529940d"
      url: "https://pub.dev"
    source: hosted
    version: "2.13.0"
  webview_flutter_wkwebview:
    dependency: transitive
    description:
      name: webview_flutter_wkwebview
      sha256: a3d461fe3467014e05f3ac4962e5fdde2a4bf44c561cb53e9ae5c586600fdbc3
      url: "https://pub.dev"
    source: hosted
    version: "3.22.0"
  win32:
    dependency: transitive
    description:
      name: win32
      sha256: "329edf97fdd893e0f1e3b9e88d6a0e627128cc17cc316a8d67fda8f1451178ba"
      url: "https://pub.dev"
    source: hosted
    version: "5.13.0"
  win32_registry:
    dependency: transitive
    description:
      name: win32_registry
      sha256: "21ec76dfc731550fd3e2ce7a33a9ea90b828fdf19a5c3bcf556fa992cfa99852"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.5"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: "7a3f37b05d989967cdddcbb571f1ea834867ae2faa29725fd085180e0883aa15"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: b015a8ad1c488f66851d762d3090a21c600e479dc75e68328c52774040cf9226
      url: "https://pub.dev"
    source: hosted
    version: "6.5.0"
  xxh3:
    dependency: transitive
    description:
      name: xxh3
      sha256: "399a0438f5d426785723c99da6b16e136f4953fb1e9db0bf270bd41dd4619916"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  yaml:
    dependency: transitive
    description:
      name: yaml
      sha256: b9da305ac7c39faa3f030eccd175340f968459dae4af175130b3fc47e40d76ce
      url: "https://pub.dev"
    source: hosted
    version: "3.1.3"
sdks:
  dart: ">=3.8.0 <4.0.0"
  flutter: ">=3.27.0"
