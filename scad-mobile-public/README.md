# SCAD Mobile

## Run command
flutter run --dart-define-from-file=env-<env>.json

## Build 
add run args to fetch secret from env
--dart-define-from-file=env-<env>.json

flutter build apk --release --dart-define-from-file=env-.json
flutter build xcarchive --release --dart-define-from-file=env-.json

## build generated files
flutter packages pub run build_runner build;flutter pub run easy_localization:generate --source-dir assets/translations -O lib//translations;
dart run easy_localization:generate --source-dir ./assets/translations -O lib/translations -o locale_keys.g.dart -f keys
