# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _fe_analyzer_shared:
    dependency: transitive
    description:
      name: _fe_analyzer_shared
      sha256: ae92f5d747aee634b87f89d9946000c2de774be1d6ac3e58268224348cd0101a
      url: "https://pub.dev"
    source: hosted
    version: "61.0.0"
  _flutterfire_internals:
    dependency: transitive
    description:
      name: _flutterfire_internals
      sha256: "2350805d7afefb0efe7acd325cb19d3ae8ba4039b906eade3807ffb69938a01f"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.33"
  analyzer:
    dependency: transitive
    description:
      name: analyzer
      sha256: ea3d8652bda62982addfd92fdc2d0214e5f82e43325104990d4f4c4a2a313562
      url: "https://pub.dev"
    source: hosted
    version: "5.13.0"
  archive:
    dependency: transitive
    description:
      name: archive
      sha256: "7b875fd4a20b165a3084bd2d210439b22ebc653f21cea4842729c0c30c82596b"
      url: "https://pub.dev"
    source: hosted
    version: "3.4.9"
  args:
    dependency: transitive
    description:
      name: args
      sha256: eef6c46b622e0494a36c5a12d10d77fb4e855501a91c1b9ef9339326e58f0596
      url: "https://pub.dev"
    source: hosted
    version: "2.4.2"
  async:
    dependency: transitive
    description:
      name: async
      sha256: "947bfcf187f74dbc5e146c9eb9c0f10c9f8b30743e341481c1e2ed3ecc18c20c"
      url: "https://pub.dev"
    source: hosted
    version: "2.11.0"
  audioplayers:
    dependency: "direct main"
    description:
      name: audioplayers
      sha256: c05c6147124cd63e725e861335a8b4d57300b80e6e92cea7c145c739223bbaef
      url: "https://pub.dev"
    source: hosted
    version: "5.2.1"
  audioplayers_android:
    dependency: transitive
    description:
      name: audioplayers_android
      sha256: b00e1a0e11365d88576320ec2d8c192bc21f1afb6c0e5995d1c57ae63156acb5
      url: "https://pub.dev"
    source: hosted
    version: "4.0.3"
  audioplayers_darwin:
    dependency: transitive
    description:
      name: audioplayers_darwin
      sha256: "3034e99a6df8d101da0f5082dcca0a2a99db62ab1d4ddb3277bed3f6f81afe08"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.2"
  audioplayers_linux:
    dependency: transitive
    description:
      name: audioplayers_linux
      sha256: "60787e73fefc4d2e0b9c02c69885402177e818e4e27ef087074cf27c02246c9e"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  audioplayers_platform_interface:
    dependency: transitive
    description:
      name: audioplayers_platform_interface
      sha256: "365c547f1bb9e77d94dd1687903a668d8f7ac3409e48e6e6a3668a1ac2982adb"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.0"
  audioplayers_web:
    dependency: transitive
    description:
      name: audioplayers_web
      sha256: "22cd0173e54d92bd9b2c80b1204eb1eb159ece87475ab58c9788a70ec43c2a62"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.0"
  audioplayers_windows:
    dependency: transitive
    description:
      name: audioplayers_windows
      sha256: "9536812c9103563644ada2ef45ae523806b0745f7a78e89d1b5fb1951de90e1a"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  auto_route:
    dependency: "direct main"
    description:
      name: auto_route
      sha256: "82f8df1d177416bc6b7a449127d0270ff1f0f633a91f2ceb7a85d4f07c3affa1"
      url: "https://pub.dev"
    source: hosted
    version: "7.8.4"
  auto_route_generator:
    dependency: "direct dev"
    description:
      name: auto_route_generator
      sha256: "11067a3bcd643812518fe26c0c9ec073990286cabfd9d74b6da9ef9b913c4d22"
      url: "https://pub.dev"
    source: hosted
    version: "7.3.2"
  bloc:
    dependency: transitive
    description:
      name: bloc
      sha256: "3820f15f502372d979121de1f6b97bfcf1630ebff8fe1d52fb2b0bfa49be5b49"
      url: "https://pub.dev"
    source: hosted
    version: "8.1.2"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "6cfb5af12253eaf2b368f07bacc5a80d1301a071c73360d746b7f2e32d762c66"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  build:
    dependency: transitive
    description:
      name: build
      sha256: "80184af8b6cb3e5c1c4ec6d8544d27711700bc3e6d2efad04238c7b5290889f0"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  build_config:
    dependency: transitive
    description:
      name: build_config
      sha256: bf80fcfb46a29945b423bd9aad884590fb1dc69b330a4d4700cac476af1708d1
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  build_daemon:
    dependency: transitive
    description:
      name: build_daemon
      sha256: "0343061a33da9c5810b2d6cee51945127d8f4c060b7fbdd9d54917f0a3feaaa1"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.1"
  build_resolvers:
    dependency: transitive
    description:
      name: build_resolvers
      sha256: "64e12b0521812d1684b1917bc80945625391cb9bdd4312536b1d69dcb6133ed8"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  build_runner:
    dependency: "direct dev"
    description:
      name: build_runner
      sha256: "10c6bcdbf9d049a0b666702cf1cee4ddfdc38f02a19d35ae392863b47519848b"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.6"
  build_runner_core:
    dependency: transitive
    description:
      name: build_runner_core
      sha256: c9e32d21dd6626b5c163d48b037ce906bbe428bc23ab77bcd77bb21e593b6185
      url: "https://pub.dev"
    source: hosted
    version: "7.2.11"
  built_collection:
    dependency: transitive
    description:
      name: built_collection
      sha256: "376e3dd27b51ea877c28d525560790aee2e6fbb5f20e2f85d5081027d94e2100"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  built_value:
    dependency: transitive
    description:
      name: built_value
      sha256: "723b4021e903217dfc445ec4cf5b42e27975aece1fc4ebbc1ca6329c2d9fb54e"
      url: "https://pub.dev"
    source: hosted
    version: "8.7.0"
  cached_network_image:
    dependency: "direct main"
    description:
      name: cached_network_image
      sha256: f98972704692ba679db144261172a8e20feb145636c617af0eb4022132a6797f
      url: "https://pub.dev"
    source: hosted
    version: "3.3.0"
  cached_network_image_platform_interface:
    dependency: transitive
    description:
      name: cached_network_image_platform_interface
      sha256: "56aa42a7a01e3c9db8456d9f3f999931f1e05535b5a424271e9a38cabf066613"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  cached_network_image_web:
    dependency: transitive
    description:
      name: cached_network_image_web
      sha256: "759b9a9f8f6ccbb66c185df805fac107f05730b1dab9c64626d1008cca532257"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  carousel_slider:
    dependency: "direct main"
    description:
      name: carousel_slider
      sha256: "9c695cc963bf1d04a47bd6021f68befce8970bcd61d24938e1fb0918cf5d9c42"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.1"
  cart_stepper:
    dependency: "direct main"
    description:
      name: cart_stepper
      sha256: ae895502e082ce74f1a181acd6aad8240279c8e3cc534c126e7f6bfcd0c1842e
      url: "https://pub.dev"
    source: hosted
    version: "4.3.0"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: "04a925763edad70e8443c99234dc3328f442e811f1d8fd1a72f1c8ad0f69a605"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  checked_yaml:
    dependency: transitive
    description:
      name: checked_yaml
      sha256: feb6bed21949061731a7a75fc5d2aa727cf160b91af9a3e464c5e3a32e28b5ff
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: cb6d7f03e1de671e34607e909a7213e31d7752be4fb66a86d29fe1eb14bfb5cf
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  code_builder:
    dependency: transitive
    description:
      name: code_builder
      sha256: "1be9be30396d7e4c0db42c35ea6ccd7cc6a1e19916b5dc64d6ac216b5544d677"
      url: "https://pub.dev"
    source: hosted
    version: "4.7.0"
  collection:
    dependency: transitive
    description:
      name: collection
      sha256: ee67cb0715911d28db6bf4af1026078bd6f0128b07a5f66fb2ed94ec6783c09a
      url: "https://pub.dev"
    source: hosted
    version: "1.18.0"
  convert:
    dependency: transitive
    description:
      name: convert
      sha256: "0f08b14755d163f6e2134cb58222dd25ea2a2ee8a195e53983d57c075324d592"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
  cross_file:
    dependency: transitive
    description:
      name: cross_file
      sha256: "55d7b444feb71301ef6b8838dbc1ae02e63dd48c8773f3810ff53bb1e2945b32"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.4+1"
  crypto:
    dependency: transitive
    description:
      name: crypto
      sha256: ff625774173754681d66daaf4a448684fb04b78f902da9cb3d308c19cc5e8bab
      url: "https://pub.dev"
    source: hosted
    version: "3.0.3"
  csslib:
    dependency: transitive
    description:
      name: csslib
      sha256: "706b5707578e0c1b4b7550f64078f0a0f19dec3f50a178ffae7006b0a9ca58fb"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      sha256: d57953e10f9f8327ce64a508a355f0b1ec902193f66288e8cb5070e7c47eeb2d
      url: "https://pub.dev"
    source: hosted
    version: "1.0.6"
  dart_style:
    dependency: transitive
    description:
      name: dart_style
      sha256: "1efa911ca7086affd35f463ca2fc1799584fb6aa89883cf0af8e3664d6a02d55"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  dbus:
    dependency: transitive
    description:
      name: dbus
      sha256: "365c771ac3b0e58845f39ec6deebc76e3276aa9922b0cc60840712094d9047ac"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.10"
  device_info_plus:
    dependency: "direct main"
    description:
      name: device_info_plus
      sha256: "77f757b789ff68e4eaf9c56d1752309bd9f7ad557cb105b938a7f8eb89e59110"
      url: "https://pub.dev"
    source: hosted
    version: "9.1.2"
  device_info_plus_platform_interface:
    dependency: transitive
    description:
      name: device_info_plus_platform_interface
      sha256: d3b01d5868b50ae571cd1dc6e502fc94d956b665756180f7b16ead09e836fd64
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  dio:
    dependency: "direct main"
    description:
      name: dio
      sha256: "797e1e341c3dd2f69f2dad42564a6feff3bfb87187d05abb93b9609e6f1645c3"
      url: "https://pub.dev"
    source: hosted
    version: "5.4.0"
  dotted_border:
    dependency: "direct main"
    description:
      name: dotted_border
      sha256: "108837e11848ca776c53b30bc870086f84b62ed6e01c503ed976e8f8c7df9c04"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  dropdown_button2:
    dependency: "direct main"
    description:
      name: dropdown_button2
      sha256: b0fe8d49a030315e9eef6c7ac84ca964250155a6224d491c1365061bc974a9e1
      url: "https://pub.dev"
    source: hosted
    version: "2.3.9"
  easy_localization:
    dependency: "direct main"
    description:
      name: easy_localization
      sha256: de63e3b422adfc97f256cbb3f8cf12739b6a4993d390f3cadb3f51837afaefe5
      url: "https://pub.dev"
    source: hosted
    version: "3.0.3"
  easy_logger:
    dependency: transitive
    description:
      name: easy_logger
      sha256: c764a6e024846f33405a2342caf91c62e357c24b02c04dbc712ef232bf30ffb7
      url: "https://pub.dev"
    source: hosted
    version: "0.0.2"
  equatable:
    dependency: "direct main"
    description:
      name: equatable
      sha256: c2b87cb7756efdf69892005af546c56c0b5037f54d2a88269b4f347a505e3ca2
      url: "https://pub.dev"
    source: hosted
    version: "2.0.5"
  expandable:
    dependency: "direct main"
    description:
      name: expandable
      sha256: "9604d612d4d1146dafa96c6d8eec9c2ff0994658d6d09fed720ab788c7f5afc2"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.1"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "511392330127add0b769b75a987850d136345d9227c6b94c96a04cf4a391bf78"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  ffi:
    dependency: transitive
    description:
      name: ffi
      sha256: "7bf0adc28a23d395f19f3f1eb21dd7cfd1dd9f8e1c50051c069122e6853bc878"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  file:
    dependency: transitive
    description:
      name: file
      sha256: "5fc22d7c25582e38ad9a8515372cd9a93834027aacf1801cf01164dac0ffa08c"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  file_picker:
    dependency: "direct main"
    description:
      name: file_picker
      sha256: "4e42aacde3b993c5947467ab640882c56947d9d27342a5b6f2895b23956954a6"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.1"
  file_selector_linux:
    dependency: transitive
    description:
      name: file_selector_linux
      sha256: "045d372bf19b02aeb69cacf8b4009555fb5f6f0b7ad8016e5f46dd1387ddd492"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.2+1"
  file_selector_macos:
    dependency: transitive
    description:
      name: file_selector_macos
      sha256: b15c3da8bd4908b9918111fa486903f5808e388b8d1c559949f584725a6594d6
      url: "https://pub.dev"
    source: hosted
    version: "0.9.3+3"
  file_selector_platform_interface:
    dependency: transitive
    description:
      name: file_selector_platform_interface
      sha256: "0aa47a725c346825a2bd396343ce63ac00bda6eff2fbc43eabe99737dede8262"
      url: "https://pub.dev"
    source: hosted
    version: "2.6.1"
  file_selector_windows:
    dependency: transitive
    description:
      name: file_selector_windows
      sha256: d3547240c20cabf205c7c7f01a50ecdbc413755814d6677f3cb366f04abcead0
      url: "https://pub.dev"
    source: hosted
    version: "0.9.3+1"
  firebase_analytics:
    dependency: "direct main"
    description:
      name: firebase_analytics
      sha256: "51afa4751e8d17d1484c193b7e9759abbae324e1b8f5cc93e2a08daac4d55928"
      url: "https://pub.dev"
    source: hosted
    version: "10.10.5"
  firebase_analytics_platform_interface:
    dependency: transitive
    description:
      name: firebase_analytics_platform_interface
      sha256: ad7f6b70304e2b81c6079a5830355edc87496527d5b104d34c3e50b5b744da83
      url: "https://pub.dev"
    source: hosted
    version: "3.10.6"
  firebase_analytics_web:
    dependency: transitive
    description:
      name: firebase_analytics_web
      sha256: "63ed03d229d1c2ec2b1be037cd4760c3516cc8ecf6598a6b2fb8ca29586bf464"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.7+5"
  firebase_core:
    dependency: "direct main"
    description:
      name: firebase_core
      sha256: "372d94ced114b9c40cb85e18c50ac94a7e998c8eec630c50d7aec047847d27bf"
      url: "https://pub.dev"
    source: hosted
    version: "2.31.0"
  firebase_core_platform_interface:
    dependency: transitive
    description:
      name: firebase_core_platform_interface
      sha256: c437ae5d17e6b5cc7981cf6fd458a5db4d12979905f9aafd1fea930428a9fe63
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  firebase_core_web:
    dependency: transitive
    description:
      name: firebase_core_web
      sha256: "43d9e951ac52b87ae9cc38ecdcca1e8fa7b52a1dd26a96085ba41ce5108db8e9"
      url: "https://pub.dev"
    source: hosted
    version: "2.17.0"
  firebase_crashlytics:
    dependency: "direct main"
    description:
      name: firebase_crashlytics
      sha256: "8954a6493e05f73c87da1b15bab4970a5b325f138e254a0e1c18b4f97554720c"
      url: "https://pub.dev"
    source: hosted
    version: "3.5.5"
  firebase_crashlytics_platform_interface:
    dependency: transitive
    description:
      name: firebase_crashlytics_platform_interface
      sha256: "82f5e203cb0a83ccfce7d9b02de63c94e2f56fae96788df4b7446ca2cc81dac7"
      url: "https://pub.dev"
    source: hosted
    version: "3.6.33"
  firebase_dynamic_links:
    dependency: "direct main"
    description:
      name: firebase_dynamic_links
      sha256: "13e0290f0dc31dec739e55e512ef6baad575d85a7645914da2d9585c0446325b"
      url: "https://pub.dev"
    source: hosted
    version: "5.5.3"
  firebase_dynamic_links_platform_interface:
    dependency: transitive
    description:
      name: firebase_dynamic_links_platform_interface
      sha256: c104a67202252db5d89f45f3ff0c8183364fd98ce04bbb1e7faffbf5fd8a1105
      url: "https://pub.dev"
    source: hosted
    version: "0.2.6+31"
  firebase_messaging:
    dependency: "direct main"
    description:
      name: firebase_messaging
      sha256: e0882a7426821f7caccaabfc15a535155cd15b4daa73a5a7b3af701a552d73ab
      url: "https://pub.dev"
    source: hosted
    version: "14.9.2"
  firebase_messaging_platform_interface:
    dependency: transitive
    description:
      name: firebase_messaging_platform_interface
      sha256: "52e12cc50e1395ad7ea3552dcbe9958fb1994b5afcf58ee4c0db053932a6fce5"
      url: "https://pub.dev"
    source: hosted
    version: "4.5.35"
  firebase_messaging_web:
    dependency: transitive
    description:
      name: firebase_messaging_web
      sha256: "8812cc5929380b783f92290d934bf32e2fea06701583f47cdccd5f13f4f24522"
      url: "https://pub.dev"
    source: hosted
    version: "3.8.5"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      sha256: "25517a4deb0c03aa0f32fd12db525856438902d9c16536311e76cdc57b31d7d1"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_bloc:
    dependency: "direct main"
    description:
      name: flutter_bloc
      sha256: e74efb89ee6945bcbce74a5b3a5a3376b088e5f21f55c263fc38cbdc6237faae
      url: "https://pub.dev"
    source: hosted
    version: "8.1.3"
  flutter_cache_manager:
    dependency: transitive
    description:
      name: flutter_cache_manager
      sha256: "8207f27539deb83732fdda03e259349046a39a4c767269285f449ade355d54ba"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.1"
  flutter_chat_bubble:
    dependency: "direct main"
    description:
      name: flutter_chat_bubble
      sha256: ede33df7237fea3d89464c8493bfa0bee2f94f50fd097ebd30c517c3c0d4fcf9
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  flutter_inset_box_shadow:
    dependency: "direct main"
    description:
      name: flutter_inset_box_shadow
      sha256: "0b1930ed5f4ad84b11b41dda907fbc5d4c12929a4c51455c626e5937b47ffb93"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.8"
  flutter_keyboard_visibility:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility
      sha256: "98664be7be0e3ffca00de50f7f6a287ab62c763fc8c762e0a21584584a3ff4f8"
      url: "https://pub.dev"
    source: hosted
    version: "6.0.0"
  flutter_keyboard_visibility_linux:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_linux
      sha256: "6fba7cd9bb033b6ddd8c2beb4c99ad02d728f1e6e6d9b9446667398b2ac39f08"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  flutter_keyboard_visibility_macos:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_macos
      sha256: c5c49b16fff453dfdafdc16f26bdd8fb8d55812a1d50b0ce25fc8d9f2e53d086
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  flutter_keyboard_visibility_platform_interface:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_platform_interface
      sha256: e43a89845873f7be10cb3884345ceb9aebf00a659f479d1c8f4293fcb37022a4
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  flutter_keyboard_visibility_web:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_web
      sha256: d3771a2e752880c79203f8d80658401d0c998e4183edca05a149f5098ce6e3d1
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  flutter_keyboard_visibility_windows:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_windows
      sha256: fc4b0f0b6be9b93ae527f3d527fb56ee2d918cd88bbca438c478af7bcfd0ef73
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  flutter_lints:
    dependency: "direct dev"
    description:
      name: flutter_lints
      sha256: a25a15ebbdfc33ab1cd26c63a6ee519df92338a9c10f122adda92938253bef04
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  flutter_local_notifications:
    dependency: "direct main"
    description:
      name: flutter_local_notifications
      sha256: bb5cd63ff7c91d6efe452e41d0d0ae6348925c82eafd10ce170ef585ea04776e
      url: "https://pub.dev"
    source: hosted
    version: "16.2.0"
  flutter_local_notifications_linux:
    dependency: transitive
    description:
      name: flutter_local_notifications_linux
      sha256: "33f741ef47b5f63cc7f78fe75eeeac7e19f171ff3c3df054d84c1e38bedb6a03"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0+1"
  flutter_local_notifications_platform_interface:
    dependency: transitive
    description:
      name: flutter_local_notifications_platform_interface
      sha256: "7cf643d6d5022f3baed0be777b0662cce5919c0a7b86e700299f22dc4ae660ef"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0+1"
  flutter_localizations:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_plugin_android_lifecycle:
    dependency: transitive
    description:
      name: flutter_plugin_android_lifecycle
      sha256: b068ffc46f82a55844acfa4fdbb61fad72fa2aef0905548419d97f0f95c456da
      url: "https://pub.dev"
    source: hosted
    version: "2.0.17"
  flutter_rating_bar:
    dependency: "direct main"
    description:
      name: flutter_rating_bar
      sha256: d2af03469eac832c591a1eba47c91ecc871fe5708e69967073c043b2d775ed93
      url: "https://pub.dev"
    source: hosted
    version: "4.0.1"
  flutter_sliding_up_panel:
    dependency: "direct main"
    description:
      name: flutter_sliding_up_panel
      sha256: "94f928973d83e146bbc52051e2d9f2a7ed7a5c9e7f04b54d835fff2e41d6cb99"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  flutter_svg:
    dependency: "direct main"
    description:
      name: flutter_svg
      sha256: d39e7f95621fc84376bc0f7d504f05c3a41488c562f4a8ad410569127507402c
      url: "https://pub.dev"
    source: hosted
    version: "2.0.9"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_typeahead:
    dependency: "direct main"
    description:
      name: flutter_typeahead
      sha256: d64712c65db240b1057559b952398ebb6e498077baeebf9b0731dade62438a6d
      url: "https://pub.dev"
    source: hosted
    version: "5.2.0"
  flutter_udid:
    dependency: "direct main"
    description:
      name: flutter_udid
      sha256: "63384bd96203aaefccfd7137fab642edda18afede12b0e9e1a2c96fe2589fd07"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_widget_from_html_core:
    dependency: "direct main"
    description:
      name: flutter_widget_from_html_core
      sha256: "028f4989b9ff4907466af233d50146d807772600d98a3e895662fbdb09c39225"
      url: "https://pub.dev"
    source: hosted
    version: "0.14.11"
  flutter_xlider:
    dependency: "direct main"
    description:
      name: flutter_xlider
      sha256: b83da229b8a2153adeefc5d9e08e0060689c8dc2187b30e3502cf67c1a6495be
      url: "https://pub.dev"
    source: hosted
    version: "3.5.0"
  frontend_server_client:
    dependency: transitive
    description:
      name: frontend_server_client
      sha256: "408e3ca148b31c20282ad6f37ebfa6f4bdc8fede5b74bc2f08d9d92b55db3612"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  get_it:
    dependency: "direct main"
    description:
      name: get_it
      sha256: f79870884de16d689cf9a7d15eedf31ed61d750e813c538a6efb92660fea83c3
      url: "https://pub.dev"
    source: hosted
    version: "7.6.4"
  glob:
    dependency: transitive
    description:
      name: glob
      sha256: "0e7014b3b7d4dac1ca4d6114f82bf1782ee86745b9b42a92c9289c23d8a0ab63"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  go_router:
    dependency: "direct main"
    description:
      name: go_router
      sha256: "7ecb2f391edbca5473db591b48555a8912dde60edd0fb3013bd6743033b2d3f8"
      url: "https://pub.dev"
    source: hosted
    version: "13.2.1"
  google_fonts:
    dependency: "direct main"
    description:
      name: google_fonts
      sha256: f0b8d115a13ecf827013ec9fc883390ccc0e87a96ed5347a3114cac177ef18e8
      url: "https://pub.dev"
    source: hosted
    version: "6.1.0"
  graphs:
    dependency: transitive
    description:
      name: graphs
      sha256: aedc5a15e78fc65a6e23bcd927f24c64dd995062bcd1ca6eda65a3cff92a4d19
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  hive:
    dependency: transitive
    description:
      name: hive
      sha256: "8dcf6db979d7933da8217edcec84e9df1bdb4e4edc7fc77dbd5aa74356d6d941"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.3"
  hive_flutter:
    dependency: "direct main"
    description:
      name: hive_flutter
      sha256: dca1da446b1d808a51689fb5d0c6c9510c0a2ba01e22805d492c73b68e33eecc
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  hive_generator:
    dependency: "direct dev"
    description:
      name: hive_generator
      sha256: "06cb8f58ace74de61f63500564931f9505368f45f98958bd7a6c35ba24159db4"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  html:
    dependency: transitive
    description:
      name: html
      sha256: "3a7812d5bcd2894edf53dfaf8cd640876cf6cef50a8f238745c8b8120ea74d3a"
      url: "https://pub.dev"
    source: hosted
    version: "0.15.4"
  http:
    dependency: transitive
    description:
      name: http
      sha256: "761a297c042deedc1ffbb156d6e2af13886bb305c2a343a4d972504cd67dd938"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  http_multi_server:
    dependency: transitive
    description:
      name: http_multi_server
      sha256: "97486f20f9c2f7be8f514851703d0119c3596d14ea63227af6f7a481ef2b2f8b"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.1"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: "2aa08ce0341cc9b354a498388e30986515406668dbcc4f7c950c3e715496693b"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.2"
  image:
    dependency: "direct main"
    description:
      name: image
      sha256: "028f61960d56f26414eb616b48b04eb37d700cbe477b7fb09bf1d7ce57fd9271"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.3"
  image_picker:
    dependency: "direct main"
    description:
      name: image_picker
      sha256: fc712337719239b0b6e41316aa133350b078fa39b6cbd706b61f3fd421b03c77
      url: "https://pub.dev"
    source: hosted
    version: "1.0.5"
  image_picker_android:
    dependency: transitive
    description:
      name: image_picker_android
      sha256: ecdc963d2aa67af5195e723a40580f802d4392e31457a12a562b3e2bd6a396fe
      url: "https://pub.dev"
    source: hosted
    version: "0.8.9+1"
  image_picker_for_web:
    dependency: transitive
    description:
      name: image_picker_for_web
      sha256: "50bc9ae6a77eea3a8b11af5eb6c661eeb858fdd2f734c2a4fd17086922347ef7"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  image_picker_ios:
    dependency: transitive
    description:
      name: image_picker_ios
      sha256: eac0a62104fa12feed213596df0321f57ce5a572562f72a68c4ff81e9e4caacf
      url: "https://pub.dev"
    source: hosted
    version: "0.8.9"
  image_picker_linux:
    dependency: transitive
    description:
      name: image_picker_linux
      sha256: "4ed1d9bb36f7cd60aa6e6cd479779cc56a4cb4e4de8f49d487b1aaad831300fa"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  image_picker_macos:
    dependency: transitive
    description:
      name: image_picker_macos
      sha256: "3f5ad1e8112a9a6111c46d0b57a7be2286a9a07fc6e1976fdf5be2bd31d4ff62"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  image_picker_platform_interface:
    dependency: transitive
    description:
      name: image_picker_platform_interface
      sha256: ed9b00e63977c93b0d2d2b343685bed9c324534ba5abafbb3dfbd6a780b1b514
      url: "https://pub.dev"
    source: hosted
    version: "2.9.1"
  image_picker_windows:
    dependency: transitive
    description:
      name: image_picker_windows
      sha256: "6ad07afc4eb1bc25f3a01084d28520496c4a3bb0cb13685435838167c9dcedeb"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  intl:
    dependency: "direct main"
    description:
      name: intl
      sha256: "3bc132a9dbce73a7e4a21a17d06e1878839ffbf975568bc875c60537824b0c4d"
      url: "https://pub.dev"
    source: hosted
    version: "0.18.1"
  io:
    dependency: transitive
    description:
      name: io
      sha256: "2ec25704aba361659e10e3e5f5d672068d332fc8ac516421d483a11e5cbd061e"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  jiffy:
    dependency: transitive
    description:
      name: jiffy
      sha256: cc1d4b75016a9156c29b5d61f0c9176c3e0fb0580cc5a0e0422b5d2cab3fbfff
      url: "https://pub.dev"
    source: hosted
    version: "6.2.1"
  js:
    dependency: transitive
    description:
      name: js
      sha256: f2c445dce49627136094980615a031419f7f3eb393237e4ecd97ac15dea343f3
      url: "https://pub.dev"
    source: hosted
    version: "0.6.7"
  json_annotation:
    dependency: "direct main"
    description:
      name: json_annotation
      sha256: b10a7b2ff83d83c777edba3c6a0f97045ddadd56c944e1a23a3fdf43a1bf4467
      url: "https://pub.dev"
    source: hosted
    version: "4.8.1"
  json_reflectable:
    dependency: "direct main"
    description:
      name: json_reflectable
      sha256: "16801b7d205f2e95bf2d0ab639b848b86c07a181b63f00658802c819aa8eb8dc"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  json_serializable:
    dependency: "direct dev"
    description:
      name: json_serializable
      sha256: aa1f5a8912615733e0fdc7a02af03308933c93235bdc8d50d0b0c8a8ccb0b969
      url: "https://pub.dev"
    source: hosted
    version: "6.7.1"
  jwt_decoder:
    dependency: "direct main"
    description:
      name: jwt_decoder
      sha256: "54774aebf83f2923b99e6416b4ea915d47af3bde56884eb622de85feabbc559f"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  leak_tracker:
    dependency: transitive
    description:
      name: leak_tracker
      sha256: "78eb209deea09858f5269f5a5b02be4049535f568c07b275096836f01ea323fa"
      url: "https://pub.dev"
    source: hosted
    version: "10.0.0"
  leak_tracker_flutter_testing:
    dependency: transitive
    description:
      name: leak_tracker_flutter_testing
      sha256: b46c5e37c19120a8a01918cfaf293547f47269f7cb4b0058f21531c2465d6ef0
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  leak_tracker_testing:
    dependency: transitive
    description:
      name: leak_tracker_testing
      sha256: a597f72a664dbd293f3bfc51f9ba69816f84dcd403cdac7066cb3f6003f3ab47
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  lints:
    dependency: transitive
    description:
      name: lints
      sha256: "0a217c6c989d21039f1498c3ed9f3ed71b354e69873f13a8dfc3c9fe76f1b452"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  local_auth:
    dependency: "direct main"
    description:
      name: local_auth
      sha256: "7e6c63082e399b61e4af71266b012e767a5d4525dd6e9ba41e174fd42d76e115"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.7"
  local_auth_android:
    dependency: transitive
    description:
      name: local_auth_android
      sha256: "54e9c35ce52c06333355ab0d0f41e4c06dbca354b23426765ba41dfb1de27598"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.36"
  local_auth_ios:
    dependency: transitive
    description:
      name: local_auth_ios
      sha256: "8293faf72ef0ac4710f209edd03916c2d4c1eeab0483bdcf9b2e659c2f7d737b"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.5"
  local_auth_platform_interface:
    dependency: transitive
    description:
      name: local_auth_platform_interface
      sha256: fc5bd537970a324260fda506cfb61b33ad7426f37a8ea5c461cf612161ebba54
      url: "https://pub.dev"
    source: hosted
    version: "1.0.8"
  local_auth_windows:
    dependency: transitive
    description:
      name: local_auth_windows
      sha256: "505ba3367ca781efb1c50d3132e44a2446bccc4163427bc203b9b4d8994d97ea"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.10"
  logging:
    dependency: transitive
    description:
      name: logging
      sha256: "623a88c9594aa774443aa3eb2d41807a48486b5613e67599fb4c41c0ad47c340"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  lottie:
    dependency: "direct main"
    description:
      name: lottie
      sha256: "1f0ce68112072d66ea271a9841994fa8d16442e23d8cf8996c9fa74174e58b4e"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: d2323aa2060500f906aa31a895b4030b6da3ebdcc5619d14ce1aada65cd161cb
      url: "https://pub.dev"
    source: hosted
    version: "0.12.16+1"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: "0e0a020085b65b6083975e499759762399b4475f766c21668c4ecca34ea74e5a"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.0"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: d584fa6707a52763a52446f02cc621b077888fb63b93bbcb1143a7be5a0c0c04
      url: "https://pub.dev"
    source: hosted
    version: "1.11.0"
  mime:
    dependency: transitive
    description:
      name: mime
      sha256: e4ff8e8564c03f255408decd16e7899da1733852a9110a58fe6d1b817684a63e
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  nested:
    dependency: transitive
    description:
      name: nested
      sha256: "03bac4c528c64c95c722ec99280375a6f2fc708eec17c7b3f07253b626cd2a20"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  octo_image:
    dependency: transitive
    description:
      name: octo_image
      sha256: "45b40f99622f11901238e18d48f5f12ea36426d8eced9f4cbf58479c7aa2430d"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  open_filex:
    dependency: "direct main"
    description:
      name: open_filex
      sha256: "74e2280754cf8161e860746c3181db2c996d6c1909c7057b738ede4a469816b8"
      url: "https://pub.dev"
    source: hosted
    version: "4.4.0"
  overlay_support:
    dependency: "direct main"
    description:
      name: overlay_support
      sha256: fc39389bfd94e6985e1e13b2a88a125fc4027608485d2d4e2847afe1b2bb339c
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  package_config:
    dependency: transitive
    description:
      name: package_config
      sha256: "1c5b77ccc91e4823a5af61ee74e6b972db1ef98c2ff5a18d3161c982a55448bd"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  package_info_plus:
    dependency: "direct main"
    description:
      name: package_info_plus
      sha256: b93d8b4d624b4ea19b0a5a208b2d6eff06004bc3ce74c06040b120eeadd00ce0
      url: "https://pub.dev"
    source: hosted
    version: "8.0.0"
  package_info_plus_platform_interface:
    dependency: transitive
    description:
      name: package_info_plus_platform_interface
      sha256: f49918f3433a3146047372f9d4f1f847511f2acd5cd030e1f44fe5a50036b70e
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  path:
    dependency: transitive
    description:
      name: path
      sha256: "087ce49c3f0dc39180befefc60fdb4acd8f8620e5682fe2476afd0b3688bb4af"
      url: "https://pub.dev"
    source: hosted
    version: "1.9.0"
  path_drawing:
    dependency: transitive
    description:
      name: path_drawing
      sha256: bbb1934c0cbb03091af082a6389ca2080345291ef07a5fa6d6e078ba8682f977
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      sha256: e3e67b1629e6f7e8100b367d3db6ba6af4b1f0bb80f64db18ef1fbabd2fa9ccf
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  path_provider:
    dependency: "direct main"
    description:
      name: path_provider
      sha256: a1aa8aaa2542a6bc57e381f132af822420216c80d4781f7aa085ca3229208aaa
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: e595b98692943b4881b219f0a9e3945118d3c16bd7e2813f98ec6e532d905f72
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: "19314d595120f82aca0ba62787d58dde2cc6b5df7d2f0daf72489e38d1b57f2d"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: f7a1fe3a634fe7734c8d3f2766ad746ae2a2884abe22e241a8b301bf5cac3279
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "94b1e0dd80970c1ce43d5d4e050a9918fce4f4a775e6142424c30a29a363265c"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: "8bc9f22eee8690981c22aa7fc602f5c85b497a6fb2ceb35ee5a5e5ed85ad8170"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  permission_handler:
    dependency: "direct main"
    description:
      name: permission_handler
      sha256: "74e962b7fad7ff75959161bb2c0ad8fe7f2568ee82621c9c2660b751146bfe44"
      url: "https://pub.dev"
    source: hosted
    version: "11.3.0"
  permission_handler_android:
    dependency: transitive
    description:
      name: permission_handler_android
      sha256: "1acac6bae58144b442f11e66621c062aead9c99841093c38f5bcdcc24c1c3474"
      url: "https://pub.dev"
    source: hosted
    version: "12.0.5"
  permission_handler_apple:
    dependency: transitive
    description:
      name: permission_handler_apple
      sha256: bdafc6db74253abb63907f4e357302e6bb786ab41465e8635f362ee71fd8707b
      url: "https://pub.dev"
    source: hosted
    version: "9.4.0"
  permission_handler_html:
    dependency: transitive
    description:
      name: permission_handler_html
      sha256: "54bf176b90f6eddd4ece307e2c06cf977fb3973719c35a93b85cc7093eb6070d"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.1"
  permission_handler_platform_interface:
    dependency: transitive
    description:
      name: permission_handler_platform_interface
      sha256: "23dfba8447c076ab5be3dee9ceb66aad345c4a648f0cac292c77b1eb0e800b78"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.0"
  permission_handler_windows:
    dependency: transitive
    description:
      name: permission_handler_windows
      sha256: "1a790728016f79a41216d88672dbc5df30e686e811ad4e698bfc51f76ad91f1e"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: c15605cd28af66339f8eb6fbe0e541bfe2d1b72d5825efc6598f3e0a31b9ad27
      url: "https://pub.dev"
    source: hosted
    version: "6.0.2"
  pinput:
    dependency: "direct main"
    description:
      name: pinput
      sha256: a92b55ecf9c25d1b9e100af45905385d5bc34fc9b6b04177a9e82cb88fe4d805
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "0a279f0707af40c890e80b1e9df8bb761694c074ba7e1d4ab1bc4b728e200b59"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.3"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: da3fdfeccc4d4ff2da8f8c556704c08f912542c5fb3cf2233ed75372384a034d
      url: "https://pub.dev"
    source: hosted
    version: "2.1.6"
  pointer_interceptor:
    dependency: transitive
    description:
      name: pointer_interceptor
      sha256: bd18321519718678d5fa98ad3a3359cbc7a31f018554eab80b73d08a7f0c165a
      url: "https://pub.dev"
    source: hosted
    version: "0.10.1"
  pointer_interceptor_ios:
    dependency: transitive
    description:
      name: pointer_interceptor_ios
      sha256: "4282ebfe21b54e21e26ab982c6086f0a67dc63423026bfba8db39a2e22045f26"
      url: "https://pub.dev"
    source: hosted
    version: "0.10.0"
  pointer_interceptor_platform_interface:
    dependency: transitive
    description:
      name: pointer_interceptor_platform_interface
      sha256: "59a446ead3be360bde72c3725f5ecacbba203c8a760e3061024c20f7da53f825"
      url: "https://pub.dev"
    source: hosted
    version: "0.10.0"
  pointer_interceptor_web:
    dependency: transitive
    description:
      name: pointer_interceptor_web
      sha256: "2a8a069206f7b234a895d30ccab8b18ea267eeb79a832e5e3d1b6464d659eb6a"
      url: "https://pub.dev"
    source: hosted
    version: "0.10.0"
  pointycastle:
    dependency: transitive
    description:
      name: pointycastle
      sha256: "7c1e5f0d23c9016c5bbd8b1473d0d3fb3fc851b876046039509e18e0c7485f2c"
      url: "https://pub.dev"
    source: hosted
    version: "3.7.3"
  pool:
    dependency: transitive
    description:
      name: pool
      sha256: "20fe868b6314b322ea036ba325e6fc0711a22948856475e2c2b6306e8ab39c2a"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.1"
  provider:
    dependency: transitive
    description:
      name: provider
      sha256: "9a96a0a19b594dbc5bf0f1f27d2bc67d5f95957359b461cd9feb44ed6ae75096"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.1"
  pub_semver:
    dependency: transitive
    description:
      name: pub_semver
      sha256: "40d3ab1bbd474c4c2328c91e3a7df8c6dd629b79ece4c4bd04bee496a224fb0c"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  pubspec_parse:
    dependency: transitive
    description:
      name: pubspec_parse
      sha256: c63b2876e58e194e4b0828fcb080ad0e06d051cb607a6be51a9e084f47cb9367
      url: "https://pub.dev"
    source: hosted
    version: "1.2.3"
  reflectable:
    dependency: transitive
    description:
      name: reflectable
      sha256: "775388897bfce5b1d0386b7160706367b565cdc72b5870bc6a198d0f7be1aa0b"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.5"
  root_jailbreak_sniffer:
    dependency: "direct main"
    description:
      path: "libraries/root_jailbreak_sniffer-1.0.6"
      relative: true
    source: path
    version: "1.0.6"
  rxdart:
    dependency: transitive
    description:
      name: rxdart
      sha256: "0c7c0cedd93788d996e33041ffecda924cc54389199cde4e6a34b440f50044cb"
      url: "https://pub.dev"
    source: hosted
    version: "0.27.7"
  screenshot:
    dependency: "direct main"
    description:
      name: screenshot
      sha256: "455284ff1f5b911d94a43c25e1385485cf6b4f288293eba68f15dad711c7b81c"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  scrollable_positioned_list:
    dependency: "direct main"
    description:
      name: scrollable_positioned_list
      sha256: "1b54d5f1329a1e263269abc9e2543d90806131aa14fe7c6062a8054d57249287"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.8"
  shared_preferences:
    dependency: transitive
    description:
      name: shared_preferences
      sha256: "81429e4481e1ccfb51ede496e916348668fd0921627779233bd24cc3ff6abd02"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.2"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      sha256: "8568a389334b6e83415b6aae55378e158fbc2314e074983362d20c562780fb06"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  shared_preferences_foundation:
    dependency: transitive
    description:
      name: shared_preferences_foundation
      sha256: "7bf53a9f2d007329ee6f3df7268fd498f8373602f943c975598bbb34649b62a7"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.4"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      sha256: "9f2cbcf46d4270ea8be39fa156d86379077c8a5228d9dfdb1164ae0bb93f1faa"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      sha256: d4ec5fc9ebb2f2e056c617112aa75dcf92fc2e4faaf2ae999caa297473f75d8a
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      sha256: d762709c2bbe80626ecc819143013cc820fa49ca5e363620ee20a8b15a3e3daf
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      sha256: "841ad54f3c8381c480d0c9b508b89a34036f512482c407e6df7a9c4aa2ef8f59"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  shelf:
    dependency: transitive
    description:
      name: shelf
      sha256: ad29c505aee705f41a4d8963641f91ac4cee3c8fad5947e033390a7bd8180fa4
      url: "https://pub.dev"
    source: hosted
    version: "1.4.1"
  shelf_web_socket:
    dependency: transitive
    description:
      name: shelf_web_socket
      sha256: "9ca081be41c60190ebcb4766b2486a7d50261db7bd0f5d9615f2d653637a84c1"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  shimmer:
    dependency: "direct main"
    description:
      name: shimmer
      sha256: "5f88c883a22e9f9f299e5ba0e4f7e6054857224976a5d9f839d4ebdc94a14ac9"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  sliding_up_panel2:
    dependency: "direct main"
    description:
      name: sliding_up_panel2
      sha256: "7c2aac81c03e74fcd070799c5e2011f1c5de7026bd22a76164e81e23a49f2bdb"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.0+1"
  smart_auth:
    dependency: transitive
    description:
      name: smart_auth
      sha256: a25229b38c02f733d0a4e98d941b42bed91a976cb589e934895e60ccfa674cf6
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  smooth_page_indicator:
    dependency: "direct main"
    description:
      name: smooth_page_indicator
      sha256: "725bc638d5e79df0c84658e1291449996943f93bacbc2cec49963dbbab48d8ae"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  source_gen:
    dependency: transitive
    description:
      name: source_gen
      sha256: fc0da689e5302edb6177fdd964efcb7f58912f43c28c2047a808f5bfff643d16
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  source_helper:
    dependency: transitive
    description:
      name: source_helper
      sha256: "6adebc0006c37dd63fe05bca0a929b99f06402fc95aa35bf36d67f5c06de01fd"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.4"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: "53e943d4206a5e30df338fd4c6e7a077e02254531b138a15aec3bd143c1a8b3c"
      url: "https://pub.dev"
    source: hosted
    version: "1.10.0"
  sprintf:
    dependency: transitive
    description:
      name: sprintf
      sha256: "1fc9ffe69d4df602376b52949af107d8f5703b77cda567c4d7d86a0693120f23"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  sqflite:
    dependency: transitive
    description:
      name: sqflite
      sha256: "591f1602816e9c31377d5f008c2d9ef7b8aca8941c3f89cc5fd9d84da0c38a9a"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  sqflite_common:
    dependency: transitive
    description:
      name: sqflite_common
      sha256: "8ed044102f3135add97be8653662052838859f5400075ef227f8ad72ae320803"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.0+1"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: "73713990125a6d93122541237550ee3352a2d84baad52d375a4cad2eb9b7ce0b"
      url: "https://pub.dev"
    source: hosted
    version: "1.11.1"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: ba2aa5d8cc609d96bbb2899c28934f9e1af5cddbd60a827822ea467161eb54e7
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      sha256: "14a00e794c7c11aa145a170587321aedce29769c08d7f58b1d141da75e3b1c6f"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "556692adab6cfa87322a115640c11f13cb77b3f076ddcc5d6ae3c20242bedcde"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  syncfusion_flutter_charts:
    dependency: "direct main"
    description:
      name: syncfusion_flutter_charts
      sha256: c702a611ebb21f13b7107a470d3751ed852a1ec5de0cae7ef9339ccb761b574f
      url: "https://pub.dev"
    source: hosted
    version: "25.1.37"
  syncfusion_flutter_core:
    dependency: "direct main"
    description:
      name: syncfusion_flutter_core
      sha256: d098e3cf5ee0c5a37b083f2efdb7c6bdfaabf871f68758bb491b10b61b3dd0d7
      url: "https://pub.dev"
    source: hosted
    version: "25.1.37"
  syncfusion_flutter_datagrid:
    dependency: "direct main"
    description:
      name: syncfusion_flutter_datagrid
      sha256: "4edf6a41ab6bc398a79a0a29aa67a3fca53dab492d6c844f4c28a94c030f1785"
      url: "https://pub.dev"
    source: hosted
    version: "25.1.37"
  syncfusion_flutter_pdf:
    dependency: "direct main"
    description:
      name: syncfusion_flutter_pdf
      sha256: a40b8895d64be0eaa8c3ba8bd1d27b831dd71ed4ba8445b3ba1d15eaee9f6bf6
      url: "https://pub.dev"
    source: hosted
    version: "25.1.37"
  syncfusion_flutter_sliders:
    dependency: "direct main"
    description:
      name: syncfusion_flutter_sliders
      sha256: "59d3b4bb84cec74b7b882fec2b87c5a9bbd1610e23867733b3d3528a1876a821"
      url: "https://pub.dev"
    source: hosted
    version: "25.1.37"
  syncfusion_flutter_treemap:
    dependency: "direct main"
    description:
      name: syncfusion_flutter_treemap
      sha256: "8e211a5f423d97482ce4ae4b4aa2b9da7074a586306edb40e801efe48e7af35d"
      url: "https://pub.dev"
    source: hosted
    version: "25.1.37"
  syncfusion_flutter_xlsio:
    dependency: "direct main"
    description:
      name: syncfusion_flutter_xlsio
      sha256: "0910cf7c017867ec32ab3ac3145eff4894fb7f281dfdc4a01cfa25d4b945dd9b"
      url: "https://pub.dev"
    source: hosted
    version: "25.1.37"
  syncfusion_officecore:
    dependency: transitive
    description:
      name: syncfusion_officecore
      sha256: e63303bdf0ec64db4717e7c8b2b0867de3c7df0e22ae55a6fedc2a449cc3d808
      url: "https://pub.dev"
    source: hosted
    version: "25.1.37"
  synchronized:
    dependency: transitive
    description:
      name: synchronized
      sha256: "5fcbd27688af6082f5abd611af56ee575342c30e87541d0245f7ff99faa02c60"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: a29248a84fbb7c79282b40b8c72a1209db169a2e0542bce341da992fe1bc7e84
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: "5c2f730018264d276c20e4f1503fd1308dfbbae39ec8ee63c5236311ac06954b"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.1"
  timeline_tile:
    dependency: "direct main"
    description:
      name: timeline_tile
      sha256: "85ec2023c67137397c2812e3e848b2fb20b410b67cd9aff304bb5480c376fc0c"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  timezone:
    dependency: transitive
    description:
      name: timezone
      sha256: "1cfd8ddc2d1cfd836bc93e67b9be88c3adaeca6f40a00ca999104c30693cdca0"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.2"
  timing:
    dependency: transitive
    description:
      name: timing
      sha256: "70a3b636575d4163c477e6de42f247a23b315ae20e86442bebe32d3cabf61c32"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: facc8d6582f16042dd49f2463ff1bd6e2c9ef9f3d5da3d9b087e244a7b564b3c
      url: "https://pub.dev"
    source: hosted
    version: "1.3.2"
  uae_pass_flutter:
    dependency: "direct main"
    description:
      path: "libraries/uae_pass_plug_in-0.0.3"
      relative: true
    source: path
    version: "0.0.3"
  universal_platform:
    dependency: transitive
    description:
      name: universal_platform
      sha256: d315be0f6641898b280ffa34e2ddb14f3d12b1a37882557869646e0cc363d0cc
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0+1"
  url_launcher:
    dependency: "direct main"
    description:
      name: url_launcher
      sha256: e9aa5ea75c84cf46b3db4eea212523591211c3cf2e13099ee4ec147f54201c86
      url: "https://pub.dev"
    source: hosted
    version: "6.2.2"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      sha256: "31222ffb0063171b526d3e569079cf1f8b294075ba323443fdc690842bfd4def"
      url: "https://pub.dev"
    source: hosted
    version: "6.2.0"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      sha256: bba3373219b7abb6b5e0d071b0fe66dfbe005d07517a68e38d4fc3638f35c6d3
      url: "https://pub.dev"
    source: hosted
    version: "6.2.1"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      sha256: ab360eb661f8879369acac07b6bb3ff09d9471155357da8443fd5d3cf7363811
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      sha256: b7244901ea3cf489c5335bdacda07264a6e960b1c1b1a9f91e4bc371d9e68234
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      sha256: "980e8d9af422f477be6948bdfb68df8433be71f5743a188968b0c1b887807e50"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      sha256: "8d9e750d8c9338601e709cd0885f95825086bd8b642547f26bda435aade95d8a"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      sha256: "7754a1ad30ee896b265f8d14078b0513a4dba28d358eabb9d5f339886f4a1adc"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  uuid:
    dependency: transitive
    description:
      name: uuid
      sha256: df5a4d8f22ee4ccd77f8839ac7cb274ebc11ef9adcce8b92be14b797fe889921
      url: "https://pub.dev"
    source: hosted
    version: "4.2.1"
  vector_graphics:
    dependency: transitive
    description:
      name: vector_graphics
      sha256: "0f0c746dd2d6254a0057218ff980fc7f5670fd0fcf5e4db38a490d31eed4ad43"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.9+1"
  vector_graphics_codec:
    dependency: transitive
    description:
      name: vector_graphics_codec
      sha256: "0edf6d630d1bfd5589114138ed8fada3234deacc37966bec033d3047c29248b7"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.9+1"
  vector_graphics_compiler:
    dependency: transitive
    description:
      name: vector_graphics_compiler
      sha256: d24333727332d9bd20990f1483af4e09abdb9b1fc7c3db940b56ab5c42790c26
      url: "https://pub.dev"
    source: hosted
    version: "1.1.9+1"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  very_good_analysis:
    dependency: "direct dev"
    description:
      name: very_good_analysis
      sha256: "9ae7f3a3bd5764fb021b335ca28a34f040cd0ab6eec00a1b213b445dae58a4b8"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.0"
  vm_service:
    dependency: transitive
    description:
      name: vm_service
      sha256: b3d56ff4341b8f182b96aceb2fa20e3dcb336b9f867bc0eafc0de10f1048e957
      url: "https://pub.dev"
    source: hosted
    version: "13.0.0"
  watcher:
    dependency: transitive
    description:
      name: watcher
      sha256: "3d2ad6751b3c16cf07c7fca317a1413b3f26530319181b37e3b9039b84fc01d8"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  web:
    dependency: transitive
    description:
      name: web
      sha256: "97da13628db363c635202ad97068d47c5b8aa555808e7a9411963c533b449b27"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.1"
  web_socket_channel:
    dependency: transitive
    description:
      name: web_socket_channel
      sha256: d88238e5eac9a42bb43ca4e721edba3c08c6354d4a53063afaa568516217621b
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  webview_flutter:
    dependency: "direct main"
    description:
      name: webview_flutter
      sha256: "71e1bfaef41016c8d5954291df5e9f8c6172f1f6ff3af01b5656456ddb11f94c"
      url: "https://pub.dev"
    source: hosted
    version: "4.4.4"
  webview_flutter_android:
    dependency: transitive
    description:
      name: webview_flutter_android
      sha256: "161af93c2abaf94ef2192bffb53a3658b2d721a3bf99b69aa1e47814ee18cc96"
      url: "https://pub.dev"
    source: hosted
    version: "3.13.2"
  webview_flutter_platform_interface:
    dependency: transitive
    description:
      name: webview_flutter_platform_interface
      sha256: "68e86162aa8fc646ae859e1585995c096c95fc2476881fa0c4a8d10f56013a5a"
      url: "https://pub.dev"
    source: hosted
    version: "2.8.0"
  webview_flutter_wkwebview:
    dependency: transitive
    description:
      name: webview_flutter_wkwebview
      sha256: accdaaa49a2aca2dc3c3230907988954cdd23fed0a19525d6c9789d380f4dc76
      url: "https://pub.dev"
    source: hosted
    version: "3.9.4"
  win32:
    dependency: transitive
    description:
      name: win32
      sha256: "350a11abd2d1d97e0cc7a28a81b781c08002aa2864d9e3f192ca0ffa18b06ed3"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.9"
  win32_registry:
    dependency: transitive
    description:
      name: win32_registry
      sha256: "41fd8a189940d8696b1b810efb9abcf60827b6cbfab90b0c43e8439e3a39d85a"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: "589ada45ba9e39405c198fe34eb0f607cddb2108527e658136120892beac46d2"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: b015a8ad1c488f66851d762d3090a21c600e479dc75e68328c52774040cf9226
      url: "https://pub.dev"
    source: hosted
    version: "6.5.0"
  yaml:
    dependency: transitive
    description:
      name: yaml
      sha256: "75769501ea3489fca56601ff33454fe45507ea3bfb014161abc3b43ae25989d5"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
sdks:
  dart: ">=3.3.0 <4.0.0"
  flutter: ">=3.19.0"
