group 'com.emrys.rjsniffer.rjsniffer'
version '1.0'

buildscript {
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.2.0'
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: 'com.android.library'

android {
    compileSdkVersion 31

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    defaultConfig {
        minSdkVersion 16
    }


    externalNativeBuild {
        cmake {
            path "CMakeLists.txt"
        }
    }



    buildFeatures {
        aidl true

    }

    dependencies {
        implementation 'com.scottyab:rootbeer-lib:0.1.0'
    }


}


