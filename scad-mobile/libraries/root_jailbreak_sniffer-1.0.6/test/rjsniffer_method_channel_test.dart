import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  //MethodChannelRjsniffer platform = MethodChannelRjsniffer();
  const MethodChannel channel = MethodChannel('rjsniffer');

  TestWidgetsFlutterBinding.ensureInitialized();

  setUp(() {
    channel.setMockMethodCallHandler((MethodCall methodCall) async {
      return '42';
    });
  });

  tearDown(() {
    channel.setMockMethodCallHandler(null);
  });

  test('getPlatformVersion', () async {
    //expect(await platform.getPlatformVersion(), '42');
  });
}
