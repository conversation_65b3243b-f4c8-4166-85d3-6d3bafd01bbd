import 'package:flutter/material.dart';

class SmallTileCustomClipper extends CustomClipper<Path> {
    SmallTileCustomClipper({super.reclip, required this.isRtl});

  final bool isRtl;
  @override
 Path getClip(Size size) {
    final double height = size.height;
    final double width = size.width;

    final Path path_0 = Path();
      if(isRtl){
    path_0..moveTo(0,65)
     ..cubicTo(0, 28, 0, 30, 18, 30)
    ..cubicTo( 50, 20, 10, 0,  60, 0)
    ..lineTo(width,0)
    ..lineTo(width,height)
    ..lineTo(0,height)
    ..lineTo(0,65)
    ..close();
  }else{
    path_0..lineTo(0, 0)
    ..lineTo(0, height)
    ..lineTo(width, height)
    ..lineTo(width, 66)
    ..cubicTo(width, 28, width, 30, width - 18, 30)
    ..cubicTo(width - 50, 20, width - 10, 0, width - 60, 0)
    ..close();
  }
    return path_0;
  }

  @override
  bool shouldReclip(CustomClipper oldClipper) {
    return true;
  }
}
