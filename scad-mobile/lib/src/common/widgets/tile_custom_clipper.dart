import 'package:flutter/material.dart';

class TileCustomClipper extends CustomClipper<Path> {

  TileCustomClipper({super.reclip, required this.isRtl});

  final bool isRtl;
  
  @override
  Path getClip(Size size) {

    final double height = size.height;
    final double width = size.width;

    final Path path_0 = Path();
    
  if(isRtl){
    path_0..moveTo(0,65)
    ..cubicTo(0, 50,  9, 46, 18, 46)
    ..cubicTo( 30, 47, 44, 40,  46, 14)
    ..cubicTo(50, 10,  40, 0,  80, 0)
    ..lineTo(width,0)
    ..lineTo(width,height)
    ..lineTo(0,height)
    ..lineTo(0,65)
    ..close();
  }else{
    path_0..lineTo(0, 0)
    ..lineTo(0, height)
    ..lineTo(width, height)
    ..lineTo(width, 65)
    ..cubicTo(width, 50, width - 9, 46, width - 18, 46)
    ..cubicTo(width - 30, 47, width - 44, 40, width - 46, 14)
    ..cubicTo(width - 50, 10, width - 40, 0, width - 80, 0)
    ..close();
  }
    return path_0;
    
  }

  @override
  bool shouldReclip(CustomClipper oldClipper) {
    return true;
  }

}
