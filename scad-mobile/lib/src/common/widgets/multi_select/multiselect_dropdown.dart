import 'package:flutter/material.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/check_box_text_row.dart';
import 'package:scad_mobile/src/common/widgets/multi_select/models/value_item.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

typedef OnOptionSelected<T> = void Function(List<ValueItem<T>> selectedOptions);

class MultiSelectDropDown<T> extends StatefulWidget {
  const MultiSelectDropDown({
    required this.updatedSelection,
    required this.options,
    required this.label,
    required this.compareFn,
    super.key,
    this.hint = 'Select',
    this.minSelection = 0,
    this.enableSelectAllOption = false,
    this.selectedOptions = const [],
  }) : assert(
          selectedOptions.length >= minSelection,
          'selectedOptions.length >= minSelection should be true',
        );

  final String label;
  final String hint;
  final int minSelection;
  final bool enableSelectAllOption;

  final List<ValueItem<T>> options;
  final List<ValueItem<T>> selectedOptions;
  final bool Function(T val1, T val2) compareFn;
  final OnOptionSelected<T>? updatedSelection;

  @override
  State<MultiSelectDropDown<T>> createState() => _MultiSelectDropDownState<T>();
}

class _MultiSelectDropDownState<T> extends State<MultiSelectDropDown<T>> {
  final List<ValueItem<T>> _selectedOptions = [];

  OverlayState? _overlayState;
  OverlayEntry? _overlayEntry;
  bool _selectionMode = false;

  late final FocusNode _focusNode;
  final LayerLink _layerLink = LayerLink();

  double optionItemHeight = 60;

  bool isLightMode = true;

  @override
  void initState() {
    super.initState();
    isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    _selectedOptions.addAll(
      widget.selectedOptions,
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _overlayState ??= Overlay.of(context);
      _focusNode.addListener(_handleFocusChange);
    });
    _focusNode = FocusNode();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label.isNotEmpty)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.label,
                style: TextStyle(color: AppColors.greyShade4),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                textScaler: TextScaler.linear(textScaleFactor.value),
              ),
              const SizedBox(height: 4),
            ],
          ),
        CompositedTransformTarget(
          link: _layerLink,
          child: Focus(
            canRequestFocus: true,
            skipTraversal: true,
            focusNode: _focusNode,
            child: InkWell(
              onTap: () {
                if (_focusNode.hasFocus) {
                  _focusNode.unfocus();
                } else {
                  _focusNode.requestFocus();
                }
              },
              child: Container(
                constraints: BoxConstraints(
                  minWidth: MediaQuery.of(context).size.width,
                ),
                padding:
                    const EdgeInsets.symmetric(horizontal: 11, vertical: 9),
                decoration: BoxDecoration(
                  color: isLightMode ? AppColors.white : AppColors.blueShade36,
                  borderRadius: BorderRadius.circular(30),
                  border: Border.all(
                    color: isLightMode
                        ? AppColors.greyShade1
                        : AppColors.blackShade8,
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: _getSelectedContent(),
                    ),
                    if (!_selectionMode)
                      AnimatedRotation(
                        turns: _selectionMode ? 0.5 : 0,
                        duration: const Duration(milliseconds: 200),
                        child: Icon(
                          Icons.keyboard_arrow_down_rounded,
                          color:
                              isLightMode ? AppColors.black : AppColors.white,
                          size: 16,
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  @override
  void dispose() {
    if (_overlayEntry?.mounted == true) {
      if (_overlayState != null && _overlayEntry != null) {
        _overlayEntry?.remove();
      }
      _overlayEntry = null;
      _overlayState?.dispose();
    }
    _focusNode
      ..removeListener(_handleFocusChange)
      ..dispose();

    super.dispose();
  }

  void _handleFocusChange() {
    if (_focusNode.hasFocus && mounted) {
      _overlayEntry = _buildOverlayEntry();
      Overlay.of(context).insert(_overlayEntry!);
      return;
    }

    if (_overlayEntry != null) {
      _overlayEntry?.remove();
    }

    if (mounted) {
      setState(() {
        _selectionMode = _focusNode.hasFocus;
      });
    }
  }

  Widget _getSelectedContent() {
    if (_selectedOptions.isEmpty) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10),
        child: Text(
          widget.hint,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: TextStyle(
            fontSize: 14,
            color: AppColors.greyShade4,
          ),
          textScaler: TextScaler.linear(textScaleFactor.value),
        ),
      );
    } else {
      return Row(
        children: [
          Flexible(
            child: Text(
              (_selectedOptions.firstOrNull?.label ?? ''),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textScaler: TextScaler.linear(textScaleFactor.value),
              style: TextStyle(
                fontSize: 14,
                color: isLightMode ? AppColors.blackShade4 : AppColors.white,
              ),
            ),
          ),
          if (_selectedOptions.length > 1)
            Text(
              ' +${_selectedOptions.length - 1}',
              textScaler: TextScaler.linear(textScaleFactor.value),
              style: TextStyle(
                fontSize: 14,
                color: isLightMode ? AppColors.blackShade4 : AppColors.white,
              ),
            ),
        ],
      );
    }
  }

  List<dynamic> _calculateOffsetSize() {
    final RenderBox? renderBox = context.findRenderObject() as RenderBox?;

    final size = renderBox?.size ?? Size.zero;
    final offset = renderBox?.localToGlobal(Offset.zero) ?? Offset.zero;

    final availableHeight = MediaQuery.of(context).size.height - offset.dy;

    return [
      size,
      availableHeight <
          widget.options.length * (optionItemHeight + 50) +
              (widget.enableSelectAllOption ? optionItemHeight : 0),
    ];
  }

  OverlayEntry _buildOverlayEntry() {
    final values = _calculateOffsetSize();
    final size = values.firstOrNull as Size?;
    final showOnTop = values[1] as bool;

    return OverlayEntry(
      builder: (context) {
        final List<ValueItem<T>> options = widget.options;
        final List<ValueItem<T>> selectedOptions = [..._selectedOptions];

        return StatefulBuilder(
          builder: (context, dropdownState) {
            return Stack(
              children: [
                Positioned.fill(
                  child: GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      _focusNode.unfocus();
                    },
                    child: Container(
                      color: Colors.transparent,
                    ),
                  ),
                ),
                CompositedTransformFollower(
                  link: _layerLink,
                  targetAnchor:
                      showOnTop ? Alignment.topLeft : Alignment.bottomLeft,
                  followerAnchor:
                      showOnTop ? Alignment.bottomLeft : Alignment.topLeft,
                  child: Material(
                    color:
                        isLightMode ? Colors.white : AppColors.blueShade36,
                    elevation: 4,
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.all(
                        Radius.circular(12),
                      ),
                    ),
                    shadowColor: Colors.black,
                    child: Container(
                      decoration: BoxDecoration(
                        backgroundBlendMode: BlendMode.dstATop,
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      constraints: BoxConstraints.loose(
                        Size(
                          size?.width ?? 0,
                          options.length * optionItemHeight +
                              (widget.enableSelectAllOption
                                  ? optionItemHeight
                                  : 0),
                        ),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          _buildOption(
                            const ValueItem(label: 'Select All', value: null),
                            !widget.options
                                .map(
                                  (element) => selectedOptions.any(
                                    (element1) => widget.compareFn(
                                      element.value as T,
                                      element1.value as T,
                                    ),
                                  ),
                                )
                                .toList()
                                .any((element) => element == false),
                            dropdownState,
                            selectedOptions,
                          ),
                          ConstrainedBox(
                            constraints: BoxConstraints(
                              maxHeight:
                                  MediaQuery.of(context).size.height * 0.35,
                            ),
                            child: ListView.separated(
                              separatorBuilder: (_, __) =>
                                  const SizedBox(height: 0),
                              shrinkWrap: true,
                              padding: EdgeInsets.zero,
                              itemCount: options.length,
                              itemBuilder: (context, index) {
                                final option = options[index];
                                final bool isSelected = selectedOptions.any(
                                  (element) => widget.compareFn(
                                    element.value as T,
                                    option.value as T,
                                  ),
                                );

                                return _buildOption(
                                  option,
                                  isSelected,
                                  dropdownState,
                                  selectedOptions,
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Widget _buildOption(
    ValueItem<T> option,
    bool isSelected,
    StateSetter dropdownState,
    List<ValueItem<T>> selectedOptions,
  ) {
    return CheckBoxTextRow(
      title: option.label,
      isSelected: isSelected,
      padding: const EdgeInsets.all(12),
      onChanged: () {
        if (isSelected) {
          if (option.value == null) {
            dropdownState(() {
              selectedOptions
                ..clear()
                ..addAll(widget.selectedOptions);
            });
            setState(() {
              _selectedOptions
                ..clear()
                ..addAll(widget.selectedOptions);
            });
          } else if (widget.minSelection < selectedOptions.length) {
            dropdownState(() {
              selectedOptions.removeWhere(
                (element) =>
                    widget.compareFn(element.value as T, option.value as T),
              );
            });
            setState(() {
              _selectedOptions.removeWhere(
                (element) =>
                    widget.compareFn(element.value as T, option.value as T),
              );
            });
          }
        } else {
          if (option.value == null) {
            dropdownState(() {
              selectedOptions.addAll(widget.options);
            });
            setState(() {
              _selectedOptions.addAll(widget.options);
            });
          } else {
            dropdownState(() {
              selectedOptions.add(option);
            });
            setState(() {
              _selectedOptions.add(option);
            });
          }
        }

        widget.updatedSelection?.call(_selectedOptions);
      },
    );
  }
}
