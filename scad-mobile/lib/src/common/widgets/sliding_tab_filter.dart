import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';

class SlidingTabItem {
  SlidingTabItem({required this.name, required this.icon});
  final String name;
  final String icon;
}

class SlidingTab extends StatefulWidget {
  const SlidingTab({
    required this.tabList,
    required this.selectedTabIndex,
    required this.onTabChange,
    super.key,
  });

  final List<SlidingTabItem> tabList;
  final ValueNotifier<int> selectedTabIndex;
  final void Function(int index) onTabChange;

  @override
  State<SlidingTab> createState() => _SlidingTabState();
}

class _SlidingTabState extends State<SlidingTab> {
  double _giveHeight() {
    return textScaleFactor.value < 1
        ? (textScaleFactor.value + .3)
        : textScaleFactor.value;
  }

  @override
  Widget build(BuildContext context) {

    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      height: 30 * _giveHeight(),
      child:
      ListView.builder(
        itemCount: widget.tabList.length,
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemBuilder: (context, index) {
          return InkWell(
            radius: 30,
            borderRadius: BorderRadius.circular(30),
            onTap: () {
              widget.onTabChange(index);
            },
            child: ValueListenableBuilder(
              valueListenable: widget.selectedTabIndex,
              builder: (context, domain, widget1) {
                return AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(30),
                    color: widget.selectedTabIndex.value == index
                        ? AppColors.blueLight
                        : Colors.transparent,
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 14,
                    vertical: 6,
                  ),
                  child: Row(
                    children: [
                      if (widget.tabList[index].icon.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(right: 12),
                          child: SvgPicture.network(
                            widget.tabList[index].icon,
                            width: 18,
                            height: 18,
                            color: widget.selectedTabIndex.value == index
                                ? AppColors.white
                                : AppColors.greyShade4,
                          ),
                        ),
                      Text(
                        widget.tabList[index].name,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: widget.selectedTabIndex.value == index
                              ? AppColors.white
                              : AppColors.greyShade4,
                        ),
                        textScaler: TextScaler.linear(textScaleFactor.value),
                      ),
                    ],
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }
}
