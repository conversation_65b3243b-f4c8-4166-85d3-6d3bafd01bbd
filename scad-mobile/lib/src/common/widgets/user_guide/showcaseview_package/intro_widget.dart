
import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/showcaseview.dart';
import 'package:scad_mobile/src/features/home/<USER>/bloc/home_bloc/home_bloc.dart';
import 'package:scad_mobile/src/features/products/presentation/bloc/products_bloc.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class IntroWidget extends StatelessWidget {
  IntroWidget({
    required this.title,
    required this.description,
    required this.stepIndex,
    required this.child,
    required this.stepKey,
    required this.totalSteps,
    this.position = TooltipPosition.bottom,
    this.arrowPadding,
    this.arrowAlignment,
    super.key,
    this.isDownArrow = false,
    this.crossAxisAlignment = CrossAxisAlignment.start,
    this.targetPadding,
    this.targetBorderRadius = 150,
    this.onNext,
    this.onPrevious,
  });

  final bool isDownArrow;
  final String title;
  final String description;
  final EdgeInsetsGeometry? arrowPadding;
  final AlignmentGeometry? arrowAlignment;
  final int stepIndex;
  final int totalSteps;
  final CrossAxisAlignment crossAxisAlignment;
  final Widget child;
  final GlobalKey stepKey;
  final TooltipPosition position;
  final EdgeInsets? targetPadding;
  final double targetBorderRadius;
  final VoidCallback? onNext;
  final VoidCallback? onPrevious;

  ScrollController _scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.sizeOf(context);
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Showcase.withWidget(
      key: stepKey,
      height: size.height,
      width: size.width - 30,
      targetPadding: targetPadding ?? EdgeInsets.zero,
      targetShapeBorder: const CircleBorder(),
      targetBorderRadius: BorderRadius.all(
        Radius.circular(targetBorderRadius),
      ),
      onTargetClick: () => onNextPressed(context),
      onBarrierClick: () => onNextPressed(context),
      disableMovingAnimation: true,
      disposeOnTap: false,
      tooltipPosition: position,
      container: Column(
        crossAxisAlignment: crossAxisAlignment,
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Visibility(
            visible: !isDownArrow,
            child: Container(
              padding: arrowPadding,
              margin: const EdgeInsets.only(bottom: 10),
              alignment: arrowAlignment,
              child: SvgPicture.asset(AppImages.icUpArrow),
            ),
          ),
          Container(
            width: MediaQuery.sizeOf(context).width * .91,
            constraints: const BoxConstraints(
              // maxHeight: MediaQuery.sizeOf(context).height*.25,
              minHeight: 10,
            ),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: isLightMode ? AppColors.blueLight : AppColors.blueLightOld,
            ),
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 10),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Text(
                          title,
                          style: AppTextStyles.s16w6cWhite,
                          textScaler: TextScaler.linear(textScaleFactor.value),
                        ),
                      ),
                      InkWell(
                        // constraints: const BoxConstraints(),
                        // padding: EdgeInsets.zero,
                        child: Icon(
                          Icons.close_rounded,
                          color: AppColors.white,
                          size: 18,
                        ),
                        onTap: () async {
                          ShowCaseWidget.of(context).dismiss();
                          preStatusUpdate(context);
                          await HiveUtilsSettings.saveUserGuideStatus();
                          _navToHome(context);
                        },
                      ),
                    ],
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Text(
                      description,
                      maxLines: 8,
                      overflow: TextOverflow.ellipsis,
                      style: AppTextStyles.s14w4cBlue.copyWith(
                        color: AppColors.white,
                      ),
                      textScaler: TextScaler.linear(textScaleFactor.value),
                    ),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      SizedBox(
                        width: 60,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            SizedBox(
                              height: 30,
                              child: InkWell(
                                onTap: () {
                                  if (stepIndex != 1) {
                                    if (onPrevious != null) {
                                      onPrevious!.call();
                                    }
                                    ShowCaseWidget.of(context).previous();
                                  }
                                },
                                child: RotatedBox(
                                  quarterTurns:
                                      DeviceType.isDirectionRTL(context)
                                          ? 2
                                          : 0,
                                  child: SvgPicture.asset(
                                    AppImages.icPrevWhiteLine,
                                    colorFilter: ColorFilter.mode(
                                      stepIndex == 1
                                          ? AppColors.white.withOpacity(0.4)
                                          : AppColors.white,
                                      BlendMode.srcIn,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            if (stepIndex != totalSteps)
                              SizedBox(
                                height: 30,
                                child: InkWell(
                                  onTap: () => onNextPressed(context),
                                  child: RotatedBox(
                                    quarterTurns:
                                        DeviceType.isDirectionRTL(context)
                                            ? 2
                                            : 0,
                                    child: SvgPicture.asset(
                                      AppImages.icNextWhiteLine,
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                      Text(
                        '$stepIndex ${LocaleKeys.of.tr()} $totalSteps',
                        style: AppTextStyles.s14w4cBlue
                            .copyWith(color: AppColors.white),
                        textScaler: TextScaler.linear(textScaleFactor.value),
                      ),
                    ],
                  ),
                  if (stepIndex == totalSteps)
                    Padding(
                      padding: const EdgeInsets.only(top: 8, bottom: 8),
                      child: SizedBox(
                        height: 30,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            actionButton(context, false),
                            actionButton(context, true),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
          Visibility(
            visible: isDownArrow,
            child: Container(
              padding: arrowPadding,
              margin: const EdgeInsets.only(top: 10),
              alignment: arrowAlignment,
              child: SvgPicture.asset(AppImages.icDownArrow),
            ),
          ),
        ],
      ),
      child: child,
    );
  }

  TextButton actionButton(BuildContext context, bool isDoneButton) {
    return TextButton(
      style: TextButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 10),
        backgroundColor: isDoneButton ? AppColors.white : Colors.transparent,
        shape: RoundedRectangleBorder(
          side: BorderSide(color: AppColors.white),
          borderRadius: BorderRadius.circular(4),
        ),
      ),
      onPressed: () async {
        ShowCaseWidget.of(context).dismiss();
        preStatusUpdate(context);
        await HiveUtilsSettings.saveUserGuideStatus();
        _navToHome(context);
        if (!isDoneButton) {
          // AutoRouter.of(context).replaceAll([HomeNavigationRoute(), UserGuideScreenRoute()]);
          await AutoRouter.of(context).push(const UserGuideScreenRoute());
        }
      },
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (!isDoneButton)
            Icon(
              Icons.chevron_left_rounded,
              color: AppColors.white,
              size: 20,
            ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: isDoneButton ? 30 : 0),
            child: Text(
              isDoneButton
                  ? LocaleKeys.done.tr()
                  : LocaleKeys.backToUserGuide.tr(),
              style: AppTextStyles.s14w4cBlue.copyWith(
                color: isDoneButton ? AppColors.blueLight : AppColors.white,
              ),
              textScaler: TextScaler.linear(textScaleFactor.value),
            ),
          ),
        ],
      ),
    );
  }

  void onNextPressed(BuildContext context) async {
    if (stepIndex != totalSteps) {
      if (onNext != null) {
        onNext!.call();
      }
      ShowCaseWidget.of(context).next();
    } else {
      ShowCaseWidget.of(context).dismiss();
      await HiveUtilsSettings.saveUserGuideStatus();
    }
  }

  void _navToHome(BuildContext context) {
    AutoRouter.of(context).popUntilRoot();
    if (AutoRouter.of(context).current.name != HomeNavigationRoute.name) {
      AutoRouter.of(context).replace(HomeNavigationRoute());
    }
    context.read<HomeBloc>().add(NavToHomeEvent());
  }

  void preStatusUpdate(BuildContext context) {
    if (HiveUtilsSettings.getUserGuideStatus() == UserGuides.Products) {
      context.read<ProductsBloc>().add(ProductsResetEvent());
    }
  }
}
