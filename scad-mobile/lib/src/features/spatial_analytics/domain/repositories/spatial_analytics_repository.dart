part of 'spatial_analytics_imports.dart';

abstract class SpatialAnalyticsRepository {
  Future<RepoResponse<ArcGISAuthResponse>> getArcGisToken();

  Future<RepoResponse<SpatialAnalyticsData>> getArcGisData();

  Future<RepoResponse<ArcGISDashboardDetails>> getDashboardDetails(
      {required String urlPath});

  Future<RepoResponse<PopulationOverviewNationalityData>>
      getPopulationOverviewByNationalityData({
    required String urlPath,
  });

  Future<RepoResponse<PopulationOverviewGenderData>>
      getPopulationOverviewByGenderData({
    required String urlPath,
  });


  Future<RepoResponse<HouseholdPopulationNationalsData>>
  getHouseholdPopulationNationalsData({
    required String urlPath,
  });

  Future<RepoResponse<HouseholdPopulationGenderData>>
  getHouseholdPopulationGenderData({
    required String urlPath,
  });

  Future<RepoResponse<HouseholdPopulationRegionData>>
  getHouseholdPopulationRegionData({
    required String urlPath,
  });


  Future<RepoResponse<FlatTransactionByBedRoomsData>>
  getFlatTransactionByBedRoomsData({
    required String urlPath,
  });

  Future<RepoResponse<FlatTransactionByDistrictsData>>
  getFlatTransactionByDistrictsData({
    required String urlPath,
  });

  Future<RepoResponse<FlatTransactionByMonthWiseAvgSumData>>
  getFlatTransactionByMonthWiseAvgSumData({
    required String urlPath,
  });





  Future<RepoResponse<JobSeekersCheckBox>>
  getJobSeekersCheckbox({
    required String urlPath,
  });

  Future<RepoResponse<JobSeekersBySpecialization>> getJobSeekersDataBySpecialization({
    required String urlPath,
  });

  Future<RepoResponse<JobSeekersByWorkExperience>> getJobSeekersDataByWorkExperience({
    required String urlPath,
  });

  Future<RepoResponse<JobSeekersByJobVsVacanciesSeekers>> getJobSeekersDataByJobVsVacanciesSeekers({
    required String urlPath,
  });

  Future<RepoResponse<JobSeekersByJobVsVacanciesVacancies>> getJobSeekersDataByJobVsVacanciesVacancies({
    required String urlPath,
  });

  Future<RepoResponse<JobSeekersByVacancies>> getJobSeekersDataByVacancies({
    required String urlPath,
  });

}
