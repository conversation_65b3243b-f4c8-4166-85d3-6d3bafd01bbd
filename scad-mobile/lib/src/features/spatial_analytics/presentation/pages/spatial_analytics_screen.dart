import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart' as locale;
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_sliding_up_panel/sliding_up_panel_widget.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/widgets/bottom_sheet_top_notch.dart';
import 'package:scad_mobile/src/common/widgets/no_data_placeholder.dart';
import 'package:scad_mobile/src/common/widgets/rounded_dropdown_widget.dart';
import 'package:scad_mobile/src/config/firebase_config/firebase_config.dart';
import 'package:scad_mobile/src/features/spatial_analytics/data/models/arc_gis_dashboard_details.dart';
import 'package:scad_mobile/src/features/spatial_analytics/data/models/arc_gis_district.dart';
import 'package:scad_mobile/src/features/spatial_analytics/data/models/job_seekers/job_seekers_checkbox.dart'
    as job_seeker;
import 'package:scad_mobile/src/features/spatial_analytics/data/models/modules_model.dart';
import 'package:scad_mobile/src/features/spatial_analytics/data/models/spatial_analytics_data.dart';
import 'package:scad_mobile/src/features/spatial_analytics/presentation/bloc/spatial_analytics_bloc.dart';
import 'package:scad_mobile/src/features/spatial_analytics/presentation/widgets/labour_force/job_seekers.dart';
import 'package:scad_mobile/src/features/spatial_analytics/presentation/widgets/point_of_interest_popup.dart';
import 'package:scad_mobile/src/features/spatial_analytics/presentation/widgets/population/household_population.dart';
import 'package:scad_mobile/src/features/spatial_analytics/presentation/widgets/population/population_overview.dart';
import 'package:scad_mobile/src/features/spatial_analytics/presentation/widgets/real_estate/flat_transaction.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/app_utils/downloadHelper.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

/*
* Flutter native view is used in this screen to show the Arc GIS map.
* Method calls to and from Native view is handled using the Method channel and Event channel.
* Arc GIS documentation can be found at
*   https://developers.arcgis.com/kotlin/
*   https://developers.arcgis.com/swift/
* */

@RoutePage()
class SpatialAnalyticsScreen extends StatefulWidget {
  const SpatialAnalyticsScreen({
    this.initialModuleKey,
    super.key,
  });

  final String? initialModuleKey;

  static MyDataModel modules = MyDataModel.fromJson({
    'Population': {
      'title_en': 'Population',
      'title_ar': 'السكانية',
      'sub': [
        {
          'title': 'Population Overview',
          'title_ar': 'نظرة عامة على السكان',
          'key': '3',
          'domain_name_key': 'POP_OFFICIAL',
        },
        {
          'title': 'Household Population',
          'title_ar': 'سكان الأسرة',
          'key': '2',
          'domain_name_key': 'HOUSE_HOLD',
          'tab_module': [
            {
              'title': 'Population by Region',
              'key': '4',
              'domain_name_key': 'POPULATION_BY_REGION',
            }
          ],
        },
      ],
    },
    'Labour Force': {
      'title_en': 'Labour Force',
      'title_ar': 'القوى العاملة',
      'sub': [
        {
          'title': 'Job Seekers',
          'title_ar': 'الباحثين عن عمل',
          'key': '1',
          'domain_name_key': 'JOB_SEEKERS',
        }
      ],
    },
    // 'Real Estate': {
    //   'title_en': 'Real Estate',
    //   'title_ar': 'Real Estate Ar',
    //   'sub': [
    //     {
    //       'title': 'Flat Transaction',
    //       'title_ar': 'Flat Transaction Ar',
    //       'key': '4',
    //       'domain_name_key': 'POP_REGION',
    //     }
    //   ],
    // },
  });

  @override
  State<SpatialAnalyticsScreen> createState() => _SpatialAnalyticsScreenState();
}

class _SpatialAnalyticsScreenState extends State<SpatialAnalyticsScreen> {
  /// Platform view updates is done using the help of these function.
  /// [_handleIncomingMessage] method receives the messages from native view
  /// [_sendMessageToNative] method used to send message to native view
  /// declare a messageKey value String and call the methods according to the value

  static const String platformViewType = 'arc-gis-map';
  static const String eventChannelName = 'arcgisMapEvent';
  static const String methodChannelName = 'arcgisMapMethod';
  static const EventChannel eventChannel = EventChannel(eventChannelName);
  static const platform = MethodChannel(methodChannelName);

  static const String messageKey = 'message_key';

  static const String inMessageKeyValueMapReady = 'map_ready';
  static const String inMessageKeyValueApiResponse = 'api_response';
  static const String inMessageKeyValueDistrictSelected = 'district_selected';
  static const String inMessageKeyValueMeasurementResult = 'measurement_result';
  static const String inMessageKeyValueSelectionDone = 'selection_done';
  static const String inMessageKeyValueToast = 'toast_message';

  static const String outMessageKeyValueModuleChanged = 'module_changed';
  static const String outMessageKeyValueUpdateLayer = 'update_layer';
  static const String outMessageKeyValuePOI = 'point_of_interest';
  static const String outMessageKeyValueMeasurement = 'measurement';
  static const String outMessageKeyValueSelection = 'selection';
  static const String outMessageKeyValueClearMapSelection =
      'clear_map_selection';
  static const String outMessageKeyValueHasSomethingToClearOnMap =
      'has_something_to_clear_on_map';

  String? arcGisAccessToken;
  String? arcGisLicense;
  ArcGISDashboardDetails? dashboardDetails;
  SpatialAnalyticsData? spatialAnalyticsModules;

  ArcGISDistrict? selectedDistrict;
  FocusNode districtFocusNode = FocusNode();
  final TextEditingController districtController = TextEditingController();

  ValueNotifier<bool> fullScreenMap = ValueNotifier(false);
  ValueNotifier<List<ArcGISDistrict>> districtList = ValueNotifier([]);
  ValueNotifier<List<ArcGISDistrict>> filteredDistrictList = ValueNotifier([]);

  List<CommonLayers> selectedPOI = [];

  DataItemModel? selectedTheme;
  SubItemModel? selectedSubTheme;

  bool isLightMode = true;
  bool isMapLoading = true;

  String geometry = '';
  String measurementType = '';
  double measurementDistanceInMeters = 0;
  double measurementAreaInSquareMeters = 0;

  SuggestionsController<ArcGISDistrict> suggestionsController =
      SuggestionsController();
  SlidingUpPanelController panelController = SlidingUpPanelController();

  @override
  void initState() {
    super.initState();
    FirebaseConfig.setScreenToAnalytics('Spatial Analytics');
    isLightMode = HiveUtilsSettings.getThemeMode() == ThemeMode.light;

    context.read<SpatialAnalyticsBloc>().add(const ArcGISInitEvent());

    // WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
    //   final bool hasPermission = await AppPermissions.checkPermissions(
    //     context,
    //     [AppPermission.location],
    //     onCancel: () {
    //       context.popRoute();
    //     },
    //   );
    //   if (hasPermission) {
    //     if (mounted) {
    //       context.read<SpatialAnalyticsBloc>().add(const ArcGISInitEvent());
    //     }
    //   }
    // });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        toolbarHeight:56,
        backgroundColor: AppColors.scaffoldBackground,
        leading: Material(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          child: InkWell(
            onTap: () {
              Navigator.of(context).maybePop();
            },
            borderRadius: BorderRadius.circular(8),
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 14,
                vertical: 6,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  RotatedBox(
                    quarterTurns:
                    DeviceType.isDirectionRTL(
                      context,
                    )
                        ? 2
                        : 0,
                    child: Padding(
                      padding:
                      const EdgeInsets.all(8),
                      child: SvgPicture.asset(
                        AppImages.icArrowLeft,
                        colorFilter: ColorFilter.mode(
                          AppColors.blue,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      body: Column(
        children: [
          Expanded(
            child: BlocConsumer<SpatialAnalyticsBloc, SpatialAnalyticsState>(
              listener: (context, state) {
                if (state is ArcGISInitSuccessState) {
                  arcGisAccessToken = state.token;
                  arcGisLicense = state.license;
                  spatialAnalyticsModules = state.modules;
                  dashboardDetails = state.dashboardDetails;
                } else if (state is PopulationUpdateDotVisibilityState) {
                  _moduleChangedToHouseholdPopulationUpdateLayer(
                    showEmirati: state.showEmirati,
                    showNonEmirati: state.showNonEmirati,
                  );
                } else if (state is JobSeekersCheckBoxUpdateState) {
                  _moduleChangedToJobSeekers(
                    reloadMap: false,
                    selectedExperienceCheckBox: state.jobSeekersCheckBoxData,
                  );
                }
              },
              builder: (context, state) {
                if (state is ArcGISInitLoadingState) {
                  return const Center(child: CircularProgressIndicator());
                } else if (state is ArcGISInitFailureState) {
                  return Center(
                    child: NoDataPlaceholder(msg: state.error),
                  );
                } else if (arcGisAccessToken == null ||
                    spatialAnalyticsModules == null) {
                  return const SizedBox();
                } else {
                  return ValueListenableBuilder(
                    valueListenable: fullScreenMap,
                    builder: (context, _, __) {
                      return IgnorePointer(
                        ignoring: selectedTheme == null,
                        child: Stack(
                          children: [
                            _slidingView(state),
                            if (fullScreenMap.value) ...[
                              if (measurementType == 'area' ||
                                  measurementType == 'distance')
                                _measurementControlsWidget()
                              else if (measurementType == 'selection')
                                _selectControlsWidget(),
                            ],
                          ],
                        ),
                      );
                    },
                  );
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _slidingView(SpatialAnalyticsState state) {
    return Stack(
      children: [
        Container(
          height: MediaQuery.sizeOf(context).height * 0.9,
          alignment: Alignment.center,
          child: const CircularProgressIndicator(),
        ),
        Container(
          height: MediaQuery.sizeOf(context).height,
          alignment: Alignment.center,
          child: Platform.isAndroid
              ? PlatformViewLink(
                  viewType: platformViewType,
                  surfaceFactory: (context, controller) {
                    return AndroidViewSurface(
                      controller: controller as AndroidViewController,
                      gestureRecognizers: const <Factory<
                          OneSequenceGestureRecognizer>>{},
                      hitTestBehavior: PlatformViewHitTestBehavior.opaque,
                    );
                  },
                  onCreatePlatformView: (params) {
                    return PlatformViewsService.initExpensiveAndroidView(
                      id: params.id,
                      viewType: platformViewType,
                      layoutDirection: TextDirection.ltr,
                      creationParams: {
                        'licenseString': arcGisLicense,
                        'token': arcGisAccessToken,
                        'baseMapPortalId': HiveUtilsSettings.getThemeMode() ==
                                ThemeMode.light
                            ? spatialAnalyticsModules?.baseMapLightPortal ?? ''
                            : spatialAnalyticsModules?.baseMapDarkPortal ?? '',
                      },
                      creationParamsCodec: const StandardMessageCodec(),
                      onFocus: () {
                        params.onFocusChanged(true);
                      },
                    )
                      ..addOnPlatformViewCreatedListener((i) {
                        params.onPlatformViewCreated(
                          i,
                        );
                        _initEvent();
                      })
                      ..create();
                  },
                )
              : Platform.isIOS
                  ? UiKitView(
                      viewType: platformViewType,
                      layoutDirection: TextDirection.ltr,
                      creationParams: {
                        'licenseString': arcGisLicense,
                        'token': arcGisAccessToken,
                        'baseMapPortalId': HiveUtilsSettings.getThemeMode() ==
                                ThemeMode.light
                            ? spatialAnalyticsModules?.baseMapLightPortal ?? ''
                            : spatialAnalyticsModules?.baseMapDarkPortal ?? '',
                      },
                      onPlatformViewCreated: (id) {
                        _initEvent();
                      },
                      creationParamsCodec: const StandardMessageCodec(),
                    )
                  : const SizedBox(),
        ),
        if (!fullScreenMap.value)
          _searchBar(),
        if (!fullScreenMap.value)
          _mapButtons(),
        SlidingUpPanelWidget(
          enableOnTap: false,
          controlHeight: MediaQuery.sizeOf(context).height * (textScaleFactor.value > 1 ? 0.09: 0.07),
          upperBound: 0.97,
          elevation: 12,
          panelController: panelController,
          child: Container(
            decoration: ShapeDecoration(
              color: isLightMode
                  ? AppColors.scaffoldBackgroundLight
                  : AppColors.blueShade32,
              shadows: const [
                BoxShadow(
                  blurRadius: 5,
                  spreadRadius: 2,
                  color: Color(0x11000000),
                ),
              ],
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(10),
                  topRight: Radius.circular(10),
                ),
              ),
            ),
            child: Column(
              children: [
                const SizedBox(height: 10),
                const BottomSheetTopNotch(),
                const SizedBox(height: 10),
                Center(
                  child: Text(
                    LocaleKeys.spatialAnalysis.tr(),
                    style: AppTextStyles.s24w5cBlackOrWhiteShade.copyWith(
                      color: !isLightMode ? AppColors.white : null,
                    ),
                    textScaler: TextScaler.linear(
                      textScaleFactor.value,
                    ),
                  ),
                ),
                IgnorePointer(
                  ignoring: state is SpatialAnalyticsDataLoadingState,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 20,
                    ),
                    child: Row(
                      children: [
                        RoundedDropDownWidget<DataItemModel>(
                          constraintWidth: (MediaQuery.sizeOf(context).width - 64) / 2,
                          width: (MediaQuery.sizeOf(context).width - 64) / 2,
                          title: LocaleKeys.theme.tr(),
                          items: SpatialAnalyticsScreen.modules.data.values
                              .toList(),
                          value: selectedTheme,
                          onChanged: (val) {
                            if (selectedTheme?.titleEn != val?.titleEn) {
                              setState(() {
                                selectedTheme = val;
                                selectedSubTheme = SpatialAnalyticsScreen
                                    .modules
                                    .data[selectedTheme?.titleEn]
                                    ?.sub
                                    .first;
                                _onModuleChange();
                                districtController.text = '';
                                FocusManager.instance.primaryFocus?.unfocus();

                                suggestionsController.close();
                              });
                            }
                          },
                        ),
                        const SizedBox(width: 20),
                        RoundedDropDownWidget<SubItemModel>(
                          constraintWidth: (MediaQuery.sizeOf(context).width - 64) / 2,
                          width: (MediaQuery.sizeOf(context).width - 64) / 2,
                          title: LocaleKeys.subTheme.tr(),
                          items: SpatialAnalyticsScreen
                              .modules.data[selectedTheme?.titleEn]?.sub
                              .toList(),
                          value: selectedSubTheme,
                          onChanged: (val) {
                            if (selectedSubTheme != val) {
                              setState(() {
                                selectedSubTheme = val;
                                _onModuleChange();
                                districtController.text = '';
                                FocusManager.instance.primaryFocus?.unfocus();
                                suggestionsController.close();
                              });
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                ),
                Divider(
                  height: 1,
                  color: isLightMode
                      ? AppColors.greyShade1
                      : AppColors.blackShade4,
                  thickness: 1,
                ),
                Flexible(
                  child: ListView(
                    shrinkWrap: true,
                    padding: const EdgeInsets.only(bottom: 100),
                    children: [
                      (switch (selectedSubTheme?.titleEn) {
                        'Population Overview' => PopulationOverview(
                          hasAccess: (spatialAnalyticsModules?.modules??[]).any((element) => element.key == selectedSubTheme?.key),
                            content: _getContent(),
                            moduleKey: selectedSubTheme?.key ?? '',
                            sourceAttributes: _sourceAttributes(),
                          ),
                        'Household Population' => HouseholdPopulation(
                          hasAccess: (spatialAnalyticsModules?.modules??[]).any((element) => element.key == selectedSubTheme?.key),
                          content: _getContent(),
                            moduleKey: selectedSubTheme?.key ?? '',
                            sourceAttributes: _sourceAttributes(),
                          ),
                        'Flat Transaction' => FlatTransaction(
                          hasAccess: (spatialAnalyticsModules?.modules??[]).any((element) => element.key == selectedSubTheme?.key),
                          content: _getContent(),
                            moduleKey: selectedSubTheme?.key ?? '',
                          ),
                        'Job Seekers' => JobSeekers(
                          hasAccess: (spatialAnalyticsModules?.modules??[]).any((element) => element.key == selectedSubTheme?.key),
                          content: _getContent(),
                            moduleKey: selectedSubTheme?.key ?? '',
                            sourceAttributes: _sourceAttributes(),
                          ),
                        String() => const SizedBox(),
                        null => const SizedBox(),
                      }),
                      if (state is! SpatialAnalyticsDataLoadingState &&
                          selectedTheme?.titleEn == 'Population')
                      _methodologyButton(),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _methodologyButton() {
    final String url = spatialAnalyticsModules?.modules
            ?.firstWhere(
              (element) => element.name == selectedSubTheme?.titleEn,
              orElse: () => Modules(),
            )
            .attachmentPdf ??
        '';

    return url.isEmpty
        ? const SizedBox()
        : Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 12,
            ),
            child: Row(
              children: [
                Material(
                  color: Colors.transparent,
                  borderRadius: BorderRadius.circular(12),
                  child: InkWell(
                    borderRadius: BorderRadius.circular(12),
                    onTap: () {
                      final Uri uri = Uri.parse(url);

                      if (mounted) {
                        unawaited(
                          DownloadHelper.downloadFileToDownloadsDir(
                            context,
                            uri.toString(),
                          ),
                        );
                      }
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(8),
                      child: Row(
                        children: [
                          SvgPicture.asset(
                            AppImages.icLink,
                            colorFilter: ColorFilter.mode(
                              isLightMode
                                  ? AppColors.blueShade22
                                  : AppColors.white,
                              BlendMode.srcIn,
                            ),
                          ),
                          const SizedBox(
                            width: 12,
                          ),
                          Text(
                            LocaleKeys.methodology.tr(),
                            style: isLightMode
                                ? AppTextStyles.s14w4cBlue
                                : AppTextStyles.s14w4cBlue.copyWith(
                                    color: AppColors.white,
                                  ),
                            textScaler: TextScaler.linear(
                              textScaleFactor.value,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
  }

  Future<void> _selectDistrict(
      {int? districtId,
      bool clearPrevSelection = false,
      bool setSearchText = false,}) async {
    final int i = districtList.value
        .indexWhere((element) => element.districtId == districtId);
    if (i >= 0) {
      selectedDistrict = districtList.value[i];
      if (setSearchText) {
        setState(() {
          districtController.text = DeviceType.isDirectionRTL(context)
              ? selectedDistrict?.districtAra ?? ''
              : selectedDistrict?.districtEng ?? '';
        });
        FocusManager.instance.primaryFocus?.unfocus();
      } else {
        if (districtController.text.isNotEmpty) {
          setState(() {
            districtController.text = '';
          });
          FocusManager.instance.primaryFocus?.unfocus();
          suggestionsController.close();
        }
      }
      context
          .read<SpatialAnalyticsBloc>()
          .add(ArcGISDistrictChangeEvent(district: selectedDistrict));
      _onModuleChange(reloadMap: false);
    }
    if (clearPrevSelection) {
      await _sendMessageToNative(
        outMessageKeyValueClearMapSelection,
        <String, dynamic>{},
      );
    }
  }

  Future<bool> _downloadPoiIcons() async {
    final ValueNotifier<int> poiIconCompleted = ValueNotifier(0);
    final List<CommonLayers> list = spatialAnalyticsModules?.commonLayers ?? [];

    unawaited(
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return Material(
            color: Colors.transparent,
            child: Center(
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    const SizedBox(
                      width: 60,
                      height: 60,
                      child: CircularProgressIndicator(),
                    ),
                    ValueListenableBuilder(
                      valueListenable: poiIconCompleted,
                      builder: (context, _, __) {
                        return Text(
                          '${poiIconCompleted.value}/${list.length}',
                          style: AppTextStyles.s12w4cGreyShade4,
                          textScaler: TextScaler.linear(textScaleFactor.value),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );

    for (int i = 0; i < list.length; i++) {
      final String? filePath = await DownloadHelper.downloadFileToSupportDir(
        url: (isLightMode ? list[i].lightIcon : list[i].lightIcon) ?? '',
        folder: '/poi/${isLightMode ? 'light' : 'dark'}/',
        checkFileExists: true,
      );

      if (filePath == null) {
        break;
      }
      ++poiIconCompleted.value;
      list[i].filePath = filePath;
    }

    if (mounted) {
      Navigator.pop(context);
      if (poiIconCompleted.value != list.length) {
        AppMessage.showOverlayNotification(
          '',
          LocaleKeys.somethingWentWrong.tr(),
          msgType: 'error',
        );
        return false;
      } else {
        return true;
      }
    }
    return false;
  }

  Widget _mapButton(Widget icon, VoidCallback? onTap, String toolTipMessage) {
    return Tooltip(
      message: toolTipMessage,
      child: Material(
        color: AppColors.black.withOpacity(0.53),
        borderRadius: BorderRadius.circular(
          8,
        ),
        child: InkWell(
          borderRadius: BorderRadius.circular(
            8,
          ),
          onTap: onTap,
          child: SizedBox(
            height: 39,
            width: 39,
            child: icon,
          ),
        ),
      ),
    );
  }

  DashboardAttributes? _sourceAttributes() {
    return dashboardDetails?.features
        ?.where(
          (element) =>
              element.attributes?.domainNameKey ==
              selectedSubTheme?.domainNameKey,
        )
        .firstOrNull
        ?.attributes;
  }

  Widget _selectControlsWidget() {
    return Positioned(
      bottom: 0,
      child: Container(
        width: MediaQuery.sizeOf(context).width,
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(
              20,
            ),
          ),
          color: isLightMode ? AppColors.white : AppColors.blueShade32,
        ),
        child: Padding(
          padding: const EdgeInsets.fromLTRB(12, 0, 12, 12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const BottomSheetTopNotch(),
              const SizedBox(
                height: 12,
              ),
              Row(
                children: [
                  Expanded(
                    child: Text(
                      LocaleKeys.smallAreaSelection.tr(),
                      style: isLightMode
                          ? AppTextStyles.s16w5cBlackShade1
                          : AppTextStyles.s16w5cBlackShade1
                              .copyWith(color: AppColors.white),
                      textScaler: TextScaler.linear(textScaleFactor.value),
                    ),
                  ),
                  _selectionUndoRedoButtons(),
                ],
              ),
              const SizedBox(
                height: 30,
              ),
              ElevatedButton(
                onPressed: _selectionDone,
                child: Text(
                  LocaleKeys.done.tr(),
                  textScaler: TextScaler.linear(textScaleFactor.value),
                ),
              ),
              const SizedBox(
                height: 16,
              ),
              TextButton(
                onPressed: _selectionCancel,
                child: Text(
                  LocaleKeys.cancel.tr(),
                  textScaler: TextScaler.linear(textScaleFactor.value),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _measurementControlsWidget() {
    final bool isDisplayingResult =
        measurementDistanceInMeters > 0 || measurementAreaInSquareMeters > 0;

    String distance = '';
    String distanceUnit = '';
    if (measurementDistanceInMeters >= 1000) {
      distance = (measurementDistanceInMeters / 1000).toStringAsFixed(2);
      distanceUnit = 'Km';
    } else {
      distance = measurementDistanceInMeters.toStringAsFixed(2);
      distanceUnit = 'm';
    }

    String area = '';
    String areaUnit = '';
    if (measurementAreaInSquareMeters >= 1000000) {
      area = (measurementAreaInSquareMeters / 1000000).toStringAsFixed(2);
      areaUnit = 'km²';
    } else {
      area = measurementAreaInSquareMeters.toStringAsFixed(2);
      areaUnit = 'm²';
    }

    return Positioned(
      bottom: 0,
      child: Container(
        width: MediaQuery.sizeOf(context).width,
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(
              20,
            ),
          ),
          color: isLightMode ? AppColors.white : AppColors.blueShade32,
        ),
        child: Padding(
          padding: const EdgeInsets.fromLTRB(12, 0, 12, 12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const BottomSheetTopNotch(),
              const SizedBox(
                height: 12,
              ),
              Row(
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 5.5),
                      child: Text(
                        measurementType == 'area'
                            ? LocaleKeys.areaMeasurement.tr()
                            : measurementType == 'distance'
                                ? LocaleKeys.distanceMeasurement.tr()
                                : '',
                        style: isLightMode
                            ? AppTextStyles.s16w5cBlackShade1
                            : AppTextStyles.s16w5cBlackShade1
                                .copyWith(color: AppColors.white),
                        textScaler: TextScaler.linear(textScaleFactor.value),
                      ),
                    ),
                  ),
                  if (!isDisplayingResult) _selectionUndoRedoButtons(),
                ],
              ),
              const SizedBox(
                height: 12,
              ),
              if (measurementType == 'area')
                !isDisplayingResult
                    ? Text(
                        '--',
                        style: isLightMode
                            ? AppTextStyles.s28w6cBlueGreyShade1
                            : AppTextStyles.s28w6cBlueGreyShade1
                                .copyWith(color: AppColors.white),
                        textScaler: TextScaler.linear(textScaleFactor.value),
                      )
                    : Row(
                        children: [
                          Flexible(
                            child: Text(
                              area,
                              style: isLightMode
                                  ? AppTextStyles.s28w6cBlueGreyShade1
                                  : AppTextStyles.s28w6cBlueGreyShade1
                                      .copyWith(color: AppColors.white),
                              textScaler:
                                  TextScaler.linear(textScaleFactor.value),
                            ),
                          ),
                          Flexible(
                            flex: 2,
                            child: Text(
                              '  $areaUnit - ${LocaleKeys.area.tr()}',
                              style: isLightMode
                                  ? AppTextStyles.s22w4cGrey
                                  : AppTextStyles.s22w4cGrey
                                      .copyWith(color: AppColors.white),
                              textScaler:
                                  TextScaler.linear(textScaleFactor.value),
                            ),
                          ),
                        ],
                      ),
              const SizedBox(
                height: 8,
              ),
              if (!isDisplayingResult)
                Text(
                  '--',
                  style: isLightMode
                      ? AppTextStyles.s28w6cBlueGreyShade1
                      : AppTextStyles.s28w6cBlueGreyShade1
                          .copyWith(color: AppColors.white),
                  textScaler: TextScaler.linear(textScaleFactor.value),
                )
              else
                Row(
                  children: [
                    Flexible(
                      child: Text(
                        distance,
                        style: isLightMode
                            ? AppTextStyles.s28w6cBlueGreyShade1
                            : AppTextStyles.s28w6cBlueGreyShade1
                                .copyWith(color: AppColors.white),
                        textScaler: TextScaler.linear(textScaleFactor.value),
                      ),
                    ),
                    Flexible(
                      flex: 2,
                      child: Text(
                        '  $distanceUnit - ${measurementType == 'area' ? LocaleKeys.perimeter.tr() : LocaleKeys.distance.tr()}',
                        style: isLightMode
                            ? AppTextStyles.s22w4cGrey
                            : AppTextStyles.s22w4cGrey
                                .copyWith(color: AppColors.white),
                        textScaler: TextScaler.linear(textScaleFactor.value),
                      ),
                    ),
                  ],
                ),
              const SizedBox(
                height: 30,
              ),
              if (measurementAreaInSquareMeters > 0 ||
                  measurementDistanceInMeters > 0)
                ElevatedButton(
                  onPressed: () {
                    _measurementClear();
                    _measurementStart();
                  },
                  child: Text(
                    LocaleKeys.newMeasurement.tr(),
                    textScaler: TextScaler.linear(textScaleFactor.value),
                  ),
                )
              else
                ElevatedButton(
                  onPressed: _measurementStop,
                  child: Text(
                    LocaleKeys.done.tr(),
                    textScaler: TextScaler.linear(textScaleFactor.value),
                  ),
                ),
              const SizedBox(
                height: 16,
              ),
              TextButton(
                onPressed: _selectionCancel,
                child: Text(
                  LocaleKeys.cancel.tr(),
                  textScaler: TextScaler.linear(textScaleFactor.value),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getContent() {
    final String lang = HiveUtilsSettings.getAppLanguage();

    String contentData = '';
    for (final element in dashboardDetails?.features ?? []) {
      if (element.attributes?.domainNameKey ==
          (selectedSubTheme?.domainNameKey ?? '')) {
        contentData = (lang == 'en'
                ? element.attributes?.definitionEng
                : element.attributes?.definitionAr)
            .toString();
        break;
      }
    }

    return contentData;
  }

  void _onModuleChange({bool reloadMap = true}) {
    if (reloadMap) {
      selectedDistrict = null;
      context
          .read<SpatialAnalyticsBloc>()
          .add(ArcGISDistrictChangeEvent(district: selectedDistrict));
      selectedPOI = [];
      geometry = '';
      _measurementClear();
    }

    switch (selectedSubTheme?.titleEn) {
      case 'Population Overview':
        _moduleChangedToPopulationOverview(reloadMap: reloadMap);
      case 'Household Population':
        _moduleChangedToHouseholdPopulation(reloadMap: reloadMap);
      case 'Flat Transaction':
        _moduleChangedToFlatTransaction(reloadMap: reloadMap);
      case 'Job Seekers':
        _moduleChangedToJobSeekers(reloadMap: reloadMap);
      default:
    }
  }

  void _moduleChangedToPopulationOverview({bool reloadMap = true}) {
    final Modules moduleHousehold =
        spatialAnalyticsModules?.modules?.firstWhere(
              (element) => element.name == 'Household Population',
              orElse: () => Modules(),
            ) ??
            Modules();

    final Layers populationLayer = moduleHousehold.layers?.firstWhere(
          (element) => element.name == 'HouseholdOnlyForVisualization',
          orElse: () => Layers(),
        ) ??
        Layers();

    final Modules module = spatialAnalyticsModules?.modules?.firstWhere(
          (element) => element.name == 'Population Overview',
          orElse: () => Modules(),
        ) ??
        Modules();

    // final Layers dotDensityLayer = moduleHousehold.layers?.firstWhere(
    //       (element) => element.name == 'Dot Density',
    //       orElse: () => Layers(),
    //     ) ??
    //     Layers();

    final Layers dataByGenderLayer = module.layers?.firstWhere(
          (element) => element.name == 'Chart Male Female',
          orElse: () => Layers(),
        ) ??
        Layers();
    final Layers dataByNationalityLayer = module.layers?.firstWhere(
          (element) => element.name == 'Dot Density',
          orElse: () => Layers(),
        ) ??
        Layers();

    final Layers districtsLayer = module.layers?.firstWhere(
          (element) => element.name == 'Districts',
          orElse: () => Layers(),
        ) ??
        Layers();

    if (reloadMap) {
      _sendMessageToNative(
        outMessageKeyValueModuleChanged,
        {
          'module': module.name,
          'districtLayer': districtsLayer.endpoint,
          'layerUrls': {
            'populationLayerUrl': populationLayer.endpoint,
            // 'dotDensityLayerUrl': populationLayer.endpoint,
          },
        },
      );
    }
    context.read<SpatialAnalyticsBloc>().add(
          GetPopulationDataEvent(
            endpointDataByNationality: dataByNationalityLayer.endpoint ?? '',
            endpointDataByGender: dataByGenderLayer.endpoint ?? '',
            token: arcGisAccessToken ?? '',
            districtId: selectedDistrict?.districtId,
          ),
        );
  }

  void _moduleChangedToHouseholdPopulationUpdateLayer({
    required bool showEmirati,
    required bool showNonEmirati,
  }) {
    _sendMessageToNative(
      outMessageKeyValueUpdateLayer,
      {
        'module': 'Household Population',
        'data': {
          'showNonUae': showNonEmirati,
          'showUae': showEmirati,
        },
      },
    );
  }

  void _moduleChangedToHouseholdPopulation({bool reloadMap = true}) {
    final Modules module = spatialAnalyticsModules?.modules?.firstWhere(
          (element) => element.name == 'Household Population',
          orElse: () => Modules(),
        ) ??
        Modules();

    final Layers districtsLayer = module.layers?.firstWhere(
          (element) => element.name == 'Districts',
          orElse: () => Layers(),
        ) ??
        Layers();

    final Layers populationLayer = module.layers?.firstWhere(
          (element) => element.name == 'XY Coordinate',
          orElse: () => Layers(),
        ) ??
        Layers();

    final String regionKey =
        SpatialAnalyticsScreen.modules.data['Population']?.sub
                .firstWhere(
                  (element) => element.key == module.key,
                  orElse: () => SubItemModel(),
                )
                .tabModule
                ?.firstOrNull
                ?.key ??
            '';

    final Modules regionModule = spatialAnalyticsModules?.modules?.firstWhere(
          (element) => element.key == regionKey,
          orElse: () => Modules(),
        ) ??
        Modules();

    final Layers regionLayer = regionModule.layers?.firstWhere(
          (element) => element.name == 'POPULATION_BY_REGION',
          orElse: () => Layers(),
        ) ??
        Layers();

    if (reloadMap) {
      _sendMessageToNative(
        outMessageKeyValueModuleChanged,
        {
          'module': module.name,
          'districtLayer': districtsLayer.endpoint,
          'layerUrls': {
            'populationLayerUrl': populationLayer.endpoint,
          },
        },
      );
    }

    context.read<SpatialAnalyticsBloc>().add(
          GetHouseholdPopulationDataEvent(
            endpointDataByNationals: populationLayer.endpoint ?? '',
            endpointDataByGender: populationLayer.endpoint ?? '',
            endpointDataByRegion: regionLayer.endpoint ?? '',
            token: arcGisAccessToken ?? '',
            districtId: selectedDistrict?.districtId,
            geometry: geometry,
          ),
        );
  }

  void _moduleChangedToFlatTransaction({bool reloadMap = true}) {
    final Modules module = spatialAnalyticsModules?.modules?.firstWhere(
          (element) => element.name == 'Flat Transaction',
          orElse: () => Modules(),
        ) ??
        Modules();

    final Layers realEstateDataLayer = module.layers?.firstWhere(
          (element) => element.name == 'Realestate Data',
          orElse: () => Layers(),
        ) ??
        Layers();

    final Layers districtsLayer = module.layers?.firstWhere(
          (element) => element.name == 'Districts',
          orElse: () => Layers(),
        ) ??
        Layers();

    if (reloadMap) {
      _sendMessageToNative(
        outMessageKeyValueModuleChanged,
        {
          'module': module.name,
          'districtLayer': districtsLayer.endpoint,
          'layerUrls': {
            'realEstateDataLayerUrl': realEstateDataLayer.endpoint,
          },
        },
      );
    }

    context.read<SpatialAnalyticsBloc>().add(
          GetFlatTransactionDataEvent(
            district: selectedDistrict,
            token: arcGisAccessToken ?? '',
            endpoint: realEstateDataLayer.endpoint ?? '',
          ),
        );
  }

  void _moduleChangedToJobSeekers({
    bool reloadMap = true,
    job_seeker.JobSeekersCheckBox? selectedExperienceCheckBox,
  }) {
    final Modules module = spatialAnalyticsModules?.modules?.firstWhere(
          (element) => element.name == 'Job Seekers',
          orElse: () => Modules(),
        ) ??
        Modules();

    final Layers jobSeekersLayer = module.layers?.firstWhere(
          (element) => element.name == 'index3',
          orElse: () => Layers(),
        ) ??
        Layers();

    final Layers jobSeekersPlaceNameLayer = module.layers?.firstWhere(
          (element) => element.name == 'index1',
          orElse: () => Layers(),
        ) ??
        Layers();

    final Layers jobSeekersGreyShadeLayer = module.layers?.firstWhere(
          (element) => element.name == 'index2',
          orElse: () => Layers(),
        ) ??
        Layers();

    final Layers districtsLayer = module.layers?.firstWhere(
          (element) => element.name == 'index0',
          orElse: () => Layers(),
        ) ??
        Layers();

    if (reloadMap) {
      _sendMessageToNative(
        outMessageKeyValueModuleChanged,
        {
          'module': module.name,
          'districtLayer': districtsLayer.endpoint,
          'layerUrls': {
            // 'jobSeekersLayerUrl': jobSeekersLayer.endpoint,
            // 'jobSeekersPlaceNameLayerUrl': jobSeekersPlaceNameLayer.endpoint,
            'greyShadeLayerUrl': jobSeekersGreyShadeLayer.endpoint,
          },
        },
      );
    }

    context.read<SpatialAnalyticsBloc>().add(
          GetJobSeekersDataEvent(
            token: arcGisAccessToken ?? '',
            district: selectedDistrict,
            experienceCheckBoxes: selectedExperienceCheckBox,
            endpoint1: jobSeekersPlaceNameLayer.endpoint ?? '',
            endpoint3: jobSeekersLayer.endpoint ?? '',
          ),
        );
  }

  void _pointOfInterestLayer(List<CommonLayers> selected) {
    selectedPOI = selected;
    _sendMessageToNative(
      outMessageKeyValuePOI,
      {
        'poi': selectedPOI
            .map(
              (e) => {
                'title': 'poi-${e.name}',
                'url': e.endpoint,
                // 'imgUrl': e.lightIcon,
                'filePath': e.filePath,
              },
            )
            .toList(),
      },
    );
  }

  Future<bool> _hasSomethingToClearOnMap() async {
    return selectedDistrict != null ||
        (await _sendMessageToNative(
          outMessageKeyValueHasSomethingToClearOnMap,
          <String, dynamic>{},
        ) as bool);
  }

  void _clearMapSelection() {
    _sendMessageToNative(
      outMessageKeyValueClearMapSelection,
      <String, dynamic>{},
    );
    selectedDistrict = null;
    context
        .read<SpatialAnalyticsBloc>()
        .add(ArcGISDistrictChangeEvent(district: selectedDistrict));
    districtController.text = '';
    FocusManager.instance.primaryFocus?.unfocus();
    _onModuleChange(reloadMap: geometry.isNotEmpty);
    suggestionsController.close();
  }

  void _selectionMessageToNative(String event) {
    _sendMessageToNative(
      outMessageKeyValueSelection,
      {
        'event': event,
      },
    );
    fullScreenMap.value = true;
  }

  void _selectionStart() {
    _measurementClear();
    measurementType = 'selection';
    _selectionMessageToNative('start');
    fullScreenMap.value = true;
  }

  void _selectionDone() {
    _selectionMessageToNative('done');
    measurementType = '';
    fullScreenMap.value = false;
  }

  void _selectionCancel() {
    _selectionMessageToNative('cancel');
    measurementType = '';
    fullScreenMap.value = false;
  }

  void _measurementMessageToNative(String event) {
    _sendMessageToNative(
      outMessageKeyValueMeasurement,
      {
        'type': measurementType,
        'event': event,
      },
    );
  }

  void _measurementStart() {
    measurementAreaInSquareMeters = 0;
    measurementDistanceInMeters = 0;
    setState(() {});
    _measurementMessageToNative('start');
    fullScreenMap.value = true;
  }

  void _measurementStop() {
    _measurementMessageToNative('stop');
    fullScreenMap.value = true;
  }

  void _measurementClear() {
    _measurementMessageToNative('clear');
    measurementDistanceInMeters = 0;
    measurementAreaInSquareMeters = 0;
    setState(() {});
  }

  void _selectionUndo() {
    _selectionMessageToNative('undo');
  }

  void _selectionRedo() {
    _selectionMessageToNative('redo');
  }

  Future<dynamic> _sendMessageToNative(String method, dynamic params) async {
    try {
      return await platform.invokeMethod(method, params);
    } on PlatformException catch (e, s) {
      Completer<dynamic>().completeError(e, s);
    }
  }

  void _handleIncomingMessage(Map<dynamic, dynamic> message) {
    if (message[messageKey] == inMessageKeyValueMapReady) {
      selectedTheme = SpatialAnalyticsScreen.modules.data.values.first;
      selectedSubTheme = SpatialAnalyticsScreen
          .modules.data[selectedTheme?.titleEn]?.sub.first;
      if (widget.initialModuleKey != null) {
        for (final theme in SpatialAnalyticsScreen.modules.data.values) {
          final int i = theme.sub
              .indexWhere((element) => element.key == widget.initialModuleKey);
          if (i >= 0) {
            selectedTheme = theme;
            selectedSubTheme = theme.sub[i];
            break;
          }
        }
      }
      _onModuleChange();
    } else if (message[messageKey] == inMessageKeyValueApiResponse) {
      if (message.containsKey('key') && message['key'] == 'district_list') {
        final List<dynamic> list =
            jsonDecode(message['attributes'].toString()) as List<dynamic>;

        districtList.value = list
            .map((e) => ArcGISDistrict.fromJson(e as Map<String, dynamic>))
            .toList();
        // _onModuleChange();
      }
    } else if (message[messageKey] == inMessageKeyValueDistrictSelected) {
      final ArcGISDistrict district = ArcGISDistrict.fromJson(
        jsonDecode(message['attributes'].toString()) as Map<String, dynamic>,
      );

      _selectDistrict(districtId: district.districtId);
    } else if (message[messageKey] == inMessageKeyValueMeasurementResult) {
      final String type = message['type'].toString();

      if (type == 'area') {
        measurementAreaInSquareMeters =
            double.tryParse('${message['area']}') ?? 0;
        measurementDistanceInMeters =
            double.tryParse('${message['distance']}') ?? 0;
      } else if (type == 'distance') {
        measurementDistanceInMeters =
            double.tryParse('${message['distance']}') ?? 0;
      }
      setState(() {});
    } else if (message[messageKey] == inMessageKeyValueSelectionDone) {
      geometry = message['geometry'].toString();

      _onModuleChange(reloadMap: false);
    } else if (message[messageKey] == inMessageKeyValueToast) {
      String msg = message['message'].toString();

      final List<String> str = msg.split(' ')
        ..removeWhere((element) => element.isEmpty);
      str[0] = str[0].toLowerCase();

      for (int i = 1; i < str.length; i++) {
        str[i] =
            '${str[i][0].toUpperCase()}${str[i].substring(1).toLowerCase()}';
      }

      final String s = str.join().tr();

      if (s.isNotEmpty) {
        msg = s;
      }

      AppMessage.showOverlayNotification(
        '',
        msg,
        msgType: message['type'].toString(),
      );
    }
  }

  Future<void> _initEvent() async {
    eventChannel.receiveBroadcastStream().listen(
      (dynamic event) {
        if (event is Map) {
          _handleIncomingMessage(event);
        }
      },
      onError: (dynamic e) {},
    );
  }

  Row _selectionUndoRedoButtons() {
    final bool isRtl = DeviceType.isDirectionRTL(context);
    return Row(
      children: [
        InkWell(
          onTap: _selectionUndo,
          child: Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: AppColors.greyShade1),
            ),
            child: RotatedBox(
              quarterTurns: isRtl ? 2 : 0,
              child: SvgPicture.asset(
                AppImages.icUndo,
                width: 24,
                height: 24,
                colorFilter: isLightMode
                    ? null
                    : ColorFilter.mode(AppColors.white, BlendMode.srcIn),
              ),
            ),
          ),
        ),
        const SizedBox(
          width: 12,
        ),
        InkWell(
          onTap: _selectionRedo,
          child: Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: AppColors.greyShade1),
            ),
            child: RotatedBox(
              quarterTurns: isRtl ? 2 : 0,
              child: SvgPicture.asset(
                AppImages.icRedo,
                width: 24,
                height: 24,
                colorFilter: isLightMode
                    ? null
                    : ColorFilter.mode(AppColors.white, BlendMode.srcIn),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _searchBar() {
    return ValueListenableBuilder(
      valueListenable: districtList,
      builder: (c, val, w) {
        if (districtList.value.isNotEmpty &&
            selectedTheme?.titleEn == 'Population') {
          return Container(
            margin: const EdgeInsets.fromLTRB(24,16,24,6),
            decoration: ShapeDecoration(
              color: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(60),
              ),
              shadows: const [
                BoxShadow(
                  color: Color(0x1E0F1219),
                  blurRadius: 15.90,
                  offset: Offset(0, 21),
                  spreadRadius: -15,
                ),
              ],
            ),
            height: 46,
            child: TypeAheadField<ArcGISDistrict>(
              controller: districtController,
              focusNode: districtFocusNode,
              suggestionsController: suggestionsController,
              builder: (
                  context,
                  controller,
                  focusNode,
                  ) {
                return TextField(
                  controller: controller,
                  focusNode: focusNode,
                  onSubmitted: (value) {
                    FocusManager.instance.primaryFocus?.unfocus();
                  },
                  style: TextStyle(
                    fontSize: 14 * textScaleFactor.value,
                    color: AppColors.black,
                  ),
                  decoration: InputDecoration(
                    suffixIcon: FittedBox(
                      fit: BoxFit.scaleDown,
                      child: SvgPicture.asset(
                        AppImages.iconSearchBlack,
                        width: 16,
                        height: 16,
                      ),
                    ),
                    hintText: LocaleKeys.search.tr(),
                    hintStyle: TextStyle(
                      fontSize: 14 * textScaleFactor.value,
                      fontWeight: FontWeight.w300,
                      color: AppColors.grey,
                    ),
                    isDense: true,
                    border: OutlineInputBorder(
                      borderSide: const BorderSide(
                        color: Colors.blueAccent,
                        width: 32,
                      ),
                      borderRadius: BorderRadius.circular(
                        25,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderSide: const BorderSide(
                        color: Colors.white,
                        width: 32,
                      ),
                      borderRadius: BorderRadius.circular(
                        25,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: const BorderSide(
                        color: Colors.white,
                        width: 32,
                      ),
                      borderRadius: BorderRadius.circular(
                        25,
                      ),
                    ),
                  ),
                );
              },
              itemBuilder: (
                  BuildContext context,
                  value,
                  ) {
                return ListTile(
                  title: Text(
                    DeviceType.isDirectionRTL(context)
                        ? value.districtAra ?? '-'
                        : value.districtEng ?? '-',
                    textScaler: TextScaler.linear(textScaleFactor.value),
                  ),
                );
              },
              onSelected: (ArcGISDistrict? value) {
                _selectDistrict(
                  districtId: value?.districtId,
                  clearPrevSelection: true,
                  setSearchText: true,
                );
              },
              emptyBuilder: (context){
                return Padding(
                  padding: const EdgeInsets.all(12),
                  child: Text(LocaleKeys.noResults.tr()),
                );
              },
              suggestionsCallback: (String search) {
                final bool isEnglish = HiveUtilsSettings.getAppLanguage() == 'en';
                List<ArcGISDistrict> filteredSuggestions = [];

                if(isEnglish) {
                  filteredSuggestions = districtList.value
                      .where(
                        (district) =>
                    district.districtEng?.toLowerCase().contains(
                      search.toLowerCase(),
                    ) ??
                        false,
                  )
                      .toList();
                }else{
                  filteredSuggestions = districtList.value
                      .where(
                        (district) =>
                    district.districtAra?.contains(
                      search.toLowerCase(),
                    ) ??
                        false,
                  )
                      .toList();
                }

                return filteredSuggestions;
              },
            ),
          );
        } else {
          return const SizedBox();
        }
      },
    );
  }

  Widget _mapButtons() {
    return Positioned(
      right: 24,
      bottom: (42 * 3) + MediaQuery.paddingOf(context).bottom,
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(
              top: 12,
            ),
            child: FutureBuilder(
              future: _hasSomethingToClearOnMap(),
              builder: (
                  BuildContext c,
                  AsyncSnapshot<bool> snapshot,
                  ) {
                if (snapshot.connectionState == ConnectionState.done &&
                    snapshot.data == true) {
                  return _mapButton(
                    Icon(
                      Icons.clear,
                      color: AppColors.white,
                    ),
                    _clearMapSelection,
                    LocaleKeys.clearAll.tr(),
                  );
                } else {
                  return const SizedBox(
                    height: 39,
                    width: 39,
                  );
                }
              },
            ),
          ),
          if (selectedSubTheme?.titleEn == 'Household Population')
            Padding(
              padding: const EdgeInsets.only(
                top: 12,
              ),
              child: _mapButton(
                SvgPicture.asset(
                  AppImages.icSelection,
                ),
                _selectionStart,
                LocaleKeys.smallAreaSelection.tr(),
              ),
            ),
          Padding(
            padding: const EdgeInsets.only(top: 12),
            child: _mapButton(
              PopupMenuButton(
                tooltip: LocaleKeys.measurement.tr(),
                itemBuilder: (_) => <PopupMenuItem<String>>[
                  PopupMenuItem<String>(
                    value: 'distance',
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 18,
                            vertical: 10,
                          ),
                          child: Text(
                            LocaleKeys.distanceMeasurement.tr(),
                            textScaler: TextScaler.linear(
                              textScaleFactor.value,
                            ),
                            style: AppTextStyles.s14w4cBlueTitleText
                                .copyWith(
                              color: isLightMode
                                  ? AppColors.grey
                                  : AppColors.white,
                            ),
                          ),
                        ),
                        const Divider(
                          height: 1,
                        ),
                      ],
                    ),
                  ),
                  PopupMenuItem<String>(
                    value: 'area',
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 18,
                            vertical: 10,
                          ),
                          child: Text(
                            LocaleKeys.areaMeasurement.tr(),
                            textScaler: TextScaler.linear(
                              textScaleFactor.value,
                            ),
                            style: AppTextStyles.s14w4cBlueTitleText
                                .copyWith(
                              color: isLightMode
                                  ? AppColors.grey
                                  : AppColors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
                onSelected: (String option) {
                  if (option == 'distance' || option == 'area') {
                    measurementType = option;
                    measurementDistanceInMeters = 0;
                    measurementAreaInSquareMeters = 0;
                    _measurementStart();
                  }
                },
                surfaceTintColor: Colors.transparent,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(
                    10,
                  ),
                ),
                child: ClipRRect(
                  child: Material(
                    color: Colors.transparent,
                    borderRadius: BorderRadius.circular(8),
                    child: SizedBox(
                      height: 39,
                      width: 39,
                      child: SvgPicture.asset(
                        AppImages.icMeasurement,
                      ),
                    ),
                  ),
                ),
              ),
              null,
              '',
            ),
          ),
          if ((spatialAnalyticsModules?.commonLayers ?? []).isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(
                top: 12,
              ),
              child: _mapButton(
                SvgPicture.asset(
                  AppImages.icLayers,
                ),
                    () async {
                  final bool isDownloadComplete =
                  await _downloadPoiIcons();

                  if (!isDownloadComplete) {
                    return;
                  }

                  if (mounted) {
                    unawaited(
                      showModalBottomSheet<void>(
                        isScrollControlled: true,
                        useRootNavigator: true,
                        constraints: BoxConstraints(
                          minHeight: 100,
                          maxHeight: MediaQuery.sizeOf(
                            context,
                          ).height *
                              .90,
                        ),
                        shape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(
                              20,
                            ),
                            topRight: Radius.circular(
                              20,
                            ),
                          ),
                        ),
                        backgroundColor: Colors.transparent,
                        context: context,
                        builder: (context) {
                          return PointOfInterestPopup(
                            poiList:
                            spatialAnalyticsModules?.commonLayers ??
                                [],
                            selectedPoi: selectedPOI,
                            onDone: _pointOfInterestLayer,
                          );
                        },
                      ),
                    );
                  }
                },
                LocaleKeys.pointOfInterest.tr(),
              ),
            ),
        ],
      ),
    );
  }
}
