class PopulationOverviewNationalityData {
  PopulationOverviewNationalityData({
    this.features,
  });

  PopulationOverviewNationalityData.fromJson(Map<String, dynamic> json) {
    features = (json['features'] as List?)
        ?.map((dynamic e) => Features.fromJson(e as Map<String, dynamic>))
        .toList();
  }

  List<Features>? features;

  Attributes? get attributes => features?.firstOrNull?.attributes;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['features'] = features?.map((e) => e.toJson()).toList();
    return json;
  }
}

class Features {
  Features({
    this.attributes,
  });

  Features.fromJson(Map<String, dynamic> json) {
    attributes = (json['attributes'] as Map<String, dynamic>?) != null
        ? Attributes.fromJson(json['attributes'] as Map<String, dynamic>)
        : null;
  }

  Attributes? attributes;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['attributes'] = attributes?.toJson();
    return json;
  }
}

class Attributes {
  Attributes({
    this.totalNonCitizen,
    this.totalPopulation,
    this.totalCitizen,
  });

  Attributes.fromJson(Map<String, dynamic> json) {
    totalNonCitizen = json['total_non_citizen'] as int?;
    totalPopulation = json['total_population'] as int?;
    totalCitizen = json['total_citizen'] as int?;
  }

  int? totalNonCitizen;
  int? totalPopulation;
  int? totalCitizen;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['total_non_citizen'] = totalNonCitizen;
    json['total_population'] = totalPopulation;
    json['total_citizen'] = totalCitizen;
    return json;
  }
}
