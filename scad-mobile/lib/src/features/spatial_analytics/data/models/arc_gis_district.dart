class ArcGISDistrict {
  ArcGISDistrict({
    this.area,
    this.districtAra,
    this.districtEng,
    this.districtId,
    this.dmtDistrictId,
    this.emirateAra,
    this.emirateEng,
    this.emirateId,
    this.objectid,
    this.regionAra,
    this.regionEng,
    this.regionId,
    this.scadDistrictId,
    this.sHAPEArea,
    this.sHAPELength,
  });

  ArcGISDistrict.fromJson(Map<String, dynamic> json) {
    area = json['area']?.toString();

    if ((json['district_ara'] as String?) != null) {
      districtAra = json['district_ara'] as String?;
    } else if ((json['d_name_ar'] as String?) != null) {
      districtAra = json['d_name_ar'] as String?;
    } else if ((json['districtnamearabic'] as String?) != null) {
      districtAra = json['districtnamearabic'] as String?;
    }

    if ((json['district_eng'] as String?) != null) {
      districtEng = json['district_eng'] as String?;
    } else if ((json['d_name_en'] as String?) != null) {
      districtEng = json['d_name_en'] as String?;
    } else if ((json['districtnameeng'] as String?) != null) {
      districtEng = json['districtnameeng'] as String?;
    }

    districtId = json['district_id'] as int?;
    dmtDistrictId = json['dmt_district_id'] as int?;
    emirateAra = json['emirate_ara'] as String?;
    emirateEng = json['emirate_eng'] as String?;
    emirateId = json['emirate_id'] as int?;
    objectid = json['objectid'] as int?;
    regionAra = json['region_ara'] as String?;
    regionEng = json['region_eng'] as String?;
    regionId = json['region_id'] as int?;
    scadDistrictId = json['scad_district_id'] as String?;
    sHAPEArea = json['SHAPE__Area'] as double?;
    sHAPELength = json['SHAPE__Length'] as double?;
  }

  String? area;
  String? districtAra;
  String? districtEng;
  int? districtId;
  int? dmtDistrictId;
  String? emirateAra;
  String? emirateEng;
  int? emirateId;
  int? objectid;
  String? regionAra;
  String? regionEng;
  int? regionId;
  String? scadDistrictId;
  double? sHAPEArea;
  double? sHAPELength;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['area'] = area;
    json['district_ara'] = districtAra;
    json['district_eng'] = districtEng;
    json['district_id'] = districtId;
    json['districtnamearabic'] = districtAra;
    json['districtnameeng'] = districtEng;
    json['d_name_ar'] = districtAra;
    json['d_name_en'] = districtEng;
    json['dmt_district_id'] = dmtDistrictId;
    json['emirate_ara'] = emirateAra;
    json['emirate_eng'] = emirateEng;
    json['emirate_id'] = emirateId;
    json['objectid'] = objectid;
    json['region_ara'] = regionAra;
    json['region_eng'] = regionEng;
    json['region_id'] = regionId;
    json['scad_district_id'] = scadDistrictId;
    json['SHAPE__Area'] = sHAPEArea;
    json['SHAPE__Length'] = sHAPELength;
    return json;
  }
}
