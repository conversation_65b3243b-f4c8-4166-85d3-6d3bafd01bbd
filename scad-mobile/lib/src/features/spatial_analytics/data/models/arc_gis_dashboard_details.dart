class ArcGISDashboardDetails {
  ArcGISDashboardDetails({
    this.features,
  });

  ArcGISDashboardDetails.fromJson(Map<String, dynamic> json) {
    features = (json['features'] as List?)
        ?.map((dynamic e) => Features.fromJson(e as Map<String, dynamic>))
        .toList();
  }

  List<Features>? features;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['features'] = features?.map((e) => e.toJson()).toList();
    return json;
  }
}

class Features {
  Features({
    this.attributes,
  });

  Features.fromJson(Map<String, dynamic> json) {
    attributes = (json['attributes'] as Map<String, dynamic>?) != null
        ? DashboardAttributes.fromJson(json['attributes'] as Map<String, dynamic>)
        : null;
  }

  DashboardAttributes? attributes;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['attributes'] = attributes?.toJson();
    return json;
  }
}

class DashboardAttributes {
  DashboardAttributes({
    this.domainNameEng,
    this.sourceAr,
    this.domainNameKey,
    this.definitionEng,
    this.definitionAr,
    this.obsDt,
    this.domainNameAr,
    this.objectid,
    this.sourceEng,
  });

  DashboardAttributes.fromJson(Map<String, dynamic> json) {
    domainNameEng = json['domain_name_eng'] as String?;
    sourceAr = json['source_ar'] as String?;
    domainNameKey = json['domain_name_key'] as String?;
    definitionEng = json['definition_eng'] as String?;
    definitionAr = json['definition_ar'] as String?;
    obsDt = json['obs_dt'] as String?;
    domainNameAr = json['domain_name_ar'] as String?;
    objectid = json['objectid'] as int?;
    sourceEng = json['source_eng'] as String?;
  }

  String? domainNameEng;
  String? sourceAr;
  String? domainNameKey;
  String? definitionEng;
  String? definitionAr;
  String? obsDt;
  String? domainNameAr;
  int? objectid;
  String? sourceEng;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['domain_name_eng'] = domainNameEng;
    json['source_ar'] = sourceAr;
    json['domain_name_key'] = domainNameKey;
    json['definition_eng'] = definitionEng;
    json['definition_ar'] = definitionAr;
    json['obs_dt'] = obsDt;
    json['domain_name_ar'] = domainNameAr;
    json['objectid'] = objectid;
    json['source_eng'] = sourceEng;
    return json;
  }
}
