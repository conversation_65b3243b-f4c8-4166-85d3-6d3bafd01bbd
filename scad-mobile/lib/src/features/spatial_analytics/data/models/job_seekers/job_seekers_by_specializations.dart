class JobSeekersBySpecialization {
  JobSeekersBySpecialization({
    this.features,
  });

  JobSeekersBySpecialization.fromJson(Map<String, dynamic> json) {
    features = (json['features'] as List?)
        ?.map((dynamic e) => Features.fromJson(e as Map<String, dynamic>))
        .toList();
  }

  List<Features>? features;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['features'] = features?.map((e) => e.toJson()).toList();
    return json;
  }
}

class Features {
  Features({
    this.attributes,
  });

  Features.fromJson(Map<String, dynamic> json) {
    attributes = (json['attributes'] as Map<String, dynamic>?) != null
        ? Attributes.fromJson(json['attributes'] as Map<String, dynamic>)
        : null;
  }

  Attributes? attributes;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['attributes'] = attributes?.toJson();
    return json;
  }
}

class Attributes {
  Attributes({
    this.totalMoeHeGradEucationIsced,
    this.moeHeGradEucationIsced,
  });

  Attributes.fromJson(Map<String, dynamic> json) {
    totalMoeHeGradEucationIsced =
        json['total_moe_he_grad_eucation_isced'] as int?;
    moeHeGradEucationIsced = json['moe_he_grad_eucation_isced'] as String?;
  }

  int? totalMoeHeGradEucationIsced;
  String? moeHeGradEucationIsced;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['total_moe_he_grad_eucation_isced'] = totalMoeHeGradEucationIsced;
    json['moe_he_grad_eucation_isced'] = moeHeGradEucationIsced;
    return json;
  }
}
