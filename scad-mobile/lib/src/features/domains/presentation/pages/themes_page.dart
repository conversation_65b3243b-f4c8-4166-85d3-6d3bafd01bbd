import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/common/widgets/app_sliding_tab.dart';
import 'package:scad_mobile/src/common/widgets/appbar/flat_app_bar.dart';
import 'package:scad_mobile/src/common/widgets/count_widget.dart';
import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';
import 'package:scad_mobile/src/common/widgets/expandable_widget.dart';
import 'package:scad_mobile/src/common/widgets/expandable_widget_list_item.dart';
import 'package:scad_mobile/src/common/widgets/no_data_placeholder.dart';
import 'package:scad_mobile/src/common/widgets/sub_things_tile.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/src/showcase_widget.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/widget_extension.dart';
import 'package:scad_mobile/src/config/dependancy_injection/injection_container.dart';
import 'package:scad_mobile/src/features/domains/data/models/domain_classification_model/domain_classification_model.dart';
import 'package:scad_mobile/src/features/domains/data/models/domain_model/domain_model.dart';
import 'package:scad_mobile/src/features/domains/data/models/theme_page_model.dart';
import 'package:scad_mobile/src/features/domains/data/models/theme_subtheme_response.dart';
import 'package:scad_mobile/src/features/domains/domain/repositories/domains_repository_imports.dart';
import 'package:scad_mobile/src/features/domains/presentation/bloc/domains_bloc.dart';
import 'package:scad_mobile/src/features/domains/presentation/pages/theme_indicators_screen.dart';
import 'package:scad_mobile/src/features/home/<USER>/widgets/persistent_bottom_nav_bar/persistent_bottom_nav_bar_v2.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

import '../../../chat_with_sme/data/models/domain_theme_sub_theme_model.dart';
import '../../../my_apps/data/models/response/collection_list_response.dart';

@RoutePage()
class ThemesPage extends StatefulWidget {
  const ThemesPage({
    this.domainId,
    super.key,
    this.collection,
  });

  final String? domainId;
  final CollectionResult? collection;

  @override
  State<ThemesPage> createState() => _ThemesPageState();
}

class _ThemesPageState extends State<ThemesPage>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  List<ThemePageDomainModel> list = [];

  List<DomainModel> domainList = [];
  List<DomainClassificationModel> classificationList = [];
  List<ThemeSubThemeResponse> themeSubThemeList = [];

  int selectedDomainTabIndex = 0;

  late TabController classificationTabController;
  ScrollController scrollController = ScrollController();
  num indicatorsRefreshedAt = 0;

  List<GlobalKey> steps = [];
  final ValueNotifier<bool> domainLoder = ValueNotifier(true);

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();

    if (HiveUtilsSettings.getUserGuideStatus() == UserGuides.Domains) {
      steps = [GlobalKey(debugLabel: 'official-tab'), GlobalKey(debugLabel: 'experimental-tab')];
      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        ShowCaseWidget.of(context).startShowCase(steps);
      });
    }

    classificationTabController = TabController(length: 2, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      if (widget.domainId != null) {
        context
            .read<DomainsBloc>()
            .add(ThemesInitEvent(domainId: widget.domainId ?? ''));
      } else {
        _getDomainList();
      }
    });
  }

  Future<void> _getDomainList() async {
    final List<RepoResponse<dynamic>> responses = await Future.wait([
      servicelocator<DomainsRepository>().getDomainList(),
      servicelocator<DomainsRepository>().getDomainListIfp(),
    ]);
    if (responses.every((element) => element.isSuccess)) {
      final RepoResponse<
          List<DomainModel>> response1 = responses[0] as RepoResponse<List<DomainModel>>;
      final  RepoResponse<DomainThemeSubThemeModelResponse> response2 = responses[1] as RepoResponse<DomainThemeSubThemeModelResponse>;
      final List<DomainModel> domainList = response1.response!;
      final List<DomainModel> domainListCreated = [];
      final DomainThemeSubThemeModelResponse domainListIfp = response2.response!;
      final Map<String,String> domainListGrouped = {};
      for(final DomainThemeSubThemeModel domainValue in domainListIfp.dataList ?? []) {
        for( final Domain domainList in domainValue.domains ?? []) {
          domainListGrouped[ domainList.id ?? '' ] = domainList.name ?? '';
        }

      }
      for(final domainValue in domainListGrouped.entries) {
        for(final DomainModel domain in domainList) {
          if (domain.domainId == domainValue.key) {
            domainListCreated.add(domain);
          }
        }
      }
      if (domainList.isNotEmpty) {
        WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
          context.read<DomainsBloc>().add(ThemesInitEvent(
              domainId: domainListCreated.firstOrNull?.domainId ?? ''));
        });
      }
    }

  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return widget.domainId != null
        ? AppDrawer(child: buildThemePage())
        : buildThemePage();
  }

  Scaffold buildThemePage() {
    final rtl = DeviceType.isDirectionRTL(context);
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Scaffold(
      body: Column(
        children: [
          FlatAppBar(
            mainScreen: widget.domainId == null,
            title: LocaleKeys.statisticalDomains.tr(),
            scrollController: scrollController,
          ),
          Expanded(
            child: BlocConsumer<DomainsBloc, DomainsState>(
              listener: (context, state) {
                if (state is ThemeClassificationShowResponseState) {
                  classificationList = state.list;
                  domainList = state.domains;
                  selectedDomainTabIndex = domainList.indexWhere(
                    (element) => element.domainId == widget.domainId,
                  );
                  if (selectedDomainTabIndex == -1) {
                    selectedDomainTabIndex = 0;
                  }

                  final int index = classificationTabController.index;
                  context.read<DomainsBloc>().add(
                        GetThemesInitEvent(
                          domainId: widget.domainId ??
                              (domainList.firstOrNull?.domainId ?? ''),
                          classificationId: '${classificationList[index].id}',
                        ),
                      );
                  indicatorsRefreshedAt = DateTime.now().microsecondsSinceEpoch;
                }
                if (state is ThemeListShowResponseState) {
                  themeSubThemeList = state.list;

                  domainLoder.value = false;
                }
              },
              builder: (context, state) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if(domainList.isNotEmpty)
                    AppSlidingTab(
                      key: Key('theme.domain.$indicatorsRefreshedAt'),
                      initialTabIndex: selectedDomainTabIndex,
                      onTabChange: (int index) {
                        selectedDomainTabIndex = index;
                        context.read<DomainsBloc>().add(
                              GetThemesInitEvent(
                                domainId: '${domainList[index].domainId}',
                                classificationId:
                                    '${classificationList[classificationTabController.index].id}',
                              ),
                            );
                      },
                      tabs: domainList
                          .map(
                            (e) => AppSlidingTabItem(
                              label: rtl
                                  ? e.domainNameAr ?? '-'
                                  : e.domainName ?? '-',
                              iconUrl: e.domainIcon,
                            ),
                          )
                          .toList(),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    if (classificationList.isNotEmpty)
                      TabBar(
                        onTap: (index) {
                          context.read<DomainsBloc>().add(
                                GetThemesInitEvent(
                                  domainId:
                                      '${domainList[selectedDomainTabIndex].domainId}',
                                  classificationId:
                                      '${classificationList[classificationTabController.index].id}',
                                ),
                              );
                        },
                        controller: classificationTabController,
                        overlayColor: MaterialStateProperty.all<Color>(
                          AppColors.blueLight.withOpacity(0.1),
                        ),
                        labelPadding: const EdgeInsets.symmetric(horizontal: 1),
                        indicatorSize: TabBarIndicatorSize.tab,
                        labelColor: AppColors.blueLightOld,
                        indicator: UnderlineTabIndicator(
                          borderSide: BorderSide(
                            width: 3,
                            color: AppColors.blueLightOld,
                          ),
                          insets: const EdgeInsets.symmetric(horizontal: 4),
                        ),
                        tabs: _tabs(),
                      ),
                    if (state is ThemeLoadingState)
                      const Expanded(
                        child: Center(child: Padding(
                          padding: EdgeInsets.only(bottom: 150),
                          child: CircularProgressIndicator(),
                        ),),
                      )
                    else if (themeSubThemeList.isEmpty)
                      ValueListenableBuilder(
                        builder: (context,_ , __) {
                          if (domainLoder.value) {
                            return const Expanded(
                              child: Center(
                                child: Padding(
                                  padding: EdgeInsets.only(bottom: 150),
                                  child: CircularProgressIndicator(),
                                ),
                              ),
                            );
                          } else {
                            return const Expanded(
                              child: Center(
                                child: NoDataPlaceholder(
                                  padding: EdgeInsets.only(bottom: 150),
                                ),
                              ),
                            );
                          }
                        }, valueListenable: domainLoder,
                      )
                    else
                      Expanded(
                        child: buildOfficialTab(
                          themeSubThemeList,
                          isLightMode,
                        ),
                      ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _tabs() {
    final double width = MediaQuery.sizeOf(context).width;
    return [
      Tab(
        child: Padding(
          padding: const EdgeInsets.only(left: 4,right: 6),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Flexible(
                child: Text(
                  classificationList
                          .singleWhere(
                            (element) => element.key == 'official_statistics',
                          )
                          .name ??
                      '-',
                  textScaler: TextScaler.linear(
                    textScaleFactor.value,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(
                width: 4,
              ),
              if (classificationTabController.index == 0)
                SvgPicture.asset(
                  AppImages.icOfficialActive,
                )
              else
                SvgPicture.asset(
                  AppImages.icOfficialInactive,
                ),
            ],
          ),
        ),
      ).introWidget(
        steps: steps,
        index: 0,
        title: LocaleKeys.official.tr(),
        description: LocaleKeys.officialDescription.tr(),
        arrowAlignment: Alignment.topLeft,
        crossAxisAlignment: CrossAxisAlignment.start,
        targetBorderRadius: 60,
        arrowPadding: width * .2,
      ),
      Tab(
        child: Padding(
          padding: const EdgeInsets.only(left: 6,right: 4),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Flexible(
                child: Text(
                  classificationList
                          .singleWhere(
                            (element) => element.key == 'experimental_statistics',
                          )
                          .name ??
                      '-',
                  textScaler: TextScaler.linear(
                    textScaleFactor.value,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(
                width: 2,
              ),
              if (classificationTabController.index == 1)
                SvgPicture.asset(
                  AppImages.icExperimentalActive,
                )
              else
                SvgPicture.asset(
                  AppImages.icExperimentalInactive,
                  colorFilter: ColorFilter.mode(AppColors.greyShade11, BlendMode.srcIn),
                ),
              // const SizedBox(width: 10,)
            ],
          ),
        ),
      ).introWidget(
        steps: steps,
        index: 1,
        title: LocaleKeys.experimental.tr(),
        description: LocaleKeys.experimentalDescription.tr(),
        arrowAlignment: Alignment.topRight,
        crossAxisAlignment: CrossAxisAlignment.end,
        targetBorderRadius: 60,
        arrowPadding: width * .2,
      ),
    ];

    // return [
    //   IntroWidget(
    //   stepKey: keyOfficialTab,
    //   stepIndex: 1,
    //   totalSteps: 2,
    //   title: LocaleKeys.globalSearch.tr(),
    //   description: LocaleKeys.globalSearchGuideDesc.tr(),
    //   arrowAlignment: Alignment.topLeft,
    //   crossAxisAlignment: CrossAxisAlignment.start,
    //   targetBorderRadius: 60,
    //   arrowPadding: const EdgeInsets.only(
    //     right: 2,
    //     left: 2,
    //     bottom: 10,
    //   ),
    //   child: Tab(
    //     child: Row(
    //       mainAxisAlignment: MainAxisAlignment.center,
    //       children: [
    //         Flexible(
    //           child: Text(
    //             classificationList
    //                 .singleWhere(
    //                   (element) =>
    //               element.key ==
    //                   'official_statistics',
    //             )
    //                 .name ??
    //                 '-',
    //             textScaler: TextScaler.linear(
    //               textScaleFactor.value,
    //             ),
    //             maxLines: 1,
    //             overflow: TextOverflow.ellipsis,
    //           ),
    //         ),
    //         const SizedBox(
    //           width: 6,
    //         ),
    //         if (classificationTabController.index == 0)
    //           SvgPicture.asset(
    //             AppImages.icOfficialActive,
    //           )
    //         else
    //           SvgPicture.asset(
    //             AppImages.icOfficialInactive,
    //           ),
    //       ],
    //     ),
    //   ),
    // ),
    //   IntroWidget(
    //     stepKey: keyExperimentalTab,
    //     stepIndex: 2,
    //     totalSteps: 2,
    //     title: LocaleKeys.globalSearch.tr(),
    //     description: LocaleKeys.globalSearchGuideDesc.tr(),
    //     arrowAlignment: Alignment.topRight,
    //     crossAxisAlignment: CrossAxisAlignment.end,
    //     targetBorderRadius: 60,
    //     arrowPadding: const EdgeInsets.only(
    //       right: 2,
    //       left: 2,
    //       bottom: 10,
    //     ),
    //     child: Tab(
    //       child: Row(
    //         mainAxisAlignment: MainAxisAlignment.center,
    //         children: [
    //           Flexible(
    //             child: Text(
    //               classificationList
    //                   .singleWhere(
    //                     (element) =>
    //                 element.key ==
    //                     'experimental_statistics',
    //               )
    //                   .name ??
    //                   '-',
    //               textScaler: TextScaler.linear(
    //                 textScaleFactor.value,
    //               ),
    //               maxLines: 1,
    //               overflow: TextOverflow.ellipsis,
    //             ),
    //           ),
    //           const SizedBox(
    //             width: 4,
    //           ),
    //           if (classificationTabController.index == 1)
    //             SvgPicture.asset(
    //               AppImages.icExperimentalActive,
    //             )
    //           else
    //             SvgPicture.asset(
    //               AppImages.icExperimentalInactive,
    //             ),
    //         ],
    //       ),
    //     ),
    //   ),
    // ];
  }

  Widget buildOfficialTab(
    List<ThemeSubThemeResponse> list,
    bool isLightMode,
  ) {
    // final rtl = DeviceType.isDirectionRTL(context);
    return ListView.separated(
      controller: scrollController,
      shrinkWrap: true,
      padding: const EdgeInsets.fromLTRB(24, 24, 24, 150),
      itemCount: list.length,
      separatorBuilder: (context, index) => const SizedBox(height: 14),
      itemBuilder: (context, index) {
        final ThemeSubThemeResponse item = list[index];

        return ExpandableWidget(
          title: item.name ?? '-',
          trailingIcon: CountWidget(count: item.subthemes!.length),
          // headerChild: Expanded(
          //   child: Row(
          //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
          //     children: [
          //       Expanded(
          //         child: Padding(
          //           padding: EdgeInsets.only(left: 16, right: rtl ? 16 : 0),
          //           child: Text(
          //             item.name ?? '-',
          //             style: TextStyle(
          //               fontSize: 14,
          //               fontWeight: FontWeight.w500,
          //               color:
          //                   isLightMode ? AppColors.black : AppColors.white,
          //             ),
          //             maxLines: 1,
          //             overflow: TextOverflow.ellipsis,
          //             textScaler: TextScaler.linear(textScaleFactor.value),
          //           ),
          //         ),
          //       ),
          //       CountWidget(count: item.subthemes!.length),
          //     ],
          //   ),
          // ),
          expandedChild: ListView.separated(
            itemCount: item.subthemes!.length,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: const EdgeInsets.symmetric(
              vertical: 14,
              horizontal: 14,
            ),
            separatorBuilder: (context, i) => const SizedBox(height: 10),
            itemBuilder: (context, i) {
              final Subthemes subThemeItem = item.subthemes![i];

              return ExpandableWidgetListItem(
                title: subThemeItem.name ?? '-',
                trailingIcon: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Icon(
                    Icons.chevron_right_rounded,
                    size: 18,
                    color:
                        isLightMode ? AppColors.blueShade22 : AppColors.white,
                  ),
                ),
                onTap: () {
                  // AutoRouter.of(context).push(
                  //   ThemeIndicatorsScreenRoute(
                  //     title: subThemeItem.name!,
                  //     domain: domainList[selectedDomainTabIndex],
                  //     classification: classificationList[
                  //         classificationTabController.index],
                  //     subDomain: item,
                  //     subTheme: subThemeItem,
                  //     screenerConfiguration: item.screenerConfiguration,
                  //     collection: widget.collection,
                  //   ),
                  // );
                  pushScreen(
                    context,
                    withNavBar: true,
                    screen: ThemeIndicatorsScreen(
                      isFromMainScreen: widget.domainId != null,
                      title: subThemeItem.name!,
                      domain: domainList[selectedDomainTabIndex],
                      classification:
                          classificationList[classificationTabController.index],
                      subDomain: item,
                      subTheme: subThemeItem,
                      screenerConfiguration: item.screenerConfiguration,
                      collection: widget.collection,
                    ),
                  );
                },
              );

              return SubThingsTile(
                thing: subThemeItem.name!,
                count: 0,
                onTap: () {
                  pushScreen(
                    context,
                    withNavBar: true,
                    screen: ThemeIndicatorsScreen(
                      title: subThemeItem.name!,
                      domain: domainList[selectedDomainTabIndex],
                      classification:
                          classificationList[classificationTabController.index],
                      subDomain: item,
                      subTheme: subThemeItem,
                      screenerConfiguration: item.screenerConfiguration,
                      collection: widget.collection,
                    ),
                  );
                },
              );
            },
          ),
        );
      },
    );
  }
}
