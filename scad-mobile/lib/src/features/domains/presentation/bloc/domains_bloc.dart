import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/config/dependancy_injection/injection_container.dart';
import 'package:scad_mobile/src/features/domains/data/models/domain_classification_model/domain_classification_model.dart';
import 'package:scad_mobile/src/features/domains/data/models/domain_model/domain_model.dart';
import 'package:scad_mobile/src/features/domains/data/models/experimental_filters_response_item.dart';
import 'package:scad_mobile/src/features/domains/data/models/experimental_indicator_list_response.dart';
import 'package:scad_mobile/src/features/domains/domain/repositories/domains_repository_imports.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_keys.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_api_cache.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

import '../../../chat_with_sme/data/models/domain_theme_sub_theme_model.dart';
import '../../data/models/theme_indicator_list_response.dart';
import '../../data/models/theme_subtheme_response.dart';

part 'domains_event.dart';

part 'domains_state.dart';

class DomainsBloc extends Bloc<DomainsEvent, DomainsState> {
  DomainsBloc() : super(DomainLoadingState()) {
    on(eventHandler);
  }

  FutureOr<void> eventHandler(
    DomainsEvent event,
    Emitter<DomainsState> emit,
  ) async {
    if (event is DomainsInitEvent) {
      try {
        // final dynamic res = HiveUtilsApiCache.get(HiveKeys.keyDomainList);
        // if (res != null) {
        //   emit(
        //     DomainShowResponseState(
        //       list: (res as List<dynamic>)
        //           .map((e) => DomainModel.fromJson(e as Map<String, dynamic>))
        //           .toList().where((element) => element.hasAccess).toList(),
        //     ),
        //   );
        //   return;
        // }

        emit(DomainLoadingState());
        final List<RepoResponse<dynamic>> responses = await Future.wait([
          servicelocator<DomainsRepository>().getDomainList(),
          servicelocator<DomainsRepository>().getDomainListIfp(),
        ]);
        if (responses.every((element) => element.isSuccess)) {
          final RepoResponse<
              List<DomainModel>> response1 = responses[0] as RepoResponse<List<DomainModel>>;
          final  RepoResponse<DomainThemeSubThemeModelResponse> response2 = responses[1] as RepoResponse<DomainThemeSubThemeModelResponse>;
          final List<DomainModel> domainList = response1.response!;
          final List<DomainModel> domainListCreated = [];
          final DomainThemeSubThemeModelResponse domainListIfp = response2.response!;
          final Map<String,String> domainListGrouped = {};
          for(final DomainThemeSubThemeModel domainValue in domainListIfp.dataList ?? []) {
            for( final Domain domainList in domainValue.domains ?? []) {
              domainListGrouped[ domainList.id ?? '' ] = domainList.name ?? '';
            }
          }

          // List<Map<String, dynamic>> allDomains = [];

          for(final domainValue in domainListGrouped.entries) {
            for(final DomainModel domain in domainList) {
              if (domain.domainId == domainValue.key) {
                domain.hasAccess = true;
                domainListCreated.add(domain);
              }
              // else {
              //   domain.hasAccess = false;
              // }
              // allDomains.add(domain.toJson());
            }
          }

          emit(DomainShowResponseState(list: domainListCreated));

        } else {
          emit(
            DomainErrorState(error: responses
                .firstWhere((element) => !element.isSuccess)
                .errorMessage),
          );
        }
      } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
        emit(DomainErrorState(error:  LocaleKeys.somethingWentWrong.tr()));
      }
    }
    if (event is ThemesInitEvent) {
      try {
        emit(ThemeLoadingState());
        final RepoResponse<List<DomainClassificationModel>> response =
            await servicelocator<DomainsRepository>()
                .getDomainClassificationList({
          'domain_id': event.domainId,
        });
        if (response.isSuccess) {

          final List<RepoResponse<dynamic>> responses = await Future.wait([
            servicelocator<DomainsRepository>().getDomainList(),
            servicelocator<DomainsRepository>().getDomainListIfp(),
          ]);
          if (responses.every((element) => element.isSuccess)) {
            final RepoResponse<
                List<DomainModel>> response1 = responses[0] as RepoResponse<List<DomainModel>>;
            final  RepoResponse<DomainThemeSubThemeModelResponse> response2 = responses[1] as RepoResponse<DomainThemeSubThemeModelResponse>;
            final List<DomainModel> domainList = response1.response!;
            final List<DomainModel> domainListCreated = [];
            final DomainThemeSubThemeModelResponse domainListIfp = response2.response!;
            final Map<String,String> domainListGrouped = {};
            for(final DomainThemeSubThemeModel domainValue in domainListIfp.dataList ?? []) {
              for( final Domain domainList in domainValue.domains ?? []) {
                domainListGrouped[ domainList.id ?? '' ] = domainList.name ?? '';
              }
            }
            for(final domainValue in domainListGrouped.entries) {
              for(final DomainModel domain in domainList) {
                if (domain.domainId == domainValue.key) {
                  domainListCreated.add(domain);
                }
              }
            }
            emit(
              ThemeClassificationShowResponseState(
                domainId: event.domainId,
                list: response.response!,
                domains: domainListCreated,
              ),
            );
          } else {
            emit(
              ThemeErrorState(error: responses
                  .firstWhere((element) => !element.isSuccess)
                  .errorMessage),
            );
          }

        } else {
          emit(
            ThemeErrorState(error: response.errorMessage),
          );
        }
      } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
        emit(ThemeErrorState(error:  LocaleKeys.somethingWentWrong.tr()));
      }
    }
    if (event is GetThemesInitEvent) {
      try {
        emit(ThemeLoadingState());
        final RepoResponse<List<ThemeSubThemeResponse>> response =
            await servicelocator<DomainsRepository>().getThemeList({
          'domain_id': event.domainId,
          'classification_id': event.classificationId
        });
        if (response.isSuccess) {
          // final RepoResponse<List<DomainModel>> responseDomains =
          //     await servicelocator<DomainsRepository>().getDomainList();

          emit(
            ThemeListShowResponseState(
              domainId: event.domainId,
              list: response.response!,
            ),
          );
        } else {
          emit(
            ThemeErrorState(error: response.errorMessage),
          );
        }
      } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
        emit(ThemeErrorState(error:  LocaleKeys.somethingWentWrong.tr()));
      }
    }

    if (event is GetThemeIndicatorsEvent) {
      try {
        emit(ThemeIndicatorsLoadingState());
        final RepoResponse<ThemeIndicatorListResponse> response =
            await servicelocator<DomainsRepository>().getThemeIndicatorList({
          'domain_id': event.domainId,
          'classification_id': event.classificationId,
          'sub_theme_id': event.subThemeId,
          'sub_domain_id': event.subDomainId,
          'page': event.pageNo.toString(),
        });
        if (response.isSuccess) {
          emit(
            ThemeIndicatorsListState(
              themeIndicatorListResponse: response.response!,
            ),
          );
        } else {
          emit(
            ThemeErrorState(error: response.errorMessage),
          );
        }
      } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
        emit(ThemeErrorState(error:  LocaleKeys.somethingWentWrong.tr()));
      }
    }

    if (event is CompareIndicatorGetThemesEvent) {
      try {
        emit(ThemeIndicatorsLoadingState());
        final RepoResponse<ThemeIndicatorListResponse> response =
            await servicelocator<DomainsRepository>().getThemeIndicatorList({
          'domain_id': event.domainId,
          'classification_id': event.classificationId,
          'sub_theme_id': event.subThemeId,
          'sub_domain_id': event.subDomainId,
          'page': event.pageNo.toString(),
        });
        if (response.isSuccess) {
          emit(
            CompareThemeIndicatorListState(
              themeIndicatorListResponse: response.response!,
              subThemeId: event.subThemeId,
            ),
          );
        } else {
          emit(
            ThemeErrorState(error: response.errorMessage),
          );
        }
      } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
        emit(ThemeErrorState(error: LocaleKeys.somethingWentWrong.tr()));
      }
    }

    if (event is GetThemeExperimentalFiltersEvent) {
      try {
        emit(ThemeIndicatorsLoadingState());
        final RepoResponse<List<ExperimentalFiltersResponseItem>> response =
            await servicelocator<DomainsRepository>().getExperimentalFilters(
          screenerIndicator: event.screenerIndicator,
        );
        if (response.isSuccess) {
          emit(
            ExperimentalFilterListState(
              experimentalFilters: response.response!,
            ),
          );
        } else {
          emit(
            ThemeErrorState(error: response.errorMessage),
          );
        }
      } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
        emit(ThemeErrorState(error:  LocaleKeys.somethingWentWrong.tr()));
      }
    }

    if (event is GetThemeExperimentalIndicatorListEvent) {
      try {
        emit(ThemeIndicatorsLoadingState());
        final RepoResponse<ExperimentalIndicatorListResponse> response =
            await servicelocator<DomainsRepository>()
                .getThemeExperimentalIndicatorList(
          pageNo: event.pageNo,
          filters: event.filters,
        );
        if (response.isSuccess) {
          emit(
            ThemeExperimentalIndicatorListResponseState(
              response: response.response!,
            ),
          );
        } else {
          emit(
            ThemeErrorState(error: response.errorMessage),
          );
        }
      } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
        emit(ThemeErrorState(error: LocaleKeys.somethingWentWrong.tr()));
      }
    }

    if (event is DomainSearchEvent) {
      try {
        List<DomainModel> searchedItems = [];
        searchedItems = event.domains
            .where(
              (element) => (element.domainName ?? '')
                  .toLowerCase()
                  .contains(event.searchQuery),
            )
            .toList();
        emit(
          DomainSearchState(
            searchResult: searchedItems,
          ),
        );
      } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
        emit(ThemeErrorState(error:  LocaleKeys.somethingWentWrong.tr()));
      }
    }
    if (event is GetClassificationsEvent) {
      try {
        emit(ThemeLoadingState());
        final RepoResponse<List<DomainClassificationModel>> response =
            await servicelocator<DomainsRepository>()
                .getDomainClassificationList({
          'domain_id': event.domainId,
        });
        if (response.isSuccess) {
          emit(
            GetThemeClassificationState(
              domainId: event.domainId,
              list: response.response!,
            ),
          );
        } else {
          emit(
            ThemeErrorState(error: response.errorMessage),
          );
        }
      } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
        emit(ThemeErrorState(error:  LocaleKeys.somethingWentWrong.tr()));
      }
    }
  }
}
