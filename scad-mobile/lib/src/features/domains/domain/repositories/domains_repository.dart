part of 'domains_repository_imports.dart';

abstract class DomainsRepository {
  Future<RepoResponse<List<DomainModel>>> getDomainList();

  Future<RepoResponse<List<DomainClassificationModel>>>
      getDomainClassificationList(RequestParamsMap requestParams);

  Future<RepoResponse<List<ThemeSubThemeResponse>>> getThemeList(
      RequestParamsMap requestParams);

  Future<RepoResponse<ThemeIndicatorListResponse>> getThemeIndicatorList(
      RequestParamsMap requestParams);
  Future<RepoResponse<ExperimentalIndicatorListResponse>>
      getThemeExperimentalIndicatorList(
          {required int pageNo, required Map<String, dynamic> filters});

  Future<RepoResponse<List<ExperimentalFiltersResponseItem>>>
      getExperimentalFilters({
    required String screenerIndicator,
  });
  Future<RepoResponse<DomainThemeSubThemeModelResponse>> getDomainListIfp();
}
