import 'package:json_annotation/json_annotation.dart';

part 'domain_classification_model.g.dart';

@JsonSerializable()
class DomainClassificationModel {
  String? id;
  String? key;
  String? name;
  @JsonKey(name: 'light_icon')
  String? lightIcon;
  @J<PERSON><PERSON>ey(name: 'dark_icon')
  String? darkIcon;
  dynamic count;

  DomainClassificationModel(
      this.id, this.key, this.name, this.lightIcon, this.darkIcon, this.count);

  factory DomainClassificationModel.fromJson(Map<String, dynamic> json) =>
      _$DomainClassificationModelFromJson(json);

  Map<String, dynamic> toJson() => _$DomainClassificationModelToJson(this);
}
