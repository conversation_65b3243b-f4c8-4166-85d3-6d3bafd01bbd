class ExperimentalIndicatorListResponse {
  ExperimentalIndicatorListResponse({
    this.totalCount,
    this.page,
    this.limit,
    this.data,
  });

  ExperimentalIndicatorListResponse.fromJson(Map<String, dynamic> json) {
    totalCount = json['totalCount'] as int?;
    page = json['page'] as int?;
    limit = json['limit'] as int?;
    data = (json['data'] as List?)
        ?.map((dynamic e) =>
            ExperimentalIndicatorListItem.fromJson(e as Map<String, dynamic>))
        .toList();
  }

  int? totalCount;
  int? page;
  int? limit;
  List<ExperimentalIndicatorListItem>? data;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['totalCount'] = totalCount;
    json['page'] = page;
    json['limit'] = limit;
    json['data'] = data?.map((e) => e.toJson()).toList();
    return json;
  }
}

class ExperimentalIndicatorListItem {
  ExperimentalIndicatorListItem({
    this.indicatorId,
    this.title,
    this.compareFilters,
    this.valueFormat,
    this.templateFormat,
    this.baseDate,
    this.value,
    this.yearlyCompareValue,
    this.yearlyChangeValue,
    this.quarterlyCompareValue,
    this.quarterlyChangeValue,
    this.monthlyCompareValue,
    this.monthlyChangeValue,
    this.unit,
  });

  ExperimentalIndicatorListItem.fromJson(Map<String, dynamic> json) {
    indicatorId = json['indicatorId'] as String?;
    title = json['title'] as String?;
    compareFilters = (json['compareFilters'] as List?)
        ?.map((dynamic e) => e as String)
        .toList();
    valueFormat = json['valueFormat'] as String?;
    templateFormat = json['templateFormat'] as String?;
    baseDate = json['baseDate'] as String?;
    value = json['value'].toString();
    yearlyCompareValue = json['yearlyCompareValue'].toString();
    yearlyChangeValue = json['yearlyChangeValue'].toString();
    quarterlyCompareValue = json['quarterlyCompareValue'].toString();
    quarterlyChangeValue = json['quarterlyChangeValue'] .toString();
    monthlyCompareValue = json['monthlyCompareValue'].toString();
    monthlyChangeValue = json['monthlyChangeValue'].toString();
    unit = json['unit'] as String?;
  }

  String? indicatorId;
  String? title;
  List<String>? compareFilters;
  String? valueFormat;
  String? templateFormat;
  String? baseDate;
  String? value;
  String? yearlyCompareValue;
  String? yearlyChangeValue;
  String? quarterlyCompareValue;
  String? quarterlyChangeValue;
  String? monthlyCompareValue;
  String? monthlyChangeValue;
  String? unit;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['indicatorId'] = indicatorId;
    json['title'] = title;
    json['compareFilters'] = compareFilters;
    json['valueFormat'] = valueFormat;
    json['templateFormat'] = templateFormat;
    json['baseDate'] = baseDate;
    json['value'] = value;
    json['yearlyCompareValue'] = yearlyCompareValue;
    json['yearlyChangeValue'] = yearlyChangeValue;
    json['quarterlyCompareValue'] = quarterlyCompareValue;
    json['quarterlyChangeValue'] = quarterlyChangeValue;
    json['monthlyCompareValue'] = monthlyCompareValue;
    json['monthlyChangeValue'] = monthlyChangeValue;
    json['unit'] = unit;
    return json;
  }
}
