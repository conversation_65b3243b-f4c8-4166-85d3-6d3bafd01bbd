class DomainModel {
  DomainModel.fromJson(Map<String, dynamic> json)
      : uuid = json['uuid'] as String?,
        addedBy = (json['added_by'] as Map<String, dynamic>?) != null
            ? AddedBy.fromJson(json['added_by'] as Map<String, dynamic>)
            : null,
        editedBy = (json['edited_by'] as Map<String, dynamic>?) != null
            ? EditedBy.fromJson(json['edited_by'] as Map<String, dynamic>)
            : null,
        domainIcon = json['domain_icon'] as String?,
        domainId = json['domain_id'] as String?,
        domainName = json['domain_name'] as String?,
        domainNameAr = json['domain_name_ar'] as String?,
        active = json['active'] as bool?,
        createdTime = json['created_time'] as String?,
        updatedTime = json['updated_time'] as String?,
        hasAccess = json['has_access'] == null ?false: json['has_access'] as bool;

  DomainModel({
    this.uuid,
    this.addedBy,
    this.editedBy,
    this.domainIcon,
    this.domainId,
    this.domainName,
    this.domainNameAr,
    this.active,
    this.createdTime,
    this.updatedTime,
  });
  final String? uuid;
  final AddedBy? addedBy;
  final EditedBy? editedBy;
  final String? domainIcon;
  final String? domainId;
  final String? domainName;
  final String? domainNameAr;
  final bool? active;
  final String? createdTime;
  final String? updatedTime;
  bool hasAccess = false;

  Map<String, dynamic> toJson() => {
        'uuid': uuid,
        'added_by': addedBy?.toJson(),
        'edited_by': editedBy?.toJson(),
        'domain_icon': domainIcon,
        'domain_id': domainId,
        'domain_name': domainName,
        'domain_name_ar': domainNameAr,
        'active': active,
        'created_time': createdTime,
        'updated_time': updatedTime,
        'has_access': hasAccess,
      };
}

class AddedBy {
  AddedBy({
    this.uuid,
    this.name,
  });

  AddedBy.fromJson(Map<String, dynamic> json)
      : uuid = json['uuid'] as String?,
        name = json['name'] as String?;
  final String? uuid;
  final String? name;

  Map<String, dynamic> toJson() => {'uuid': uuid, 'name': name};
}

class EditedBy {
  EditedBy({
    this.uuid,
    this.name,
  });

  EditedBy.fromJson(Map<String, dynamic> json)
      : uuid = json['uuid'] as String?,
        name = json['name'] as String?;
  final String? uuid;
  final String? name;

  Map<String, dynamic> toJson() => {'uuid': uuid, 'name': name};
}
