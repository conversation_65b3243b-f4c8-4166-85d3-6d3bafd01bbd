part of 'home_bloc.dart';

abstract class HomeEvent extends Equatable {
  const HomeEvent();

  @override
  List<Object> get props => [];
}

class NavToHomeEvent extends HomeEvent {
  NavToHomeEvent() {
    rnd = Random().nextInt(10000);
  }

  late final int rnd;

  @override
  List<Object> get props => [rnd];
}

class KeyIndicatorsEvent extends HomeEvent {
  const KeyIndicatorsEvent();

  @override
  List<Object> get props => [];
}

class RecommendedIndicatorsEvent extends HomeEvent {
  const RecommendedIndicatorsEvent();

  @override
  List<Object> get props => [];
}

class IndicatorHomeCurrentEvent extends HomeEvent {
  const IndicatorHomeCurrentEvent({
    required this.id,
    required this.contentType,
    required this.status
  });
  final String id;
  final String contentType;
  final String status;
  @override
  List<Object> get props => [id,contentType,status ];
}

class HomeProductsStatusEvent extends HomeEvent {
  const HomeProductsStatusEvent();

  @override
  List<Object> get props => [];
}
