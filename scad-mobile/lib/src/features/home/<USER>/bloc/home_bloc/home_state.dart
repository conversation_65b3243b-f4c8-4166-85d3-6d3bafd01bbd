part of 'home_bloc.dart';

abstract class HomeState extends Equatable {
  const HomeState();

  // const HomeState({required this.isKeyIndicators, });

  /// For toggling key indicators and recommeded for you
  // final bool isKeyIndicators;

  @override
  List<Object> get props => [
        /*isKeyIndicators*/
      ];
}

class HomeInitial extends HomeState {}

class KeyIndicatorListState extends HomeState {
  const KeyIndicatorListState({
    required this.list,
  });

  final List<KeyIndicatorListResponseItem> list;

  @override
  List<Object> get props => [list];
}

class RecommendedIndicatorListState extends HomeState {
  const RecommendedIndicatorListState({
    required this.list,
  });

  final List<RecommendedIndicatorListResponseItem> list;

  @override
  List<Object> get props => [list];
}

class NavToHomeState extends HomeState {
  NavToHomeState() {
    rnd = Random().nextInt(10000);
  }

  late final int rnd;

  @override
  List<Object> get props => [rnd];
}

class KeyIndicatorLoadingState extends HomeState {}

class RecommendedIndicatorLoadingState extends HomeState {}

class KeyIndicatorCompleteState extends HomeState {}

class RecommendedIndicatorCompleteState extends HomeState {}

class KeyIndicatorErrorState extends HomeState {
  const KeyIndicatorErrorState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}

class RecommendedIndicatorErrorState extends HomeState {
  const RecommendedIndicatorErrorState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}
class IndicatorHomeCurrentStatusState extends HomeState {
  IndicatorHomeCurrentStatusState({
    required this.statusMap,
  }) {
    rnd = Random().nextInt(10000);
  }
  late final int rnd;
  final Map<String, String> statusMap;


  @override
  List<Object> get props => [statusMap, rnd];
}

class HomeProductsStatusSuccessState extends HomeState {
  HomeProductsStatusSuccessState({
    required this.hasAccessToScenarioDrivers,
    required this.hasAccessToForecasts,
    required this.hasAccessToInsightsDiscovery,
    required this.hasAccessToGeoSpatial,
  }){
    rnd = Random().nextInt(10000);
  }

  late final int rnd;
  final bool hasAccessToScenarioDrivers;
  final bool hasAccessToForecasts;
  final bool hasAccessToInsightsDiscovery;
  final bool hasAccessToGeoSpatial;

  @override
  List<Object> get props => [
        rnd,
        hasAccessToScenarioDrivers,
        hasAccessToForecasts,
        hasAccessToInsightsDiscovery,
        hasAccessToGeoSpatial,
      ];
}
