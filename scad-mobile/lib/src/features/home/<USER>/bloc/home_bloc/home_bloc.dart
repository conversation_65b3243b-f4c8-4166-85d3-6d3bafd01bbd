import 'dart:async';
import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/route_manager/route_imports.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/config/dependancy_injection/injection_container.dart';
import 'package:scad_mobile/src/features/home/<USER>/models/key_indicator_list/key_indicator_list_response.dart';
import 'package:scad_mobile/src/features/home/<USER>/models/recommended_indicator_list_item/recommended_indicator_list_item.dart';
import 'package:scad_mobile/src/features/home/<USER>/repositories/home_repository_imports.dart';
import 'package:scad_mobile/src/features/onboarding/data/models/interest_domain_entity.dart';
import 'package:scad_mobile/src/features/products/data/models/response/product_response1.dart';
import 'package:scad_mobile/src/features/products/domain/repositories/products_repository_imports.dart';
import 'package:scad_mobile/src/features/spatial_analytics/data/models/spatial_analytics_data.dart';
import 'package:scad_mobile/src/features/spatial_analytics/domain/repositories/spatial_analytics_imports.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_api_cache.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

part 'home_event.dart';
part 'home_state.dart';

class HomeBloc extends Bloc<HomeEvent, HomeState> {
  HomeBloc(this.homeRepository) : super(HomeInitial()) {
    on<HomeEvent>((event, emit) {});
    on<NavToHomeEvent>(_onNavToHome);
    on<KeyIndicatorsEvent>(keyIndicatorEventHandler);
    on<RecommendedIndicatorsEvent>(recommendedIndicatorEventHandler);
    on<IndicatorHomeCurrentEvent>(indicatorHomeCurrentEventHandler);
    on<HomeProductsStatusEvent>(_onHomeProductsStatusEvent);
  }

  final HomeRepository homeRepository;

  FutureOr<void> _onNavToHome(
    NavToHomeEvent event,
    Emitter<HomeState> emit,
  ) {
    emit(NavToHomeState());
  }

  FutureOr<void> keyIndicatorEventHandler(
    KeyIndicatorsEvent event,
    Emitter<HomeState> emit,
  ) async {
    try {
      emit(KeyIndicatorLoadingState());

      final RepoResponse<List<KeyIndicatorListResponseItem>> response =
          await servicelocator<HomeRepository>().keyIndicatorList();

      if (response.isSuccess) {
        emit(KeyIndicatorListState(list: response.response ?? []));
      } else {
        emit(
          KeyIndicatorErrorState(error: response.errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(KeyIndicatorErrorState(error:  LocaleKeys.somethingWentWrong.tr()));
    } finally {
      // emit(KeyIndicatorCompleteState());
    }
  }

  FutureOr<void> recommendedIndicatorEventHandler(
    RecommendedIndicatorsEvent event,
    Emitter<HomeState> emit,
  ) async {
    try {
      emit(RecommendedIndicatorLoadingState());

      final RepoResponse<List<InterestDomainEntity>> response =
          await servicelocator<HomeRepository>().getSelectedDomainList();

      if (response.isSuccess) {
        final List<String> list = [];
        for (int i = 0; i < response.response!.length; i++) {
          list.add(response.response![i].domainId!);
        }

        final RepoResponse<List<RecommendedIndicatorListResponseItem>>
            nodeList = await servicelocator<HomeRepository>()
                .recommendedIndicatorList(list);

        if (nodeList.isSuccess) {
          emit(
            RecommendedIndicatorListState(
              list: nodeList.response ?? [],
            ),
          );
          emit(RecommendedIndicatorLoadingState());
        } else {
          emit(
            RecommendedIndicatorErrorState(error: response.errorMessage),
          );
        }
      } else {
        emit(
          RecommendedIndicatorErrorState(error: response.errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(RecommendedIndicatorErrorState(error:  LocaleKeys.somethingWentWrong.tr()));
    } finally {
      emit(RecommendedIndicatorCompleteState());
    }
  }

FutureOr<void>indicatorHomeCurrentEventHandler(
    IndicatorHomeCurrentEvent event,
    Emitter<HomeState> emit,
    ) async {
  final BuildContext ctx = servicelocator<AppRouter>().navigatorKey.currentContext!;

  final String hiveKeyStatus =
      'indicatorStatusListEpx^xwQMEMOzgQ#YY_I#c{2YMx~@@:${Localizations.localeOf(ctx).languageCode}';
  final Map<dynamic,dynamic> map =  (HiveUtilsApiCache.get(hiveKeyStatus) ?? {}) as Map<dynamic,dynamic>;
  final Map<String, String> resStatus = {
    for(var m in map.entries)
      m.key.toString(): m.value.toString(),
  };
  resStatus['${event.id}---${event.contentType}' ] = event.status;
  emit(IndicatorHomeCurrentStatusState(statusMap:resStatus));
  HiveUtilsApiCache.set(hiveKeyStatus, resStatus);
}

  FutureOr<void> _onHomeProductsStatusEvent(
    HomeProductsStatusEvent event,
    Emitter<HomeState> emit,
  ) async {
    try {

      final List<RepoResponse<dynamic>> responses = await Future.wait([
        servicelocator<ProductsRepository>().productData(),
        servicelocator<SpatialAnalyticsRepository>().getArcGisData(),
      ]);

      bool hasAccessToScenarioDrivers = false;
      bool hasAccessToInsightsDiscovery = false;
      bool hasAccessToForecasts =false ;
      bool hasAccessToGeoSpatial=false;

      if(responses[0].isSuccess) {
        final List<ProductResponse> responseProducts = responses[0].response as List<ProductResponse>;
        hasAccessToScenarioDrivers= responseProducts.any((element) => element.name == 'Scenario Drivers'||element.name == 'محركات السيناريوهات');
        hasAccessToInsightsDiscovery= responseProducts.any((element) => element.name == 'Insights Discovery'||element.name == 'اكتشاف الرؤى');
        hasAccessToForecasts= responseProducts.any((element) => element.name == 'Forecasts'||  element.name == 'التنبؤ' );
      }

      if(responses[1].isSuccess) {
        final SpatialAnalyticsData responseSpatial = responses[1].response as SpatialAnalyticsData;
        hasAccessToGeoSpatial = (responseSpatial.modules ?? []).isNotEmpty;
      }

      emit(
        HomeProductsStatusSuccessState(
          hasAccessToScenarioDrivers: hasAccessToScenarioDrivers,
          hasAccessToInsightsDiscovery: hasAccessToInsightsDiscovery,
          hasAccessToForecasts: hasAccessToForecasts,
          hasAccessToGeoSpatial: hasAccessToGeoSpatial,
        ),
      );

    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
    }
  }
}
