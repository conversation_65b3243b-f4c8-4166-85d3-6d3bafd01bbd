import 'package:json_annotation/json_annotation.dart';

part 'key_indicator_list_response.g.dart';

@JsonSerializable()
class KeyIndicatorListResponseItem {
  String? uuid;
  @Json<PERSON>ey(name: 'added_by')
  KeyIndicatorListResponseAddedBy? addedBy;
  @Json<PERSON><PERSON>(name: 'edited_by')
  KeyIndicatorListResponseEditedBy? editedBy;
  @Json<PERSON>ey(name: 'node_id')
  String? nodeId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'app_type')
  String? appType;
  @J<PERSON><PERSON><PERSON>(name: 'content_type')
  String? contentType;
  String? key;
  String? title;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'domain_name')
  String? domainName;
  bool? active;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_time')
  String? createdTime;
  @Json<PERSON>ey(name: 'updated_time')
  String? updatedTime;

  KeyIndicatorListResponseItem(
      this.uuid,
      this.addedBy,
      this.editedBy,
      this.nodeId,
      this.appType,
      this.contentType,
      this.key,
      this.title,
      this.domainName,
      this.active,
      this.createdTime,
      this.updatedTime);

  factory KeyIndicatorListResponseItem.fromJson(Map<String, dynamic> json) =>
      _$KeyIndicatorListResponseItemFromJson(json);

  Map<String, dynamic> toJson() => _$KeyIndicatorListResponseItemToJson(this);
}

@JsonSerializable()
class KeyIndicatorListResponseAddedBy {
  String? uuid;
  String? name;

  KeyIndicatorListResponseAddedBy(this.uuid, this.name);

  factory KeyIndicatorListResponseAddedBy.fromJson(Map<String, dynamic> json) =>
      _$KeyIndicatorListResponseAddedByFromJson(json);

  Map<String, dynamic> toJson() =>
      _$KeyIndicatorListResponseAddedByToJson(this);
}

@JsonSerializable()
class KeyIndicatorListResponseEditedBy {
  String? uuid;
  String? name;

  KeyIndicatorListResponseEditedBy(this.uuid, this.name);

  factory KeyIndicatorListResponseEditedBy.fromJson(
          Map<String, dynamic> json) =>
      _$KeyIndicatorListResponseEditedByFromJson(json);

  Map<String, dynamic> toJson() =>
      _$KeyIndicatorListResponseEditedByToJson(this);
}
