// import 'package:auto_route/annotations.dart';
// import 'package:easy_localization/easy_localization.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:scad_mobile/main.dart';
// import 'package:scad_mobile/src/common/widgets/dropdown_widget.dart';
// import 'package:scad_mobile/src/common/widgets/expandable_widget.dart';
// import 'package:scad_mobile/src/features/home/<USER>/models/doughnut_chart_data.dart';
// import 'package:scad_mobile/src/features/spatial_analytics/data/models/point_of_interest.dart';
// import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
// import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
// import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
// import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
// import 'package:scad_mobile/translations/locale_keys.g.dart';
// import 'package:sliding_up_panel2/sliding_up_panel2.dart';
// import 'package:syncfusion_flutter_charts/charts.dart';
//
// @RoutePage()
// class SpatialAnalysisDetailScreen extends StatefulWidget {
//   const SpatialAnalysisDetailScreen({super.key});
//
//   @override
//   State<SpatialAnalysisDetailScreen> createState() =>
//       _SpatialAnalysisDetailScreenState();
// }
//
// class _SpatialAnalysisDetailScreenState
//     extends State<SpatialAnalysisDetailScreen> {
//   String loremIpsum =
//       'Lorem ipsum dolor sit amet consectetur. Auctor magna nunc sem diam. Duis'
//       ' enim tortor non ut non adipiscing tellus eu. Mus diam mauris risus non'
//       ' amet vitae faucibus faucibus. Tempor eros nulla arcu cursus velit arcu '
//       'scelerisque.';
//
//   List<String> nationality = ['Emirati', 'Non-Emirati'];
//   ValueNotifier<String> selectedNationality = ValueNotifier('');
//
//   List<String> tabs = ['By Nationals', 'By Gender'];
//   ValueNotifier<String> selectedTab = ValueNotifier('By Nationals');
//
//   List<PointOfInterests> pointOfInterests = [
//     PointOfInterests(
//         title: 'Hospitals',
//         icon: AppImages.icHospitals,
//         count: 0,
//         color: AppColors.blue2),
//     PointOfInterests(
//         title: 'Schools',
//         icon: AppImages.icSchools,
//         count: 0,
//         color: AppColors.amber),
//     PointOfInterests(
//         title: 'Nurseries',
//         icon: AppImages.icNurseries,
//         count: 0,
//         color: AppColors.blue3),
//     PointOfInterests(
//         title: 'Clinics',
//         icon: AppImages.icClinics,
//         count: 0,
//         color: AppColors.green4),
//     PointOfInterests(
//         title: 'Sports club',
//         icon: AppImages.icSportsClub,
//         count: 0,
//         color: AppColors.purple),
//   ];
//
//   final List<ChartData> chartData = [
//     ChartData('Non-Emirati', 42, AppColors.chartBlue),
//     ChartData('Emirati', 58, AppColors.chartGreen),
//   ];
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: SlidingUpPanel(
//         minHeight: MediaQuery.sizeOf(context).height * 0.38,
//         maxHeight: MediaQuery.sizeOf(context).height * 0.9,
//         borderRadius: const BorderRadius.only(
//           topLeft: Radius.circular(20),
//           topRight: Radius.circular(20),
//         ),
//         body: Column(
//           children: [
//             const SizedBox(height: 60),
//             Row(
//               children: [
//                 InkWell(
//                   onTap: () {
//                     Navigator.of(context).maybePop();
//                   },
//                   borderRadius: BorderRadius.circular(8),
//                   child: Padding(
//                     padding: const EdgeInsets.symmetric(
//                       horizontal: 14,
//                       vertical: 6,
//                     ),
//                     child: Row(
//                       mainAxisSize: MainAxisSize.min,
//                       children: [
//                         RotatedBox(
//                           quarterTurns:
//                               DeviceType.isDirectionRTL(context) ? 2 : 0,
//                           child: SvgPicture.asset(
//                             AppImages.icArrowLeft,
//                             colorFilter: ColorFilter.mode(
//                               AppColors.blue,
//                               BlendMode.srcIn,
//                             ),
//                           ),
//                         ),
//                         Padding(
//                           padding: const EdgeInsets.only(left: 10),
//                           child: Text(
//                             'Back',
//                             style: AppTextStyles.s14w4cBlue,
//                             textScaler:
//                                 TextScaler.linear(textScaleFactor.value),
//                           ),
//                         ),
//                       ],
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//             Stack(
//               alignment: Alignment.topCenter,
//               children: [
//                 Container(
//                   height: MediaQuery.sizeOf(context).height * 0.6,
//                   alignment: Alignment.center,
//                   color: AppColors.greyShade5,
//                   child: Text(
//                     'Map View',
//                     textScaler: TextScaler.linear(textScaleFactor.value),
//                   ),
//                 ),
//                 Padding(
//                   padding:
//                       const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
//                   child: SizedBox(
//                     height: 46,
//                     child: TextField(
//                       style: TextStyle(
//                         height: 1,
//                         fontSize: 14,
//                         color: AppColors.blueLight,
//                       ),
//                       onChanged: (value) {},
//                       decoration: InputDecoration(
//                         suffixIcon: SizedBox(
//                           width: 27,
//                           height: 27,
//                           child: Padding(
//                             padding: const EdgeInsets.all(8),
//                             child:
//                                 SvgPicture.asset(AppImages.icSearchGreenRound),
//                           ),
//                         ),
//                         hintText: LocaleKeys.areaName.tr(),
//                         isDense: true,
//                         border: OutlineInputBorder(
//                           borderSide: const BorderSide(
//                               color: Colors.blueAccent, width: 32),
//                           borderRadius: BorderRadius.circular(25),
//                         ),
//                         enabledBorder: OutlineInputBorder(
//                           borderSide:
//                               const BorderSide(color: Colors.white, width: 32),
//                           borderRadius: BorderRadius.circular(25),
//                         ),
//                         focusedBorder: OutlineInputBorder(
//                           borderSide:
//                               const BorderSide(color: Colors.white, width: 32),
//                           borderRadius: BorderRadius.circular(25),
//                         ),
//                       ),
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//           ],
//         ),
//         panelBuilder: () {
//           return Column(
//             children: [
//               const SizedBox(height: 10),
//               Center(
//                 child: Container(
//                   height: 5,
//                   width: 50,
//                   decoration: BoxDecoration(
//                     borderRadius: BorderRadius.circular(50),
//                     color: AppColors.greyShade5,
//                   ),
//                 ),
//               ),
//               const SizedBox(height: 10),
//               Center(
//                 child: Text(
//                   'Spatial Analysis',
//                   style: AppTextStyles.s24w5cBlackOrWhiteShade,
//                   textScaler: TextScaler.linear(textScaleFactor.value),
//                 ),
//               ),
//               Padding(
//                 padding:
//                     const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
//                 child: Row(
//                   children: [
//                     Expanded(
//                       child: DropDownWidget(
//                         label: 'Theme',
//                         items: ['Population']
//                             .map(
//                               (String item) => DropdownMenuItem<String>(
//                                 value: item,
//                                 child: Text(
//                                   item,
//                                   style: AppTextStyles.s14w4cblackShade4,
//                                   overflow: TextOverflow.ellipsis,
//                                   textScaler:
//                                       TextScaler.linear(textScaleFactor.value),
//                                 ),
//                               ),
//                             )
//                             .toList(),
//                         selectedValue: 'Population',
//                         onChanged: (value) {},
//                       ),
//                     ),
//                     const SizedBox(width: 20),
//                     Expanded(
//                       child: DropDownWidget(
//                         label: 'Sub theme',
//                         items: ['Household Population']
//                             .map(
//                               (String item) => DropdownMenuItem<String>(
//                                 value: item,
//                                 child: Text(
//                                   item,
//                                   style: AppTextStyles.s14w4cblackShade4,
//                                   overflow: TextOverflow.ellipsis,
//                                   textScaler:
//                                       TextScaler.linear(textScaleFactor.value),
//                                 ),
//                               ),
//                             )
//                             .toList(),
//                         selectedValue: 'Household Population',
//                         onChanged: (value) {},
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//               Divider(
//                 height: 1,
//                 color: AppColors.greyShade1,
//                 thickness: 1,
//               ),
//               Expanded(
//                 child: ListView(
//                   shrinkWrap: true,
//                   // physics: const NeverScrollableScrollPhysics(),
//                   padding: EdgeInsets.zero,
//                   children: [
//                     ExpandableWidget(
//                       shape: const RoundedRectangleBorder(),
//                       headerChild: Expanded(
//                         child: Padding(
//                           padding: const EdgeInsets.only(left: 16),
//                           child: Text(
//                             'Household Population',
//                             style: const TextStyle(
//                               fontSize: 14,
//                               fontWeight: FontWeight.w500,
//                             ),
//                             textScaler:
//                                 TextScaler.linear(textScaleFactor.value),
//                           ),
//                         ),
//                       ),
//                       expandedChild: Padding(
//                         padding: const EdgeInsets.all(16),
//                         child: Column(
//                           children: [
//                             Row(
//                               children: [
//                                 InkWell(
//                                   onTap: () {},
//                                   child: SvgPicture.asset(
//                                     AppImages.icBell,
//                                   ),
//                                 ),
//                                 const SizedBox(width: 12),
//                                 InkWell(
//                                   onTap: () {},
//                                   child: SvgPicture.asset(
//                                     AppImages.icAddToMyAppsOff,
//                                   ),
//                                 ),
//                               ],
//                             ),
//                             Padding(
//                               padding: const EdgeInsets.symmetric(vertical: 16),
//                               child: Text(
//                                 loremIpsum,
//                                 style: AppTextStyles.s14w4cblackShade4
//                                     .copyWith(color: AppColors.greyShade4),
//                                 textScaler:
//                                     TextScaler.linear(textScaleFactor.value),
//                               ),
//                             ),
//                             Container(
//                               decoration: BoxDecoration(
//                                 color: AppColors.greyShade8,
//                                 borderRadius: BorderRadius.circular(10),
//                               ),
//                               child: Row(
//                                 mainAxisAlignment:
//                                     MainAxisAlignment.spaceBetween,
//                                 children: [
//                                   Padding(
//                                     padding: const EdgeInsets.only(left: 16),
//                                     child: Text(
//                                       'Nationality',
//                                       style: AppTextStyles.s14w5cBlack.copyWith(
//                                           color: AppColors.blackShade1),
//                                       textScaler: TextScaler.linear(
//                                           textScaleFactor.value),
//                                     ),
//                                   ),
//                                   Row(
//                                     children: [
//                                       for (final String item in nationality)
//                                         Row(
//                                           children: [
//                                             ValueListenableBuilder(
//                                               valueListenable:
//                                                   selectedNationality,
//                                               builder:
//                                                   (context, checkValue, _) {
//                                                 return SizedBox(
//                                                   width: 20,
//                                                   child: Checkbox(
//                                                     checkColor: AppColors.white,
//                                                     activeColor:
//                                                         AppColors.blueLight,
//                                                     side: BorderSide(
//                                                       color:
//                                                           AppColors.greyShade1,
//                                                     ),
//                                                     value: item == checkValue,
//                                                     onChanged: (value) {
//                                                       selectedNationality
//                                                           .value = item;
//                                                     },
//                                                   ),
//                                                 );
//                                               },
//                                             ),
//                                             const SizedBox(width: 6),
//                                             Text(
//                                               item,
//                                               style: AppTextStyles.s14w4cBlue
//                                                   .copyWith(
//                                                 color: AppColors.grey,
//                                               ),
//                                               textScaler: TextScaler.linear(
//                                                   textScaleFactor.value),
//                                             ),
//                                             const SizedBox(width: 18),
//                                           ],
//                                         ),
//                                     ],
//                                   ),
//                                 ],
//                               ),
//                             ),
//                             const SizedBox(height: 16),
//                             Container(
//                               decoration: ShapeDecoration(
//                                 color: Colors.white,
//                                 shape: RoundedRectangleBorder(
//                                   side: const BorderSide(
//                                     strokeAlign: BorderSide.strokeAlignOutside,
//                                     color: Color(0xFFF3F4F6),
//                                   ),
//                                   borderRadius: BorderRadius.circular(60),
//                                 ),
//                                 shadows: const [
//                                   BoxShadow(
//                                     color: Color(0x119EA9C7),
//                                     blurRadius: 29,
//                                     offset: Offset(0, 4),
//                                   ),
//                                 ],
//                               ),
//                               child: ValueListenableBuilder(
//                                 valueListenable: selectedTab,
//                                 builder: (context, item, _) {
//                                   return Row(
//                                     mainAxisSize: MainAxisSize.min,
//                                     crossAxisAlignment:
//                                         CrossAxisAlignment.start,
//                                     children: [
//                                       for (final String tab in tabs)
//                                         Expanded(
//                                           child: GestureDetector(
//                                             onTap: () {
//                                               selectedTab.value = tab;
//                                             },
//                                             child: Container(
//                                               padding:
//                                                   const EdgeInsets.symmetric(
//                                                 horizontal: 20,
//                                                 vertical: 5,
//                                               ),
//                                               decoration: ShapeDecoration(
//                                                 color: tab == item
//                                                     ? AppColors.blue
//                                                     : AppColors.white,
//                                                 shape: RoundedRectangleBorder(
//                                                   borderRadius:
//                                                       BorderRadius.circular(80),
//                                                 ),
//                                               ),
//                                               child: Text(
//                                                 tab,
//                                                 overflow: TextOverflow.ellipsis,
//                                                 textAlign: TextAlign.center,
//                                                 style: AppTextStyles.s14w4cBlue
//                                                     .copyWith(
//                                                   color: tab == item
//                                                       ? AppColors.white
//                                                       : AppColors.grey,
//                                                   height: 0,
//                                                 ),
//                                                 textScaler: TextScaler.linear(
//                                                     textScaleFactor.value),
//                                               ),
//                                             ),
//                                           ),
//                                         ),
//                                     ],
//                                   );
//                                 },
//                               ),
//                             ),
//                             Padding(
//                               padding: const EdgeInsets.symmetric(vertical: 16),
//                               child: Row(
//                                 mainAxisAlignment:
//                                     MainAxisAlignment.spaceBetween,
//                                 children: [
//                                   const DoughnutMetaData(
//                                     title: 'Total Population',
//                                     count: '7,000,000',
//                                   ),
//                                   DoughnutMetaData(
//                                     title: 'Emirati',
//                                     count: '3,534,345',
//                                     color: AppColors.chartBlue,
//                                   ),
//                                   DoughnutMetaData(
//                                     title: 'Non-Emirati',
//                                     count: '4,345,234',
//                                     color: AppColors.chartGreen,
//                                   ),
//                                 ],
//                               ),
//                             ),
//                             Row(
//                               mainAxisAlignment: MainAxisAlignment.center,
//                               children: [
//                                 const DoughnutMetaData(
//                                   title: 'Non-Emirati',
//                                   count: '4,345,234',
//                                 ),
//                                 Padding(
//                                   padding: const EdgeInsets.symmetric(
//                                     horizontal: 12,
//                                   ),
//                                   child: Row(
//                                     children: [
//                                       const SizedBox(
//                                         width: 40,
//                                         child: Divider(),
//                                       ),
//                                       Container(
//                                         height: 150,
//                                         width: 120,
//                                         alignment: Alignment.center,
//                                         child: SfCircularChart(
//                                           margin: EdgeInsets.zero,
//                                           series: <CircularSeries>[
//                                             // Renders doughnut chart
//                                             DoughnutSeries<ChartData, String>(
//                                               dataSource: chartData,
//                                               pointColorMapper:
//                                                   (ChartData data, _) =>
//                                                       data.color,
//                                               xValueMapper:
//                                                   (ChartData data, _) => data.x,
//                                               yValueMapper:
//                                                   (ChartData data, _) => data.y,
//                                               radius: '60',
//                                               innerRadius: '48',
//                                             ),
//                                           ],
//                                         ),
//                                       ),
//                                       const SizedBox(
//                                         width: 40,
//                                         child: Divider(),
//                                       ),
//                                     ],
//                                   ),
//                                 ),
//                                 const DoughnutMetaData(
//                                   title: 'Emirati',
//                                   count: '42%',
//                                 ),
//                               ],
//                             ),
//                           ],
//                         ),
//                       ),
//                     ),
//                     ExpandableWidget(
//                       shape: const RoundedRectangleBorder(),
//                       headerChild: Expanded(
//                         child: Padding(
//                           padding: const EdgeInsets.only(left: 16),
//                           child: Text(
//                             'Points of Interest',
//                             style: const TextStyle(
//                               fontSize: 14,
//                               fontWeight: FontWeight.w500,
//                             ),
//                             textScaler:
//                                 TextScaler.linear(textScaleFactor.value),
//                           ),
//                         ),
//                       ),
//                       expandedChild: Center(
//                         child: Wrap(
//                           runSpacing: 18,
//                           children: [
//                             for (final PointOfInterests item
//                                 in pointOfInterests)
//                               SizedBox(
//                                 width: MediaQuery.sizeOf(context).width * 0.32,
//                                 child: Column(
//                                   mainAxisSize: MainAxisSize.min,
//                                   children: [
//                                     Container(
//                                       height: 46,
//                                       width: 46,
//                                       alignment: Alignment.center,
//                                       decoration: BoxDecoration(
//                                         color: item.color,
//                                         borderRadius: BorderRadius.circular(30),
//                                       ),
//                                       child: SvgPicture.asset(item.icon),
//                                     ),
//                                     Padding(
//                                       padding: const EdgeInsets.symmetric(
//                                         vertical: 10,
//                                       ),
//                                       child: Text(
//                                         item.title,
//                                         style: AppTextStyles.s14w4cblackShade4
//                                             .copyWith(
//                                           color: AppColors.grey,
//                                         ),
//                                         textScaler: TextScaler.linear(
//                                             textScaleFactor.value),
//                                       ),
//                                     ),
//                                     Text(
//                                       item.count.toString(),
//                                       style: AppTextStyles.s19w6cBlack,
//                                       textScaler: TextScaler.linear(
//                                           textScaleFactor.value),
//                                     ),
//                                   ],
//                                 ),
//                               ),
//                           ],
//                         ),
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//             ],
//           );
//         },
//       ),
//     );
//   }
// }
//
// class DoughnutMetaData extends StatelessWidget {
//   const DoughnutMetaData({
//     required this.title,
//     required this.count,
//     super.key,
//     this.color,
//   });
//
//   final String title;
//   final String count;
//   final Color? color;
//
//   @override
//   Widget build(BuildContext context) {
//     return Row(
//       mainAxisSize: MainAxisSize.min,
//       children: [
//         Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Row(
//               mainAxisSize: MainAxisSize.min,
//               children: [
//                 if (color != null)
//                   Padding(
//                     padding: const EdgeInsets.only(right: 6),
//                     child: Icon(
//                       Icons.circle,
//                       size: 14,
//                       color: color,
//                     ),
//                   ),
//                 Text(
//                   title,
//                   style: AppTextStyles.s14w4cGrey,
//                   textScaler: TextScaler.linear(textScaleFactor.value),
//                 ),
//               ],
//             ),
//             Text(
//               count,
//               style: AppTextStyles.s14w4cGrey.copyWith(
//                 color: AppColors.black,
//                 fontWeight: FontWeight.w600,
//               ),
//               textScaler: TextScaler.linear(textScaleFactor.value),
//             ),
//           ],
//         ),
//       ],
//     );
//   }
// }
