import 'package:auto_route/auto_route.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/common/widgets/app_box_shadow.dart';
import 'package:scad_mobile/src/common/widgets/app_sliding_tab.dart';
import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/presentation/pages/indicator_card_v2.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/intro_widget.dart';
import 'package:scad_mobile/src/common/widgets/no_data_placeholder.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/showcaseview.dart';
import 'package:scad_mobile/src/features/domains/data/models/domain_model/domain_model.dart';
import 'package:scad_mobile/src/features/domains/presentation/bloc/domains_bloc.dart';
import 'package:scad_mobile/src/features/home/<USER>/models/indicator_tab.dart';
import 'package:scad_mobile/src/features/home/<USER>/models/key_indicator_list/key_indicator_list_response.dart';
import 'package:scad_mobile/src/features/home/<USER>/models/recommended_indicator_list_item/recommended_indicator_list_item.dart';
import 'package:scad_mobile/src/features/home/<USER>/bloc/home_bloc/home_bloc.dart';
import 'package:scad_mobile/src/features/home/<USER>/widgets/home_page_list_toggler/home_page_list_toggler.dart';
import 'package:scad_mobile/src/features/notification/presentation/bloc/notification_bloc.dart';
import 'package:scad_mobile/src/features/settings/presentation/bloc/setting_bloc.dart';
import 'package:scad_mobile/src/features/spatial_analytics/presentation/pages/spatial_analytics_screen.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

@RoutePage()
class HomePage extends StatefulWidget {
  const HomePage({
    required this.navToDomainsPage,
    required this.navToProductsPage,
    super.key,
  });

  final VoidCallback navToDomainsPage;
  final void Function(String type) navToProductsPage;

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage>
    with AutomaticKeepAliveClientMixin {
  ValueNotifier<int> currentTabIndex = ValueNotifier(0);
  final CarouselController _carouselController = CarouselController();

  List<IndicatorTab> tabItems = [
    IndicatorTab(
      title: LocaleKeys.tableauDashboards.tr(),
      icon: AppImages.icDashboard,
      color: AppColors.blueShade23,
      productType: 'td',
    ),
    IndicatorTab(
      title: LocaleKeys.publications.tr(),
      icon: AppImages.icPublications,
      color: AppColors.blueShade25,
      productType: 'pb',
    ),
    IndicatorTab(
      title: LocaleKeys.webReports.tr(),
      icon: AppImages.icWebReports,
      color: AppColors.blueShade25,
      productType: 'wr',
    ),
  ];

  final GlobalKey step1 = GlobalKey(debugLabel: 'home-step1');
  final GlobalKey step2 = GlobalKey(debugLabel: 'home-step2');
  final GlobalKey step3 = GlobalKey(debugLabel: 'home-step3');
  final GlobalKey step4 = GlobalKey(debugLabel: 'home-step4');
  List<GlobalKey> steps = [];

  List<KeyIndicatorListResponseItem> keyIndicatorList = [];
  List<RecommendedIndicatorListResponseItem> recommendedIndicatorList = [];

  num indicatorsRefreshedAt = 0;
  ValueNotifier<int> indicatorListTabIndex = ValueNotifier(0);
  List<DomainModel> domainList = [];
  ValueNotifier<int> recommendedDomainTabIndex = ValueNotifier(0);
  bool isUnreadAllNotification = false;

  /// to detect if user is coming back to home
  /// from details page through user guide
  bool isPreviousFromDetailsScreen = false;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    steps = [step1, step2, step3, step4];
    isUnreadAllNotification =
        HiveUtilsSettings.getAllNotificationsUnReadStatus();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (mounted) {
        await _loadIndicators();
        if (HiveUtilsSettings.getUserGuideStatus() == UserGuides.Home) {
          ShowCaseWidget.of(context).startShowCase(steps);
        }
      }

      // if (widget.homeCarouselInitialIndex != null) {
      //  Future.delayed(const Duration(milliseconds: 300,),(){
      //    _carouselController.animateToPage(widget.homeCarouselInitialIndex!);
      //  currentTabIndex.value=widget.homeCarouselInitialIndex!;});
      // }
    });
  }

  @override
  void dispose() {
    super.dispose();

    // ShowCaseWidget.of(context).dispose();
  }

  Future<void> _loadIndicators() async {
    // To check the status of products buttons
    // currently only checking the geospatial status
    context.read<HomeBloc>().add(const HomeProductsStatusEvent());

    keyIndicatorList = [];
    recommendedIndicatorList = [];

    context.read<DomainsBloc>().add(const DomainsInitEvent());
    context.read<HomeBloc>().add(const KeyIndicatorsEvent());
    context.read<HomeBloc>().add(const RecommendedIndicatorsEvent());
    recommendedDomainTabIndex.value = 0;
    indicatorsRefreshedAt = DateTime.now().microsecondsSinceEpoch;
  }

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    final Size size = MediaQuery.sizeOf(context);
    final Widget child = Container(
      height: 11,
      width: 11,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: AppColors.red,
      ),
    );
    super.build(context);

    return Stack(
      alignment: Alignment.topCenter,
      children: [
        // Image.asset(
        //   AppImages.animatedBg,
        //   height: MediaQuery.sizeOf(context).height * 0.3,
        //   width: MediaQuery.sizeOf(context).width,
        //   fit: BoxFit.cover,
        // ),
        Container(
          height: MediaQuery.sizeOf(context).height * 0.3,
          width: MediaQuery.sizeOf(context).width,
          decoration: BoxDecoration(
              gradient: LinearGradient(
            colors: [
              AppColors.blueGradientShade1,
              AppColors.blueGradientShade2,
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),),
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            BlocListener<DomainsBloc, DomainsState>(
              listener: (context, state) {
                if (state is DomainShowResponseState) {
                  domainList = state.list;
                }
              },
              child: const SizedBox(),
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 20),
              child: Column(
                children: [
                  SizedBox(
                    height: MediaQuery.paddingOf(context).top,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 10,
                      vertical: 10,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        IntroWidget(
                          stepKey: step1,
                          stepIndex: 1,
                          totalSteps: 10,
                          title: LocaleKeys.hamburgerMenu.tr(),
                          description: LocaleKeys.hamburgerMenuDesc.tr(),
                          arrowAlignment: Alignment.bottomLeft,
                          targetPadding: const EdgeInsets.all(3),
                          onNext: () {
                            indicatorListTabIndex.value = 1;
                          },
                          arrowPadding: const EdgeInsets.only(
                            left: 14,
                            bottom: 10,
                          ),
                          child: appDrawerController.drawerButton(
                            isArabic: HiveUtilsSettings.getAppLanguage() == 'ar',
                          ),
                        ),
                        Expanded(
                          child: GestureDetector(
                            onTap: () {
                              context.router.removeWhere((route) =>
                                  route.name == SearchScreenRoute.name,);
                              context.pushRoute(SearchScreenRoute());
                            },
                            child: Hero(
                              tag: 'search_box',
                              child: Material(
                                type: MaterialType.transparency,
                                child: Container(
                                  margin: const EdgeInsets.symmetric(
                                      horizontal: 10,),
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 10,
                                    vertical: 9,
                                  ),
                                  // /  clipBehavior: Clip.antiAlias,
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    border: Border.all(color: Colors.white),
                                    borderRadius: BorderRadius.circular(60),
                                  ),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Padding(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 12,
                                        ),
                                        child: Text(
                                          LocaleKeys.search.tr(),
                                          style: AppTextStyles.s14w3cGrey,
                                          textScaler: TextScaler.linear(
                                            textScaleFactor.value,
                                          ),
                                        ),
                                      ),
                                      SizedBox(
                                        width: 12,
                                        height: 12,
                                        child: SvgPicture.asset(
                                          AppImages.icSearchWhite,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                        Stack(
                          clipBehavior: Clip.none,
                          children: [
                            // Positioned(
                            //   top: -60,
                            //   right: -45,
                            //   child: Lottie.asset(
                            //     AnimationAsset.animationRipple,
                            //   ),
                            // ),
                            IconButton(
                              onPressed: () {
                                context
                                    .pushRoute(const NotificationListRoute());
                              },
                              icon: Stack(
                                children: [
                                  SvgPicture.asset(AppImages.icNotification),
                                  BlocConsumer<SettingBloc, SettingState>(
                                    listener: (context, state) {
                                      if (state is DefaultSettingSuccessState) {
                                        isUnreadAllNotification = state
                                                .defaultSettingResponse
                                                .notificationUnread ??
                                            false;
                                      }
                                    },
                                    builder: (context, state) => BlocConsumer<
                                        NotificationBloc, NotificationState>(
                                      listener: (context, state) {
                                        if (state
                                            is ReadNotificationSuccessState) {
                                          isUnreadAllNotification =
                                              state.isAllUnRead;
                                        }
                                      },
                                      builder: (context, state) {
                                        return isUnreadAllNotification
                                            ? DeviceType.isDirectionRTL(context)
                                                ? Positioned(
                                                    top: 0,
                                                    left: 0,
                                                    child: child,
                                                  )
                                                : Positioned(
                                                    top: 0,
                                                    right: 0,
                                                    child: child,
                                                  )
                                            : const SizedBox();
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  // const SizedBox(height: 10),
                  // GestureDetector(
                  //   onTap: () {
                  //     context.router.removeWhere((route) => route.name == SearchScreenRoute.name);
                  //     context.pushRoute(SearchScreenRoute());
                  //   },
                  //   child: Hero(
                  //     tag: 'search_box',
                  //     child: Material(
                  //       type: MaterialType.transparency,
                  //       child: Container(
                  //         margin: const EdgeInsets.symmetric(horizontal: 36),
                  //         padding: const EdgeInsets.symmetric(
                  //           horizontal: 10,
                  //           vertical: 9,
                  //         ),
                  //         clipBehavior: Clip.antiAlias,
                  //         decoration: ShapeDecoration(
                  //           color: Colors.white,
                  //           shape: RoundedRectangleBorder(
                  //             borderRadius: BorderRadius.circular(60),
                  //           ),
                  //         ),
                  //         child: Row(
                  //           children: [
                  //             Expanded(
                  //               child: Padding(
                  //                 padding: const EdgeInsets.symmetric(
                  //                   horizontal: 12,
                  //                 ),
                  //                 child: Text(
                  //                   LocaleKeys.search.tr(),
                  //                   style: AppTextStyles.s14w3cHintColor,
                  //                   textScaler: TextScaler.linear(
                  //                     textScaleFactor.value,
                  //                   ),
                  //                 ),
                  //               ),
                  //             ),
                  //             SizedBox(
                  //               width: 27,
                  //               height: 27,
                  //               child: SvgPicture.asset(
                  //                 AppImages.icSearchGreenRound,
                  //               ),
                  //             ),
                  //           ],
                  //         ),
                  //       ),
                  //     ),
                  //   ),
                  // ),
                  // const SizedBox(height: 16),
                ],
              ),
            ),
            Expanded(
              child: RefreshIndicator(
                onRefresh: _loadIndicators,
                child: ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(40),
                    topRight: Radius.circular(40),
                  ),
                  child: Container(
                    decoration: ShapeDecoration(
                      color: isLightMode
                          ? AppColors.scaffoldBackgroundLight
                          : AppColors.scaffoldBackgroundDark,
                      shape: const RoundedRectangleBorder(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(40),
                          topRight: Radius.circular(40),
                        ),
                      ),
                    ),
                    child: ListView(
                      padding: const EdgeInsets.only(bottom: 150),
                      children: [
                        slidingTabView(context, isLightMode),
                        const SizedBox(height: 16),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 24),
                          child: Align(
                            child: Text(
                              LocaleKeys.keyFiguresAtAGlance.tr(),
                              style: AppTextStyles.s16w5cBlueTitleText.copyWith(
                                color: !isLightMode ? AppColors.white : null,
                              ),
                              textScaler:
                                  TextScaler.linear(textScaleFactor.value),
                            ),
                          ),
                        ),
                        const SizedBox(
                          height: 16,
                        ),
                        BlocConsumer<HomeBloc, HomeState>(
                          listener: (context, state) {
                            if (state is HomeProductsStatusSuccessState) {
                              tabItems = [
                                IndicatorTab(
                                  title: LocaleKeys.tableauDashboards.tr(),
                                  icon: AppImages.icDashboard,
                                  color: AppColors.blueShade23,
                                  productType: 'td',
                                ),
                                IndicatorTab(
                                  title: LocaleKeys.publications.tr(),
                                  icon: AppImages.icPublications,
                                  color: AppColors.blueShade25,
                                  productType: 'pb',
                                ),
                                IndicatorTab(
                                  title: LocaleKeys.webReports.tr(),
                                  icon: AppImages.icWebReports,
                                  color: AppColors.blueShade25,
                                  productType: 'wr',
                                ),
                              ];

                              if (state.hasAccessToScenarioDrivers) {
                                tabItems.add(
                                  IndicatorTab(
                                    title: LocaleKeys.scenarioDrivers.tr(),
                                    icon: AppImages.icScenarioDrivers,
                                    color: AppColors.blueShade24,
                                    productType: LocaleKeys.scenarioDriver.tr(),
                                  ),
                                );
                              }
                              if (state.hasAccessToForecasts) {
                                tabItems.add(
                                  IndicatorTab(
                                    title: LocaleKeys.forecasts.tr(),
                                    icon: AppImages.icForecast,
                                    color: const Color(0xffE55353),
                                    productType: LocaleKeys.forcastHome.tr(),
                                  ),
                                );
                              }

                              if (state.hasAccessToInsightsDiscovery) {
                                tabItems.add(
                                  IndicatorTab(
                                    title: LocaleKeys.insightsDiscovery.tr(),
                                    icon: AppImages.icInsightsDiscovery,
                                    color: AppColors.greyShade18,
                                    productType:
                                        LocaleKeys.insightsDiscoverys.tr(),
                                  ),
                                );
                              }

                              if (!isDemoMode) {
                                if (state.hasAccessToGeoSpatial) {
                                  tabItems.add(
                                    IndicatorTab(
                                      title: LocaleKeys.geoSpatial.tr(),
                                      icon: AppImages.icGeoSpatial,
                                      color: AppColors.greyShade17,
                                    ),
                                  );
                                }
                              }
                            } else if (state is IndicatorHomeCurrentStatusState) {
                              for(var e in state.statusMap.entries) {
                               final List<String> values =  e.key.split('---');
                               final int index = keyIndicatorList.indexWhere((element) {
                                 return element.nodeId == values.first && element.contentType == values[1] &&  e.value == 'error';
                               });

                               if (index != -1) {
                                 keyIndicatorList.removeAt(index);
                               }
                              }
                            }
                            if (state is RecommendedIndicatorListState) {
                              recommendedIndicatorList = state.list;
                            }
                            if (state is KeyIndicatorListState) {
                              keyIndicatorList = state.list;
                            }
                          },
                          builder: (context, state) {
                            return ValueListenableBuilder(
                              valueListenable: indicatorListTabIndex,
                              builder: (context, i, w) {
                                return Column(
                                  crossAxisAlignment:
                                      CrossAxisAlignment.stretch,
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 24,
                                      ),
                                      child: Container(
                                        decoration: BoxDecoration(
                                          boxShadow: isLightMode
                                              ? AppBox.shadow()
                                              : null,
                                          color: isLightMode
                                              ? AppColors.blueShadeTabInset
                                              : AppColors.blueShade34,
                                          borderRadius:
                                              BorderRadius.circular(40),
                                        ),
                                        height: 40,
                                        width: size.width,
                                        // borderRadius: 40,
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Expanded(
                                              child: HomePageListToggler(
                                                title: LocaleKeys.keyIndicators
                                                    .tr(),
                                                isSelected:
                                                    indicatorListTabIndex
                                                            .value ==
                                                        0,
                                                onTap: () {
                                                  indicatorListTabIndex.value =
                                                      0;
                                                },
                                              ),
                                            ),
                                            Expanded(
                                              child: IntroWidget(
                                                stepKey: step2,
                                                stepIndex: 2,
                                                totalSteps: 10,
                                                title: LocaleKeys
                                                    .recommendedForYou
                                                    .tr(),
                                                description: LocaleKeys
                                                    .recommendedForYouDesc
                                                    .tr(),
                                                arrowAlignment:
                                                    Alignment.topRight,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.end,
                                                arrowPadding: EdgeInsets.only(
                                                  right:
                                                      MediaQuery.sizeOf(context)
                                                              .width *
                                                          0.24,
                                                  left:
                                                      MediaQuery.sizeOf(context)
                                                              .width *
                                                          0.24,
                                                  bottom: 10,
                                                ),
                                                targetPadding:
                                                    const EdgeInsets.all(8),
                                                child: HomePageListToggler(
                                                  title: LocaleKeys.forYou.tr(),
                                                  isSelected:
                                                      indicatorListTabIndex
                                                              .value ==
                                                          1,
                                                  onTap: () {
                                                    indicatorListTabIndex
                                                        .value = 1;
                                                  },
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    AnimatedSwitcher(
                                      duration:
                                          const Duration(milliseconds: 200),
                                      child: indicatorListTabIndex.value == 0
                                          ? _buildKeyIndicatorList(
                                              state,
                                              isLightMode,
                                            )
                                          : ValueListenableBuilder(
                                              valueListenable:
                                                  recommendedDomainTabIndex,
                                              builder: (context, i, w) {
                                                return _buildRecommendedIndicatorList(
                                                  state,
                                                  isLightMode,
                                                );
                                              },
                                            ),
                                    ),
                                  ],
                                );
                              },
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget slidingTabView(BuildContext context, bool isLightMode) {
    int currentCount = 0;

    currentCount =
        MediaQuery.sizeOf(context).width ~/ (90 * textScaleFactor.value);

    return BlocBuilder<HomeBloc, HomeState>(
      builder: (context, state) {
        return Column(
          children: [
            Container(
              color: isLightMode
                  ? AppColors.greyShade15_1
                  : AppColors.blueTitleText,
              // borderColor: isLightMode ? AppColors.white : AppColors.greyBorder,
              width: MediaQuery.sizeOf(context).width,
              height: 114,
              child: Padding(
                padding: const EdgeInsets.fromLTRB(5, 14, 5, 14),
                child: CarouselSlider.builder(
                  carouselController: _carouselController,
                  options: CarouselOptions(
                    onPageChanged: (index, reason) {
                      currentTabIndex.value = index;
                    },
                    viewportFraction: 1,
                    enableInfiniteScroll: false,
                  ),
                  itemCount: (tabItems.length / currentCount).ceil(),
                  itemBuilder: (context, index, realIdx) {
                    final int currentStartingValue = index * currentCount;
                    final list = List<int>.generate(
                      currentCount,
                      (i) => currentStartingValue + i,
                    );
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: list.map((idx) {
                        if (idx >= tabItems.length) {
                          return Container(
                            constraints: BoxConstraints(
                              minWidth:
                                  (MediaQuery.sizeOf(context).width - 112) /
                                      currentCount,
                            ),
                            margin: const EdgeInsets.symmetric(horizontal: 2),
                          );
                        }
                        final VoidCallback onTap =
                            tabItems[idx].title == LocaleKeys.geoSpatial.tr()
                                ? () {
                                    AutoRouter.of(context).push(
                                      SpatialAnalyticsScreenRoute(),
                                    );
                                  }
                                : () {
                                    if (tabItems[idx].productType != null) {
                                      widget.navToProductsPage(
                                        tabItems[idx].productType!,
                                      );
                                    }
                                  };

                        return idx < tabItems.length
                            ? _indicator(
                                tabItems[idx].title,
                                tabItems[idx].icon,
                                tabItems[idx].color,
                                onTap: onTap,
                                count: currentCount,
                                isLightMode: isLightMode,
                              )
                            : const SizedBox();
                      }).toList(),
                    );
                  },
                ),
              ),
            ),
            Container(
              color: isLightMode
                  ? AppColors.greyShade15_1
                  : AppColors.blueTitleText,
              padding: const EdgeInsets.only(bottom: 10),
              child: ValueListenableBuilder(
                valueListenable: currentTabIndex,
                builder: (context, currentIndex, _) {
                  return Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(
                      (tabItems.length / currentCount).ceil(),
                      (index) => AnimatedContainer(
                        duration: const Duration(milliseconds: 200),
                        margin: const EdgeInsets.only(right: 5),
                        height: 5,
                        // width: currentIndex == index ? 14 : 5,
                        width: 5,
                        decoration: BoxDecoration(
                          color: currentIndex == index
                              ? isLightMode
                                  ? AppColors.blueLightOld
                                  : AppColors.greyShade4
                              : (isLightMode
                                      ? AppColors.blueLightOld
                                      : AppColors.greyShade4)
                                  .withOpacity(0.4),
                          borderRadius: BorderRadius.circular(3),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _indicator(
    String label,
    String icon,
    Color color, {
    required VoidCallback onTap,
    int count = 4,
    required bool isLightMode,
  }) {
    return Container(
      constraints: BoxConstraints(
        minWidth: (MediaQuery.sizeOf(context).width - 112) / count,
      ),
      margin: const EdgeInsets.symmetric(horizontal: 2),
      // width: (MediaQuery.sizeOf(context).width-48)/4,
      child: InkWell(
        onTap: onTap,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Spacer(flex: 3),
            ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: Container(
                width: 50,
                height: 50,
                decoration: ShapeDecoration(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                  ),
                ),
                child: SvgPicture.asset(icon),
              ),
            ),
            const SizedBox(height: 6),
            Text(
              label,
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                color: isLightMode
                    ? AppColors.blueTitleText
                    : AppColors.greyShade1,
                fontSize: 12,
                fontWeight: FontWeight.w400,
                height: 0,
              ),
              textScaler: TextScaler.linear(textScaleFactor.value),
            ),
            const Spacer(
              flex: 2,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildKeyIndicatorList(HomeState state, bool isLightMode) {
    if (keyIndicatorList.isEmpty) {
      if (state is KeyIndicatorLoadingState) {
        return const Center(
          child: Padding(
            padding: EdgeInsets.symmetric(
              vertical: 100,
            ),
            child: CircularProgressIndicator(),
          ),
        );
      }
      return const Center(
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 100),
          child: NoDataPlaceholder(),
        ),
      );
    }
    return ListView.separated(
      key: const Key('keyIndicatorList'),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.fromLTRB(20, 15, 20, 0),
      itemCount: keyIndicatorList.length,
      separatorBuilder: (context, index) => const SizedBox(
        height: 25,
      ),
      itemBuilder: (context, index) {
        final KeyIndicatorListResponseItem item = keyIndicatorList[index];
        return IndicatorCardV2(
          key: Key(
            'home.keyIndicators.IndicatorCardV2-${item.nodeId}-$indicatorsRefreshedAt',
          ),
          id: item.nodeId!,
          contentType: item.contentType!,
        );
      },
    );
  }

  String? _checkDomain(String e) {
    try {
      return domainList
          .singleWhere(
            (element) =>
                e ==
                (DeviceType.isDirectionRTL(context)
                    ? element.domainNameAr
                    : element.domainName),
          )
          .domainIcon;
    } catch (e) {
      return '';
    }
  }

  Widget _buildRecommendedIndicatorList(HomeState state, bool isLightMode) {
    // if (domainList.isEmpty) {
    //   _loadIndicators();
    // }

    if (recommendedIndicatorList.isEmpty) {
      if (state is RecommendedIndicatorLoadingState) {
        return const Center(
          child: Padding(
            padding: EdgeInsets.symmetric(
              vertical: 100,
            ),
            child: CircularProgressIndicator(),
          ),
        );
      }
      return const Center(
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 100),
          child: NoDataPlaceholder(),
        ),
      );
    }

    // todo need domain id in recommendedIndicatorList
    // on changing language to ar, build error happens
    List<String> domainInterestList = recommendedIndicatorList
        .map((e) => e.domain!)
        .toList()
        .toSet()
        .toList();

    final List<RecommendedIndicatorListResponseItem> indicatorList =
        recommendedIndicatorList
            .where(
              (element) =>
                  element.domain ==
                  domainInterestList[recommendedDomainTabIndex.value],
            )
            .toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Divider(
          height: 40,
          color: AppColors.greyShade9,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: Text(
            LocaleKeys.statisticsInterest.tr(),
            style: AppTextStyles.s16w5cBlueTitleText
                .copyWith(color: !isLightMode ? AppColors.white : null),
            textScaler: TextScaler.linear(textScaleFactor.value),
          ),
        ),
        const SizedBox(
          height: 16,
        ),
        AppSlidingTab(
          initialTabIndex: recommendedDomainTabIndex.value,
          horizontalPadding: 24,
          key: Key('recommended.domains.$indicatorsRefreshedAt'),
          onTabChange: (i) {
            recommendedDomainTabIndex.value = i;
          },
          tabs: [
            ...domainInterestList.map(
              (e) => AppSlidingTabItem(
                label: e,
                iconUrl: _checkDomain(e),
              ),
            ),
            // HiveUtilsApiCache.getDomainImage(),),)
          ],
        ),
        ListView.separated(
          key: const Key('recommendedIndicatorList'),
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: const EdgeInsets.fromLTRB(20, 15, 20, 0),
          itemCount: indicatorList.length,
          separatorBuilder: (context, index) => const SizedBox(
            height: 25,
          ),
          itemBuilder: (context, index) {
            final RecommendedIndicatorListResponseItem item =
                indicatorList[index];

            return IndicatorCardV2(
              key: Key(
                'home.recommended.IndicatorCardV2-${item.indicatorId}-$indicatorsRefreshedAt',
              ),
              id: item.indicatorId!,
              contentType: item.type!,
              actionButtonsKey: index == 0 ? step3 : null,
              openButtonKey: index == 0 ? step4 : null,
              onUserGuideBackFromDetailsPage: (isPreviousActionTriggered) {
                isPreviousFromDetailsScreen = isPreviousActionTriggered;
                if (isPreviousActionTriggered) {
                  if (HiveUtilsSettings.getUserGuideStatus() ==
                      UserGuides.Home) {
                    ShowCaseWidget.of(context).startShowCase(steps);
                    ShowCaseWidget.of(context).jumpToId(steps.length-1);
                  }
                }
              },
            );
          },
        ),
      ],
    );
  }
}
