// Original Author: <PERSON><PERSON><PERSON> (bilal<PERSON><EMAIL>)
// Version 2 maintained by: <PERSON><PERSON> (be<PERSON><PERSON><PERSON><PERSON>@gmail.com)

library persistent_bottom_nav_bar_v2;

import "dart:math";
import "dart:ui";

import "package:flutter/cupertino.dart";
import "package:flutter/material.dart";
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import "package:go_router/go_router.dart";
import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';

import '../../bloc/home_bloc/home_bloc.dart';

part "components/custom_tab_view.dart";
part "components/decorated_navbar.dart";
part "components/persistent_tab_view.dart";
part "components/persistent_tab_view_scaffold.dart";
part "models/animations.dart";
part "models/configs.dart";
part "models/navbar_decoration.dart";
part "models/navbar_overlap.dart";
part "models/page_route_transitions.dart";
part "models/persistent_tab_controller.dart";
part "utils/functions_utils.dart";
part "utils/navigator_functions.dart";
