import 'package:flutter/material.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

class HomePageListToggler extends StatelessWidget {
  const HomePageListToggler({
    required this.title,
    required this.isSelected,
    required this.onTap,
    super.key,
  });

  final String title;
  final bool isSelected;
  final Function onTap;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return InkWell(
        onTap: () => onTap(),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 350),
          height: 40,
          padding: const EdgeInsets.symmetric(
            horizontal: 10,
            vertical: 5,
          ),
          alignment: Alignment.center,
          decoration: ShapeDecoration(
            color: isSelected ? AppColors.blueLightOld : Colors.transparent,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(80),
            ), /**/
          ),
          child: AnimatedSwitcher(
            duration: const Duration(milliseconds: 350),
            child: isSelected
                ? Text(
                    key: const Key('selected'),
                    title,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      height: 0,
                    ),
                    textScaler: TextScaler.linear(textScaleFactor.value),
                  )
                : Text(
                    key: const Key('unselected'),
                    title,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color:
                          isLightMode ? AppColors.blueGreyShade2 : AppColors.greyShade4,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      height: 0,
                    ),
                    textScaler: TextScaler.linear(textScaleFactor.value),
                  ),
          ),
        ));
  }
}
