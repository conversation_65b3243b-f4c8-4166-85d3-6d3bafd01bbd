part of 'reset_password_bloc.dart';

abstract class ResetPasswordEvent extends Equatable {
  const ResetPasswordEvent();

  @override
  List<Object> get props => [];
}

class CheckPasswordSetEvent extends ResetPasswordEvent {
  const CheckPasswordSetEvent({required this.isResendOtp, required this.token});

  final String token;
  final bool isResendOtp;
  @override
  List<Object> get props => [token, isResendOtp];
}

class OnResetPasswordEvent extends ResetPasswordEvent {
  const OnResetPasswordEvent({
    required this.newPassword,
    required this.confirmPassword,
    required this.otp,
    required this.token,
  });

  final String newPassword;
  final String confirmPassword;
  final String otp;
  final String token;

  @override
  List<Object> get props => [newPassword, confirmPassword, otp, token];
}

class TimerUpdateEvent extends ResetPasswordEvent {
  const TimerUpdateEvent({required this.isTimerRunning});

  final bool isTimerRunning;

  @override
  List<Object> get props => [isTimerRunning];
}
