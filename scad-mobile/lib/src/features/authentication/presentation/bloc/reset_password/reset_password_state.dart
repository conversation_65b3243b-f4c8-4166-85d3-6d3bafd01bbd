part of 'reset_password_bloc.dart';

abstract class ResetPasswordState extends Equatable {
  const ResetPasswordState();

  @override
  List<Object> get props => [];
}

class ResetPasswordInitialState extends ResetPasswordState {
  const ResetPasswordInitialState();

  @override
  List<Object> get props => [];
}
class ResendOtpLoadingState extends ResetPasswordState {
  const ResendOtpLoadingState();

  @override
  List<Object> get props => [];
}

class ResetPasswordErrorState extends ResetPasswordState {
  const ResetPasswordErrorState({this.errorText});

  final String? errorText;

  @override
  List<Object> get props => [errorText ?? ''];
}

class CheckPasswordSetState extends ResetPasswordState {
  const CheckPasswordSetState({this.isPasswordSet, this.token});

  final bool? isPasswordSet;
  final String? token;

  @override
  List<Object> get props => [isPasswordSet ?? false];
}

class CheckPasswordSetErrorState extends ResetPasswordState {
  const CheckPasswordSetErrorState({this.errorText});

  final String? errorText;

  @override
  List<Object> get props => [errorText ?? ''];
}

class OnResetPasswordSuccessState extends ResetPasswordState {
  const OnResetPasswordSuccessState({this.isSuccess, this.text});
  final bool? isSuccess;
  final String? text;

  @override
  List<Object> get props => [isSuccess ?? false, text ?? ''];
}

class TimerUpdateState extends ResetPasswordState {
  const TimerUpdateState({required this.isTimerRunning});


  final bool isTimerRunning;

  @override
  List<Object> get props => [isTimerRunning];
}
