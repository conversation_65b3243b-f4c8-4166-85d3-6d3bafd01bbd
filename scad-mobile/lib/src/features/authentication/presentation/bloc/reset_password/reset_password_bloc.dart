import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/config/dependancy_injection/injection_container.dart';
import 'package:scad_mobile/src/features/authentication/data/models/login_model/response/check_password_set_model.dart';
import 'package:scad_mobile/src/features/authentication/data/models/login_model/response/reset_password_model.dart';
import 'package:scad_mobile/src/features/authentication/domain/repositories/auth_repository_imports.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

part 'reset_password_event.dart';

part 'reset_password_state.dart';

class ResetPasswordBloc extends Bloc<ResetPasswordEvent, ResetPasswordState> {
  ResetPasswordBloc(this.authRepo) : super(const ResetPasswordInitialState()) {
    on<ResetPasswordEvent>((event, emit) {});
    on<CheckPasswordSetEvent>(_onCheckPasswordSet);
    on<OnResetPasswordEvent>(_onResetPassword);
    on<TimerUpdateEvent>(_onTimerUpdate);
  }

  final AuthRepository authRepo;

  /// for checking whether password is already set or not
  Future<void> _onCheckPasswordSet(
    CheckPasswordSetEvent event,
    Emitter<ResetPasswordState> emit,
  ) async {
    if(event.isResendOtp){
      emit(const ResendOtpLoadingState());
    } else {
      emit(const ResetPasswordInitialState());
    }
    try {
      final RepoResponse<CheckPasswordSetModel> response =
          await servicelocator<AuthRepository>().checkPasswordSet(
        token: event.token,
      );

      if (response.isSuccess) {
        emit(
          CheckPasswordSetState(
            isPasswordSet: response.response?.passwordSet,
            token: event.token,
          ),
        );
      } else {
        emit(
          CheckPasswordSetErrorState(
            errorText: response.errorMessage,//'Link Expired! Please contact admin',
          ),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(ResetPasswordErrorState(errorText:LocaleKeys.somethingWentWrong.tr()));
    }
  }

  /// for resetting the password
  Future<void> _onResetPassword(
    OnResetPasswordEvent event,
    Emitter<ResetPasswordState> emit,
  ) async {
    emit(const ResetPasswordInitialState());

    try {
      final RepoResponse<ResetPasswordModel> response =
          await servicelocator<AuthRepository>().resetPassword(
        newPassword: event.newPassword,
        confirmPassword: event.confirmPassword,
        otp: event.otp,
        token: event.token,
      );
      if (response.isSuccess) {
        emit(
          OnResetPasswordSuccessState(
            text: response.response?.message,
            isSuccess: response.isSuccess,
          ),
        );
      } else {
        emit(ResetPasswordErrorState(errorText: response.errorMessage));
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(ResetPasswordErrorState(errorText:LocaleKeys.somethingWentWrong.tr()));
    }
  }

  void _onTimerUpdate(
      TimerUpdateEvent event,
    Emitter<ResetPasswordState> emit,
  ) {
    emit(TimerUpdateState(isTimerRunning: event.isTimerRunning));
  }
}
