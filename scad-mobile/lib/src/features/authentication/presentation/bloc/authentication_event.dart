part of 'authentication_bloc.dart';

abstract class AuthenticationEvent extends Equatable {
  const AuthenticationEvent();

  @override
  List<Object> get props => [];
}

class UaePassLoginEvent extends AuthenticationEvent {
  const UaePassLoginEvent({
    required this.accessToken,
  });

  final String accessToken;

  @override
  List<Object> get props => [accessToken];
}

class EmailLoginSubmitEvent extends AuthenticationEvent {
  const EmailLoginSubmitEvent({
    required this.loginRequest,
  });

  final UserLoginRequestModel loginRequest;

  @override
  List<Object> get props => [loginRequest];
}

class SplashInitCheckEvent extends AuthenticationEvent {
  const SplashInitCheckEvent();

  @override
  List<Object> get props => [];
}
