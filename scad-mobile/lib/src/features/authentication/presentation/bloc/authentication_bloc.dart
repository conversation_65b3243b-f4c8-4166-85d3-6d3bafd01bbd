import 'dart:async';
import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/config/dependancy_injection/injection_container.dart';
import 'package:scad_mobile/src/config/firebase_config/firebase_config.dart';
import 'package:scad_mobile/src/features/authentication/data/models/login_model/request/user_login_request.dart';
import 'package:scad_mobile/src/features/authentication/data/models/login_model/response/user_login_response.dart';
import 'package:scad_mobile/src/features/authentication/domain/repositories/auth_repository_imports.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_auth.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_persistent.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

part 'authentication_event.dart';

part 'authentication_state.dart';

class AuthenticationBloc
    extends Bloc<AuthenticationEvent, AuthenticationState> {
  AuthenticationBloc(this.authRepo) : super(AuthenticationInitial()) {
    on(eventHandler);
  }

  final AuthRepository authRepo;

  Map<String, dynamic> deepLinkData = {};

  FutureOr<void> eventHandler(
    AuthenticationEvent event,
    Emitter<AuthenticationState> emit,
  ) async {
    /// User login event
    if (event is UaePassLoginEvent) {
      emit(LoginLoadingState());

      try {
        final RepoResponse<UserLoginResponseModel> response =
            await servicelocator<AuthRepository>().emailLogin({
          'uae_pass_token': event.accessToken,
          'device_uuid': HiveUtilsPersistent.getUuid(),
        });
        if (response.isSuccess) {
          await HiveUtilsAuth.login(
            response.response?.key ?? '',
            response.response?.refresh ?? '',
            uuid: response.response?.uuid ?? '',
            isDomainsSelected: response.response?.isDomainSelected ?? false,
          );
          await FirebaseConfig.setUserIdToAnalytics(
            response.response?.uuid ?? '',
          );
          emit(LoginSuccessState());
        } else {
          emit(
            LoginFailureState(error: response.errorMessage),
          );
        }
      } catch (e, s) {
        Completer<dynamic>().completeError(e, s);
        emit(LoginFailureState(error: LocaleKeys.somethingWentWrong.tr()));
      }
    } else if (event is EmailLoginSubmitEvent) {
      emit(LoginLoadingState());

      try {
        final RepoResponse<UserLoginResponseModel> response =
            await servicelocator<AuthRepository>()
                .emailLogin(event.loginRequest.toJson());
        if (response.isSuccess) {
          await HiveUtilsAuth.login(
            response.response?.key ?? '',
            response.response?.refresh ?? '',
            uuid: response.response?.uuid ?? '',
            isDomainsSelected: response.response?.isDomainSelected ?? false,
          );
          await FirebaseConfig.setUserIdToAnalytics(
            event.loginRequest.username,
          );
          emit(LoginSuccessState());
        } else {
          emit(
            LoginFailureState(error: response.errorMessage),
          );
        }
      } catch (e, s) {
        Completer<dynamic>().completeError(e, s);
        emit(LoginFailureState(error: LocaleKeys.somethingWentWrong.tr()));
      }
    } else if (event is SplashInitCheckEvent) {
      await FirebaseConfig.setScreenToAnalytics('Splash');
      final bool isLoggedIn =
          await servicelocator<AuthRepository>().initialCheck();

      if (isLoggedIn) {
        emit(NavToHomeState());
      } else {
        emit(NavToLogInState());
      }
    }
  }
}
