import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/config/dependancy_injection/injection_container.dart';
import 'package:scad_mobile/src/features/authentication/data/models/login_model/response/logout_response.dart';
import 'package:scad_mobile/src/features/authentication/domain/repositories/auth_repository_imports.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

part 'logout_event.dart';
part 'logout_state.dart';

class LogoutBloc extends Bloc<LogoutEvent, LogoutState> {
  LogoutBloc() : super(const LogoutInitialState()) {
    on<LogoutEvent>((event, emit) {});
    on<OnLogoutEvent>(_onLogout);
  }

  /// for logout
  Future<void> _onLogout(
    OnLogoutEvent event,
    Emitter<LogoutState> emit,
  ) async {
    emit(const LogoutLoadingState());

    try {
      final RepoResponse<LogoutResponseModel> response =
          await servicelocator<AuthRepository>().logout();
      if (response.isSuccess) {
        emit(
          OnLogoutSuccessState(
            text: response.response?.detail,
            isSuccess: response.isSuccess,
          ),
        );
      } else {
        emit(LogoutErrorState(errorText: response.errorMessage));
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(LogoutErrorState(errorText:LocaleKeys.somethingWentWrong.tr()));
    }
  }
}
