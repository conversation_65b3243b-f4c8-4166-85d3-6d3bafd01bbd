part of 'authentication_bloc.dart';

/// auth state
abstract class AuthenticationState extends Equatable {
  /// constructor
  const AuthenticationState();

  @override
  List<Object> get props => [];
}

///initial state
class AuthenticationInitial extends AuthenticationState {
  AuthenticationInitial(){
    rnd = Random().nextInt(10000);
  }

  late final int rnd;

  @override
  List<Object> get props => [rnd];
}

///loading state
class LoginLoadingState extends AuthenticationState {
  LoginLoadingState(){
    rnd = Random().nextInt(10000);
  }

  late final int rnd;

  @override
  List<Object> get props => [rnd];
}

/// login submitted state
class LoginSubmittedState extends AuthenticationState {
  LoginSubmittedState(){
    rnd = Random().nextInt(10000);
  }

  late final int rnd;

  @override
  List<Object> get props => [rnd];
}

/// emit when login success
class LoginSuccessState extends AuthenticationState {
  LoginSuccessState(){
    rnd = Random().nextInt(10000);
  }

  late final int rnd;

  @override
  List<Object> get props => [rnd];
}

/// emit when failure
class LoginFailureState extends AuthenticationState {
  LoginFailureState({required this.error}) {
    rnd = Random().nextInt(10000);
  }

  late final int rnd;
  final String error;

  @override
  List<Object> get props => [error,rnd];
}

class NavToLogInState extends AuthenticationState {
  NavToLogInState(){
    rnd = Random().nextInt(10000);
  }

 late final int rnd;

  @override
  List<Object> get props => [rnd];
}

class NavToHomeState extends AuthenticationState {
  NavToHomeState(){
    rnd = Random().nextInt(10000);
  }

  late final int rnd;

  @override
  List<Object> get props => [rnd];
}

class SplashInitCheckState extends AuthenticationState {
  SplashInitCheckState(){
    rnd = Random().nextInt(10000);
  }

  late final int rnd;

  @override
  List<Object> get props => [rnd];
}
