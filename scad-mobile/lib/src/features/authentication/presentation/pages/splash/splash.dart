import 'dart:async';
import 'dart:io';

import 'package:audioplayers/audioplayers.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_udid/flutter_udid.dart';
import 'package:lottie/lottie.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/features/authentication/presentation/bloc/authentication_bloc.dart';
import 'package:scad_mobile/src/utils/app_utils/app_biometric_auth.dart';
import 'package:scad_mobile/src/utils/app_utils/app_permission.dart';
import 'package:scad_mobile/src/utils/app_utils/dynamic_link.dart';
import 'package:scad_mobile/src/utils/app_utils/secure_device_utils.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/animation_asset.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/audio_assets.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_api_cache.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_auth.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_persistent.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

@RoutePage()
class SplashPage extends StatefulWidget {
  const SplashPage({this.toAnimateLogo = true, super.key});

  final bool toAnimateLogo;

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  ValueNotifier<PackageInfo?> packageInfo = ValueNotifier(null);

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await HiveUtilsPersistent.initUuid();

      unawaited(
        PackageInfo.fromPlatform().then((value) {
          packageInfo.value = value;
        }),
      );

      if (await SecureDeviceUtils.isSecureDevice(context)) {
        textScaleFactor.value = HiveUtilsSettings.getTextSizeFactor();

        final bool hasInitLink = await DynamicLink.initListener();
        await HiveUtilsApiCache.clear();
        final Directory dir = Directory(
          '${(await getApplicationSupportDirectory()).path}/poi/',
        );
        if (dir.existsSync()) {
          dir.deleteSync(recursive: true);
        }

        if (mounted && !hasInitLink) {
          if (widget.toAnimateLogo) {
            unawaited(
              Future.delayed(const Duration(milliseconds: 200)).then((value) {
                if (context.routeData.name == SplashPageRoute.name) {
                  playAudio();
                }
              }),
            );
          }
          unawaited(
            Future.delayed(const Duration(seconds: 7)).then((value) async {
              if (mounted) {
                await AppPermissions.checkPermissions(
                  context,
                  [AppPermission.notification],
                );
                context
                    .read<AuthenticationBloc>()
                    .add(const SplashInitCheckEvent());
              }
            }),
          );
        }
      }
    });
  }

  Future<void> playAudio() async {
    final player = AudioPlayer();
    try {
      await player.play(AssetSource(AudioAsset.startupSound));
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<AuthenticationBloc, AuthenticationState>(
        listener: (context, state) async {
          if (state is NavToLogInState) {
            context.replaceRoute(const LoginPageRoute());
          } else if (state is NavToHomeState) {
            final bool b = await AppBiometricAuth.canRequestAuthentication();
            if (b) {
              if (HiveUtilsAuth.getToken().isNotEmpty) {
                _biometricAuthInit();
              } else {
                context.replaceRoute(const LoginPageRoute());
              }
            } else {
              if (HiveUtilsAuth.isDomainsSelected()) {
                AutoRouter.of(context).replaceAll([HomeNavigationRoute()]);
              } else {
                context.replaceRoute(const OnboardingScreenRoute());
              }
            }
          }
          // else if (state is NavToResetPasswordState) {
          //   context
          //       .read<ResetPasswordBloc>()
          //       .add(CheckPasswordSetEvent(token: state.token));
          //   context.replaceRoute(const ResetPasswordScreenRoute());
          // }
        },
        builder: (context, state) {
          return Container(
            width: double.maxFinite,
            height: double.maxFinite,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: <Color>[
                  AppColors.blueShade3,
                  AppColors.blueShade2,
                ],
              ),
            ),
            child: Stack(
              alignment: AlignmentDirectional.center,
              children: [
                // Positioned.fill(
                //   child: Image(
                //     height: MediaQuery.sizeOf(context).height,
                //     fit: BoxFit.fitWidth,
                //     image: const AssetImage(AppImages.bgSplash),
                //   ),
                // ),
                Positioned.fill(
                  child: Image(
                    height: MediaQuery.sizeOf(context).height * .25,
                    fit: BoxFit.fitWidth,
                    alignment: Alignment.bottomCenter,
                    image: const AssetImage(AppImages.bgSplashBottom),
                  ),
                ),
                Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Lottie.asset(
                        AnimationAsset.animationBayaanLogo,
                        repeat: false,
                      ),
                      // SvgPicture.asset(
                      //   'assets/images/bayan_new_logo.svg',
                      // ),
                      // if (!widget.toAnimateLogo) ...[
                      //   SvgPicture.asset(
                      //     AppImages.logoTamm,
                      //     height: 70,
                      //     width: 120,
                      //   ),
                      //   const SizedBox(height: 6),
                      // ] else
                      //   Image.asset(
                      //     AppImages.logoTammGif,
                      //     height: 110,
                      //     width: 120,
                      //   ),
                      // SvgPicture.asset(
                      //   AppImages.logoBayaanText,
                      //   width: 110,
                      //   height: 50,
                      //   // fit: BoxFit.fitHeight,
                      //   // colorFilter: ColorFilter.mode(
                      //   //   AppColors.white,
                      //   //   BlendMode.srcIn,
                      //   // ),
                      // ),
                    ],
                  ),
                ),
                Positioned(
                  bottom: 0,
                  child: ValueListenableBuilder(
                    valueListenable: packageInfo,
                    builder: (context, i, w) {
                      return Padding(
                        padding: const EdgeInsets.all(24),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              ' v${packageInfo.value?.version ?? ''} ${const String.fromEnvironment('Instance')}',
                              style: TextStyle(
                                color: AppColors.white,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  void _biometricAuthInit() {
    AppBiometricAuth.authorizeBiometric(
      onSuccessFn: () {
        if (HiveUtilsAuth.isDomainsSelected()) {
          AutoRouter.of(context).replaceAll([HomeNavigationRoute()]);
        } else {
          context.pushRoute(const OnboardingScreenRoute());
        }
        // context.replaceRoute(const HomeNavigationRoute());
      },
      onFailFn: //_onBiometricAuthDismissed
          () {
        context.replaceRoute(const LoginPageRoute());
      },
    );
  }
}
