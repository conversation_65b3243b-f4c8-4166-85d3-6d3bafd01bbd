import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:lottie/lottie.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/config/firebase_config/firebase_config.dart';
import 'package:scad_mobile/src/features/authentication/presentation/bloc/forgot_password/forgot_password_bloc.dart';
import 'package:scad_mobile/src/features/authentication/presentation/pages/login/widgets/email_forgot-password.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/animation_asset.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

@RoutePage()
class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  final TextEditingController email = TextEditingController();
  ValueNotifier<bool> emailValidation = ValueNotifier(false);
  ValueNotifier<bool> submit = ValueNotifier(false);

  @override
  void initState() {
    FirebaseConfig.setScreenToAnalytics('Forgot Password');
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Scaffold(
      // backgroundColor: Colors.red,
      body: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
    child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 60),
            child: Row(
              children: [
                InkWell(
                  onTap: () {
                    Navigator.of(context).maybePop();
                  },
                  borderRadius: BorderRadius.circular(8),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 14,
                      vertical: 4,
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        RotatedBox(
                          quarterTurns:
                              DeviceType.isDirectionRTL(context) ? 2 : 0,
                          child: SvgPicture.asset(
                            AppImages.icArrowLeft,
                            colorFilter: ColorFilter.mode(
                              AppColors.blue,
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                        // const SizedBox(width: 10),
                        // Text(
                        //   LocaleKeys.back.tr(),
                        //   style: AppTextStyles.s14w4cBlue,
                        //   textScaler: TextScaler.linear(textScaleFactor.value),
                        // ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            flex: 2,
            child: SingleChildScrollView(
              child: SizedBox(
                height: MediaQuery.sizeOf(context).height * 0.60,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // SvgPicture.asset(
                    //   isLightMode
                    //       ? AppImages.icPasswordLock
                    //       : AppImages.icPasswordLockDark,
                    //   width: 160,
                    //   height: 160,
                    // ),
                    if (isLightMode)
                        Lottie.asset(
                          AnimationAsset.animationPassword,
                          width: 160,
                          height: 160,
                        )
                      else
                        Lottie.asset(
                          AnimationAssetDark.animationPassword,
                          width: 160,
                          height: 160,
                        ),
                    const SizedBox(height: 20),
                    Text(
                      LocaleKeys.forgotPassword.tr(),
                      style: TextStyle(
                        color: isLightMode
                            ? AppColors.blackShade1
                            : AppColors.white,
                        fontWeight: FontWeight.w500,
                        fontSize: 20,
                      ),
                      textAlign: TextAlign.center,
                      textScaler: TextScaler.linear(textScaleFactor.value),
                    ),
                    const SizedBox(height: 20),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                      child: Text(
                        LocaleKeys.forgotPasswordDesc.tr(),
                        textAlign: TextAlign.center,
                        style: AppTextStyles.s14w4cblackShade4.copyWith(
                          color: isLightMode
                              ? AppColors.greyShade4
                              : AppColors.greyShade4,
                        ),
                        textScaler: TextScaler.linear(textScaleFactor.value),
                      ),
                    ),
                    const SizedBox(height: 40),
                  ],
                ),
              ),
            ),
          ),
          Container(
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(30),
                topRight: Radius.circular(30),
              ),
              color: isLightMode ? AppColors.white : AppColors.newmorphicDark,
            ),
            padding: const EdgeInsets.all(24),
            child: BlocConsumer<ForgotPasswordBloc, ForgotPasswordState>(
              listener: (BuildContext context, state) {
                if (state is OnForgotPasswordSuccessState) {
                  AppMessage.showOverlayNotification('', state.text ?? '');
                  if (state.isSuccess == true) {
                    context.replaceRoute(const LoginPageRoute());
                  }
                }
                if (state is ForgotPasswordErrorState) {
                  AppMessage.showOverlayNotification('', state.errorText ?? '');
                }
              },
              builder: (context, state) {
                final isLoading = state is ForgotPasswordLoadingState;
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    ForgotPasswordField(
                      controller: email,
                      validator: emailValidation,
                      label: LocaleKeys.emailId.tr(),
                    ),
                    const SizedBox(height: 10),
                    ValueListenableBuilder(
                      valueListenable: submit,
                      builder: (context, i, w) {
                        return ValueListenableBuilder(
                          valueListenable: emailValidation,
                          builder: (context, i, w) {
                            if (emailValidation.value && submit.value) {
                              return Text(
                                LocaleKeys.invalidEmail.tr(),
    textScaler: TextScaler.linear(textScaleFactor.value),
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: AppColors.red,
                                ),
                              );
                            }
                            return const SizedBox();
                          },
                        );
                      },
                    ),
                    const SizedBox(height: 20),
                    ElevatedButton(
                      onPressed: isLoading
                          ? null
                          : () {
                              submit.value = true;
                              if (!emailValidation.value) {
                                context.read<ForgotPasswordBloc>().add(
                                      OnForgotPasswordEvent(email: email.text),
                                    );
                              }
                            },
                      child: isLoading
                          ? SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                color: AppColors.white,
                              ),
                            )
                          : Text(
                              LocaleKeys.send.tr(),
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                color: AppColors.white,
                              ),
                              textScaler:
                                  TextScaler.linear(textScaleFactor.value),
                            ),
                    ),
                    const SizedBox(height: 10),
                  ],
                );
              },
            ),
          ),
        ],
      ),
      ),
    );
  }
}
