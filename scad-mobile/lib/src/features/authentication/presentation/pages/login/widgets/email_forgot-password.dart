import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class ForgotPasswordField extends StatefulWidget {
  const ForgotPasswordField({
    required this.label,
    required this.controller,
    required this.validator,
    super.key,
  });

  final String label;
  final TextEditingController controller;
  final ValueNotifier<bool> validator;

  @override
  State<ForgotPasswordField> createState() => _ForgotPasswordFieldState();
}

class _ForgotPasswordFieldState extends State<ForgotPasswordField> {
  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Text(
            widget.label,
            style: AppTextStyles.s14w4cBlueTitleText.copyWith(
              color: !isLightMode ? AppColors.greyShade4 : null,
            ),
            textScaler: TextScaler.linear(textScaleFactor.value),
          ),
        ),
        Container(
          height: 50,
          padding: const EdgeInsets.fromLTRB(8, 8, 0, 8),
          clipBehavior: Clip.antiAlias,
          decoration: ShapeDecoration(
            color: Colors.white,
            shape: RoundedRectangleBorder(
              side: const BorderSide(
                color: Color(0xFFD1D5DA),
              ),
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Expanded(
                child: TextFormField(
                  keyboardType: TextInputType.emailAddress,
                  controller: widget.controller,
                  onChanged: (value) {
                    if (validateEmail(value) == null) {
                      widget.validator.value = false;
                    } else {
                      widget.validator.value = true;
                    }
                  },
                  style: TextStyle(
                    color: AppColors.black,
                  ),
                  validator: validateEmail,
                  decoration: InputDecoration(
                    fillColor: AppColors.white,
                    filled: true,
                    hintText: LocaleKeys.enter.tr(),
                    hintStyle: const TextStyle(
                      color: Color(0xFFD1D5DA),
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                    ),
                    contentPadding: const EdgeInsets.all(8),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String? validateEmail(String? value) {
    const pattern = r"(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'"
        r'*+/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-'
        r'\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*'
        r'[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:(2(5[0-5]|[0-4]'
        r'[0-9])|1[0-9][0-9]|[1-9]?[0-9]))\.){3}(?:(2(5[0-5]|[0-4][0-9])|1[0-9]'
        r'[0-9]|[1-9]?[0-9])|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\'
        r'x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])';
    final regex = RegExp(pattern);

    return value!.isEmpty || !regex.hasMatch(value) ? 'invalid' : null;
  }
}
