import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class PasswordField extends StatefulWidget {
  const PasswordField({
    required this.label,
    required this.controller,
    required this.obscureText,
    required this.onIconPressed,
    super.key,
  });
  final String label;
  final TextEditingController controller;
  final bool obscureText;
  final VoidCallback onIconPressed;

  @override
  State<PasswordField> createState() => _PasswordFieldState();
}

class _PasswordFieldState extends State<PasswordField> {
  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Text(
            widget.label,
            style: AppTextStyles.s14w4cBlueTitleText.copyWith(
              color: !isLightMode ? AppColors.greyShade4 : null,
            ),
            textScaler: TextScaler.linear(textScaleFactor.value),
          ),
        ),
        Container(
          height: 50,
          padding: const EdgeInsets.fromLTRB(8, 8, 0, 8),
          clipBehavior: Clip.antiAlias,
          decoration: ShapeDecoration(
            color: Colors.white,
            shape: RoundedRectangleBorder(
              side: const BorderSide(
                color: Color(0xFFD1D5DA),
              ),
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Expanded(
                child: TextField(
                  obscureText: !widget.obscureText,
                  controller: widget.controller,
                  style: TextStyle(
                    color: AppColors.black,
                  ),
                  decoration: InputDecoration(
                    fillColor: AppColors.white,
                    filled: true,
                    hintText: LocaleKeys.enter.tr(),
                    hintStyle: const TextStyle(
                      color: Color(0xFFD1D5DA),
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                    ),
                    contentPadding: const EdgeInsets.all(8),
                  ),
                ),
              ),
              const SizedBox(width: 10),
              IconButton(
                padding: EdgeInsets.zero,
                onPressed: widget.onIconPressed,
                icon: SizedBox(
                  width: 20,
                  height: 20,
                  child: SvgPicture.asset(
                    widget.obscureText
                        ? AppImages.icEyeShow
                        : AppImages.icEyeHide,
                    colorFilter:
                        ColorFilter.mode(AppColors.greyShade1, BlendMode.srcIn),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
