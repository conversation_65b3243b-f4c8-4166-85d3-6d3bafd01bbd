import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';

class TextFieldLogin extends StatefulWidget {
  const TextFieldLogin(
      {required this.hintText,
      required this.controller,
      this.isEnabled = true,
      this.obscureText = false,
      super.key});

  final bool isEnabled;
  final String hintText;
  final bool obscureText;
  final TextEditingController controller;

  @override
  State<TextFieldLogin> createState() => _TextFieldLoginState();
}

class _TextFieldLoginState extends State<TextFieldLogin> {
  bool showText = false;

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      enabled: widget.isEnabled,
      controller: widget.controller,
      obscureText: widget.obscureText ? !showText : false,
      cursorColor: AppColors.white,
      style: TextStyle(
        fontSize: 16,
        color: AppColors.white,
      ),
      decoration: InputDecoration(
        contentPadding: const EdgeInsets.all(16),
        filled: true,
        suffixIcon: widget.obscureText
            ? InkWell(
                borderRadius: BorderRadius.circular(12),
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: SvgPicture.asset(
                    showText ? AppImages.icEyeShow1 : AppImages.icEyeHide1,
                    height: 20,
                    width: 20,
                  ),
                ),
                onTap: () {
                  setState(() {
                    showText = !showText;
                  });
                },
              )
            : null,
        fillColor: AppColors.whiteShade1,
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(color: AppColors.whiteShade1, width: 0),
          borderRadius: const BorderRadius.all(Radius.circular(12)),
        ),
        border: OutlineInputBorder(
          borderSide: BorderSide(color: AppColors.whiteShade1, width: 0),
          borderRadius: const BorderRadius.all(Radius.circular(12)),
        ),
        enabledBorder: OutlineInputBorder(
          borderSide: BorderSide(color: AppColors.whiteShade1, width: 0),
          borderRadius: const BorderRadius.all(Radius.circular(12)),
        ),
        hintText: widget.hintText,
        hintStyle: TextStyle(
          fontSize: 16,
          color: AppColors.white.withOpacity(0.4),
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }
}
