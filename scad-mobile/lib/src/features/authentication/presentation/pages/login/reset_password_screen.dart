import 'dart:async';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:lottie/lottie.dart';
import 'package:pinput/pinput.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/route_manager/route_imports.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/config/dependancy_injection/injection_container.dart';
import 'package:scad_mobile/src/config/firebase_config/firebase_config.dart';
import 'package:scad_mobile/src/features/authentication/presentation/bloc/reset_password/reset_password_bloc.dart';
import 'package:scad_mobile/src/features/authentication/presentation/pages/login/widgets/pasword_field.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/animation_asset.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_auth.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

@RoutePage()
class ResetPasswordScreen extends StatefulWidget {
  const ResetPasswordScreen({required this.token, super.key});

  final String token;

  @override
  State<ResetPasswordScreen> createState() => _ResetPasswordScreenState();
}

class _ResetPasswordScreenState extends State<ResetPasswordScreen> {
  final List<bool> visibility = [false, false];
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController reEnterPasswordController =
      TextEditingController();
  final pinController = TextEditingController();
  final focusNode = FocusNode();
  final formKey = GlobalKey<FormState>();

  Timer? timer;

  @override
  void initState() {
    super.initState();
    FirebaseConfig.setScreenToAnalytics('Reset Password');
    context
        .read<ResetPasswordBloc>()
        .add(CheckPasswordSetEvent(token: widget.token, isResendOtp: false));
  }

  void _startTimer() {
    int seconds = 120;
    timer = Timer.periodic(
      const Duration(seconds: 1),
      (Timer timer) {
        if (seconds == 0) {
          timer.cancel();
          context
              .read<ResetPasswordBloc>()
              .add(TimerUpdateEvent(isTimerRunning: timer.isActive));
        } else {
          seconds--;
        }
      },
    );
    context.read<ResetPasswordBloc>().add(
          TimerUpdateEvent(isTimerRunning: timer?.isActive ?? false),
        );
  }

  @override
  void dispose() {
    pinController.dispose();
    focusNode.dispose();
    timer?.cancel();

    super.dispose();
  }

  String? token = '';

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    final defaultPinTheme = PinTheme(
      width: 45,
      height: 56,
      textStyle: const TextStyle(
        fontSize: 18,
        color: Color.fromRGBO(30, 60, 87, 1),
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: AppColors.greyShade1),
        color: AppColors.white,
      ),
    );

    return Scaffold(
      body:GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: Padding(
          padding: EdgeInsets.only(top: MediaQuery.paddingOf(context).top),
          child: BlocConsumer<ResetPasswordBloc, ResetPasswordState>(
            listener: (BuildContext context, state) async {
              if (state is CheckPasswordSetErrorState) {
                AppMessage.showOverlayNotification(
                  '',
                  state.errorText ?? '',
                  duration: const Duration(seconds: 6),
                );
              } else if (state is CheckPasswordSetState) {
                token = state.token;
                setState(() {});
                if (state.isPasswordSet == false) {
                  _startTimer();
                  AppMessage.showOverlayNotification(
                    '',
                    LocaleKeys.otpSentForPasswordSetting.tr(),
                    duration: const Duration(seconds: 6),
                  );
                } else {
                  AppMessage.showOverlayNotification(
                    '',
                    LocaleKeys.passwordAlreadySet.tr(),
                    duration: const Duration(seconds: 6),
                  );
                  //todo splash screen is stuck
                  servicelocator<AppRouter>()
                      .replaceAll([SplashPageRoute(toAnimateLogo: false)]);
                  // context.replaceRoute(const LoginPageRoute());
                }
              } else if (state is OnResetPasswordSuccessState) {
                AppMessage.showOverlayNotification(
                  '',
                  state.text ?? '',
                  duration: const Duration(seconds: 6),
                );
                if (state.isSuccess == true) {
                  await HiveUtilsAuth.logout();
                  // context.replaceRoute(const LoginPageRoute());
                }
              } else if (state is ResetPasswordErrorState) {
                AppMessage.showOverlayNotification(
                  '',
                  state.errorText ?? '',
                  msgType: 'error',
                );
              }
            },
            builder: (context, state) {
              if (state is ResetPasswordInitialState) {
                return const Center(
                  child: CircularProgressIndicator(),
                );
              }
              return SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const SizedBox(height: 20),
                    // SvgPicture.asset(
                    //   isLightMode
                    //       ? AppImages.icPasswordLock
                    //       : AppImages.icPasswordLockDark,
                    //   width: 160,
                    //   height: 160,
                    // ),
                    if (isLightMode) Lottie.asset(
                      AnimationAsset.animationPassword,
                      width: 160,
                      height: 160,
                    ) else Lottie.asset(
                      AnimationAssetDark.animationPassword,
                      width: 160,
                      height: 160,
                    ),
                    const SizedBox(height: 50),
                    Text(
                      LocaleKeys.setUpPassword.tr(),
                      style: TextStyle(
                        color:
                            isLightMode ? AppColors.blackShade1 : AppColors.white,
                        fontWeight: FontWeight.w500,
                        fontSize: 20,
                      ),
                      textAlign: TextAlign.center,
                      textScaler: TextScaler.linear(textScaleFactor.value),
                    ),
                    const SizedBox(height: 20),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                      child: Text(
                        LocaleKeys.setUpPasswordDesc.tr(),
                        textAlign: TextAlign.center,
                        style: AppTextStyles.s14w4cblackShade4
                            .copyWith(color: AppColors.greyShade4),
                        textScaler: TextScaler.linear(textScaleFactor.value),
                      ),
                    ),
                    const SizedBox(height: 40),
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(30),
                          topRight: Radius.circular(30),
                        ),
                        color: isLightMode
                            ? AppColors.white
                            : AppColors.newmorphicDark,
                      ),
                      padding: const EdgeInsets.all(24),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          PasswordField(
                            label: LocaleKeys.password.tr(),
                            controller: passwordController,
                            obscureText: visibility.firstOrNull ?? false,
                            onIconPressed: () {
                              setState(() {
                              if ( visibility.firstOrNull != null ) {
                                    visibility.first = !visibility.first;
                              }
                              });
                            },
                          ),
                          const SizedBox(height: 20),
                          PasswordField(
                            label: LocaleKeys.reEnterPassword.tr(),
                            controller: reEnterPasswordController,
                            obscureText: visibility[1],
                            onIconPressed: () {
                              setState(() {
                                visibility[1] = !visibility[1];
                              });
                            },
                          ),
                          const SizedBox(height: 20),
                          Padding(
                            padding: const EdgeInsets.only(bottom: 8),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  '${LocaleKeys.enterPassKeyTo.tr()} ',
                                  style: AppTextStyles.s14w4cBlueTitleText.copyWith(
                                    color: !isLightMode
                                        ? AppColors.greyShade4
                                        : null,
                                  ),
                                  textScaler:
                                      TextScaler.linear(textScaleFactor.value),
                                ),
                                Text(
                                  token!.isEmpty
                                      ? ''
                                      : '"${JwtDecoder.decode(token!)['email']}"',
                                  style: AppTextStyles.s14w4cBlueTitleText.copyWith(
                                    fontWeight: FontWeight.w500,
                                    color: isLightMode
                                        ? AppColors.blueGreyShade1
                                        : AppColors.greyShade4,
                                  ),
                                  textScaler:
                                      TextScaler.linear(textScaleFactor.value),
                                ),
                              ],
                            ),
                          ),
                          Pinput(
                            controller: pinController,
                            focusNode: focusNode,
                            androidSmsAutofillMethod:
                                AndroidSmsAutofillMethod.smsUserConsentApi,
                            listenForMultipleSmsOnAndroid: true,
                            length: 6,
                            defaultPinTheme: defaultPinTheme,
                            separatorBuilder: (index) => const SizedBox(width: 8),
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            hapticFeedbackType: HapticFeedbackType.lightImpact,
                            focusedPinTheme: defaultPinTheme.copyWith(
                              decoration: defaultPinTheme.decoration!.copyWith(
                                borderRadius: BorderRadius.circular(10),
                                border: Border.all(
                                  color: AppColors.blueLight.withOpacity(0.6),
                                ),
                              ),
                            ),
                            submittedPinTheme: defaultPinTheme.copyWith(
                              decoration: defaultPinTheme.decoration!.copyWith(
                                color: AppColors.white,
                                borderRadius: BorderRadius.circular(10),
                                border: Border.all(
                                  color: AppColors.blueLight.withOpacity(0.6),
                                ),
                              ),
                            ),
                            errorPinTheme: defaultPinTheme.copyBorderWith(
                              border: Border.all(color: Colors.redAccent),
                            ),
                          ),
                          const SizedBox(height: 20),
                          BlocConsumer<ResetPasswordBloc, ResetPasswordState>(
                            listener: (context, state) {},
                            builder: (context, state) {
                              return Row(
                                children: [
                                  Material(
                                    color: Colors.transparent,
                                    child: InkWell(
                                      onTap: state is ResendOtpLoadingState ||
                                              (timer?.isActive ?? false)
                                          ? null
                                          : () {
                                              context
                                                  .read<ResetPasswordBloc>()
                                                  .add(
                                                    CheckPasswordSetEvent(
                                                      token: token ?? '',
                                                      isResendOtp: true,
                                                    ),
                                                  );
                                            },
                                      borderRadius: BorderRadius.circular(4),
                                      child: Padding(
                                        padding: const EdgeInsets.all(8),
                                        child: Text(
                                          LocaleKeys.resendOTP.tr(),
                                          style:
                                              AppTextStyles.s14w5cBlack.copyWith(
                                            color:
                                                state is ResendOtpLoadingState ||
                                                        (timer?.isActive ?? false)
                                                    ? AppColors.grey
                                                    : AppColors.blue,
                                          ),
                                          textScaler: TextScaler.linear(
                                              textScaleFactor.value),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              );
                            },
                          ),
                          const SizedBox(height: 20),
                          ElevatedButton(
                            onPressed: () {
                              context.read<ResetPasswordBloc>().add(
                                    OnResetPasswordEvent(
                                      newPassword: passwordController.text,
                                      confirmPassword:
                                          reEnterPasswordController.text,
                                      otp: pinController.text,
                                      token: token ?? '',
                                    ),
                                  );
                            },
                            child: Text(
                              LocaleKeys.create.tr(),
                              textScaler:
                                  TextScaler.linear(textScaleFactor.value),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
