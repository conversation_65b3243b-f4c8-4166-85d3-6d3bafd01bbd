import 'dart:async';
import 'dart:developer';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:lottie/lottie.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/config/app_config/secret.dart';
import 'package:scad_mobile/src/config/firebase_config/firebase_config.dart';
import 'package:scad_mobile/src/features/authentication/data/models/login_model/request/user_login_request.dart';
import 'package:scad_mobile/src/features/authentication/presentation/bloc/authentication_bloc.dart';
import 'package:scad_mobile/src/features/authentication/presentation/pages/login/widgets/textfield_login.dart';
import 'package:scad_mobile/src/utils/app_utils/app_biometric_auth.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/animation_asset.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_auth.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_persistent.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';
import 'package:uae_pass_flutter/uae_pass.dart';

@RoutePage()
class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final TextEditingController _textEditingControllerEmail =
      TextEditingController();
  final TextEditingController _textEditingControllerPassword =
      TextEditingController();

  ValueNotifier<bool> enableBiometricAuth = ValueNotifier(false);

  // ScrollController scrollController=ScrollController();

  @override
  void initState() {
    super.initState();
    FirebaseConfig.setScreenToAnalytics('Login');
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      enableBiometricAuth.value =
          await AppBiometricAuth.canRequestAuthentication();
 
    });
  }

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;

    return Scaffold(
      backgroundColor: Colors.transparent,
      body: _body(isLightMode),
    );
  }

  Widget _body(bool isLightMode) {
    final size = MediaQuery.sizeOf(context);

    return GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Stack(
        children: [
          Image.asset(
            AppImages.animatedLoginBg,
            height: size.height,
            width: size.width,
            fit: BoxFit.cover,
          ),
          SizedBox(
            height: size.height,
            child: ListView(
              padding: const EdgeInsets.only(left: 24, right: 24),
              children: [
                Container(
                  padding: const EdgeInsets.only(top: 55),
                  child: Row(
                    children: [
                      const Spacer(),
                      SvgPicture.asset(
                        AppImages.imgBayaanLogo,
                        height: 100,
                        width: 100,
                        fit: BoxFit.cover,
                      ),
                      const Spacer(),
                    ],
                  ),
                ),
                // Stack(
                //   clipBehavior: Clip.none,
                //   children: [
                //     Stack(
                //       alignment: AlignmentDirectional.center,
                //       children: [
                //         Center(
                //           child: SvgPicture.asset(
                //             AppImages.icLogo,
                //             height: 45,
                //           ),
                //         ),
                //         // Center(
                //         //   child: SvgPicture.asset(
                //         //     AppImages.bgCircles,
                //         //   ),
                //         // ),
                //         Center(
                //           child:isLightMode ?
                //            Lottie.asset(
                //             AnimationAsset.animationRipple,
                //           ): Lottie.asset(
                //             AnimationAssetDark.animationRipple,
                //           ) ,
                //         ),
                //       ],
                //     ),
                //     Positioned(
                //       left: 0,
                //       right: 0,
                //       bottom: -40,
                //       child: _textTilte(),
                //     ),
                //   ],
                // ),
                const SizedBox(height: 50,),
                _textTilte(),
                BlocConsumer<AuthenticationBloc, AuthenticationState>(
                  listener: (context, state) {
                    if (state is LoginSuccessState) {
                      if (HiveUtilsAuth.isDomainsSelected()) {
                        AutoRouter.of(context)
                            .replaceAll([HomeNavigationRoute()]);
                      } else {
                        // context.pushRoute(const OnboardingScreenRoute());
                        AutoRouter.of(context)
                            .replaceAll([const OnboardingScreenRoute()]);
                      }
                      // context.replaceRoute(const OnboardingScreenRoute());
                    }
                    if (state is LoginFailureState) {
                      AppMessage.showOverlayNotification('', state.error,
                          msgType: 'error');
                    }
                  },
                  builder: (context, state) {
                    final bool isLoading = state is LoginLoadingState;
                    return SafeArea(
                      child: IgnorePointer(
                        ignoring: isLoading,
                        child: Column(
                          children: [
                            const SizedBox(height: 50),
                            Padding(
                              padding: const EdgeInsets.symmetric(vertical: 15),
                              child: TextFieldLogin(
                                hintText: LocaleKeys.governmentEmail.tr(),
                                controller: _textEditingControllerEmail,
                                isEnabled: !isLoading,
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.symmetric(vertical: 15),
                              child: TextFieldLogin(
                                hintText: LocaleKeys.password.tr(),
                                controller: _textEditingControllerPassword,
                                isEnabled: !isLoading,
                                obscureText: true,
                              ),
                            ),
                            Align(
                              alignment: Alignment.centerRight,
                              child: TextButton(
                                style: TextButton.styleFrom(
                                  backgroundColor: Colors.transparent,
                                  side: BorderSide.none,
                                ),
                                onPressed: () {
                                  AutoRouter.of(context)
                                      .push(const ForgotPasswordScreenRoute());
                                },
                                child: Text(
                                  LocaleKeys.forgotPasswordQue.tr(),
                                  style: TextStyle(
                                    color: AppColors.white,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w400,
                                  ),
                                  textScaler:
                                      TextScaler.linear(textScaleFactor.value),
                                ),
                              ),
                            ),
                            const SizedBox(height: 20),
                            ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                textStyle: TextStyle(
                                  color: AppColors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w400,
                                ),
                                minimumSize: const Size.fromHeight(50),
                                maximumSize: const Size.fromHeight(50),
                                backgroundColor: AppColors.blueLightOld,
                              ),
                              onPressed: _onLogin,
                              child: isLoading
                                  ? SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                        color: AppColors.white,
                                      ),
                                    )
                                  : Text(
                                      LocaleKeys.login.tr(),
                                      style: TextStyle(
                                        color: AppColors.white,
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                      ),
                                      textScaler: TextScaler.linear(
                                          textScaleFactor.value),
                                    ),
                            ),
                            const SizedBox(height: 16),
                            _otherAreaText(),
                            _uaePassAndFace(),
                            const SizedBox(height: 20),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                // Text(
                                //   'Need help? ',
                                //   style: TextStyle(
                                //     color: AppColors.whiteShade2,
                                //     fontSize: 16,
                                //     fontWeight: FontWeight.w400,
                                //   ),
                                //   textScaler: TextScaler.linear(textScaleFactor.value),
                                // ),
                                TextButton(
                                  style: TextButton.styleFrom(
                                    backgroundColor: Colors.transparent,
                                    side: BorderSide.none,
                                  ),
                                  onPressed: () {
                                    context.pushRoute(
                                        const TermsAndConditionsScreenRoute());
                                  },
                                  child: Text(
                                    LocaleKeys.privacyPolicy.tr(),
                                    style: TextStyle(
                                      color: AppColors.white,
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                    textScaler:
                                        TextScaler.linear(textScaleFactor.value),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _textTilte() {
    return Text(
      textAlign: TextAlign.center,
      LocaleKeys.loginTagLine.tr(),
      style: TextStyle(
        color: AppColors.white,
        fontSize: 22,
        fontWeight: FontWeight.w500,
      ),
      textScaler: TextScaler.linear(textScaleFactor.value),
    );
  }

  Widget _otherAreaText() {
    return Row(
      children: [
        Expanded(child: Divider(color: AppColors.whiteShade1)),
        Flexible(
          flex: 2,
          fit: FlexFit.tight,
          child: Padding(
            padding: const EdgeInsets.all(10),
            child: Text(
              LocaleKeys.otherLoginOptions.tr(),
              textAlign: TextAlign.center,
              style: TextStyle(
                color: AppColors.whiteShade2,
                fontSize: 16,
                fontWeight: FontWeight.w400,
              ),
              textScaler: TextScaler.linear(textScaleFactor.value),
            ),
          ),
        ),
        Expanded(child: Divider(color: AppColors.whiteShade1)),
      ],
    );
  }

  Widget _uaePassAndFace() {
    return SizedBox(
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 10, bottom: 20),
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                textStyle: TextStyle(
                  color: AppColors.black,
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                ),
                minimumSize: const Size.fromHeight(50),
                maximumSize: const Size.fromHeight(50),
                backgroundColor: AppColors.white,
              ),
              onPressed: () async {
                try {
                  final UaePass uaePass = UaePass();
                  await uaePass.setUpEnvironment(
                    Secret.uaePassClientId,
                    Secret.uaePassClientSecret,
                    Secret.uaePassUrlScheme,
                    'urn:uae:digitalid:profile:general',
                    isProduction: Secret.isProduction,
                    redirectUri: '${Secret.uaePassUrlScheme}://scad.gov.ae',
                  );
                  unawaited(
                    uaePass.signIn().then((String accessToken) async {
                      context.read<AuthenticationBloc>().add(
                            UaePassLoginEvent(
                              accessToken: accessToken,
                            ),
                          );
                    }),
                  );
                } catch (e, s) {
                  Completer<dynamic>().completeError(e, s);
                  AppMessage.showOverlayNotification(
                    '',
                    LocaleKeys.somethingWentWrong.tr(),
                    msgType: 'error',
                  );
                }
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SvgPicture.asset(AppImages.icUaePassLogoDark),
                  const SizedBox(width: 10),
                  Text(
                    LocaleKeys.uaePassButtonText.tr(),
                    style: TextStyle(
                      color: AppColors.black,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                    textScaler: TextScaler.linear(textScaleFactor.value),
                  ),
                ],
              ),
            ),
          ),
          if (HiveUtilsAuth.getToken().isEmpty)
            const SizedBox(height: 84)
          else
            ValueListenableBuilder(
            valueListenable: enableBiometricAuth,
            builder: (context, _, __) {
              return InkWell(
                onTap: enableBiometricAuth.value ? _biometricAuthInit : null,
                child: Center(
                  child: Padding(
                    padding: const EdgeInsets.only(top: 10, bottom: 30),
                    child: SvgPicture.asset(
                      AppImages.icFaceId,
                      colorFilter: enableBiometricAuth.value
                          ? null
                          : ColorFilter.mode(AppColors.white, BlendMode.srcIn),
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  void _onLogin() {
    if (_textEditingControllerEmail.text.isEmpty) {
      AppMessage.showOverlayNotification(
        '',
        LocaleKeys.inputOfficialEmail.tr(),
        msgType: 'error',
      );
    } else if (_textEditingControllerPassword.text.isEmpty) {
      AppMessage.showOverlayNotification(
        '',
        LocaleKeys.inputPassword.tr(),
        msgType: 'error',
      );
    } else {
      context.read<AuthenticationBloc>().add(
            EmailLoginSubmitEvent(
              loginRequest: UserLoginRequestModel(
                username: _textEditingControllerEmail.text,
                password: _textEditingControllerPassword.text,
                deviceRegId: HiveUtilsPersistent.getDeviceToken(),
                uuid: HiveUtilsPersistent.getUuid(),
              ),
            ),
          );
    }
  }

  Future<void> _biometricAuthInit() async {
    await AppBiometricAuth.authorizeBiometric(
      onSuccessFn: () {
        if (HiveUtilsAuth.isDomainsSelected()) {
          AutoRouter.of(context).replaceAll([HomeNavigationRoute()]);
        } else {
          context.pushRoute(const OnboardingScreenRoute());
        }
        // context.replaceRoute(const HomeNavigationRoute());
        // context.pushRoute(const HomeNavigationRoute());
      },
      onFailFn: () {},
    );
  }
}
