part of 'auth_repository_imports.dart';

abstract class AuthRepository {
  Future<bool> initialCheck();

  Future<RepoResponse<UserLoginResponseModel>> emailLogin(
    Map<String, dynamic> body,
  );

  Future<RepoResponse<dynamic>> uaePassLogin();

  /// for checking the user already created password or not
  Future<RepoResponse<CheckPasswordSetModel>> checkPasswordSet({
    required String token,
  });

  /// for setting the password
  Future<RepoResponse<ResetPasswordModel>> resetPassword({
    required String newPassword,
    required String confirmPassword,
    required String otp,
    required String token,
  });

  Future<RepoResponse<ForgotPasswordResponseModel>> forgotPassword({
    required String email,
  });
  Future<RepoResponse<LogoutResponseModel>> logout();
}
