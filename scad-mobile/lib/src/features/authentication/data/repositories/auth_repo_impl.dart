import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:scad_mobile/demo/demo_api_responses.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/features/authentication/data/data_sources/api_end_points.dart';
import 'package:scad_mobile/src/features/authentication/data/models/login_model/request/refresh_token_request.dart';
import 'package:scad_mobile/src/features/authentication/data/models/login_model/response/check_password_set_model.dart';
import 'package:scad_mobile/src/features/authentication/data/models/login_model/response/forgot_password_response.dart';
import 'package:scad_mobile/src/features/authentication/data/models/login_model/response/logout_response.dart';
import 'package:scad_mobile/src/features/authentication/data/models/login_model/response/refresh_token_response.dart';
import 'package:scad_mobile/src/features/authentication/data/models/login_model/response/reset_password_model.dart';
import 'package:scad_mobile/src/features/authentication/data/models/login_model/response/user_login_response.dart';
import 'package:scad_mobile/src/features/authentication/domain/repositories/auth_repository_imports.dart';
import 'package:scad_mobile/src/services/http_service_impl.dart';
import 'package:scad_mobile/src/services/http_services.dart';
import 'package:scad_mobile/src/utils/app_utils/app_log.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_auth.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class AuthRepositoryImpl implements AuthRepository {
  AuthRepositoryImpl() {
    _httpService = HttpServiceRequests();
  }

  late HttpService _httpService;

  @override
  Future<RepoResponse<UserLoginResponseModel>> emailLogin(
    Map<String, dynamic> body,
  ) async {
    try {
      // demo user login
      if (body['username'] == demoUsername &&
          body['password'] == demoUserPassword) {
        isDemoMode = true;
        return RepoResponse<UserLoginResponseModel>.success(
          response: UserLoginResponseModel.fromJson(demoLoginResponse),
        );
      }

      final response = await _httpService.postJson(
        AuthEndPoints.userLogin,
        jsonPayloadMap: body,
        shouldAuthenticate: false,
      );
      if (response.isSuccess) {
        return RepoResponse<UserLoginResponseModel>.success(
          response: UserLoginResponseModel.fromJson(response.response),
        );
      } else {
        return RepoResponse<UserLoginResponseModel>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<UserLoginResponseModel>.error(
          errorMessage: LocaleKeys.somethingWentWrong.tr());
    }
  }

  @override
  Future<RepoResponse<dynamic>> uaePassLogin() async {
    return RepoResponse<dynamic>.success(response: null);
  }

  @override
  Future<bool> initialCheck() async {
    try {
      final String token = HiveUtilsAuth.getToken();

      if (token.isNotEmpty) {

        if(isDemoMode){
          return true;
        }

        final DateTime refreshTokenExpDateTime =
            DateTime.fromMillisecondsSinceEpoch(
                (HiveUtilsAuth.getRefreshTokenJWTDetails().exp! * 1000));

        if (DateTime.now().isAfter(refreshTokenExpDateTime)) {
          return false;
        } else {
          final RefreshTokenRequestModel requestModel =
              RefreshTokenRequestModel(
            refresh: HiveUtilsAuth.getRefreshToken(),
          );
          final response = await _httpService.postJson(
            AuthEndPoints.tokenRefresh,
            jsonPayloadMap: requestModel.toJson(),
            shouldAuthenticate: false,
            shouldIntercept: false,
          );
          if (response.isSuccess) {
            AppLog.info('Token refreshed');
            final RefreshTokenResponseModel res =
                RefreshTokenResponseModel.fromJson(response.response);
            final Map<String, dynamic> decodedToken = JwtDecoder.decode(
              res.access!,
            );
            final String uuid = decodedToken['uuid'] as String;
            await HiveUtilsAuth.login(
              res.access!,
              res.refresh!,
              uuid: uuid,
            );
          } else {
            AppLog.warning('Unable to refresh token');
            return false;
          }
        }
        return true;
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
    }
    return false;
  }

  @override
  Future<RepoResponse<CheckPasswordSetModel>> checkPasswordSet({
    required String token,
  }) async {
    try {
      final response = await _httpService.get(
        AuthEndPoints.passwordCheck,
        token: token,
      );
      if (response.isSuccess) {
        return RepoResponse<CheckPasswordSetModel>.success(
          response: CheckPasswordSetModel.fromJson(response.response),
        );
      } else {
        return RepoResponse<CheckPasswordSetModel>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<CheckPasswordSetModel>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<ResetPasswordModel>> resetPassword({
    required String newPassword,
    required String confirmPassword,
    required String otp,
    required String token,
  }) async {
    try {
      final response = await _httpService.postJson(
        AuthEndPoints.resetPassword,
        jsonPayloadMap: {
          'new_password': newPassword,
          'confirm_password': confirmPassword,
          'otp': otp,
        },
        token: token,
      );
      if (response.isSuccess) {
        return RepoResponse<ResetPasswordModel>.success(
          response: ResetPasswordModel.fromJson(response.response),
        );
      } else {
        return RepoResponse<ResetPasswordModel>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<ResetPasswordModel>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<ForgotPasswordResponseModel>> forgotPassword(
      {required String email}) async {
    try {
      final response = await _httpService.postJson(
        AuthEndPoints.forgotPassword,
        jsonPayloadMap: {
          'email': email,
        },
      );
      if (response.isSuccess) {
        return RepoResponse<ForgotPasswordResponseModel>.success(
          response: ForgotPasswordResponseModel.fromJson(response.response),
        );
      } else {
        return RepoResponse<ForgotPasswordResponseModel>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<ForgotPasswordResponseModel>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<LogoutResponseModel>> logout() async {
    try {
      final response = await _httpService.postJson(
        AuthEndPoints.logout,
        jsonPayloadMap: {
          'refresh': HiveUtilsAuth.getRefreshToken(),
        },
      );
      if (response.isSuccess) {
        return RepoResponse<LogoutResponseModel>.success(
          response: LogoutResponseModel.fromJson(response.response),
        );
      } else {
        return RepoResponse<LogoutResponseModel>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<LogoutResponseModel>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }
}
