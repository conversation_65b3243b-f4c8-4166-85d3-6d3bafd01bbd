import 'package:json_annotation/json_annotation.dart';

part 'refresh_token_request.g.dart';

@JsonSerializable()
class RefreshTokenRequestModel {
  RefreshTokenRequestModel({
    required this.refresh,
  });

  factory RefreshTokenRequestModel.fromJson(Map<String, dynamic> json) =>
      _$RefreshTokenRequestModelFromJson(json);
  final String refresh;

  Map<String, dynamic> toJson() => _$RefreshTokenRequestModelToJson(this);
}
