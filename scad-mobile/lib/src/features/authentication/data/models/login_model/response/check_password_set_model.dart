import 'package:json_annotation/json_annotation.dart';

part 'check_password_set_model.g.dart';

@JsonSerializable()
class CheckPasswordSetModel {
  CheckPasswordSetModel({
    this.status,
    this.passwordSet,
  });

  factory CheckPasswordSetModel.fromJson(Map<String, dynamic> json) =>
      _$CheckPasswordSetModelFromJson(json);

  @Json<PERSON>ey(name: 'status')
  String? status;
  @Json<PERSON>ey(name: 'password_set')
  bool? passwordSet;

  Map<String, dynamic> toJson() => _$CheckPasswordSetModelToJson(this);
}
