import 'package:json_annotation/json_annotation.dart';

part 'refresh_token_response.g.dart';

@JsonSerializable()
class RefreshTokenResponseModel {
  RefreshTokenResponseModel({
    this.access,
    this.refresh,
  });

  factory RefreshTokenResponseModel.fromJson(Map<String, dynamic> json) =>
      _$RefreshTokenResponseModelFromJson(json);
  String? access;
  String? refresh;

  Map<String, dynamic> toJson() => _$RefreshTokenResponseModelToJson(this);
}
