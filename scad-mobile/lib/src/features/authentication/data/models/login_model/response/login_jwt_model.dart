// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:json_annotation/json_annotation.dart';

part 'login_jwt_model.g.dart';

@JsonSerializable()
class LoginJWTModel {
  @Json<PERSON>ey(name: 'token_type')
  final String? tokenType;
  final int? exp;
  final int? iat;
  final String? jti;
  @J<PERSON><PERSON>ey(name: 'user_id')
  final int? userId;
  final String? iss;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'preferred_username')
  final String? preferredUsername;
  final List<String>? groups;
  final String? name;
  final String? uuid;

  LoginJWTModel({
    this.tokenType,
    this.exp,
    this.iat,
    this.jti,
    this.userId,
    this.iss,
    this.preferredUsername,
    this.groups,
    this.name,
    this.uuid,
  });

  factory LoginJWTModel.fromJson(Map<String, dynamic> json) =>
      _$LoginJWTModelFromJson(json);

  Map<String, dynamic> toJson() => _$LoginJWTModelToJson(this);
}
