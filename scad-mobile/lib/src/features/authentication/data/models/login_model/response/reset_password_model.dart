import 'package:json_annotation/json_annotation.dart';

part 'reset_password_model.g.dart';

@JsonSerializable()
class ResetPasswordModel {
  ResetPasswordModel({
    this.status,
    this.message,
  });

  factory ResetPasswordModel.fromJson(Map<String, dynamic> json) =>
      _$ResetPasswordModelFromJson(json);

  @JsonKey(name: 'status')
  String? status;
  @JsonKey(name: 'message')
  String? message;

  Map<String, dynamic> toJson() => _$ResetPasswordModelToJson(this);
}
