// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'login_jwt_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LoginJWTModel _$LoginJWTModelFromJson(Map<String, dynamic> json) =>
    LoginJWTModel(
      tokenType: json['token_type'] as String?,
      exp: json['exp'] as int?,
      iat: json['iat'] as int?,
      jti: json['jti'] as String?,
      userId: json['user_id'] as int?,
      iss: json['iss'] as String?,
      preferredUsername: json['preferred_username'] as String?,
      groups:
          (json['groups'] as List<dynamic>?)?.map((e) => e as String).toList(),
      name: json['name'] as String?,
      uuid: json['uuid'] as String?,
    );

Map<String, dynamic> _$LoginJWTModelToJson(LoginJWTModel instance) =>
    <String, dynamic>{
      'token_type': instance.tokenType,
      'exp': instance.exp,
      'iat': instance.iat,
      'jti': instance.jti,
      'user_id': instance.userId,
      'iss': instance.iss,
      'preferred_username': instance.preferredUsername,
      'groups': instance.groups,
      'name': instance.name,
      'uuid': instance.uuid,
    };
