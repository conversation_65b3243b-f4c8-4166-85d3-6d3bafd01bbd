import 'package:json_annotation/json_annotation.dart';

part 'user_login_response.g.dart';

@JsonSerializable()
class UserLoginResponseModel {
  UserLoginResponseModel({
    this.status,
    this.message,
    this.uuid,
    this.role,
    this.key,
    this.refresh,
    this.isDomainSelected,
  });

  factory UserLoginResponseModel.fromJson(Map<String, dynamic> json) =>
      _$UserLoginResponseModelFromJson(json);
  String? status;
  String? message;
  String? uuid;
  String? role;
  String? key;
  String? refresh;
  @JsonKey(name: 'domain_selected')
  bool? isDomainSelected;

  Map<String, dynamic> toJson() => _$UserLoginResponseModelToJson(this);
}
