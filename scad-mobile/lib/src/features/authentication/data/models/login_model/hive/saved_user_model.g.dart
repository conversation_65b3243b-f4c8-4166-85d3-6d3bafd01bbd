// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'saved_user_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class SavedUserModelAdapter extends TypeAdapter<SavedUserModel> {
  @override
  final int typeId = 1;

  @override
  SavedUserModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SavedUserModel(
      email: fields[0] as String,
      password: fields[1] as String,
    );
  }

  @override
  void write(BinaryWriter writer, SavedUserModel obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.email)
      ..writeByte(1)
      ..write(obj.password);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SavedUserModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
