import 'package:json_annotation/json_annotation.dart';

part 'user_login_request.g.dart';

@JsonSerializable()
class UserLoginRequestModel {
  UserLoginRequestModel({
    required this.username,
    required this.password,
    required this.deviceRegId,
    required this.uuid,
  });

  factory UserLoginRequestModel.fromJson(Map<String, dynamic> json) =>
      _$UserLoginRequestModelFromJson(json);
  final String username;
  final String password;
  @JsonKey(name: 'device_reg_id')
  final String deviceRegId;
  @JsonKey(name: 'device_uuid')
  final String uuid;

  Map<String, dynamic> toJson() => _$UserLoginRequestModelToJson(this);
}
