class InsightFilterModelResponse {
  InsightFilterModelResponse({
    this.dataList,
  });

  factory InsightFilterModelResponse.fromJson(Map<String, dynamic> json) =>
      InsightFilterModelResponse(
        dataList: (json['data'] as List?)
            ?.map(
              (dynamic e) =>
                  InsightFilterModel.fromJson(e as Map<String, dynamic>),
            )
            .toList(),
      );

  List<InsightFilterModel>? dataList;
}

class InsightFilterModel {
  InsightFilterModel({
    this.dbColumn,
    this.dbIndicatorId,
    this.viewName,
    this.yearlyData,
    this.data,
  });

  factory InsightFilterModel.fromJson(Map<String, dynamic> json) =>
      InsightFilterModel(
        dbColumn: json['dbColumn'] as String?,
        dbIndicatorId: json['dbIndicatorId'] as String?,
        viewName: json['viewName'] as String?,
        yearlyData: json['yearlyData'] == null
            ? null
            : YearlyData.fromJson(json['yearlyData'] as Map<String, dynamic>),
        data: (json['data'] as List?)
            ?.map((dynamic e) => e as Map<String, dynamic>)
            .toList(),
      );

  String? dbColumn;
  String? dbIndicatorId;
  String? viewName;
  YearlyData? yearlyData;
  List<Map<String, dynamic>>? data;

  Map<String, dynamic> toJson() => {
        'dbColumn': dbColumn,
        'dbIndicatorId': dbIndicatorId,
        'viewName': viewName,
        'yearlyData': yearlyData?.toJson(),
        'data': data?.map((e) => e).toList(),
      };
}

class YearlyData {
  YearlyData({
    this.viewName,
    this.dbColumn,
    this.dbIndicatorId,
    this.filterBy,
  });

  factory YearlyData.fromJson(Map<String, dynamic> json) => YearlyData(
        viewName: json['viewName'] as String?,
        dbColumn: json['dbColumn'] as String?,
        dbIndicatorId: json['dbIndicatorId'] as String?,
        filterBy: json['filterBy'] == null
            ? null
            : FilterBy.fromJson(json['filterBy'] as Map<String, dynamic>),
      );

  String? viewName;
  String? dbColumn;
  String? dbIndicatorId;
  FilterBy? filterBy;

  Map<String, dynamic> toJson() => {
        'viewName': viewName,
        'dbColumn': dbColumn,
        'dbIndicatorId': dbIndicatorId,
        'filterBy': filterBy?.toJson(),
      };
}

class FilterBy {
  FilterBy({
    this.channel,
    this.customerProductClass,
    this.spendCategorization,
    this.spendCategorySegment,
  });

  factory FilterBy.fromJson(Map<String, dynamic> json) => FilterBy(
        channel: json['CHANNEL'] as String?,
        customerProductClass: json['CUSTOMER_PRODUCT_CLASS'] as String?,
        spendCategorization: json['SPEND_CATEGORIZATION'] as String?,
        spendCategorySegment: json['SPEND_CATEGORY_SEGMENT'] as String?,
      );

  String? channel;
  String? customerProductClass;
  String? spendCategorization;
  String? spendCategorySegment;

  Map<String, dynamic> toJson() => {
        'CHANNEL': channel,
        'CUSTOMER_PRODUCT_CLASS': customerProductClass,
        'SPEND_CATEGORIZATION': spendCategorization,
        'SPEND_CATEGORY_SEGMENT': spendCategorySegment,
      };
}
