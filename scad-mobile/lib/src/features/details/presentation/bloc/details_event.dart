part of 'details_bloc.dart';

abstract class DetailsEvent extends Equatable {
  const DetailsEvent();

  @override
  List<Object> get props => [];
}

class ExpandIndicatorTileEvent extends DetailsEvent {
  const ExpandIndicatorTileEvent({
    required this.index,
    required this.isChild,
    this.parentIndex = -1,
  });

  final int index;
  final bool isChild;
  final int parentIndex;

  @override
  List<Object> get props => [index, isChild, parentIndex];
}

class DetailsLoadEvent extends DetailsEvent {
  const DetailsLoadEvent();

  @override
  List<Object> get props => [];
}

class PresentationTypeSelectionEvent extends DetailsEvent {
  const PresentationTypeSelectionEvent({required this.selectType});

  final String selectType;

  @override
  List<Object> get props => [selectType];
}

class PresentationTypeSelectionDoneEvent extends DetailsEvent {
  const PresentationTypeSelectionDoneEvent();

  @override
  List<Object> get props => [];
}
// class ToggleExpansionEvent extends DetailsEvent {
//   const ToggleExpansionEvent({required this.isExpanded});

//   final bool isExpanded;

//   @override
//   List<Object> get props => [isExpanded];
// }

/// the event used to create chat thread
class DetailsCreateChatThreadEvent extends DetailsEvent {
  const DetailsCreateChatThreadEvent({
    required this.domain,
    required this.domainId,
    required this.theme,
    required this.subTheme,
    required this.indicatorNodeId,
    required this.indicatorAppType,
    required this.inicatorContentType,
    required this.indicatorKey,
    required this.indicatorName,
  });

  final String domain;
  final int domainId;
  final String theme;
  final String subTheme;

  final String indicatorNodeId;
  final String indicatorAppType;
  final String inicatorContentType;
  final String indicatorKey;
  final String indicatorName;

  @override
  List<Object> get props => [
        domain,
        domainId,
        theme,
        subTheme,
        indicatorNodeId,
        indicatorAppType,
        inicatorContentType,
        indicatorKey,
        indicatorName,
      ];
}

class ToggleTermsAndConditionCheckEvent extends DetailsEvent {
  const ToggleTermsAndConditionCheckEvent({this.isAccepted});

  final bool? isAccepted;

  @override
  List<Object> get props => [isAccepted!];
}

/// event for compute bottom sheet
class ComputeBottomSheetValueEvent extends DetailsEvent {
  const ComputeBottomSheetValueEvent({required this.value});

  final String value;

  @override
  List<Object> get props => [value];
}

/// event for switching next and previous heading
class SwitchTitleEvent extends DetailsEvent {
  const SwitchTitleEvent({required this.value, required this.direction});

  final String value;
  final int direction; // 1 for next, -1 for previous

  @override
  List<Object> get props => [value, direction];
}

/// the event used to select first indicator from dropdown
class FirstIndicatorDropdownEvent extends DetailsEvent {
  const FirstIndicatorDropdownEvent({
    required this.indicatorList,
    required this.selectedIndicator,
  });

  final List<Properties>? indicatorList;
  final Properties? selectedIndicator;

  @override
  List<Object> get props =>
      [indicatorList ?? [], selectedIndicator ?? Properties()];
}

/// the event used to select from first inidcator list
class FirstIndicatorRadioButtonEvent extends DetailsEvent {
  const FirstIndicatorRadioButtonEvent({
    required this.selectedIndicator,
    required this.groupValue,
  });

  final Properties selectedIndicator;
  final String groupValue;

  @override
  List<Object> get props => [selectedIndicator, groupValue];
}

/// the event used to open and close first inidcator list
class ToggleSecondIndicatorExpansionEvent extends DetailsEvent {
  const ToggleSecondIndicatorExpansionEvent({
    required this.updatedList,
    required this.index,
  });

  final List<Properties> updatedList;
  final int index;

  @override
  List<Object> get props => [updatedList, index];
}

/// the event used to select from second inidcator list
class SecondIndicatorCheckBoxEvent extends DetailsEvent {
  const SecondIndicatorCheckBoxEvent({
    required this.index,
    required this.isSelected,
    required this.itemList,
  });

  final int index;
  final bool isSelected;
  final List<OptionList> itemList;

  @override
  List<Object> get props => [index, isSelected, itemList];
}

/// the event used to compute the data
class ComputeDataEvent extends DetailsEvent {
  const ComputeDataEvent({
    required this.dataMap,
    required this.selectedMeasurement,
    required this.nameOfIndicator,
  });

  final Map<String, dynamic> dataMap;
  final String? selectedMeasurement;
  final String? nameOfIndicator;

  @override
  List<Object> get props =>
      [dataMap, selectedMeasurement ?? '', nameOfIndicator ?? ''];
}

/// the event used to update the values of change indicators
class ChangeDriversValueUpdateEvent extends DetailsEvent {
  const ChangeDriversValueUpdateEvent({
    required this.indicatorDetails,
  });

  final IndicatorDetailsResponseHelper? indicatorDetails;

  @override
  List<Object> get props => [indicatorDetails!];
}

class ToResetChangeDriversEvent extends DetailsEvent {
  const ToResetChangeDriversEvent({
    required this.toReset,
    this.scadProjectionValue,
    this.indicatorVal,
  });

  final bool toReset;
  final bool? scadProjectionValue;
  final IndicatorDetailsResponseHelper? indicatorVal;

  @override
  List<Object> get props => [toReset, scadProjectionValue!, indicatorVal!];
}

/// the event used to select the measurement for compute data
class SelectMeasurementEvent extends DetailsEvent {
  const SelectMeasurementEvent({
    required this.selectedValue,
  });

  final String? selectedValue;

  @override
  List<Object> get props => [selectedValue ?? ''];
}

/// the event used for setting the default filter values
class DefaultFilterValueEvent extends DetailsEvent {
  const DefaultFilterValueEvent({
    required this.propertyList,
  });

  final List<Properties> propertyList;

  @override
  List<Object> get props => [propertyList];
}

/// the event used for opening and closing filer dropdown
class FilterDropdownExpansionEvent extends DetailsEvent {
  const FilterDropdownExpansionEvent({
    required this.index,
    required this.propertyList,
  });

  final int index;
  final List<Properties> propertyList;

  @override
  List<Object> get props => [index, propertyList];
}

/// the event used for filter checkbox selection
class FilterCheckboxEvent extends DetailsEvent {
  const FilterCheckboxEvent({
    required this.index,
    required this.childIndex,
    required this.propertiesList,
  });

  final int index;
  final int childIndex;
  final List<Properties> propertiesList;

  @override
  List<Object> get props => [index, childIndex, propertiesList];
}

/// the event used for filter radio selection
class FilterRadioEvent extends DetailsEvent {
  const FilterRadioEvent({
    required this.index,
    required this.childIndex,
    required this.propertiesList,
  });

  final int index;
  final int childIndex;
  final List<Properties> propertiesList;

  @override
  List<Object> get props => [index, childIndex, propertiesList];
}

/// the event for applying filter
class FilterApplyEvent extends DetailsEvent {
  const FilterApplyEvent({
    required this.propertyList,
    required this.originlIndicatorDetails,
    this.selectedVisualization,
    this.insightToFilterData,
    this.isForeCast,
  });

  final List<Properties> propertyList;
  final IndicatorDetailsResponse originlIndicatorDetails;
  final Visualizations? selectedVisualization;
  final List<Map<String, dynamic>>? insightToFilterData;
  final bool? isForeCast;

  @override
  List<Object> get props => [
        propertyList,
        originlIndicatorDetails,
        selectedVisualization ?? Visualizations(),
        insightToFilterData ?? [],
        isForeCast ?? false,
      ];
}

/// the event used to update chart presentation type
class CompareIndicatorEvent extends DetailsEvent {
  const CompareIndicatorEvent({
    required this.payload,
    this.domainName,
    this.indicatorDetails,
  });

  final Map<String, dynamic> payload;
  final String? domainName;
  final IndicatorDetailsResponseHelper? indicatorDetails;

  @override
  List<Object> get props => [];
}

class DetailsPageFilterResetEvent extends DetailsEvent {
  const DetailsPageFilterResetEvent({
    this.indicatorDetails,
  });

  final IndicatorDetailsResponseHelper? indicatorDetails;

  @override
  List<Object> get props => [];
}

/// the event used to change data frequency
class ChangeDataFrequencyEvent extends DetailsEvent {
  const ChangeDataFrequencyEvent({
    required this.index,
    required this.frequencyList,
  });

  final int index;
  final List<FrequencySelectorModel> frequencyList;

  @override
  List<Object> get props => [index, frequencyList];
}

/// the event used to change data frequency
class ChangeDataFrequencyApplyEvent extends DetailsEvent {
  const ChangeDataFrequencyApplyEvent({
    required this.selectedFrequency,
    required this.inidicatorData,
    required this.freqList,
    this.filteredDataList,
    required this.forecastSeriesList,
    this.isFromMonthly,
  });

  final String selectedFrequency;
  final IndicatorDetailsResponse inidicatorData;
  final List<FrequencySelectorModel> freqList;
  final List<List<Map<String, dynamic>>>? filteredDataList;
  final List<List<Map<String, dynamic>>>? forecastSeriesList;
  final bool? isFromMonthly;

  @override
  List<Object> get props => [
        selectedFrequency,
        inidicatorData,
        filteredDataList ?? [],
        forecastSeriesList ?? [],
        isFromMonthly ?? false,
      ];
}

/// the event used to update forecast visibility switch in details page
class ForecastVisibilityUpdateEvent extends DetailsEvent {
  const ForecastVisibilityUpdateEvent({
    required this.isVisibilityOn,
  });

  final bool isVisibilityOn;

  @override
  List<Object> get props => [isVisibilityOn];
}

/// the event used to change MONTH/YEAR data filter
class MonthYearFilterEvent extends DetailsEvent {
  const MonthYearFilterEvent({
    required this.indicatorData,
    required this.options,
    required this.index,
    this.filteredDataList,
    this.isFromVisulization,
    this.resetPeriodFilter = false
  });

  final IndicatorDetailsResponse indicatorData;
  final List<Options> options;
  final int index;
  final List<List<Map<String, dynamic>>>? filteredDataList;
  final bool? isFromVisulization;
  final bool resetPeriodFilter;

  @override
  List<Object> get props => [
        indicatorData,
        options,
        index,
        filteredDataList ?? [],
        isFromVisulization ?? false,
        resetPeriodFilter,
      ];
}

/// the event used to generate meta data list
class GenerateMetaDataEvent extends DetailsEvent {
  const GenerateMetaDataEvent({
    required this.metaData,
    required this.isOpened,
  });

  final List<MetaData?> metaData;
  final bool isOpened;

  @override
  List<Object> get props => [metaData, isOpened];
}

/// the event used to send selected indicator id from search
class SelectedIndicatorIdFromSearchEvent extends DetailsEvent {
  const SelectedIndicatorIdFromSearchEvent({
    required this.indicatorId,
  });

  final String indicatorId;

  @override
  List<Object> get props => [indicatorId];
}

/// event for selecting visualization if type is insight-discovery
class SelectVisualizationEvent extends DetailsEvent {
  const SelectVisualizationEvent({required this.selectedVisualization});

  final Visualizations? selectedVisualization;

  @override
  List<Object> get props => [selectedVisualization ?? Visualizations()];
}

/// event for update legends
class UpdateLegendEvent extends DetailsEvent {
  const UpdateLegendEvent({this.seriesList, this.filterKey, this.filterList});

  final List<List<Map<String, dynamic>>>? seriesList;
  final List<List<Map<String, dynamic>>>? filterList;
  final String? filterKey;

  @override
  List<Object> get props =>
      [seriesList ?? [], filterKey ?? '', filterList ?? []];
}

/// event for updating solid series
class SolidStateUpdateEvent extends DetailsEvent {
  const SolidStateUpdateEvent(
      {this.seriesList,
      this.forecastSeries,
      this.isCompareTrue,
      required this.selectedFrequencyForFilter});

  final List<List<Map<String, dynamic>>>? seriesList;
  final List<List<Map<String, dynamic>>>? forecastSeries;
  final bool? isCompareTrue;
  final String selectedFrequencyForFilter;
  @override
  List<Object> get props => [
        seriesList ?? [],
        forecastSeries ?? [],
        isCompareTrue ?? false,
      ];
}

class BackClearEvent extends DetailsEvent {
  @override
  List<Object> get props => [];
}
