part of 'details_bloc.dart';

abstract class DetailsState extends Equatable {
  const DetailsState();

  @override
  List<Object> get props => [];
}

class DetailsInitial extends DetailsState {}

// this is added to fix the issue when switching period filter
// in this state, the ChangeDataFrequencyApplyEvent event will be called
// https://app.clickup.com/t/86cvdxfxb
class PeriodFilterApplyAgainState extends DetailsState {
  PeriodFilterApplyAgainState() {
    rnd = Random().nextInt(10000);
  }

  late final int rnd;

  @override
  List<Object> get props => [rnd];
}

class CompareIndicatorExpandedState extends DetailsState {
  const CompareIndicatorExpandedState({
    required this.expandedIndex,
    required this.isChildExpanded,
  });

  final int expandedIndex;
  final bool isChildExpanded;

  @override
  List<Object> get props => [expandedIndex, isChildExpanded];
}

class PresentationTypeSelectionState extends DetailsState {
  const PresentationTypeSelectionState({required this.selectedType});

  final String selectedType;

  @override
  List<Object> get props => [selectedType];
}

class PresentationTypeSelectionDoneState extends DetailsState {
  const PresentationTypeSelectionDoneState();
  @override
  List<Object> get props => [];
}

// class CompareIndicatorToggleExpansionState extends DetailsState {
//   const CompareIndicatorToggleExpansionState({required this.isExpanded});

//   final bool isExpanded;

//   @override
//   List<Object> get props => [isExpanded];
// }

class DetailsCreateChatThreadSuccessState extends DetailsState {
  const DetailsCreateChatThreadSuccessState({
    required this.chatThread,
    required this.domain,
    required this.domainId,
    required this.theme,
    required this.subTheme,
    required this.indicatorNodeId,
    required this.indicatorAppType,
    required this.inicatorContentType,
    required this.indicatorKey,
    required this.indicatorName,
    this.chatDisabled,
    this.chatThreadClosed,
  });

  final ChatThreadCreateModel? chatThread;
  final String? domain;
  final int? domainId;
  final String? theme;
  final String? subTheme;
  final String? indicatorNodeId;
  final String? indicatorAppType;
  final String? inicatorContentType;
  final String? indicatorKey;
  final String? indicatorName;
  final bool? chatDisabled;
  final bool? chatThreadClosed;

  @override
  List<Object> get props => [chatThread ?? ChatThreadCreateModel()];
}

class DetailsCreateChatThreadErrorState extends DetailsState {
  const DetailsCreateChatThreadErrorState({required this.errorText});

  final String? errorText;

  @override
  List<Object> get props => [errorText ?? ''];
}

class ToggleTermsAndConditionCheckState extends DetailsState {
  const ToggleTermsAndConditionCheckState({required this.isAccepted});

  final bool? isAccepted;

  @override
  List<Object> get props => [isAccepted ?? ''];
}

/// state for compute bottom sheet
class ComputeBottomSheetValueState extends DetailsState {
  const ComputeBottomSheetValueState({required this.value});

  final String value;

  @override
  List<Object> get props => [value];
}

/// state for compute page title
class ComputePageTitleState extends DetailsState {
  const ComputePageTitleState({required this.value});

  final String value;

  @override
  List<Object> get props => [value];
}

/// the state used to select first indicator from dropdown
class FirstIndicatorDropdownState extends DetailsState {
  const FirstIndicatorDropdownState({
    required this.selectedIndicator,
    required this.optionList,
  });

  final Properties? selectedIndicator;
  final List<String> optionList;

  @override
  List<Object> get props => [
        selectedIndicator ?? Properties(),
        optionList,
      ];
}

/// the state used to select from first inidcator list
class FirstIndicatorRadioButtonState extends DetailsState {
  const FirstIndicatorRadioButtonState({
    required this.selectedIndicator,
    required this.groupValue,
  });

  final Properties selectedIndicator;
  final String groupValue;

  @override
  List<Object> get props => [selectedIndicator, groupValue];
}

/// the state used to enable the expansionTile of second indicator list
class EnableCheckboxTilesState extends DetailsState {
  const EnableCheckboxTilesState({
    required this.isCheckboxEnabled,
    required this.selectedRadioValue,
  });

  final bool isCheckboxEnabled;
  final String selectedRadioValue;

  @override
  List<Object> get props => [isCheckboxEnabled, selectedRadioValue];
}

/// the state used to get the second inidcator list
class ToggleSecondIndicatorExpansionState extends DetailsState {
  const ToggleSecondIndicatorExpansionState({required this.updatedList});

  final List<Properties> updatedList;

  @override
  List<Object> get props => [updatedList];
}

/// the state used to select from second inidcator list
class SecondIndicatorCheckBoxState extends DetailsState {
  const SecondIndicatorCheckBoxState({
    required this.selectedItems,
  });

  final List<OptionList> selectedItems;

  @override
  List<Object> get props => [selectedItems];
}

/// the state used for showing the load while computing
class ComputeDataLoadingState extends DetailsState {
  @override
  List<Object> get props => [];
}

/// the state used for compute data success
class ComputeDataSuccessState extends DetailsState {
  const ComputeDataSuccessState({
    required this.isLoading,
    required this.indicatorDetails,
    required this.nameOfIndicator,
    required this.selectedMeasurement,
  });

  final bool isLoading;
  final IndicatorDetailsResponse indicatorDetails;
  final String? nameOfIndicator;
  final String? selectedMeasurement;

  @override
  List<Object> get props => [
        isLoading,
        indicatorDetails,
        nameOfIndicator ?? '',
        selectedMeasurement ?? '',
      ];
}

/// the state used for showing the load while computing
class ComputeDataErrorState extends DetailsState {
  const ComputeDataErrorState({
    required this.isLoading,
    required this.errorMessage,
  });

  final bool isLoading;
  final String errorMessage;
  @override
  List<Object> get props => [isLoading, errorMessage];
}

/// the state used to get updated driver values
class ChangeDriversValueUpdateState extends DetailsState {
  const ChangeDriversValueUpdateState({
    required this.indicatorDetails,
  });

  final IndicatorDetailsResponseHelper? indicatorDetails;

  @override
  List<Object> get props => [indicatorDetails!];
}

class ResetChangeDriversState extends DetailsState {
  const ResetChangeDriversState({
    required this.toReset,
    this.scadProjectionValue,
    this.indicatorVal,
  });

  final bool toReset;
  final bool? scadProjectionValue;
  final IndicatorDetailsResponseHelper? indicatorVal;

  @override
  List<Object> get props => [toReset, scadProjectionValue!,indicatorVal!];
}

/// the state used for compare indicator success
class CompareIndicatorSuccessState extends DetailsState {
  const CompareIndicatorSuccessState({
    required this.indicatorDetails,
    required this.initialIndicatorDetails,
    this.domainName,
  });

  final IndicatorDetailsResponse indicatorDetails;
  final String? domainName;
  final IndicatorDetailsResponseHelper? initialIndicatorDetails;

  @override
  List<Object> get props => [indicatorDetails, initialIndicatorDetails??IndicatorDetailsResponseHelper(IndicatorDetailsResponse())];
}

/// the state used for compare indicator loading
class CompareIndicatorLoadingState extends DetailsState {}

/// the state used for compare indicator error
class CompareIndicatorFailureState extends DetailsState {
  const CompareIndicatorFailureState({required this.errorText});

  final String errorText;

  @override
  List<Object> get props => [errorText];
}

class DetailsPageFilterResetState extends DetailsState {
  const DetailsPageFilterResetState({required this.indicatorDetails});

  final IndicatorDetailsResponseHelper? indicatorDetails;

  @override
  List<Object> get props => [indicatorDetails!];
}

/// the event used to select the measurement for compute data
class SelectMeasurementState extends DetailsState {
  const SelectMeasurementState({
    required this.selectedValue,
  });

  final String? selectedValue;

  @override
  List<Object> get props => [selectedValue ?? ''];
}

/// the state used for opening and closing filer dropdown
class FilterDropdownExpansionSatte extends DetailsState {
  const FilterDropdownExpansionSatte({required this.updatedList});

  final List<Properties> updatedList;

  @override
  List<Object> get props => [updatedList];
}

/// the state used for filter checkbox/radio selection
class FilterCheckboxState extends DetailsState {
  const FilterCheckboxState({
    required this.propertiesList,
  });

  final List<Properties> propertiesList;

  @override
  List<Object> get props => [propertiesList];
}

/// the state used for loading the filter apply
class FilterApplyLoadingState extends DetailsState {
  const FilterApplyLoadingState({required this.isLoading});

  final bool isLoading;

  @override
  List<Object> get props => [isLoading];
}

/// the state used for applying the filters
class FilterApplyState extends DetailsState {
  const FilterApplyState({
    this.filteredData,
    this.filteredDataList,
    this.dateRepresentation,
    this.isForecast,
  });

  final List<Map<String, dynamic>>? filteredData;
  final List<List<Map<String, dynamic>>>? filteredDataList;
  final String? dateRepresentation;
  final bool? isForecast;

  @override
  List<Object> get props => [
        filteredData ?? [],
        filteredDataList ?? [],
        dateRepresentation ?? '',
        isForecast ?? false,
      ];
}

/// the state used for selecting the data frequency
class ChangeDataFrequencyState extends DetailsState {
  const ChangeDataFrequencyState({required this.frequencyList});

  final List<FrequencySelectorModel> frequencyList;

  @override
  List<Object> get props => [frequencyList];
}

/// the state used for applying the data frequency
class DataFrequencyApplyState extends DetailsState {
  const DataFrequencyApplyState({
    required this.filteredData,
    required this.selectedFrequency,
    required this.inidicatorData,
    required this.freqList,
    this.isFromMonthly,
    this.forecastData,
  });

  final List<List<Map<String, dynamic>>> filteredData;
  final List<List<Map<String, dynamic>>>? forecastData;
  final String selectedFrequency;
  final IndicatorDetailsResponse inidicatorData;
  final List<FrequencySelectorModel> freqList;
  final bool? isFromMonthly;

  @override
  List<Object> get props => [
        forecastData ?? [],
        filteredData,
        selectedFrequency,
        inidicatorData,
        freqList,
        isFromMonthly ?? false,
      ];
}

/// only to use if UI is not updating
class EmptyState extends DetailsState {
  @override
  List<Object> get props => [];
}

class ForecastVisibilityUpdateState extends DetailsState {
  const ForecastVisibilityUpdateState({
    required this.isVisibilityOn,
  });

  final bool isVisibilityOn;

  @override
  List<Object> get props => [isVisibilityOn];
}

/// the state used to change MONTH/YEAR data filter
class MonthYearFilterState extends DetailsState {
  const MonthYearFilterState({
    required this.options,
    required this.filteredData,
    required this.index,
    this.isFromVisulization,
  });

  final List<Options> options;
  final List<List<Map<String, dynamic>>> filteredData;
  final int index;
  final bool? isFromVisulization;

  @override
  List<Object> get props => [
        options,
        filteredData,
        index,
        isFromVisulization ?? false,
      ];
}

/// the state used to generate meta data
class GenerateMetaDataState extends DetailsState {
  const GenerateMetaDataState({
    required this.metaData,
    required this.isOpened,
  });

  final List<MetaData?> metaData;
  final bool isOpened;

  @override
  List<Object> get props => [metaData, isOpened];
}

/// the state used to emit Indicator Id from search results in Global Search
class SelectedIndicatorIdFromSearchState extends DetailsState {
  const SelectedIndicatorIdFromSearchState({
    required this.indicatorId,
  });

  final String indicatorId;

  @override
  List<Object> get props => [indicatorId];
}

/// state for selecting visualization if type is insight-discovery
class SelectVisualizationState extends DetailsState {
  const SelectVisualizationState({
    required this.selectedVisualization,
    required this.fullData,
  });

  final Visualizations? selectedVisualization;
  final List<InsightFilterModel>? fullData;

  @override
  List<Object> get props =>
      [selectedVisualization ?? Visualizations(), fullData ?? []];
}

/// state for insight discovery full data filter loading/error
class InsightDiscoveryFullDataLoadState extends DetailsState {
  const InsightDiscoveryFullDataLoadState({
    this.isLoading,
    this.errorText,
  });

  final bool? isLoading;
  final String? errorText;

  @override
  List<Object> get props => [isLoading ?? false, errorText ?? ''];
}

/// state for updating legend
class UpdateLegendState extends DetailsState {
  const UpdateLegendState({this.seriesList, this.filterKey, this.filterList});
  final List<List<Map<String, dynamic>>>? filterList;
  final List<List<Map<String, dynamic>>>? seriesList;
  final String? filterKey;

  @override
  List<Object> get props =>
      [seriesList ?? [], filterKey ?? '', this.filterList ?? []];
}

/// state for updating solid state
class SolidStateUpdateState extends DetailsState {
  const SolidStateUpdateState(
      {this.seriesList,
      this.forecastSeriesList,
      this.isCompareTrue,
      required this.selectedFrequencyForFilter});

  final List<List<Map<String, dynamic>>>? seriesList;
  final List<List<Map<String, dynamic>>>? forecastSeriesList;
  final bool? isCompareTrue;
  final String selectedFrequencyForFilter;
  @override
  List<Object> get props => [
        seriesList ?? [],
        forecastSeriesList ?? [],
        isCompareTrue ?? false,
      ];
}

class BackClearState extends DetailsState {
  @override
  List<Object> get props => [];
}
