// import 'package:auto_route/auto_route.dart';
// import 'package:easy_localization/easy_localization.dart';
// import 'package:flutter/gestures.dart';
// import 'package:flutter/material.dart';
// import 'package:scad_mobile/route_manager/route_imports.gr.dart';
// import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
// import 'package:scad_mobile/translations/locale_keys.g.dart';
//
// class DetailsPageTermsAndConditions extends StatelessWidget {
//   DetailsPageTermsAndConditions(
//       {required this.onChanged, required this.isChecked, super.key});
//
//   final void Function(bool) onChanged;
//   ValueNotifier<bool> isChecked = ValueNotifier(false);
//
//   @override
//   Widget build(BuildContext context) {
//     return Row(
//       children: [
//         ValueListenableBuilder(
//           valueListenable: isChecked,
//           builder: (context, accepted, _) {
//             return SizedBox(
//               width: 16,
//               height: 16,
//               child: Checkbox(
//                 checkColor: AppColors.white,
//                 activeColor: AppColors.blueLight,
//                 side: BorderSide(color: AppColors.greyShade1),
//                 value: accepted,
//                 onChanged: (value) {
//                   isChecked.value = value!;
//                   onChanged(value);
//                 },
//               ),
//             );
//           },
//         ),
//         const SizedBox(width: 10),
//         Text.rich(
//           TextSpan(
//             children: [
//               TextSpan(
//                 text: LocaleKeys.iAgreeTo.tr(),
//                 style: TextStyle(
//                   color: AppColors.greyShade4,
//                   fontSize: 10,
//                   fontWeight: FontWeight.w500,
//                 ),
//               ),
//               TextSpan(
//                 text: ' ${LocaleKeys.termsAndConditions.tr()}',
//                 recognizer: TapGestureRecognizer()
//                   ..onTap = () => AutoRouter.of(context)
//                       .push(const TermsAndConditionsScreenRoute()),
//                 style: TextStyle(
//                   color: AppColors.selectedChipBlue,
//                   fontSize: 10,
//                   fontWeight: FontWeight.w500,
//                 ),
//               ),
//             ],
//           ),
//           textAlign: TextAlign.center,
//         ),
//       ],
//     );
//   }
// }
