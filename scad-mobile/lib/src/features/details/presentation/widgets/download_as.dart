import 'dart:async';
import 'dart:io';
import 'dart:ui' as ui;

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_inset_box_shadow/flutter_inset_box_shadow.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_svg/svg.dart';
import 'package:image/image.dart' as img;
import 'package:lottie/lottie.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/common/widgets/charts/column_chart.dart';
import 'package:scad_mobile/src/common/widgets/charts/spline_chart.dart';
import 'package:scad_mobile/src/common/widgets/charts/treemap_chart.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_response_helper.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/features/details/presentation/bloc/details_bloc.dart';
import 'package:scad_mobile/src/features/details/presentation/widgets/download_as_chip_widget.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/app_utils/app_permission.dart';
import 'package:scad_mobile/src/utils/app_utils/downloadHelper.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/animation_asset.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';
import 'package:screenshot/screenshot.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart';
import 'package:syncfusion_flutter_xlsio/xlsio.dart' hide Border, Column, Row;

class DownloadAs extends StatefulWidget {
  const DownloadAs({
    required this.indicatorDetails,
    required this.selectedFrequencyForFilterDownload,
    required this.forecastVisibility,
    required this.enableDownload,
    required this.lendendListCompare,
    super.key,
    this.whatIfScenarioItemIndex,
    this.screenshotWidget,
    this.isComparisonActive = false,
    this.chartType = '',
    this.comparedIndicatorName,
    this.chartData,
    this.forecastChartDataDownload,
    this.selectedVisualization,
    this.computedIndicatorName,
  });

  final IndicatorDetailsResponseHelper? indicatorDetails;
  final int? whatIfScenarioItemIndex;
  final Widget? screenshotWidget;
  final bool isComparisonActive;
  final bool forecastVisibility;
  final String chartType;
  final String? comparedIndicatorName;
  final List<List<Map<String, dynamic>>>? chartData;
  final List<List<Map<String, dynamic>>>? forecastChartDataDownload;
  final Visualizations? selectedVisualization;
  final String? computedIndicatorName;
  final String selectedFrequencyForFilterDownload;
  final bool enableDownload;
  final List<String> lendendListCompare;

  @override
  State<DownloadAs> createState() => _DownloadAsState();
}

class _DownloadAsState extends State<DownloadAs> {
  ScreenshotController screenshotController = ScreenshotController();
  List<SeriesMeta> seriesMeta = [];

  ValueNotifier<bool> isTermsAccepted = ValueNotifier(false);

  List<List<Map<String, dynamic>>>? chartData;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode = HiveUtilsSettings.getThemeMode() == ThemeMode.light;

    if (widget.indicatorDetails?.indicatorDetails.security?.id == '1') {
      // 0 - open
      // 1 - confidential
      // 2 - sensitive
      // 3 - secrete
      return Container(
        padding: const EdgeInsets.all(6),
        decoration: BoxDecoration(
          color: isLightMode
              ? AppColors.colorF4E7E9
              : AppColors.colorF4E7E9.withOpacity(0.2),
          borderRadius: BorderRadius.circular(8),
        ),
        child: IntrinsicHeight(
          child: Row(
            children: [
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                child: SvgPicture.asset(
                  isLightMode
                      ? AppImages.icDownloadRestrictedLight
                      : AppImages.icDownloadRestrictedDark,
                  height: 29,
                  width: 34,
                ),
              ),
              VerticalDivider(
                color: AppColors.greyShade4,
                width: 1,
                indent: 0,
                endIndent: 0,
              ),
              Expanded(
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  child: Text(
                    LocaleKeys.indicatorDownloadRestricted.tr(),
                    style: isLightMode
                        ? AppTextStyles.s12w3cBlueGreyShade1
                        : AppTextStyles.s12w3cWhite,
                    textScaler: TextScaler.linear(textScaleFactor.value),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    chartData = widget.chartData;
    seriesMeta = widget.indicatorDetails
            ?.getFilteredVisualizationMetaList()
            .firstOrNull
            ?.seriesMeta ??
        [];

    final bool hasDataToDownload =
        widget.enableDownload || widget.whatIfScenarioItemIndex != null;
    return BlocConsumer<DetailsBloc, DetailsState>(
      listener: (context, state) {
        if (state is PresentationTypeSelectionDoneState) {
          isTermsAccepted.value = false;
        } else if (state is SolidStateUpdateState) {
          isTermsAccepted.value = false;
        }
      },
      builder: (context, state) {
        return Opacity(
          opacity: hasDataToDownload ? 1 : 0.5,
          child: ValueListenableBuilder(
            valueListenable: isTermsAccepted,
            builder: (context, accepted, _) {
              return Column(
                children: [
                  IgnorePointer(
                    ignoring: !hasDataToDownload,
                    child: Container(
                      height: 82,
                      decoration: BoxDecoration(
                        color: isLightMode
                            ? const Color(0xFFEDEDEF)
                            : AppColors.blueShade36,
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(
                          color: isLightMode
                              ? AppColors.greyF3F4F6
                              : AppColors.blueShade36,
                        ),
                        // boxShadow: [
                        //   BoxShadow(
                        //     offset: const Offset(0, -5),
                        //     blurRadius: 15,
                        //     color: isLightMode
                        //         ? Colors.black26
                        //         : Colors.transparent,
                        //     inset: true,
                        //   ),
                        //   BoxShadow(
                        //     offset: const Offset(0, 5),
                        //     blurRadius: 8,
                        //     color: isLightMode
                        //         ? Colors.black45
                        //         : Colors.transparent,
                        //     inset: true,
                        //   ),
                        // ],
                      ),
                      child: Row(
                        children: [
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 18),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                if (isLightMode)
                                  Lottie.asset(
                                    AnimationAsset.animationDownload,
                                  )
                                else
                                  Lottie.asset(
                                    AnimationAssetDark.animationDownload,
                                  ),
                                const SizedBox(height: 6),
                                Text(
                                  LocaleKeys.downloadAs.tr(),
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    color: isLightMode
                                        ? AppColors.blueShade22
                                        : AppColors.white,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w400,
                                  ),
                                  textScaler:
                                      TextScaler.linear(textScaleFactor.value),
                                ),
                              ],
                            ),
                          ),
                          Expanded(
                            child: Container(
                              height: 82,
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 14),
                              decoration: BoxDecoration(
                                color: isLightMode
                                    ? AppColors.greyF3F4F6
                                    : AppColors.blueShade32,
                                borderRadius: BorderRadius.circular(10),
                                border: Border.all(
                                  color: isLightMode
                                      ? AppColors.greyF3F4F6
                                      : Colors.transparent,
                                ),
                              ),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceAround,
                                children: [
                                  Opacity(
                                    opacity: isTermsAccepted.value &&
                                            widget.chartType == 'table-view'
                                        ? 0.5
                                        : 1,
                                    child: IgnorePointer(
                                      ignoring: isTermsAccepted.value &&
                                          widget.chartType == 'table-view',
                                      child: DownloadAsChipWidget(
                                        isTermsAccepted: isTermsAccepted.value,
                                        text: LocaleKeys.pdf.tr(),
                                        icon: isLightMode
                                            ? 'assets/images/pdf-light.svg'
                                            : 'assets/images/pdf-dark.svg',
                                        onTap: !isTermsAccepted.value
                                            ? () {
                                                AppMessage
                                                    .showOverlayNotification(
                                                  '',
                                                  LocaleKeys
                                                      .termsAndConditionsWarning
                                                      .tr(),
                                                  msgType: 'error',
                                                );
                                              }
                                            : () {
                                                _downloadChart(context, true);
                                              },
                                      ),
                                    ),
                                  ),
                                  DownloadAsChipWidget(
                                    isTermsAccepted: isTermsAccepted.value,
                                    text: LocaleKeys.excel.tr(),
                                    icon: isLightMode
                                            ? 'assets/images/xls-light.svg'
                                        : 'assets/images/xls-dark.svg',
                                    onTap: !isTermsAccepted.value
                                        ? () {
                                            AppMessage.showOverlayNotification(
                                              '',
                                              LocaleKeys
                                                  .termsAndConditionsWarning
                                                  .tr(),
                                              msgType: 'error',
                                            );
                                          }
                                        : generateAndDownloadExcel,
                                  ),
                                  Opacity(
                                    opacity: isTermsAccepted.value &&
                                            widget.chartType == 'table-view'
                                        ? 0.5
                                        : 1,
                                    child: IgnorePointer(
                                      ignoring:
                                          widget.chartType == 'table-view',
                                      child: DownloadAsChipWidget(
                                        isTermsAccepted: isTermsAccepted.value,
                                        text: LocaleKeys.image.tr(),
                                        icon: isLightMode
                                            ? 'assets/images/img-light.svg'
                                            : 'assets/images/img-dark.svg',
                                        onTap: !isTermsAccepted.value
                                            ? () {
                                                AppMessage
                                                    .showOverlayNotification(
                                                  '',
                                                  LocaleKeys
                                                      .termsAndConditionsWarning
                                                      .tr(),
                                                  msgType: 'error',
                                                );
                                              }
                                            : () {
                                                _downloadChart(context, false);
                                              },
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 10),
                  Row(
                    children: [
                      IgnorePointer(
                        ignoring: !hasDataToDownload,
                        child: SizedBox(
                          width: 16,
                          height: 16,
                          child: Checkbox(
                            checkColor: AppColors.white,
                            activeColor: AppColors.blueLight,
                            side: BorderSide(color: AppColors.greyShade1),
                            value: isTermsAccepted.value,
                            onChanged: (value) {
                              isTermsAccepted.value = value!;
                            },
                          ),
                        ),
                      ),
                      const SizedBox(width: 10),
                      Text.rich(
                        textScaler: TextScaler.linear(textScaleFactor.value),
                        TextSpan(
                          children: [
                            TextSpan(
                              text: LocaleKeys.iAgreeTo.tr(),
                              style: TextStyle(
                                color: AppColors.grey,
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            TextSpan(
                              text: ' ${LocaleKeys.termsAndConditions.tr()}',
                              recognizer: TapGestureRecognizer()
                                ..onTap = () => AutoRouter.of(context).push(
                                    const TermsAndConditionsScreenRoute(),),
                              style: TextStyle(
                                color: isLightMode? AppColors.blueLight : AppColors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ],
              );
            },
          ),
        );
      },
    );
  }

  Future<void> _downloadChart(BuildContext context, bool isPDF) async {
    final bool hasPermission = await AppPermissions.checkPermissions(
      context,
      [AppPermission.storageAndroid],
    );
    if (!hasPermission) {
      return;
    }

    if (mounted) {
      unawaited(showDialog<void>(
        context: context,
        builder: (BuildContext context) {
          return StatefulBuilder(
            builder: (context, setState) {
              return AlertDialog(
                contentPadding: EdgeInsets.zero,
                insetPadding: const EdgeInsets.all(4),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),),
                backgroundColor: AppColors.white,
                elevation: 0,
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Flexible(
                      child:
                          SingleChildScrollView(child: _childWidget(context)),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                        bottom: 12,
                        top: 12,
                        left: 16,
                        right: 16,
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: TextButton(
                              style: TextButton.styleFrom(
                                foregroundColor: AppColors.blueShade22,
                                backgroundColor: AppColors.white,
                                textStyle: AppTextStyles.s16w5cBlueLight,
                                shape: RoundedRectangleBorder(
                                  side: BorderSide(
                                      color: AppColors.blueShade22, width: 1,),
                                  borderRadius: BorderRadius.circular(10),
                                ),
                              ),
                              child: Text(
                                isPDF
                                    ? LocaleKeys.downloadPDF.tr()
                                    : LocaleKeys.downloadImage.tr(),
                                textScaler:
                                    TextScaler.linear(textScaleFactor.value),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              onPressed: () {
                                screenshotController
                                    .capture()
                                    .then((Uint8List? image) {
                                  if (isPDF) {
                                    generateAndSavePdfFile(
                                      image?.toList() ?? [],
                                      '${getFileTitle()}${DateTime.now().toIso8601String()}.pdf',
                                    );
                                  } else {
                                    saveAndLaunchFile(
                                      image?.toList() ?? [],
                                      '${getFileTitle()}${DateTime.now().toIso8601String()}.png',
                                    );
                                  }
                                }).catchError((onError) {});
                                Navigator.of(context).pop();
                              },
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: TextButton(
                              style: TextButton.styleFrom(
                                foregroundColor: AppColors.blueShade22,
                                backgroundColor: AppColors.white,
                                textStyle: AppTextStyles.s16w5cBlueLight,
                                shape: RoundedRectangleBorder(
                                  side: BorderSide(
                                      color: AppColors.blueShade22, width: 1,),
                                  borderRadius: BorderRadius.circular(10),
                                ),
                              ),
                              child: Text(
                                LocaleKeys.cancel.tr(),
                                textScaler:
                                    TextScaler.linear(textScaleFactor.value),
                              ),
                              onPressed: () {
                                Navigator.of(context).pop();
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          );
        },
      ),);
    }
  }

  Widget _childWidget(BuildContext context) {
    final List<List<SplineChartData>> nowCast = [];
    final List<List<SplineChartData>> foreCast = [];
    // final List<List<ColumnChartData>> nowCastColumnChartMeta = [];
    final List<List<SplineChartData>> lowerAreaForecast = [];
    final List<List<SplineChartData>> upperAreaForecast = [];
    List<List<SplineChartData>> areaForecast = [];

    List<SeriesMeta> treeMapSeriesMeta = [];
    // final List<TreemapColorMapper> colorMappers = [];

    if (widget.indicatorDetails?.indicatorDetails.type == 'Internal' &&
        widget.indicatorDetails!.indicatorDetails.multiDrivers == true) {
// if (widget.whatIfScenarioItemIndex != null) {
      final List<List<Map<String, dynamic>>> l =
          widget.indicatorDetails!.getFilteredSeriesForMultiDrivers(
        visualizationsMetaIndex: widget.whatIfScenarioItemIndex ?? 0,
      );

      // Map<String, dynamic> dataFrequacyChange = IndicatorDateSetting.setFrequancy(
      //     l: l, indicatorDetails: widget.indicatorDetails);
      // String filterName =
      //     dataFrequacyChange['selectedFrequencyForFilter'] as String;
      // l = dataFrequacyChange['value'] as List<List<Map<String, dynamic>>>;
      for (int i = 0; i < l.length; i++) {
        if (seriesMeta[i].id!.contains(
                '-forecast',) /*&&
          widget.forecastVisibility*/
            ) {
          // if (filterName == 'Monthly') {
          //   l[i] = IndicatorDateSetting.setUpNameMonth(list: [l[i]])[0];
          // } else if (filterName == 'Quarterly') {
          //   l[i] = IndicatorDateSetting.setUpName(list: [l[i]])[0];
          // } else if (filterName == 'Yearly') {
          //   l[i] =
          //       IndicatorDateSetting.setUpName(list: [l[i]], type: 'yearly')[0];
          // }

          foreCast.add(
            l[i]
                .map(
                  (e) => SplineChartData(
                    e['OBS_DT'].toString(),
                    num.parse('${e['VALUE'] ?? '0'}'),
                  ),
                )
                .toList(),
          );
          final List<SplineChartData> lowData = [];

          for (var e in l[i]) {
            if (e['VALUE_LL'] != null) {
              lowData.add(
                SplineChartData(
                  e['OBS_DT'].toString(),
                  num.parse('${e['VALUE'] ?? '0'}'),
                  y: num.parse('${e['VALUE_LL'] ?? '0'}'),
                ),
              );
            }
          }

          lowerAreaForecast.add(lowData);

          final List<SplineChartData> data = [];

          for (var e in l[i]) {
            if (e['VALUE_UL'] != null) {
              data.add(
                SplineChartData(
                  e['OBS_DT'].toString(),
                  num.parse('${e['VALUE'] ?? '0'}'),
                  y: num.parse('${e['VALUE_UL'] ?? '0'}'),
                ),
              );
            }

            upperAreaForecast.add(data);
          }
          areaForecast = [...lowerAreaForecast, ...upperAreaForecast];
        } else {
          nowCast.add(
            l[i]
                .map(
                  (e) => SplineChartData(
                    e['OBS_DT'].toString(),
                    num.parse('${e['VALUE'] ?? '0'}'),
                  ),
                )
                .toList()
            /*.reversed
              .take(12)
              .toList()
              .reversed
              .toList()*/
            ,
          );
          // nowCastColumnChartMeta.add(
          //   l[i]
          //       .map(
          //         (e) => ColumnChartData(
          //           e['OBS_DT'].toString(),
          //           double.parse('${e['VALUE'] ?? '0'}'),
          //           0,
          //           0,
          //         ),
          //       )
          //       .toList()
          //   /*.reversed
          //     .take(12)
          //     .toList()
          //     .reversed
          //     .toList()*/
          //   ,
          // );
        }
      }
    } else {
      for (var i = 0; i < (chartData ?? []).length; i++) {
        nowCast.add(
          chartData![i]
              .map(
                (e) => SplineChartData(
                  e['OBS_DT'].toString(),
                  num.parse('${e['VALUE'] ?? '0'}'),
                ),
              )
              .toList(),
        );

        // nowCastColumnChartMeta.add(
        //   widget.chartData![i]
        //       .map(
        //         (e) => ColumnChartData(
        //           e['OBS_DT'].toString(),
        //           double.parse('${e['VALUE'] ?? '0'}'),
        //           0,
        //           0,
        //         ),
        //       )
        //       .toList()
        //   /*.reversed
        //       .take(12)
        //       .toList()
        //       .reversed
        //       .toList()*/
        //   ,
        // );
      }

      for (int i = 0;
          i < (widget.forecastChartDataDownload ?? []).length;
          i++) {
        foreCast.add(
          widget.forecastChartDataDownload![i]
              .map(
                (e) => SplineChartData(
                  e['OBS_DT'].toString(),
                  num.parse('${e['VALUE'] ?? '0'}'),
                ),
              )
              .toList(),
        );

        // for (int i = 0; i < forecastSeries.length; i++) {
        final List<SplineChartData> data = [];

        for (var e in widget.forecastChartDataDownload![i]) {
          if (e['VALUE_LL'] != null) {
            data.add(
              SplineChartData(
                e['OBS_DT'].toString(),
                num.parse('${e['VALUE'] ?? '0'}'),
                y: num.parse('${e['VALUE_LL'] ?? '0'}'),
              ),
            );
          }
        }

        lowerAreaForecast.add(data);
        // }

        // for (int i = 0; i < forecastSeries.length; i++) {
        final List<SplineChartData> dataUl = [];

        for (var e in widget.forecastChartDataDownload![i]) {
          if (e['VALUE_UL'] != null) {
            dataUl.add(
              SplineChartData(
                e['OBS_DT'].toString(),
                num.parse('${e['VALUE'] ?? '0'}'),
                y: num.parse('${e['VALUE_UL'] ?? '0'}'),
              ),
            );
          }
        }

        upperAreaForecast.add(dataUl);
        // }
        // areaForecast = [...lowerAreaForecast, ...upperAreaForecast];
      }
      areaForecast = [...lowerAreaForecast, ...upperAreaForecast];
    }

    if (widget.chartType == 'tree-map') {
      treeMapSeriesMeta = seriesMeta;
    }
    return Screenshot(
      controller: screenshotController,
      child: Container(
        margin: const EdgeInsets.all(8),
        child: DecoratedBox(
          decoration: BoxDecoration(
            border: Border.all(
              color: AppColors.grey,
            ),
            color: AppColors.white,
          ),
          child: Padding(
            padding: const EdgeInsets.all(1),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      AppImages.imgBayaanLogoDarkText,
                      height: 110,
                      width: 84,
                      fit: BoxFit.fill,
                    ),
                  ],
                ),
                const Divider(height: 16),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 6),
                  child: Row
                    (
                    mainAxisAlignment: (widget.computedIndicatorName != null &&
                            widget.computedIndicatorName!.isNotEmpty)
                        ? MainAxisAlignment.center
                        : MainAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Text.rich(
                          textScaler: TextScaler.linear(textScaleFactor.value),
                          textAlign: (widget.computedIndicatorName != null &&
                                  widget.computedIndicatorName!.isNotEmpty)
                              ? TextAlign.center
                              : null,
                          TextSpan(
                            children: [
                              // if (widget.computedIndicatorName != null &&
                              //     widget.computedIndicatorName!.isNotEmpty)
                              //   TextSpan(
                              //     text: widget.computedIndicatorName,
                              //     style: AppTextStyles.s16w5cBlackShade1.copyWith(
                              //       color: !isLightMode ? AppColors.white : null,
                              //     ),
                              //   )
                              // else
                              if ((widget.computedIndicatorName != null &&
                                      widget.computedIndicatorName!.isNotEmpty) ||
                                  widget.indicatorDetails
                                          ?.getFilteredVisualizationMetaList()
                                          .first
                                          .id ==
                                      'compare-chart' ||
                                  (widget.indicatorDetails?.indicatorDetails
                                              .componentTitle !=
                                          null &&
                                      (widget.indicatorDetails?.indicatorDetails
                                                  .componentTitle ??
                                              '')
                                          .isNotEmpty))
                                TextSpan(
                                  text: _getTitle(),
                                  style: AppTextStyles.s16w5cBlackShade1,
                                ),
                              if (widget.selectedVisualization != null)
                                TextSpan(
                                  text:
                                      ' (${widget.selectedVisualization?.componentTitle ?? ''})',
                                  style: AppTextStyles.s14w5cBlack.copyWith(
                                    color: AppColors.blackShade1,
                                  ),
                                ),
                              const WidgetSpan(child: SizedBox(width: 6)),
                              WidgetSpan(
                                child: widget.indicatorDetails!.indicatorDetails
                                            .contentClassificationKey ==
                                        'official_statistics'
                                    ? SvgPicture.asset(AppImages.icOfficialActive)
                                    : widget.indicatorDetails!.indicatorDetails
                                                .contentClassificationKey ==
                                            'official_statistics'
                                        ? SvgPicture.asset(
                                            AppImages.icExperimentalActive,
                                          )
                                        : const SizedBox(),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 10),
                if ((widget.indicatorDetails?.indicatorDetails
                            .componentSubtitle ??
                        '')
                    .isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 8,left: 6,right: 6),
                    child: Text(
                      widget.indicatorDetails?.indicatorDetails
                              .componentSubtitle ??
                          '',
                      textScaler: TextScaler.linear(textScaleFactor.value),
                      style: TextStyle(
                        fontSize: 13,
                        color: AppColors.black,
                      ),
                    ),
                  ),
                if (widget.chartType == 'bar-chart')
                  SizedBox(
                    height: 200,
                    width: MediaQuery.sizeOf(context).width * 0.9,
                    child: ColumnChart(
                        overrideAxisColorToDark: true,
                        frequance: widget.selectedFrequencyForFilterDownload,
                        chartDataList: getFilteredListForColumn(),
                        isLightMode: true,
                        isCompareActive: widget.indicatorDetails
                                ?.getFilteredVisualizationMetaList()
                                .first
                                .id ==
                            'compare-chart',),
                  )
                else if (widget.chartType == 'tree-map')
                  FittedBox(
                    child: SizedBox(
                      height: 210,// * (textScaleFactor.value < 1 ? 1 : textScaleFactor.value),
                      width: MediaQuery.sizeOf(context).width * 0.9,
                      child: IgnorePointer(
                        child: TreemapChart(
                          key: Key(
                              'treemap.download.indicator.${treeMapSeriesMeta.map((e) => e.color).join()}',),
                          chartSeriesData: treeMapSeriesMeta,
                          // colorMappers: colorMappers,
                          // chartData: ,
                        ),
                      ),
                    ),
                  )
                else if (widget.chartType == 'line-chart' ||
                    widget.chartType == '')
                  if (foreCast.isNotEmpty)
                    SizedBox(
                      height: 200,
                      // width: MediaQuery.sizeOf(context).width * 0.9,
                      child: SplineChart(
                        overrideAxisColorToDark: true,
                        frequance: widget.selectedFrequencyForFilterDownload,
                        isForecast: widget.forecastVisibility,
                        chartDataList: nowCast,
                        forecastChartDataList: foreCast,
                        areaHighlightChartData: areaForecast,
                      ),
                    )
                  else
                    SizedBox(
                      height: 200,
                      width: MediaQuery.sizeOf(context).width * 0.9,
                      child: SplineChart(
                          frequance: widget.selectedFrequencyForFilterDownload,
                          isLightMode: true,
                          comparisonChartData: widget.indicatorDetails
                                      ?.getFilteredVisualizationMetaList()
                                      .first
                                      .id ==
                                  'compare-chart'
                              ? widget.indicatorDetails!
                                  .getFilteredSeries(seriesMetaIndex: 1)
                                  .map(
                                    (e) => SplineChartData(
                                      e['OBS_DT'].toString(),
                                      double.parse(
                                        '${e['VALUE'] ?? '0'}',
                                      ),
                                    ),
                                  )
                                  .toList()
                              : [],
                          chartDataList: getFilteredListForSpline(),
                          isCompareActive: widget.indicatorDetails
                                  ?.getFilteredVisualizationMetaList()
                                  .first
                                  .id ==
                              'compare-chart',),
                    ),
                if (widget.chartType != 'table-view' &&
                    widget.chartType != 'tree-map' &&
                    widget.chartData != null &&
                    (widget.chartData ?? []).length > 1) ...[
                      if(widget.isComparisonActive)
                        Center(
                          child: Padding(
                            padding: const EdgeInsets.only(top: 8),
                            child: Wrap(
                              runSpacing: 10,
                              runAlignment: WrapAlignment.spaceBetween,
                              children:
                              List.generate(widget.lendendListCompare.length ?? 0, (i) {
                                if (widget.lendendListCompare[i].isNotEmpty) {
                                  return Padding(
                                    padding: EdgeInsets.only(
                                      right:
                                      HiveUtilsSettings.getAppLanguage() == 'ar'
                                          ? 0
                                          : 10,
                                      left: HiveUtilsSettings.getAppLanguage() == 'ar'
                                          ? 10
                                          : 0,
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Padding(
                                          padding: const EdgeInsets.only(top: 5),
                                          child: SvgPicture.asset(
                                            AppImages.icLegendGreen,
                                            colorFilter: ColorFilter.mode(
                                              AppColors.chartColorSet[widget
                                                  .chartData!
                                                  .indexOf(widget.chartData![i]) %
                                                  AppColors.chartColorSet.length],
                                              BlendMode.srcIn,
                                            ),
                                          ),
                                        ),
                                        const SizedBox(width: 5),
                                        Flexible(
                                          child: Text(widget.lendendListCompare[i],
                                            textAlign: TextAlign.start,
                                            style: TextStyle(
                                              color: AppColors.grey,
                                              fontSize: 12,
                                              fontWeight: FontWeight.w400,
                                            ),
                                            maxLines: 4,
                                            textScaler: TextScaler.linear(
                                              textScaleFactor.value,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                } else {
                                  return const SizedBox.shrink();
                                }
                              }),
                            ),
                          ),
                        ),
                        if(!widget.isComparisonActive)
                  Center(
                    child: Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Wrap(
                        runSpacing: 10,
                        runAlignment: WrapAlignment.spaceBetween,
                        children:
                            List.generate(widget.chartData?.length ?? 0, (i) {
                          if (widget.chartData![i].isNotEmpty) {
                            return Padding(
                              padding: EdgeInsets.only(
                                right:
                                    HiveUtilsSettings.getAppLanguage() == 'ar'
                                        ? 0
                                        : 10,
                                left: HiveUtilsSettings.getAppLanguage() == 'ar'
                                    ? 10
                                    : 0,
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(top: 5),
                                    child: SvgPicture.asset(
                                      AppImages.icLegendGreen,
                                      colorFilter: ColorFilter.mode(
                                        AppColors.chartColorSet[widget
                                                .chartData!
                                                .indexOf(widget.chartData![i]) %
                                            AppColors.chartColorSet.length],
                                        BlendMode.srcIn,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 5),
                                  Flexible(
                                    child: Text(
                                      !widget.isComparisonActive
                                          ? (widget.chartData?[i]
                                                  .first['legend_name']
                                                  ?.toString() ??
                                              '')
                                          : (widget.lendendListCompare[i]),
                                      textAlign: TextAlign.start,
                                      style: TextStyle(
                                        color: AppColors.grey,
                                        fontSize: 12,
                                        fontWeight: FontWeight.w400,
                                      ),
                                      maxLines: 4,
                                      textScaler: TextScaler.linear(
                                        textScaleFactor.value,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          } else {
                            return const SizedBox.shrink();
                          }
                        }),
                      ),
                    ),
                  ),
                ],

                const Divider(height: 20),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6),
                  alignment: Alignment.centerLeft,
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          LocaleKeys.copyright.tr(),
                          style: AppTextStyles.s10w5cWhite.copyWith(
                            color: AppColors.blueGreyShade1,
                          ),
                          textScaler: TextScaler.linear(textScaleFactor.value),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 4),
              ],
            ),
          ),
        ),
      ),
    );
  }

  List<List<SplineChartData>> getFilteredListForSpline() {
    final List<List<SplineChartData>> solidSplineChartMeta = [];

    for (final series in chartData ?? <List<Map<String, dynamic>>>[]) {
      solidSplineChartMeta.add(
        series
            .map(
              (e) => SplineChartData(
                e['OBS_DT'].toString(),
                double.parse('${e['VALUE'] ?? '0'}'),
              ),
            )
            .toList(),
      );
    }

    return solidSplineChartMeta;
  }

  List<List<ColumnChartData>> getFilteredListForColumn() {
    final List<List<ColumnChartData>> solidColumnChartMeta = [];

    for (final series in chartData ?? <List<Map<String, dynamic>>>[]) {
      solidColumnChartMeta.add(
        series
            .map(
              (e) => ColumnChartData(
                e['OBS_DT'].toString(),
                double.parse('${e['VALUE'] ?? '0'}'),
                0,
                0,
              ),
            )
            .toList(),
      );
    }
    return solidColumnChartMeta;
  }

  Future<void> generateAndDownloadExcel() async {
    final bool hasPermission = await AppPermissions.checkPermissions(
      context,
      [AppPermission.storageAndroid],
    );
    if (!hasPermission) {
      return;
    }

    /// Single Series Data
    final List<Map<String, dynamic>> seriesData = [];

    if (widget.indicatorDetails?.indicatorDetails.type == 'Internal' &&
        widget.indicatorDetails!.indicatorDetails.multiDrivers == true) {
      chartData = widget.indicatorDetails!.getFilteredSeriesForMultiDrivers(
        visualizationsMetaIndex: widget.whatIfScenarioItemIndex ?? 0,
      );

      for (final collection in chartData ?? [[]]) {
        for (final element in collection) {
          seriesData.add(element as Map<String, dynamic>);
        }
      }
      for (final collection in widget.forecastChartDataDownload ?? [[]]) {
        for (final element in collection) {
          if ((element['TYPE'] as String?)?.trim() != 'NOWCAST') {
            seriesData.add(element as Map<String, dynamic>);
          }
        }
      }
    } else {
      for (final collection in chartData ?? [[]]) {
        for (final element in collection) {
          seriesData.add(element as Map<String, dynamic>);
        }
      }
      for (final collection in widget.forecastChartDataDownload ?? [[]]) {
        for (final element in collection) {
          if ((element['TYPE'] as String?)?.trim() != 'NOWCAST') {
            seriesData.add(element as Map<String, dynamic>);
          }
        }
      }
    }

    final List<TableFields> tableFields = [];
        // widget.indicatorDetails?.indicatorDetails.tableFields ?? [];

    for (final TableFields element in widget.indicatorDetails?.indicatorDetails.tableFields ?? []) {
      if (widget.chartData?.firstOrNull?.firstOrNull?.containsKey(element.path)??false) {
        tableFields.add(element);
      }
    }

    final Workbook workbook = Workbook();

    final Worksheet sheet = workbook.worksheets[0]..showGridlines = true;

    final ByteData imageBytes1 =
        await getUiImage(AppImages.imgBayaanLogoDarkText, 130, 100);
    final Uint8List byteList1 = imageBytes1.buffer.asUint8List();
    sheet.pictures.addStream(1, 1, byteList1);

    sheet.getRangeByName('A1:E8').merge();
    sheet.getRangeByName('A9:E9').merge();
    sheet.getRangeByName('A10:E10').merge();

    final Style globalStyle1 = workbook.styles.add('style1')
      ..backColor = '#D09A57'
      ..fontName = 'Arial'
      ..fontSize = 10
      ..fontColor = '#FFFFFF'
      ..bold = true
      // ..wrapText = true
      ..hAlign = HAlignType.center
      ..vAlign = VAlignType.center;
    globalStyle1.borders.all.lineStyle = LineStyle.thin;
    globalStyle1.borders.all.color = '#000000';

    final Style globalStyle2 = workbook.styles.add('style2')
      ..backColor = '#D6FEE7'
      ..fontName = 'Calibri (Body)'
      ..fontSize = 11
      ..fontColor = '#000000'
      // ..wrapText = true
      ..hAlign = HAlignType.left
      ..vAlign = VAlignType.center;
    globalStyle2.borders.all.lineStyle = LineStyle.thin;
    globalStyle2.borders.all.color = '#000000';

    final Style globalStyle3 = workbook.styles.add('style3')
      ..fontName = 'Calibri (Body)'
      ..fontSize = 11
      ..fontColor = '#000000'
      ..hAlign = HAlignType.left
      ..vAlign = VAlignType.center;

    final Style globalStyle4 = workbook.styles.add('style4')
      ..fontName = 'Arial'
      ..fontSize = 14
      ..fontColor = '#000000'
      ..bold = true
      // ..wrapText = true
      ..hAlign = HAlignType.left
      ..vAlign = VAlignType.center;

    sheet.getRangeByName('A9')
    ..setText(_getTitle())
      // ..setText(widget.whatIfScenarioItemIndex != null
      //     ? widget.indicatorDetails?.indicatorDetails.indicatorVisualizations
      //             ?.visualizationsMeta
      //             ?.elementAt(widget.whatIfScenarioItemIndex!)
      //             .vizLabel ??
      //         ''
      //     : widget.indicatorDetails?.indicatorDetails.componentTitle,)
      ..cellStyle = globalStyle4;

    const int startRow = 12;
    if (tableFields.isNotEmpty) {
      sheet.setRowHeightInPixels(11, 60);
      int columnIndex = 0;
      for (final TableFields field in tableFields) {
        // if (seriesData.first.containsKey(field.path)) {
        sheet.setColumnWidthInPixels(columnIndex + 1, 100);
        sheet.getRangeByName(
          '${String.fromCharCode(65 + columnIndex)}${startRow - 1}',
        )
          ..setText(field.label)
          ..cellStyle = globalStyle1;
        for (int i = 0; i < seriesData.length; i++) {
          if (seriesData[i][field.path].runtimeType == double) {
            sheet.getRangeByName(
              '${String.fromCharCode(65 + columnIndex)}${startRow + i}',
            )
              ..setText(
                double.parse(seriesData[i][field.path].toString())
                    .toStringAsFixed(2),
              )
              ..cellStyle = globalStyle3;
          } else {
            sheet.getRangeByName(
              '${String.fromCharCode(65 + columnIndex)}${startRow + i}',
            )
              ..setText(
                seriesData[i][field.path] != null
                    ? seriesData[i][field.path].toString()
                    : '',
              )
              ..cellStyle = globalStyle3;
          }
        }
        columnIndex++;
        // }
      }
      sheet.getRangeByName(
        '''${String.fromCharCode(65)}${startRow + seriesData.length + 1}:${String.fromCharCode(65 + tableFields.length - 1)}${startRow + seriesData.length + 1}''',
      ).merge();
      sheet.getRangeByName(
        '${String.fromCharCode(65)}${startRow + seriesData.length + 1}',
      )
        ..setText(
          LocaleKeys.sensitiveInformation.tr(),
        )
        ..cellStyle = globalStyle2;
    } else {
      /// MultiDrivers/What If Series Data List
      // final List<SeriesMeta> seriesMeta = widget.selectedVisualization != null
      //     ? widget.selectedVisualization?.indicatorVisualizations!
      //             .visualizationsMeta![0].seriesMeta ??
      //         []
      //     : widget.indicatorDetails?.indicatorDetails.indicatorVisualizations!
      //             .visualizationsMeta![0].seriesMeta ??
      //         [];
      final List<Map<String, dynamic>> seriesDatas = [];
      for (final collection in chartData ?? [[]]) {
        for (final element in collection) {
          seriesDatas.add(element as Map<String, dynamic>);
        }
      }
      for (final collection in widget.forecastChartDataDownload ?? [[]]) {
        for (final element in collection) {
          if ((element['TYPE'] as String).trim() != 'NOWCAST') {
            seriesDatas.add(element as Map<String, dynamic>);
          }
        }
      }

      // final List<List<Map<String, dynamic>>> seriesDataList =
      //     widget.selectedVisualization != null
      //         ? (widget.chartData ?? [])
      //         : widget.indicatorDetails?.getFilteredSeriesForMultiDrivers(
      //               visualizationsMetaIndex:
      //                   widget.whatIfScenarioItemIndex ?? 0,
      //             ) ??
      //  [];

      List<String> keys = [];
      List<Map<String, dynamic>> tempData = [];
      for (final List<Map<String, dynamic>> seriesData in chartData ?? []) {
        if (keys.isEmpty) {
          sheet.setRowHeightInPixels(11, 60);
          int columnIndex = 0;
          if (seriesData.isNotEmpty) {
            keys = seriesData.first.keys.toList();
            for (final String key in keys) {
              if (key != 'legend_name' && key != 'legend_title') {
                sheet.setColumnWidthInPixels(columnIndex + 1, 130);
                sheet.getRangeByName(
                  '${String.fromCharCode(65 + columnIndex)}${startRow - 1}',
                )
                  ..setText(key)
                  ..cellStyle = globalStyle1;
                columnIndex++;
              }
            }
          }
        }

        int columnIndex = 0;
        // List<List<Map<String, dynamic>>> tempDataList = [];
        // final List<List<Map<String, dynamic>>> nowCastList = [];
        // final List<List<Map<String, dynamic>>> foreCastList = [];
        // for (int s1 = 0; s1 < seriesMeta.length; s1++) {
        //   if (seriesMeta[s1].id!.contains('-forecast')) {
        //     foreCastList.add(seriesDataList[s1]);
        //   } else {
        //     nowCastList.add(
        //       // seriesDataList[s1].reversed.take(12).toList().reversed.toList(),
        //       seriesDataList[s1],
        //     );
        //   }
        // }

        // if (foreCastList.isNotEmpty &&
        //     foreCastList[0].isNotEmpty &&
        //     nowCastList[nowCastList.length - 1]
        //                     [nowCastList[nowCastList.length - 1].length - 1]
        //                 ['OBS_DT']
        //             .toString() ==
        //         foreCastList[0][0]['OBS_DT'].toString()) {
        //   foreCastList[0].removeAt(0);
        // }

        // tempDataList = [
        //   ...nowCastList,
        //   ...foreCastList,
        // ];

        // for (final List<Map<String, dynamic>> item in tempDataList) {
        //   for (int a = 0; a < item.length; a++) {
        //     tempData.add(item[a]);
        //   }
        // }

        // final List<Map<String, dynamic>> filteredTempData = [];

        // if ((widget.chartData?.length ?? 0) > 1) {
        //   for (final List<Map<String, dynamic>> item
        //       in widget.chartData ?? []) {
        //     for (int a = 0; a < item.length; a++) {
        //       filteredTempData.add(item[a]);
        //     }
        //   }
        //   tempData = seriesData;
        // }
        tempData = seriesDatas;
        for (int i = 0; i < tempData.toSet().toList().length; i++) {
          for (final String key in keys) {
            if (key != 'legend_name' && key != 'legend_title') {
              columnIndex = keys.indexOf(key);
              if(tempData[i][key] == null) {
                sheet.getRangeByName(
                  '${String.fromCharCode(65 + columnIndex)}${startRow + i}',
                )
                  ..setText('-')
                  ..cellStyle = globalStyle3;
              } else {
                if (tempData[i][key].runtimeType == double) {
                  sheet.getRangeByName(
                    '${String.fromCharCode(65 + columnIndex)}${startRow + i}',
                  )
                    ..setText(
                      double.parse(tempData[i][key].toString())
                          .toStringAsFixed(2),
                    )
                    ..cellStyle = globalStyle3;
                } else {
                  sheet.getRangeByName(
                    '${String.fromCharCode(65 + columnIndex)}${startRow + i}',
                  )
                    ..setText(
                      tempData[i][key].toString(),
                    )
                    ..cellStyle = globalStyle3;
                }
              }
            }
          }
        }
      }
      sheet.getRangeByName(
        '''${String.fromCharCode(65)}${startRow + tempData.length + 1}:${String.fromCharCode(65 + keys.length - 1)}${startRow + tempData.length + 1}''',
      ).merge();
      sheet.getRangeByName(
        '${String.fromCharCode(65)}${startRow + tempData.length + 1}',
      )
        ..setText(
          LocaleKeys.sensitiveInformation.tr(),
        )
        ..cellStyle = globalStyle2;
    }

    final List<int> bytes = workbook.saveSync();
    workbook.dispose();

    await saveAndLaunchFile(
      bytes,
      '${getFileTitle()}${DateTime.now().toIso8601String()}.xlsx',
    );
  }

  String getFileTitle() {
    if (widget.computedIndicatorName != null &&
        widget.computedIndicatorName!.isNotEmpty) {
      return widget.computedIndicatorName!;
    } else if (widget.isComparisonActive) {
      return widget.comparedIndicatorName ??
          LocaleKeys.compareIndicatorsResult.tr();
    } else if (widget.selectedVisualization != null) {
      return '${widget.indicatorDetails?.indicatorDetails.componentTitle}_${widget.selectedVisualization?.componentTitle}';
    } else {
      return widget.indicatorDetails?.indicatorDetails.componentTitle ?? '';
    }
  }

  String _getTitle() {
    return ((widget.computedIndicatorName != null && widget.computedIndicatorName!.isNotEmpty)
        ? widget.computedIndicatorName
        : (widget.indicatorDetails?.getFilteredVisualizationMetaList().first.id == 'compare-chart'
        ? widget.comparedIndicatorName ?? LocaleKeys.compareIndicatorsResult.tr()
        : widget.whatIfScenarioItemIndex != null
        ? widget.indicatorDetails?.indicatorDetails.indicatorVisualizations?.visualizationsMeta?.elementAt(widget.whatIfScenarioItemIndex!,).vizLabel ?? ''
        : widget.indicatorDetails?.indicatorDetails.componentTitle ?? '')) ??'';
  }
}

Future<void> saveAndLaunchFile(List<int> bytes, String fileName) async {
  final status = await Permission.storage.status;
  if (!status.isGranted) {
    await Permission.storage.request();
  }
  Directory directory = Directory('');
  if (Platform.isAndroid) {
    directory = Directory('/storage/emulated/0/Download/BayaanGov');
  } else {
    directory = await getApplicationDocumentsDirectory();
  }
  final path = directory.path;
  await Directory(path).create(recursive: true);

  final sanitizedFileName = fileName.replaceAll(RegExp(r'[^\w\s.]+'), '_');

  final File file = File('$path/$sanitizedFileName');
  await file.writeAsBytes(bytes);

  AppMessage.showOverlayNotification(LocaleKeys.fileDownloaded.tr(),
      '${LocaleKeys.fileDownloaded.tr()} $fileName', onTap: () async {
        unawaited(DownloadHelper.openFile(file.path));
  },);
}

Future<ByteData> getUiImage(
  String imageAssetPath,
  int height,
  int width,
) async {
  final ByteData assetImageByteData = await rootBundle.load(imageAssetPath);
  final img.Image? baseSizeImage =
      img.decodeImage(assetImageByteData.buffer.asUint8List());
  final img.Image resizeImage =
      img.copyResize(baseSizeImage!, height: height, width: width);
  final ui.Codec codec =
      await ui.instantiateImageCodec(img.encodePng(resizeImage));
  final ui.FrameInfo frameInfo = await codec.getNextFrame();
  final ByteData? byteData =
      await frameInfo.image.toByteData(format: ui.ImageByteFormat.png);
  return byteData!;
}

Future<void> generateAndSavePdfFile(List<int> bytes, String fileName) async {
  final PdfDocument document = PdfDocument();
  final PdfPage page = document.pages.add();

  page.graphics.drawImage(
    PdfBitmap(bytes),
    Rect.fromLTWH(
      0,
      0,
      page.getClientSize().width,
      page.getClientSize().height,
    ),
  );
  final List<int> docBytes = document.saveSync();
  document.dispose();

  await saveAndLaunchFile(docBytes, fileName);
}
