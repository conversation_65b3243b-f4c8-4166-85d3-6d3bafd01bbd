import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

class GlossaryCustomPaint extends CustomPainter {
  GlossaryCustomPaint({required this.isRtl});

  final bool isRtl;
  final bool isLightMode = HiveUtilsSettings.getThemeMode() == ThemeMode.light;
  @override
  void paint(Canvas canvas, Size size) {
    final Path path_0 = Path();

    Paint paintFill;
    if (isRtl) {
      path_0..moveTo(0, size.height * 0.6394471)
      ..cubicTo(
          0,
          size.height * 0.5540718,
          size.width * 0.02563989,
          size.height * 0.4958694,
          size.width * 0.04473684,
          size.height * 0.4958694,)
      ..cubicTo(
          size.width * 0.08252474,
          size.height * 0.4958694,
          size.width * 0.1131579,
          size.height * 0.3589212,
          size.width * 0.1131579,
          size.height * 0.1899871,)
      ..cubicTo(size.width * 0.1131579, size.height * 0.1071299,
          size.width * 0.1256871, 0, size.width * 0.1442211, 0,)
      ..lineTo(size.width * 0.9473684, 0)
      ..cubicTo(size.width * 0.9764368, 0, size.width,
          size.height * 0.1053447, size.width, size.height * 0.2352941,)
      ..lineTo(size.width, size.height * 0.7647059)
      ..cubicTo(
          size.width,
          size.height * 0.8946553,
          size.width * 0.9764368,
          size.height,
          size.width * 0.9473684,
          size.height,)
      ..lineTo(size.width * 0.04060711, size.height)
      ..cubicTo(size.width * 0.01818037, size.height, 0,
          size.height * 0.9187235, 0, size.height * 0.8184624,)
      ..lineTo(0, size.height * 0.6394471)
      ..close();

      paintFill = Paint()..style = PaintingStyle.fill
        ..color = !isLightMode
            ? const Color(0xFF2B5281).withOpacity(1)
            : const Color(0xFF0054B8).withOpacity(.8);
      // ..shader = ui.Gradient.linear(
      //   Offset(size.width * 0.9278681, size.height),
      //      Offset(size.width * 0.01543739, size.height),
      //        isLightMode
      //         ? [
      //             const Color(0xFFFFE6FC).withOpacity(1),
      //             const Color(0xFFD5CFFF).withOpacity(1),
      //           ]
      //         : [
      //             const Color(0xff8976AF).withOpacity(1),
      //             const Color(0xff4F5691).withOpacity(1),
      //           ],
      //     [0, 1],);
    } else {
      path_0
        ..moveTo(size.width, size.height * 0.6376953)
        ..cubicTo(
          size.width,
          size.height * 0.5523200,
          size.width * 0.9742929,
          size.height * 0.4941176,
          size.width * 0.9551451,
          size.height * 0.4941176,
        )
        ..lineTo(size.width * 0.9551451, size.height * 0.4941176)
        ..cubicTo(
          size.width * 0.9172586,
          size.height * 0.4941176,
          size.width * 0.8865435,
          size.height * 0.3571694,
          size.width * 0.8865435,
          size.height * 0.1882353,
        )
        ..lineTo(size.width * 0.8865435, size.height * 0.1882353)
        ..cubicTo(
          size.width * 0.8865435,
          size.height * 0.1058514,
          size.width * 0.8740660,
          0,
          size.width * 0.8555884,
          0,
        )
        ..lineTo(size.width * 0.05277045, 0)
        ..cubicTo(
          size.width * 0.02362615,
          0,
          0,
          size.height * 0.1053447,
          0,
          size.height * 0.2352941,
        )
        ..lineTo(0, size.height * 0.7647059)
        ..cubicTo(
          0,
          size.height * 0.8946553,
          size.width * 0.02362612,
          size.height,
          size.width * 0.05277045,
          size.height,
        )
        ..lineTo(size.width * 0.9591636, size.height)
        ..cubicTo(
          size.width * 0.9817177,
          size.height,
          size.width,
          size.height * 0.9184765,
          size.width,
          size.height * 0.8179129,
        )
        ..lineTo(size.width, size.height * 0.6376953)
        ..close();
        paintFill = Paint()..style = PaintingStyle.fill
        ..color = !isLightMode
            ? const Color(0xFF2B5281).withOpacity(1)
            : const Color(0xFF0054B8).withOpacity(.8);
        // ..shader = ui.Gradient.linear(
        //   Offset(size.width * 0.01543739, size.height),
        //   Offset(size.width * 0.9278681, size.height),
        //   isLightMode
        //       ? [
        //           const Color(0xFFFFE6FC).withOpacity(1),
        //           const Color(0xFFD5CFFF).withOpacity(1),
        //         ]
        //       : [
        //           const Color(0xff8976AF).withOpacity(1),
        //           const Color(0xff4F5691).withOpacity(1),
        //         ],
        //   [0, 1],
        // );
    }

    canvas.drawPath(path_0, paintFill);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
