import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart' show textScaleFactor;
import 'package:scad_mobile/src/features/details/presentation/bloc/details_bloc.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

class ComputationTitle extends StatelessWidget {
  const ComputationTitle({required this.titleValue, super.key});

  final String titleValue;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(
        horizontal: 10,
        vertical: 8,
      ),
      decoration: BoxDecoration(
        color: isLightMode ? AppColors.white : AppColors.blueShade32,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // InkWell(
          //   onTap: () {
          //     context.read<DetailsBloc>().add(
          //           SwitchTitleEvent(
          //             value: titleValue,
          //             direction: -1,
          //           ),
          //         );
          //   },
          //   child: SvgPicture.asset(
          //     'assets/images/${HiveUtilsSettings.getAppLanguage() == 'ar' ? 'right-compute' : 'left-compute'}.svg',
          //   ),
          // ),
          Text(
            titleValue,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: isLightMode ? AppColors.blackShade1 : AppColors.white,
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
            textScaler: TextScaler.linear(textScaleFactor.value),
          ),
          // InkWell(
          //   onTap: () {
          //     context.read<DetailsBloc>().add(
          //           SwitchTitleEvent(
          //             value: titleValue,
          //             direction: 1,
          //           ),
          //         );
          //   },
          //   child: SvgPicture.asset(
          //     'assets/images/${HiveUtilsSettings.getAppLanguage() == 'ar' ? 'left-compute' : 'right-compute'}.svg',
          //   ),
          // ),
        ],
      ),
    );
  }
}
