import 'dart:convert';

import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_response_helper.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/features/details/presentation/widgets/common_indicator_view.dart';
import 'package:scad_mobile/src/features/details/presentation/widgets/expanded_indicator_details_view.dart';
import 'package:scad_mobile/src/features/details/presentation/widgets/notification_and_my_app_button.dart';
import 'package:scad_mobile/src/features/my_apps/presentation/pages/add_to_myapps.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_api_cache.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

class ExpandableIndicatorCardWidget extends StatefulWidget {
  const ExpandableIndicatorCardWidget({
    required this.id,
    required this.contentType,
    required this.index,
    this.indicatorDetails,
    this.originalIndicatorForFilter,
    super.key,
  });

  final String id;
  final int index;
  final String contentType;
  final IndicatorDetailsResponseHelper? indicatorDetails;
  final IndicatorDetailsResponse? originalIndicatorForFilter;

  @override
  State<ExpandableIndicatorCardWidget> createState() =>
      ExpandableIndicatorCardWidgetState();
}

class ExpandableIndicatorCardWidgetState
    extends State<ExpandableIndicatorCardWidget>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return SizedBox(
      width: double.infinity,
      child: ExpandableNotifier(
        child: ScrollOnExpand(
          child: Stack(
            alignment: Alignment.bottomCenter,
            children: [
              Expandable(
                theme: const ExpandableThemeData(
                  headerAlignment: ExpandablePanelHeaderAlignment.center,
                  tapBodyToCollapse: false,
                  tapHeaderToExpand: false,
                  hasIcon: false,
                ),
                collapsed: detailsCardWidget(
                  childWidget: CommonIndicatorView(
                    isSearch: false,
                    indicatorDetails: widget.indicatorDetails,
                    nodeId: widget.id,
                    index: widget.index,
                    originalIndicatorForFilter:
                        IndicatorDetailsResponse.fromJson(jsonDecode(
                                jsonEncode(widget.originalIndicatorForFilter))
                            as Map<String, dynamic>),
                  ),
                  isLightMode: isLightMode,
                ),
                expanded: detailsCardWidget(
                  childWidget: ExpandedIndicatorDetailsView(
                    indicatorDetails: widget.indicatorDetails,
                    originalIndicatorForFilter:
                        IndicatorDetailsResponse.fromJson(jsonDecode(
                                jsonEncode(widget.originalIndicatorForFilter))
                            as Map<String, dynamic>),
                    id: widget.id,
                    index: widget.index,
                    isSearch: false
                  ),
                  isLightMode: isLightMode,
                  
                ),
              ),
              Positioned(
                bottom: 20,
                child: Builder(
                  builder: (context) {
                    final controller = ExpandableController.of(
                      context,
                      required: true,
                    )!;
                    return SizedBox(
                      height: 20,
                      width: 40,
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor:AppColors.blueShade27,
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          padding: EdgeInsets.zero,
                        ),
                        onPressed: controller.toggle,
                        child: Icon(
                          controller.expanded
                              ? Icons.keyboard_arrow_up_rounded
                              : Icons.keyboard_arrow_down_rounded,
                          color: Colors.white,
                          size: 22,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget detailsCardWidget({
    required Widget childWidget,
    required bool isLightMode,
  }) {
    final List<VisualizationsMeta>? indicatorList = widget.indicatorDetails
        ?.indicatorDetails.indicatorVisualizations?.visualizationsMeta;
         final rtl = DeviceType.isDirectionRTL(context);
    return SizedBox(
      width: double.infinity,
      child: Padding(
        padding: const EdgeInsets.only(bottom: 30, right: 20, left: 20),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: const Color(0xff707B9C).withOpacity(0.08),
                blurRadius: 50,
                offset: const Offset(0, 35),
                spreadRadius: -23,
              ),
            ],
          ),
          child: Card(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            margin: EdgeInsets.zero,
            color: isLightMode ? AppColors.white : AppColors.blueShade32,
            elevation: 0,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(
                    top: 20,
                    left: 20,
                    right: 20,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Container(
                            child: Row(
                              children: [
                                SvgPicture.network(
                                  //todo set image
                                 HiveUtilsApiCache.getDomainImageByName(
                                                                        widget.  indicatorDetails
                                                                              ?.indicatorDetails
                                                                              .domain,
                                                                          rtl,
                                                                        ),
                                  width: 16,
                                  colorFilter:   isLightMode ? const ColorFilter.mode(
                                     Colors.black,
                                    BlendMode.srcIn,
                                  ):const ColorFilter.mode(
                                     Colors.white,
                                    BlendMode.srcIn,
                                  ) ,
                                ),
                            const  SizedBox(width: 10),
                                Text(  widget.  indicatorDetails
                                    ?.indicatorDetails
                                    .domain ?? ''),
                              ],
                            ),
                          ),
                          NotificationAndMyAppButton(
                            key: UniqueKey(),
                            indicatorDetails: widget.indicatorDetails,
                            contentType: widget.contentType,
                          ),
                          // Row(
                          //   children: [
                          //     InkWell(
                          //       onTap: () {},
                          //       child: SvgPicture.asset(AppImages.icBell),
                          //     ),
                          //     const SizedBox(width: 12),
                          //     InkWell(
                          //       onTap: () {
                          //         showModalBottomSheet<void>(
                          //           isScrollControlled: true,
                          //           constraints: BoxConstraints(
                          //             minHeight: 100,
                          //             maxHeight:
                          //                 MediaQuery.sizeOf(context).height *
                          //                     .90,
                          //           ),
                          //           shape: const RoundedRectangleBorder(
                          //             borderRadius: BorderRadius.only(
                          //               topLeft: Radius.circular(20),
                          //               topRight: Radius.circular(20),
                          //             ),
                          //           ),
                          //           backgroundColor: AppColors.white,
                          //           context: context,
                          //           builder: (context) {
                          //             return AddToMyApps(
                          //               indicatorDetails:
                          //                   widget.indicatorDetails!,
                          //               nodeId: widget.id,
                          //               contentType: widget.contentType,
                          //             );
                          //           },
                          //         );
                          //       },
                          //       child: SvgPicture.asset(
                          //         AppImages.icAddToMyAppsOff,
                          //       ),
                          //     ),
                          //   ],
                          // ),
                        ],
                      ),
                      const SizedBox(height: 10),
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Flexible(
                            child: Text(
                              indicatorList?[widget.index].vizLabel ?? '',
                              style: TextStyle(
                                color: isLightMode
                                    ? AppColors.black
                                    : AppColors.white,
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                              // maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              textScaler: TextScaler.linear(
                                textScaleFactor.value,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          if (widget.indicatorDetails?.indicatorDetails
                                  .contentClassificationKey ==
                              'official_statistics')
                            SvgPicture.asset(
                              AppImages.icOfficialActive,
                            )
                          else
                            SvgPicture.asset(
                              AppImages.icExperimentalActive,
                            ),
                        ],
                      ),
                      const SizedBox(height: 5),
                    ],
                  ),
                ),
                childWidget,
              ],
            ),
          ),
        ),
      ),
    );
  }
}
