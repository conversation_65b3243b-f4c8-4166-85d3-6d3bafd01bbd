import 'dart:convert';

import 'package:easy_localization/easy_localization.dart' as mt;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/functions/indicator_date_setting.dart';
import 'package:scad_mobile/src/common/functions/insight_discovery_value_setting.dart';
import 'package:scad_mobile/src/common/widgets/charts/column_chart.dart';
import 'package:scad_mobile/src/common/widgets/charts/spline_chart.dart';
import 'package:scad_mobile/src/common/widgets/charts/treemap_chart.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_response_helper.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/common/widgets/indicator_value.dart';
import 'package:scad_mobile/src/features/details/data/table_data_grid_source/table_data_grid_source.dart';
import 'package:scad_mobile/src/features/details/domain/usecases/frequency_selector_model.dart';
import 'package:scad_mobile/src/features/details/presentation/bloc/details_bloc.dart';
import 'package:scad_mobile/src/features/details/presentation/pages/details_page.dart';
import 'package:scad_mobile/src/features/details/presentation/widgets/data_table/custom_data_table.dart';
import 'package:scad_mobile/src/features/details/presentation/widgets/forecast_and_info_widget.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/extentions/color_extensions.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';
import 'package:syncfusion_flutter_core/theme.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:syncfusion_flutter_treemap/treemap.dart';

class CommonIndicatorView extends StatefulWidget {
  const CommonIndicatorView({
    required this.nodeId,
    required this.index,
    required this.isSearch,
    super.key,
    this.indicatorDetails,
    this.expandedWidget,
    this.isWhatIfDetails = true,
    this.onClearFilter,
    this.comparedIndicatorName,
    this.computedIndicatorName,
    this.originalIndicatorForFilter,
    this.chartHasDataFn,
  });

  final IndicatorDetailsResponseHelper? indicatorDetails;
  final String nodeId;
  final int index;
  final Widget? expandedWidget;
  final bool isWhatIfDetails;
  final VoidCallback? onClearFilter;
  final String? comparedIndicatorName;
  final String? computedIndicatorName;
  final IndicatorDetailsResponse? originalIndicatorForFilter;
  final void Function(bool b)? chartHasDataFn;
  final bool isSearch;

  @override
  State<CommonIndicatorView> createState() => _CommonIndicatorViewState();
}

class _CommonIndicatorViewState extends State<CommonIndicatorView> {
  String selectedType = 'line-chart';
  late IndicatorDetailsResponseHelper? indicatorDetails;

  List<List<Map<String, dynamic>>> solidSeries = [];
  List<List<Map<String, dynamic>>> filterDataOld = [];
  List<List<Map<String, dynamic>>> forecastSeries = [];
  List<Map<String, dynamic>> comparisonSeries = [];
  List<SeriesMeta> seriesMeta = [];
  List<SeriesMeta> treeMapSeriesMeta = [];
  String? chartType = '';
  bool forecastVisibility = true;
  bool showAllData = false;

  IndicatorDetailsResponse? oldIndicatorDetials;

  /// note -> dont remove/delete these variable!
  /// only using the below variable for applying the plotting of the chart
  /// and not for the actual forecast button
  bool isTempForecast = false;
  List<Options> optionsForFilter = [];
  int? monthIndexForFilter;
  String? selectedFrequencyForFilter;
  List<FrequencySelectorModel>? freqListForFilter;
  Visualizations? selectedVisualization;

  /// To Check if computation is Applied or not
  bool isComputationActive = false;

  ScrollController scrollController = ScrollController();

  @override
  void initState() {
    indicatorDetails = widget.indicatorDetails;

    oldIndicatorDetials = IndicatorDetailsResponse.fromJson(
        jsonDecode(jsonEncode(widget.originalIndicatorForFilter))
            as Map<String, dynamic>);
    chartType =
        indicatorDetails?.getFilteredVisualizationMetaList().firstOrNull?.type;
    seriesMeta = indicatorDetails
            ?.getFilteredVisualizationMetaList()
            .firstOrNull
            ?.seriesMeta ??
        [];

    solidSeries.clear();
    forecastSeries.clear();

    if (indicatorDetails?.indicatorDetails.type == 'insights-discovery') {
      final String defaultVisualization =
          indicatorDetails?.indicatorDetails.defaultVisualisation ?? '';
      Visualizations? selectedvisualizationComponent;

      for (final element in indicatorDetails?.indicatorDetails.visualizations ??
          <Visualizations>[]) {
        if (element.id == defaultVisualization) {
          selectedvisualizationComponent = element;
          break;
        }
      }

      context.read<DetailsBloc>().add(
            FilterApplyEvent(
              propertyList: const [],
              originlIndicatorDetails: indicatorDetails?.indicatorDetails ??
                  IndicatorDetailsResponse(),
              insightToFilterData: selectedvisualizationComponent
                      ?.indicatorVisualizations
                      ?.visualizationsMeta
                      ?.first
                      .seriesMeta
                      ?.first
                      .data ??
                  [],
              selectedVisualization: selectedvisualizationComponent,
            ),
          );
    } else {
      for (final item in seriesMeta) {
        if (item.id!.contains('-forecast')) {
          forecastSeries.add(
            widget.indicatorDetails!
                .getFilteredSeries(seriesMetaIndex: seriesMeta.indexOf(item)),
          );
        } else {
          solidSeries.add(
            widget.indicatorDetails!
                .getFilteredSeries(seriesMetaIndex: seriesMeta.indexOf(item)),
          );
        }
      }

      if (widget.isSearch) {
        context.read<DetailsBloc>().add(
              FilterApplyEvent(
                propertyList: const [],
                originlIndicatorDetails: widget.originalIndicatorForFilter ??
                    IndicatorDetailsResponse(),
                // insightToFilterData: a,
                isForeCast: forecastSeries.isNotEmpty,
              ),
            );
      }
    }
    isComputationActive = indicatorDetails?.indicatorDetails
            ?.indicatorVisualizations?.visualizationsMeta?.firstOrNull?.id ==
        'compare-chart';
    if (chartType == 'tree-map-with-change-chart') {
      treeMapSeriesMeta = seriesMeta;
    }

    freqListForFilter = getFrequencyList(isFirstTime: true);
    // final indicatorDateSetting =
    //       IndicatorDateSetting.setFrequancy(l: solidSeries, indicatorDetails: indicatorDetails);
    //       selectedFrequencyForFilter = indicatorDateSetting['selectedFrequencyForFilter']  as String;
    super.initState();
  }

  void _updateSolidSeries(BuildContext context, {bool isCompareTrue = false}) {
    context.read<DetailsBloc>().add(
          SolidStateUpdateEvent(
              isCompareTrue: isCompareTrue,
              seriesList: solidSeries,
              forecastSeries: forecastSeries,
              selectedFrequencyForFilter: selectedFrequencyForFilter ?? ''),
        );
  }

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return SizedBox(
      child: Padding(
        padding: const EdgeInsets.all(15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            SizedBox(
              height: ((widget.comparedIndicatorName ?? '').isNotEmpty
                      ? 240
                      : widget.onClearFilter != null
                          ? 270
                          : (widget.computedIndicatorName ?? '').isNotEmpty
                              ? 270
                              : 305) +
                  (textScaleFactor.value > 1 ? 12 : 0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        /// The Chart representation
                        BlocConsumer<DetailsBloc, DetailsState>(
                          listener: (context, state) {
                            if (state is PresentationTypeSelectionState) {
                              selectedType = state.selectedType;
                              // if ((indicatorDetails
                              //             ?.indicatorDetails.indicatorDrivers ??
                              //         [])
                              //     .isNotEmpty) {
                              //   context.read<DetailsBloc>().add(
                              //         ChangeDriversValueUpdateEvent(
                              //           indicatorDetails: indicatorDetails,
                              //         ),
                              //       );
                              // }
                            } else if (state is ChangeDriversValueUpdateState) {
                              indicatorDetails = state.indicatorDetails;

                              seriesMeta = indicatorDetails
                                      ?.indicatorDetails
                                      .indicatorVisualizations!
                                      .visualizationsMeta!
                                      .firstOrNull
                                      ?.seriesMeta ??
                                  [];
                              if (chartType == 'tree-map-with-change-chart') {
                                treeMapSeriesMeta = seriesMeta;
                              }
                              solidSeries.clear();
                              forecastSeries.clear();
                              for (final item in seriesMeta) {
                                if (item.id!.contains('-forecast')) {
                                  forecastSeries.add(
                                    widget.indicatorDetails!.getFilteredSeries(
                                      seriesMetaIndex: seriesMeta.indexOf(item),
                                    ),
                                  );
                                } else {
                                  solidSeries.add(
                                    widget.indicatorDetails!.getFilteredSeries(
                                      seriesMetaIndex: seriesMeta.indexOf(item),
                                    ),
                                  );
                                }
                              }
                              final indicatorDateSetting =
                                  IndicatorDateSetting.setFrequancy(
                                      l: solidSeries,
                                      indicatorDetails: indicatorDetails);
                              selectedFrequencyForFilter = indicatorDateSetting[
                                  'selectedFrequencyForFilter'] as String;
                              _updateSolidSeries(context);
                            } else if (state is CompareIndicatorSuccessState) {
                              solidSeries.clear();

                              oldIndicatorDetials = IndicatorDetailsResponse
                                  .fromJson(jsonDecode(jsonEncode(
                                          widget.originalIndicatorForFilter))
                                      as Map<String, dynamic>);

                              indicatorDetails = IndicatorDetailsResponseHelper(
                                state.indicatorDetails,
                              );

                              final visualizationMeta = (indicatorDetails
                                          ?.indicatorDetails
                                          .indicatorVisualizations
                                          ?.visualizationsMeta ??
                                      [])
                                  .first;

                              if ((visualizationMeta.seriesMeta ?? []).length >
                                      1 &&
                                  visualizationMeta.id == 'compare-chart') {
                                // solidSeries.clear();
                                for (int i = 0;
                                    i <
                                        (visualizationMeta.seriesMeta ?? [])
                                            .length;
                                    i++) {
                                  var sorting = indicatorDetails!
                                      .getFilteredSeries(seriesMetaIndex: i);

                                  sorting.sort(
                                    (a, b) => a['OBS_DT']
                                        .toString()
                                        .compareTo(b['OBS_DT'].toString()),
                                  );

                                  sorting.firstOrNull?['legend_title'] =
                                      visualizationMeta.seriesTitles?.values
                                          .toList()[i];

                                  solidSeries.add(sorting);

                                  // data.add(
                                  //     indicatorDetails!.getFilteredSeries());
                                }

                                solidSeries.sort(
                                  (a, b) => a.first['OBS_DT']
                                      .toString()
                                      .compareTo(b.first['OBS_DT'].toString()),
                                );

                                context.read<DetailsBloc>().add(
                                      UpdateLegendEvent(
                                        seriesList: solidSeries,
                                        filterList: filterDataOld,
                                        filterKey: 'legend_title',
                                      ),
                                    );

                                // comparisonSeries = indicatorDetails!
                                //     .getFilteredSeries(seriesMetaIndex: 1);

                                // solidSeries.add(comparisonSeries);
                              }
                              // Map<String, dynamic> dataFrequacyChange =
                              //     IndicatorDateSetting.setFrequancy(
                              //         l: solidSeries);
                              // solidSeries = dataFrequacyChange['value']
                              //     as List<List<Map<String, dynamic>>>;
                              selectedFrequencyForFilter = 'Monthly';

                              for (final FrequencySelectorModel element
                                  in freqListForFilter ?? []) {
                                element.isSelected = false;
                                if (element.title ==
                                    selectedFrequencyForFilter) {
                                  element.isSelected = true;
                                }
                              }
                              _updateSolidSeries(context, isCompareTrue: true);
                            } else if (state is DetailsPageFilterResetState) {
                              comparisonSeries.clear();
                              // indicatorDetails = state.indicatorDetails;
                              indicatorDetails = IndicatorDetailsResponseHelper(
                                  oldIndicatorDetials ??
                                      IndicatorDetailsResponse());

                              chartType = indicatorDetails
                                  ?.getFilteredVisualizationMetaList()
                                  .first
                                  .type;
                              seriesMeta = indicatorDetails
                                      ?.getFilteredVisualizationMetaList()
                                      .firstOrNull
                                      ?.seriesMeta ??
                                  [];
                              solidSeries.clear();
                              forecastSeries.clear();
                              for (final item in seriesMeta) {
                                if (item.id!.contains('-forecast')) {
                                  forecastSeries.add(
                                    widget.indicatorDetails!.getFilteredSeries(
                                      seriesMetaIndex: seriesMeta.indexOf(item),
                                    ),
                                  );
                                } else {
                                  solidSeries.add(
                                    widget.indicatorDetails!.getFilteredSeries(
                                      seriesMetaIndex: seriesMeta.indexOf(item),
                                    ),
                                  );
                                }
                              }
                              if (chartType == 'tree-map-with-change-chart') {
                                treeMapSeriesMeta = seriesMeta;
                              }

                              context.read<DetailsBloc>().add(
                                    FilterApplyEvent(
                                      propertyList: const [],
                                      originlIndicatorDetails:
                                          indicatorDetails?.indicatorDetails ??
                                              IndicatorDetailsResponse(),
                                      insightToFilterData: [],
                                    ),
                                  );
                              _updateSolidSeries(context);
                            } else if (state is FilterApplyState) {
                              solidSeries.clear();
                              solidSeries = List.from(
                                state.filteredDataList ?? [],
                              );
                              showAllData = true;
                              isTempForecast = state.isForecast ?? false;
                              // solidSeries[0].clear();
                              // solidSeries[0]
                              //     .addAll((state.filteredData ?? []).toList());
                              filterDataOld = List.generate(
                                solidSeries.length,
                                (outerIndex) => List.generate(
                                    solidSeries[outerIndex].length,
                                    (innerIndex) => jsonDecode(
                                          jsonEncode(
                                            solidSeries[outerIndex][innerIndex],
                                          ),
                                        ) as Map<String, dynamic>),
                              );
                              // if (isTempForecast) {
                              //   indicatorDetails
                              //       ?.indicatorDetails
                              //       .indicatorVisualizations
                              //       ?.visualizationsMeta
                              //       ?.first
                              //       .seriesMeta
                              //       ?.first
                              //       .data = solidSeries.first;
                              // }
                              final indicatorDateSetting =
                                  IndicatorDateSetting.setFrequancy(
                                      l: solidSeries,
                                      indicatorDetails: indicatorDetails);
                              selectedFrequencyForFilter = indicatorDateSetting[
                                  'selectedFrequencyForFilter'] as String;
                              if (isComputationActive) {
                                context.read<DetailsBloc>().add(
                                      UpdateLegendEvent(
                                        seriesList: solidSeries,
                                        filterList: filterDataOld,
                                        filterKey: 'legend_title',
                                      ),
                                    );
                              }

                              if (optionsForFilter.isNotEmpty) {
                                context.read<DetailsBloc>().add(
                                      MonthYearFilterEvent(
                                        indicatorData: indicatorDetails
                                                ?.indicatorDetails ??
                                            IndicatorDetailsResponse(),
                                        options: optionsForFilter,
                                        index: monthIndexForFilter ?? 0,
                                        filteredDataList: solidSeries,
                                      ),
                                    );
                              } else {
                                // if (selectedFrequencyForFilter == null) {
                                //   freqListForFilter = getFrequencyList(
                                //     isFirstTime: true,
                                //   );
                                // }
                                //                               if ((freqListForFilter ?? []).isNotEmpty) {
                                //                                 selectedFrequencyForFilter = freqListForFilter
                                //                                     ?.where((element) => element.isSelected)
                                //                                     .first
                                //                                     .title;

                                //                                 context.read<DetailsBloc>().add(
                                //                                       ChangeDataFrequencyApplyEvent(
                                //                                         selectedFrequency:
                                //                                             selectedFrequencyForFilter ?? '',
                                //                                         freqList: List.generate(
                                //                                           (freqListForFilter ?? []).length,
                                //                                           (index) =>
                                //                                               FrequencySelectorModel.fromJson(
                                //                                             jsonDecode(
                                //                                               jsonEncode(
                                //                                                 freqListForFilter?[index],
                                //                                               ),
                                //                                             ) as Map<String, dynamic>,
                                //                                           ),
                                //                                         ),
                                //                                         inidicatorData:
                                //                                             IndicatorDetailsResponse.fromJson(
                                //                                                 jsonDecode(jsonEncode(
                                //                                                         indicatorDetails
                                //                                                             ?.indicatorDetails))
                                //                                                     as Map<String, dynamic>),
                                //                                         filteredDataList: solidSeries,
                                //                                       ),
                                //                                     );
                                //                               } else if (solidSeries.isNotEmpty) {
                                //                                  final indicatorDateSetting =
                                //       IndicatorDateSetting.setFrequancy(l: solidSeries, indicatorDetails: indicatorDetails);

                                // selectedFrequencyForFilter = indicatorDateSetting['selectedFrequencyForFilter']  as String;

                                //                               }
                              }
                              _updateSolidSeries(context);
                            } else if (state is ForecastVisibilityUpdateState) {
                              forecastVisibility = state.isVisibilityOn;
                              // context.read<DetailsBloc>().add(
                              //   FilterApplyEvent(
                              //     propertyList: const [],
                              //     originlIndicatorDetails:
                              //    widget.originalIndicatorForFilter ?? IndicatorDetailsResponse(),
                              //     // insightToFilterData: a,
                              //     isForeCast: isTempForecast,
                              //   ),
                              // );
                            } else if (state is MonthYearFilterState) {
                              optionsForFilter = state.options;
                              monthIndexForFilter = state.index;
                              solidSeries.clear();
                              solidSeries = List.from(state.filteredData);
                              showAllData = true;

                              // if (isTempForecast) {
                              //   indicatorDetails
                              //       ?.indicatorDetails
                              //       .indicatorVisualizations
                              //       ?.visualizationsMeta
                              //       ?.first
                              //       .seriesMeta
                              //       ?.first
                              //       .data = state.filteredData.first;
                              // }

                              // if (selectedFrequencyForFilter == null) {
                              //   freqListForFilter = getFrequencyList(
                              //     isFirstTime: true,
                              //   );
                              //   selectedFrequencyForFilter = freqListForFilter
                              //       ?.where((element) => element.isSelected)
                              //       .first
                              //       .title;
                              // }
                              if (state.isFromVisulization != true) {
                                // freqListForFilter = getFrequencyList(
                                //   isFirstTime: false,
                                // );
                                context.read<DetailsBloc>().add(
                                      ChangeDataFrequencyApplyEvent(
                                        selectedFrequency:
                                            selectedFrequencyForFilter ?? '',
                                        freqList: List.generate(
                                          (freqListForFilter ?? []).length,
                                          (index) =>
                                              FrequencySelectorModel.fromJson(
                                            jsonDecode(
                                              jsonEncode(
                                                freqListForFilter?[index],
                                              ),
                                            ) as Map<String, dynamic>,
                                          ),
                                        ),
                                        inidicatorData:
                                            IndicatorDetailsResponse.fromJson(
                                                jsonDecode(jsonEncode(
                                                        indicatorDetails
                                                            ?.indicatorDetails))
                                                    as Map<String, dynamic>),
                                        filteredDataList: solidSeries,
                                        forecastSeriesList: forecastSeries,
                                        isFromMonthly: true,
                                      ),
                                    );
                              }

                              _updateSolidSeries(context);
                            } else if (state is DataFrequencyApplyState) {
                              selectedFrequencyForFilter =
                                  state.selectedFrequency;

                              freqListForFilter = List.generate(
                                state.freqList.length,
                                (index) => FrequencySelectorModel.fromJson(
                                  jsonDecode(
                                    jsonEncode(
                                      state.freqList[index],
                                    ),
                                  ) as Map<String, dynamic>,
                                ),
                              );
                              solidSeries.clear();
                              forecastSeries.clear();
                              solidSeries = List.from(state.filteredData);
                              showAllData = true;
                              if (isTempForecast) {
                                forecastSeries = state.forecastData ?? [];

                                // final List<SeriesMeta> dataElement =
                                //     indicatorDetails
                                //             ?.indicatorDetails
                                //             .indicatorVisualizations
                                //             ?.visualizationsMeta
                                //             ?.first
                                //             .seriesMeta ??
                                //         [];

                                // for (var i = 0; i < dataElement.length; i++) {
                                //   if (dataElement[i]
                                //       .id!
                                //       .contains('-forecast')) {
                                //     dataElement[i].data =
                                //         state.forecastData?[i];
                                //   }
                                // }

                                // indicatorDetails
                                //     ?.indicatorDetails
                                //     .indicatorVisualizations
                                //     ?.visualizationsMeta
                                //     ?.first
                                //     .seriesMeta
                                //     ?.first
                                //     .data = state.filteredData.first;
                              }
                              if (state.isFromMonthly != true &&
                                  optionsForFilter.isNotEmpty) {
                                context.read<DetailsBloc>().add(
                                      MonthYearFilterEvent(
                                          indicatorData: indicatorDetails
                                                  ?.indicatorDetails ??
                                              IndicatorDetailsResponse(),
                                          options: optionsForFilter,
                                          index: monthIndexForFilter ?? 0,
                                          filteredDataList: solidSeries,
                                          isFromVisulization: true),
                                    );
                              }

                              _updateSolidSeries(context);
                            } else if (state is ComputeDataSuccessState) {
                              isComputationActive = true;
                              solidSeries.clear();
                              indicatorDetails = IndicatorDetailsResponseHelper(
                                state.indicatorDetails,
                              );

                              solidSeries.add(
                                List.from(
                                  state
                                          .indicatorDetails
                                          .indicatorVisualizations
                                          ?.visualizationsMeta
                                          ?.first
                                          .seriesMeta
                                          ?.first
                                          .data ??
                                      [],
                                ),
                              );

                              for (var i = 0; i < solidSeries.length; i++) {
                                solidSeries[i].sort(
                                  (a, b) => a['OBS_DT']
                                      .toString()
                                      .compareTo(b['OBS_DT'].toString()),
                                );
                              }
                              Map<String, dynamic> dataFrequacyChange =
                                  IndicatorDateSetting.setFrequancy(
                                      l: solidSeries);
                              solidSeries = dataFrequacyChange['value']
                                  as List<List<Map<String, dynamic>>>;
                              selectedFrequencyForFilter = dataFrequacyChange[
                                  'selectedFrequencyForFilter'] as String;

                              for (final FrequencySelectorModel element
                                  in freqListForFilter ?? []) {
                                element.isSelected = false;
                                if (element.title ==
                                    selectedFrequencyForFilter) {
                                  element.isSelected = true;
                                }
                              }
                              _updateSolidSeries(context);
                            } else if (state is SelectVisualizationState) {
                              /// for insight-discovery
                              selectedVisualization =
                                  state.selectedVisualization;
                              solidSeries
                                ..clear()
                                ..add(
                                  indicatorDetails?.getFilteredSeries(
                                        filterVisualization:
                                            state.selectedVisualization?.id,
                                      ) ??
                                      [],
                                );

                              _updateSolidSeries(context);
                            } else if (state
                                is InsightDiscoveryFullDataLoadState) {
                            } else if (state is ResetChangeDriversState) {
                              // scadProjectionValue = state.scadProjectionValue;
                              // if (toResetChangeDrivers) {
                              //   toResetChangeDrivers = state.toReset;
                              // }
                              if (state.toReset == false) {
                                // indicatorDetails =

                                // context.read<DetailsBloc>().add(
                                //     FilterApplyEvent(
                                //         propertyList: [],
                                //         originlIndicatorDetails: state
                                //             .indicatorVal!.indicatorDetails),);

                                solidSeries.clear();
                                forecastSeries.clear();

                                indicatorDetails =
                                    IndicatorDetailsResponseHelper(
                                  state.indicatorVal!.indicatorDetails,
                                );

                                for (final item in indicatorDetails
                                        ?.indicatorDetails
                                        .indicatorVisualizations
                                        ?.visualizationsMeta
                                        ?.first
                                        .seriesMeta ??
                                    <SeriesMeta>[]) {
                                  if (item.id!.contains('-forecast')) {
                                    forecastSeries.add(
                                      indicatorDetails!.getFilteredSeries(
                                          seriesMetaIndex:
                                              seriesMeta.indexOf(item)),
                                    );
                                  } else {
                                    solidSeries.add(
                                      indicatorDetails!.getFilteredSeries(
                                          seriesMetaIndex:
                                              seriesMeta.indexOf(item)),
                                    );
                                  }
                                }
                              }
                            }

                            if (widget.isWhatIfDetails) {
                              widget.chartHasDataFn?.call(true);
                            } else {
                              widget.chartHasDataFn?.call(
                                !solidSeries
                                    .every((element) => element.isEmpty)  || !forecastSeries.every(
                                        (element) => element.isEmpty),
                              );
                            }
                          },
                          builder: (context, state) {
                            return Flexible(
                              child: Column(
                                children: [
                                  if (!isComputationActive)
                                    IndicatorValue(
                                      negativeArrow: indicatorDetails
                                                  ?.indicatorDetails.type ==
                                              'insights-discovery'
                                          ? double.parse(IsightDiscoveryValueSetting
                                                          .getValue(
                                                              selectedVisualization) ??
                                                      '0.0') >
                                                  0
                                              ? false
                                              : true
                                          : indicatorDetails?.negativeArrow ??
                                              false,
                                      value: widget.isWhatIfDetails
                                          ? (widget.indicatorDetails
                                                  ?.getFilteredSeriesForMultiDrivers(
                                                    visualizationsMetaIndex:
                                                        widget.index,
                                                  )
                                                  .first
                                                  .last['VALUE'] as double)
                                              .toStringAsFixed(2)
                                          : indicatorDetails
                                                      ?.indicatorDetails.type ==
                                                  'insights-discovery'
                                              ? IsightDiscoveryValueSetting
                                                      .getValue(
                                                          selectedVisualization) ??
                                                  ''
                                              : indicatorDetails!.value,
                                      unit: indicatorDetails
                                                  ?.indicatorDetails.type ==
                                              'insights-discovery'
                                          ? IsightDiscoveryValueSetting.getUnit(
                                              indicatorDetails
                                                  ?.indicatorDetails,
                                            )
                                          : indicatorDetails?.unit ?? '',
                                      numberUnit: indicatorDetails
                                                  ?.indicatorDetails.type ==
                                              'insights-discovery'
                                          ? IsightDiscoveryValueSetting
                                                  .getNumberUnit(
                                                selectedVisualization,
                                              ) ??
                                              ''
                                          : indicatorDetails?.numberUnit ?? '',
                                      onClearFilter: widget.onClearFilter,
                                      comparedIndicatorName:
                                          widget.comparedIndicatorName,
                                      indicatorDetails: indicatorDetails,
                                    ),
                                  if (widget.onClearFilter == null) ...[
                                    if (!isComputationActive)
                                      const SizedBox(height: 10),
                                    ForecastAndInfoWidget(
                                      indicatorDetails: indicatorDetails ??
                                          IndicatorDetailsResponseHelper(
                                              indicatorDetails
                                                      ?.indicatorDetails ??
                                                  IndicatorDetailsResponse()),
                                      isForecast: seriesMeta.length > 1 &&
                                          (chartType !=
                                              'tree-map-with-change-chart'),
                                    ),
                                  ],

                                  const SizedBox(height: 15),

                                  /// TO display What If Forecast Chart
                                  if (widget.isWhatIfDetails)
                                    _whatIfLineChartRepresentation()

                                  /// To Display Tree Map
                                  else
                                    Stack(
                                      children: [
                                        _noDataMsg(isLightMode, state),
                                        if (!solidSeries.every(
                                            (element) => element.isEmpty) || !forecastSeries.every(
                                                (element) => element.isEmpty) )
                                          ColoredBox(
                                            color: isLightMode
                                                ? AppColors.white
                                                : AppColors.blueShade32,
                                            child: chartType ==
                                                    'tree-map-with-change-chart'
                                                ? selectedType == 'table-view'
                                                    ? tableView(
                                                        isForTreeMap: true,
                                                        isLightMode:
                                                            isLightMode,
                                                      )
                                                    : _treeMapChartRepresentation(
                                                        treeMapSeriesMeta,
                                                      )

                                                /// To display Line Chart, Bar Chart, Table View
                                                : _typeWiseChartRepresentation(
                                                    indicatorDetails
                                                        ?.getFilteredVisualizationMetaList()
                                                        .first
                                                        .id,
                                                    isLightMode,
                                                  ),
                                          ),
                                      ],
                                    ),
                                ],
                              ),
                            );
                          },
                        ),
                        const SizedBox(height: 12),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            widget.expandedWidget ?? const SizedBox(),
          ],
        ),
      ),
    );
  }

  Widget _noDataMsg(bool isLightMode, DetailsState state) {
    return AnimatedOpacity(
      duration: const Duration(milliseconds: 300),
      opacity: state is SolidStateUpdateState &&
              solidSeries.every((element) => element.isEmpty)
          ? 1
          : 0,
      child: Padding(
        padding: const EdgeInsets.only(top: 50),
        child: Text(
          LocaleKeys.filterEmptyMessage.tr(),
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: isLightMode ? AppColors.black : AppColors.white,
          ),
          textAlign: TextAlign.center,
          textScaler: TextScaler.linear(
            textScaleFactor.value,
          ),
        ),
      ),
    );
  }

  Widget _treeMapChartRepresentation(List<SeriesMeta> treeMapSeriesMeta) {
    final List<TreemapColorMapper> colorMappers = [];
    for (int i = 0; i < treeMapSeriesMeta.length; i++) {
      colorMappers.add(
        TreemapColorMapper.value(
          value: treeMapSeriesMeta[i].data?.first['CHANGE'].toString(),
          color: treeMapSeriesMeta[i].color.toString().toColor(),
        ),
      );
    }
    return FittedBox(
      child: SizedBox(
        height: 210,// * (textScaleFactor.value < 1 ? 1 : textScaleFactor.value),
        width: MediaQuery.sizeOf(context).width * 0.9,
        child: TreemapChart(
          key: Key(
              'treemap.common.indicator.${treeMapSeriesMeta.map((e) => e.color).join()}'),
          chartSeriesData: treeMapSeriesMeta,
          // colorMappers: colorMappers,
          // chartData: treeMapSeriesMeta
          //     .map(
          //       (e) => TreeMapChartData(
          //         sector:
          //             e.data?[0][rtl ? 'SECTOR_AR' : 'SECTOR'].toString() ?? '',
          //         currentValue: double.parse(
          //           '${e.data?[0]['VALUE_CURRENT'] ?? '0'}',
          //         ),
          //         forecastedValue: double.parse(
          //           '${e.data?[0]['VALUE_FORECAST'] ?? '0'}',
          //         ),
          //         qqChange: double.parse(
          //           '${e.data?[0]['CHANGE'] ?? '0'}',
          //         ),
          //         yyChange: double.parse(
          //           '${e.data?[0]['CHANGE_PY'] ?? '0'}',
          //         ),
          //         proportionOfTotalEconomy: double.parse(
          //           '${e.data?[0]['VALUE_PERC_ECO'] ?? '0'}',
          //         ),
          //       ),
          //     )
          //     .toList(),
        ),
      ),
    );
  }

  Column _typeWiseChartRepresentation(
    String? visualizationMetaId,
    bool isLightMode,
  ) {
    final List<List<SplineChartData>> solidSplineChartMeta = [];
    final List<List<ColumnChartData>> solidColumnChartMeta = [];

    // if (comparisonSeries.isNotEmpty) {
    //   solidSeries.insert(0, comparisonSeries);
    // }

    for (final series in solidSeries) {
      if (showAllData) {
        solidSplineChartMeta.add(
          series
              .map(
                (e) => SplineChartData(
                  e['OBS_DT'].toString(),
                  double.parse('${e['VALUE'] ?? '0'}'),
                  legend: series.first['legend_title'] as String?,
                ),
              )
              .toList(),
        );
      } else {
        solidSplineChartMeta.add(
          series
              .map(
                (e) => SplineChartData(
                  e['OBS_DT'].toString(),
                  double.parse('${e['VALUE'] ?? '0'}'),
                ),
              )
              .toList(),
          // .limitListLength(),
        );
      }

      if (showAllData) {
        solidColumnChartMeta.add(
          series
              .map(
                (e) => ColumnChartData(
                  e['OBS_DT'].toString(),
                  double.parse('${e['VALUE'] ?? '0'}'),
                  0,
                  0,
                ),
              )
              .toList(),
        );
      } else {
        solidColumnChartMeta.add(
          series
              .map(
                (e) => ColumnChartData(
                  e['OBS_DT'].toString(),
                  double.parse('${e['VALUE'] ?? '0'}'),
                  0,
                  0,
                ),
              )
              .toList(),
          // .limitListLength(),
        );
      }
    }

    return Column(
      children: [
        if (selectedType == 'line-chart')
          SizedBox(
            height: 210,
            child: (indicatorDetails?.indicatorDetails.indicatorDrivers ?? [])
                        .isNotEmpty ||
                    seriesMeta
                        .map((e) => e.id!.contains('-forecast'))
                        .contains(true)
                ? _whatIfLineChartRepresentation()
                : SplineChart(
                    // comparisonChartData: visualizationMetaId == 'compare-chart'
                    //     ? widget.comparedIndicatorName != null
                    //         ? solidSplineChartMeta.last
                    //         : comparisonSeries
                    //             .map(
                    //               (e) => SplineChartData(
                    //                 e['OBS_DT'].toString(),
                    //                 double.parse('${e['VALUE'] ?? '0'}'),
                    //               ),
                    //             )
                    //             .toList()
                    //     : [],
                    frequance: selectedFrequencyForFilter ?? '',
                    isCompareActive: visualizationMetaId == 'compare-chart',
                    chartDataList: solidSplineChartMeta,
                  ),
          )
        else if (selectedType == 'bar-chart')
          SizedBox(
            height: 210,
            child: /*(indicatorDetails?.indicatorDetails.indicatorDrivers ?? [])
                    .isNotEmpty
                ? _whatIfColumnChartRepresentation()
                :*/
                ColumnChart(
              frequance: selectedFrequencyForFilter ?? '',
              isCompareActive: visualizationMetaId == 'compare-chart',
              chartDataList: widget.comparedIndicatorName != null
                  ? solidColumnChartMeta
                  : solidColumnChartMeta,
              // comparisonChartData: visualizationMetaId == 'compare-chart'
              //     ? widget.comparedIndicatorName != null
              //         ? solidColumnChartMeta
              //         : solidColumnChartMeta
              //     : [],
            ),
          )
        else
          tableView(isLightMode: isLightMode),
      ],
    );
  }

  Widget _whatIfLineChartRepresentation() {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    final List<List<SplineChartData>> nowCast = [];
    final List<List<SplineChartData>> foreCast = [];
    final List<List<SplineChartData>> lowerAreaForecast = [];
    final List<List<SplineChartData>> upperAreaForecast = [];
    List<List<SplineChartData>> areaForecast = [];
    List<List<Map<String, dynamic>>> l =
        indicatorDetails!.getFilteredSeriesForMultiDrivers(
      visualizationsMetaIndex: widget.index,
    );

    if (showAllData) {
      for (var i = 0; i < solidSeries.length; i++) {
        nowCast.add(
          solidSeries[i]
              .map(
                (e) => SplineChartData(
                  e['OBS_DT'].toString(),
                  num.parse('${e['VALUE'] ?? '0'}'),
                ),
              )
              .toList(),
        );
      }
    }

    if (showAllData) {
      for (int i = 0; i < forecastSeries.length; i++) {
        foreCast.add(
          forecastSeries[i]
              .map(
                (e) => SplineChartData(
                  e['OBS_DT'].toString(),
                  num.parse('${e['VALUE'] ?? '0'}'),
                ),
              )
              .toList(),
        );

        // for (int i = 0; i < forecastSeries.length; i++) {
        List<SplineChartData> data = [];

        for (var e in forecastSeries[i]) {
          if (e['VALUE_LL'] != null) {
            data.add(
              SplineChartData(
                e['OBS_DT'].toString(),
                num.parse('${e['VALUE'] ?? '0'}'),
                y: num.parse('${e['VALUE_LL'] ?? '0'}'),
              ),
            );
          }
        }

        lowerAreaForecast.add(data);
        // }

        // for (int i = 0; i < forecastSeries.length; i++) {
        List<SplineChartData> dataUl = [];

        for (var e in forecastSeries[i]) {
          if (e['VALUE_UL'] != null) {
            dataUl.add(
              SplineChartData(
                e['OBS_DT'].toString(),
                num.parse('${e['VALUE'] ?? '0'}'),
                y: num.parse('${e['VALUE_UL'] ?? '0'}'),
              ),
            );
          }
        }

        upperAreaForecast.add(dataUl);
        // }
        // areaForecast = [...lowerAreaForecast, ...upperAreaForecast];
      }
      areaForecast = [...lowerAreaForecast, ...upperAreaForecast];
    } else {
      for (var i = 0; i < l.length; i++) {
        if (seriesMeta[i].id!.contains('-forecast')) {
          // if (context.read<DetailsBloc>().selectedFrequency == 'Monthly') {
          //   l[i] = IndicatorDateSetting.setUpNameMonth(list: [l[i]])[0];
          // } else if (selectedFrequencyForFilter == 'Quarterly') {
          //   l[i] = IndicatorDateSetting.setUpName(list: [l[i]])[0];
          // } else {
          //   l[i] =
          //       IndicatorDateSetting.setUpName(list: [l[i]], type: 'yearly')[0];
          // }

          foreCast.add(
            l[i]
                .map(
                  (e) => SplineChartData(
                    e['OBS_DT'].toString(),
                    num.parse('${e['VALUE'] ?? '0'}'),
                  ),
                )
                .toList(),
            // .limitListLengthforCast(),
          );

          for (int i = 0; i < l.length; i++) {
            List<SplineChartData> data = [];

            for (var e in l[i]) {
              if (e['VALUE_LL'] != null) {
                data.add(
                  SplineChartData(
                    e['OBS_DT'].toString(),
                    num.parse('${e['VALUE'] ?? '0'}'),
                    y: num.parse('${e['VALUE_LL'] ?? '0'}'),
                  ),
                );
              }
            }

            // lowerAreaForecast.add(data.limitListLengthforCast());
            lowerAreaForecast.add(data);
          }

          for (int i = 0; i < l.length; i++) {
            List<SplineChartData> data = [];

            for (var e in l[i]) {
              if (e['VALUE_UL'] != null) {
                data.add(
                  SplineChartData(
                    e['OBS_DT'].toString(),
                    num.parse('${e['VALUE'] ?? '0'}'),
                    y: num.parse('${e['VALUE_UL'] ?? '0'}'),
                  ),
                );
              }
            }

            // upperAreaForecast.add(data.limitListLengthforCast());
            upperAreaForecast.add(data);
          }

          areaForecast = [...lowerAreaForecast, ...upperAreaForecast];
        } else {
          nowCast.add(
            l[i]
                .map(
                  (e) => SplineChartData(
                    e['OBS_DT'].toString(),
                    num.parse('${e['VALUE'] ?? '0'}'),
                  ),
                )
                .toList(),
            // .limitListLength(),
          );
        }
      }
    }

    final indicatorDateSetting = IndicatorDateSetting.setFrequancy(
        l: l, indicatorDetails: indicatorDetails);
    String selectedFrequencyForFilterLocal =
        indicatorDateSetting['selectedFrequencyForFilter'] as String;

    return SizedBox(
      height: 200,
      child: SplineChart(
        indicatorCard: widget.isWhatIfDetails && isLightMode,
        frequance: selectedFrequencyForFilter == null
            ? selectedFrequencyForFilterLocal
            : selectedFrequencyForFilter ?? '',
        isForecast: forecastVisibility,
        chartDataList: nowCast,
        forecastChartDataList: foreCast,
        areaHighlightChartData: areaForecast,
        comparisonChartData: [],
      ),
    );
  }

  Widget tableView({required bool isLightMode, bool isForTreeMap = false}) {
    List<SeriesMeta> seriesMeta = [];
    List<List<Map<String, dynamic>>> seriesDataList = [];
    List<Map<String, dynamic>> comparedData = [];
    if (indicatorDetails?.indicatorDetails.indicatorVisualizations != null) {
      final visualizationMeta = (indicatorDetails?.indicatorDetails
                  .indicatorVisualizations?.visualizationsMeta ??
              [])
          .first;
      if ((visualizationMeta.seriesMeta ?? []).length > 1 &&
          visualizationMeta.id == 'compare-chart') {
        comparedData = indicatorDetails!.getFilteredSeries(seriesMetaIndex: 1);
      }
    }
    if (solidSeries.isNotEmpty) {
      seriesDataList = List.generate(
          (solidSeries ?? []).length,
          (outerIndex) => List.generate(
              solidSeries[outerIndex].length,
              (innerIndex) => jsonDecode(
                    jsonEncode(
                      solidSeries[outerIndex][innerIndex],
                    ),
                  ) as Map<String, dynamic>));
    } else {
      seriesMeta = indicatorDetails?.indicatorDetails.indicatorVisualizations!
              .visualizationsMeta!.firstOrNull?.seriesMeta ??
          [];

      seriesDataList = List.generate(
          (indicatorDetails?.getFilteredSeriesForMultiDrivers() ?? []).length,
          (outerIndex) => List.generate(
              (indicatorDetails?.getFilteredSeriesForMultiDrivers() ??
                      [])[outerIndex]
                  .length,
              (innerIndex) => jsonDecode(
                    jsonEncode(
                      indicatorDetails
                              ?.getFilteredSeriesForMultiDrivers()[outerIndex]
                          [innerIndex],
                    ),
                  ) as Map<String, dynamic>));
      ;
    }

    if(forecastSeries.isNotEmpty){
      seriesDataList.addAll(List.generate(
        forecastSeries.length,
            (outerIndex) => List.generate(
          forecastSeries[outerIndex].length,
              (innerIndex) => jsonDecode(
            jsonEncode(
              forecastSeries[outerIndex][innerIndex],
            ),
          ) as Map<String, dynamic>,),),);
    }

    for (int i = 0; i < seriesDataList.length; i++) {
      for (int j = 0; j < seriesDataList[i].length; j++) {
        for (int k = 0; k < seriesDataList[i][j].entries.length; k++) {
          final MapEntry<String,dynamic> m = seriesDataList[i][j].entries.toList()[k];

          if (double.tryParse('${m.value}') != null && m.key != 'YEAR') {
            seriesDataList[i][j][m.key] =
                double.parse(m.value.toString()).toStringAsFixed(2);
          }
        }
      }
    }

    final List<TableFields> tableFields = [];

    seriesDataList.removeWhere((element) => element.isEmpty);

    for (final TableFields element
        in indicatorDetails?.indicatorDetails.tableFields ?? []) {
      if (seriesDataList.firstOrNull?.firstOrNull?.containsKey(element.path)??false) {
        tableFields.add(element);
      }
    }

    for (final collection in seriesDataList) {
      for (final element in collection) {
        element['OBS_DT'] = IndicatorDateSetting.setupNameAll(
            selectedFrequencyForFilter ?? '',
            element['OBS_DT'] as String? ?? '');
      }
    }

    List<String> keys = [];
    final List<Map<String, dynamic>> tempData = [];
    List<List<Map<String, dynamic>>> tempDataList = [];
    final List<List<Map<String, dynamic>>> nowCastList = [];
    final List<List<Map<String, dynamic>>> foreCastList = [];

    for (int s1 = 0; s1 < seriesMeta.length; s1++) {
      if (seriesMeta[s1].id!.contains('-forecast')) {
        foreCastList.add(seriesDataList[s1]);
      } else {
        nowCastList.add(
          // seriesDataList[s1].limitListLength(),
          seriesDataList[s1],
        );
      }
    }
    tempDataList = [
      ...nowCastList,
      ...foreCastList,
    ];
    for (final List<Map<String, dynamic>> item in tempDataList) {
      for (int a = 0; a < item.length; a++) {
        tempData.add(item[a]);
      }
    }

    final List<Map<String, dynamic>> filteredTempData = [];

    if (seriesDataList.length > 1) {
      for (final List<Map<String, dynamic>> item in seriesDataList) {
        for (int a = 0; a < item.length; a++) {
          filteredTempData.add(item[a]);
        }
      }
    }

    // for (int i = 0; i < tempData.toSet().toList().length; i++) {}

    for (final List<Map<String, dynamic>> seriesData in seriesDataList) {
      keys = (seriesData.firstOrNull?.keys ?? []).toList();
    }

    late TableDataGridSource dataGridSource;

    dataGridSource = TableDataGridSource(
      tableData: filteredTempData.isNotEmpty
          ? filteredTempData
          : seriesDataList.isNotEmpty &&
                      (seriesDataList.firstOrNull?.isNotEmpty ?? false) ||
                  comparedData.isNotEmpty
              ? (seriesDataList.firstOrNull ?? []) + comparedData
              : tempData,
      columnNames: tableFields.isNotEmpty
          ? tableFields.map((e) => e.path ?? '').toList()
          : keys,
    );

    final Map<String, String> mapAr = {
      'INDICATOR_ID': 'معرف المؤشر',
      'RUN_SEQ_ID': 'تشغيل معرف التسلسل',
      'RUN_DT': 'تشغيل التاريخ',
      'VALUE': 'قيمة',
      'VALUE_LL': 'الحد الأدنى للقيمة',
      'VALUE_UL': 'الحد الأعلى للقيمة',
      'UNIT': 'وحدة',
      'OBS_DT': 'تاريخ المراقبة',
      'OPT': 'علم التوقعات',
      'TYPE': 'يكتب',
      'OIL_NONOIL': 'فئة',
      'SECTOR': 'قطاع',
      'INDUSTRY': 'صناعة',
      'PARAMETER_COMBO_ID': 'معرف التحرير والسرد المعلمة',
      'SECTOR_AR': 'القطاع العربي',
      'VALUE_FORECAST': 'القيمة المتوقعة',
      'OBS_DT_CUR': 'تاريخ المراقبة الحقيقي',
      'VALUE_PERC_ECO': 'النسبة المئوية للقيمة',
      'VALUE_CURRENT': 'القيمة الحقيقية',
      'CHANGE': 'يتغير',
      'CHANGE_PY': 'نسبة التغيير',
      'LANGUAGE_CD': 'نوع اللغة',
      'NATIONALITY_TYPE': 'نوع الجنسية',
    };

    final Map<String, String> mapEn = {
      'INDICATOR_ID': 'Indicator ID',
      'VALUE': 'Value',
      'VALUE_LL': 'Value Upper Limit',
      'VALUE_UL': 'Value Lower Limit',
      'UNIT': 'Unit',
      'OBS_DT': 'Observation Date',
      'OPT': 'OPT',
      'TYPE': 'Type',
      'OIL_NONOIL': 'Oil/Non Oil',
      'SECTOR': 'Sector',
      'INDUSTRY': 'Industry',
      'PARAMETER_COMBO_ID': 'Parameter Combo ID',
      'SECTOR_AR': 'Sector Arabic',
      'VALUE_FORECAST': 'Value Forecast',
      'OBS_DT_CUR': 'Current Observation Date',
      'VALUE_PERC_ECO': 'Value Percentage',
      'VALUE_CURRENT': 'Current Value',
      'CHANGE': 'Change',
      'NATIONALITY_TYPE': 'Nationality Type',
      'CHANGE_PY': 'Change Percentage',
    };

    for (int i = 0; i < keys.length; i++) {
      if(HiveUtilsSettings.getAppLanguage() == 'en') {
        keys[i] =
        mapEn.containsKey(keys[i]) ? mapEn[keys[i]].toString() : keys[i];
      }else {
        keys[i] =
        mapAr.containsKey(keys[i]) ? mapAr[keys[i]].toString() : keys[i];
      }
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(10),
        child: Container(
          decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                color: AppColors.greyShade12,
                blurRadius: 50,
                offset: const Offset(0, 35),
                spreadRadius: -23,
              ),
            ],
          ),
          height: 200,
          width: double.maxFinite,
          child: CustomDataTable(
            key: Key(
              'treemap.common.indicator.1.${treeMapSeriesMeta.map((e) => e.color).join()}',
            ),
            headerCells: tableFields.isNotEmpty
                ? tableFields.map((e) => e.label).toList()
                : keys,
            rowsCells: dataGridSource.rows.toList(),
            isLightMode: isLightMode,
          ),
          // child: SingleChildScrollView(
          //   scrollDirection: Axis.horizontal,
          //   child: SingleChildScrollView(
          //     child: Column(
          //         crossAxisAlignment: CrossAxisAlignment.start,
          //         children: [
          //           ConstrainedBox(
          //             constraints: BoxConstraints(
          //                 minWidth: MediaQuery.sizeOf(context).width - 48),
          //             child: DataTable(
          //                 key: Key(
          //                     'treemap.common.indicator.1.${treeMapSeriesMeta.map((e) => e.color).join()}'),
          //                 border: TableBorder(
          //                   horizontalInside: BorderSide(
          //                       color: AppColors.greyShade8, width: 1),
          //                   verticalInside: BorderSide(
          //                       color: AppColors.greyShade5, width: 1),
          //                   borderRadius: BorderRadius.circular(10),
          //                 ),
          //                 dataRowMinHeight: 30,
          //                 dataRowMaxHeight: 30,
          //                 headingRowHeight: 30,
          //                 headingRowColor: MaterialStateColor.resolveWith(
          //                     (states) => AppColors.blue),
          //                 columns: tableFields.isNotEmpty
          //                     ? tableFields
          //                         .map(
          //                           (e) => DataColumn(
          //                               label: Text(
          //                             (e.label ?? '-').tr().toUpperCase(),
          //                             overflow: TextOverflow.ellipsis,
          //                             style: AppTextStyles.s12w5cWhite,
          //                             textScaler: TextScaler.linear(
          //                                 textScaleFactor.value),
          //                           )),
          //                         )
          //                         .toList()
          //                     : keys
          //                         .map(
          //                           (e) => DataColumn(
          //                               label: Text(
          //                             e.tr(),
          //                             overflow: TextOverflow.ellipsis,
          //                             style: AppTextStyles.s12w5cWhite,
          //                             textScaler: TextScaler.linear(
          //                                 textScaleFactor.value),
          //                           )),
          //                         )
          //                         .toList(),
          //                 rows: dataGridSource.rows
          //                     .map((e) => DataRow(
          //                         cells: e
          //                             .getCells()
          //                             .map((e1) => DataCell(Text(
          //                                 textDirection:  checkTextDirection(e1.value),
          //                                   e1.value.toString(),
          //                                   overflow: TextOverflow.ellipsis,
          //                                   style: isLightMode
          //                                       ? AppTextStyles.s12w5cBlack
          //                                       : AppTextStyles.s12w5cWhite,
          //                                   textScaler: TextScaler.linear(
          //                                       textScaleFactor.value),
          //                                 )))
          //                             .toList()))
          //                     .toList()),
          //           ),
          //           const SizedBox(
          //             height: 16,
          //           ),
          //         ]),
            ),
          ),


    );

    return SizedBox(
      height: 200,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(10),
        child: SfDataGridTheme(
          data: SfDataGridThemeData(headerColor: AppColors.blue),
          child: SfDataGrid(
            onQueryRowHeight: (details) => 26,
            columnWidthMode: /*tableFields.length < 4 || keys.length < 4
                ? ColumnWidthMode.fill
                :*/
                ColumnWidthMode.lastColumnFill,
            source: dataGridSource,
            gridLinesVisibility: GridLinesVisibility.vertical,
            headerGridLinesVisibility: GridLinesVisibility.vertical,
            isScrollbarAlwaysShown: true,
            columns: [
              if (tableFields.isNotEmpty)
                for (final TableFields field in tableFields)
                  GridColumn(
                    columnWidthMode: tableFields.length <= 3
                        ? ColumnWidthMode.fill
                        : ColumnWidthMode.lastColumnFill,
                    columnName: field.label ?? '',
                    label: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      alignment: Alignment.centerLeft,
                      child: Text(
                        field.label ?? '',
                        overflow: TextOverflow.ellipsis,
                        style: AppTextStyles.s10w5cWhite,
                        textScaler: TextScaler.linear(textScaleFactor.value),
                      ),
                    ),
                  )
              else
                for (final String key in keys)
                  GridColumn(
                    columnWidthMode: key.length <= 3
                        ? ColumnWidthMode.fill
                        : ColumnWidthMode.lastColumnFill,
                    columnName: key,
                    label: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      alignment: Alignment.centerLeft,
                      child: Text(
                        key.replaceAll('_', ' '),
                        overflow: TextOverflow.ellipsis,
                        style: AppTextStyles.s10w5cWhite,
                        textScaler: TextScaler.linear(textScaleFactor.value),
                      ),
                    ),
                  ),
            ],
          ),
        ),
      ),
    ) /*ClipRRect(
      borderRadius: BorderRadius.circular(15),
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: isLightMode ? AppColors.greyShade8 : Colors.transparent,
          ),
          borderRadius: BorderRadius.circular(15),
        ),
        child: Column(
          children: [
            Container(
              decoration: BoxDecoration(
                color:
                    isLightMode ? AppColors.blueLight : const Color(0xFF1749A1),
              ),
              child: Row(
                children: [
                  Expanded(
                    flex: 6,
                    child: TableText(
                      text: isForTreeMap ? 'Sector' : 'Month',
                      isHeader: true,
                    ),
                  ),
                  IntrinsicHeight(
                    child: SizedBox(
                      height: 30,
                      child: VerticalDivider(
                        color: isLightMode
                            ? AppColors.white
                            : const Color(0xFF474747),
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 4,
                    child: TableText(
                      text: isForTreeMap ? 'Change' : 'Values',
                      isHeader: true,
                      alignment: Alignment.center,
                      padding: EdgeInsets.zero,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(
              height: 170,
              child: SingleChildScrollView(
                child: Table(
                  columnWidths: const {
                    0: FlexColumnWidth(6),
                    1: FlexColumnWidth(4),
                  },
                  border: TableBorder(
                    verticalInside: BorderSide(
                      color: isLightMode
                          ? AppColors.greyShade1
                          : const Color(0xFF474747),
                    ),
                  ),
                  children: [
                    for (final List<Map<String, dynamic>> seriesList
                        in solidSeries)
                      for (final Map<String, dynamic> item in seriesList)
                        tableRow(
                          item,
                          seriesList,
                          isForTreeMap,
                          isLightMode,
                        ),
                    for (final Map<String, dynamic> item in comparisonSeries)
                      tableRow(
                        item,
                        comparisonSeries,
                        isForTreeMap,
                        isLightMode,
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    )*/
        ;
  }

  TableRow tableRow(
    Map<String, dynamic> item,
    List<Map<String, dynamic>> seriesData,
    bool isForTreeMap,
    bool isLightMode,
  ) {
    return TableRow(
      decoration: BoxDecoration(
        color: seriesData.indexOf(item).isOdd
            ? isLightMode
                ? AppColors.greyShade7
                : AppColors.greyShade14
            : isLightMode
                ? AppColors.white
                : const Color(0xFF505050),
      ),
      children: [
        TableText(
          text: item[isForTreeMap ? 'SECTOR' : 'OBS_DT'] as String,
          fontColor: AppColors.greyShade4,
        ),
        TableText(
          text: double.parse(
            (item[isForTreeMap ? 'CHANGE' : 'VALUE'] ?? '0').toString(),
          ).toStringAsFixed(2),
          fontColor: isLightMode ? AppColors.black : AppColors.white,
          alignment: Alignment.center,
          padding: EdgeInsets.zero,
        ),
      ],
    );
  }

  /// dont delete used for plotting the chart
  List<FrequencySelectorModel> getFrequencyList({bool isFirstTime = false}) {
    final List<String> dataList = List.from(
      indicatorDetails?.indicatorDetails.indicatorVisualizations
              ?.visualizationsMeta?.firstOrNull?.timeUnit ??
          [],
    );

    final List<FrequencySelectorModel> frequencyList = [];

    for (final element in dataList) {
      frequencyList.add(
        FrequencySelectorModel(title: element, isSelected: false),
      );
    }

    if ((indicatorDetails?.indicatorDetails.indicatorVisualizations
                    ?.visualizationsMeta?.firstOrNull?.showQuarterlyIntervals ==
                'true' ||
            indicatorDetails?.indicatorDetails.indicatorVisualizations
                    ?.visualizationsMeta?.firstOrNull?.showQuarterlyIntervals ==
                true) &&
        dataList.isEmpty) {
      frequencyList.addAll([
        FrequencySelectorModel(title: 'Yearly', isSelected: false),
        FrequencySelectorModel(title: 'Quarterly', isSelected: false),
      ]);
    }

    if (dataList.contains('Monthly') && !dataList.contains('Quarterly')) {
      frequencyList.insert(
        1,
        FrequencySelectorModel(title: 'Quarterly', isSelected: false),
      );
    }

    bool isSelectedSet = false;

    if (!isFirstTime) {
      for (final element in frequencyList) {
        if (element.title == context.read<DetailsBloc>().selectedFrequency) {
          element.isSelected = true;
          isSelectedSet = true;
          break;
        }
      }
    }

    if (!isSelectedSet) {
      for (final element in frequencyList.reversed.toList()) {
        if (element.title == 'Monthly') {
          element.isSelected = true;
          break;
        } else if (element.title == 'Quarterly') {
          element.isSelected = true;
          break;
        }
      }
    }

    if (!isSelectedSet &&
        frequencyList.isNotEmpty &&
        frequencyList.every((element) => element.isSelected == false)) {
      frequencyList.first.isSelected = true;
    }

    return frequencyList;
  }

  ///
// Widget _whatIfColumnChartRepresentation() {
//   final List<List<ColumnChartData>> nowCast = [];
//   final List<List<ColumnChartData>> foreCast = [];
//   final List<List<Map<String, dynamic>>> l =
//       indicatorDetails!.getFilteredSeriesForMultiDrivers(
//     visualizationsMetaIndex: widget.index,
//   );
//
//   for (int i = 0; i < l.length; i++) {
//     if (seriesMeta[i].id!.contains('-forecast')) {
//       foreCast.add(
//         l[i]
//             .map(
//               (e) => ColumnChartData(
//                 e['OBS_DT'].toString(),
//                 double.parse('${e['VALUE'] ?? '0'}'),
//                 0,
//                 0,
//               ),
//             )
//             .toList(),
//       );
//     } else {
//       nowCast.add(
//         l[i]
//             .map(
//               (e) => ColumnChartData(
//                 e['OBS_DT'].toString(),
//                 double.parse('${e['VALUE'] ?? '0'}'),
//                 0,
//                 0,
//               ),
//             )
//             .toList()
//             .reversed
//             .take(12)
//             .toList()
//             .reversed
//             .toList(),
//       );
//     }
//   }
//
//   return SizedBox(
//     height: 200,
//     child: ColumnChart(
//       isForecast: true,
//       chartDataList: nowCast,
//       forecastChartDataList: foreCast,
//     ),
//   );
// }
  TextDirection? checkTextDirection(value) {

    if(value is int || value is double) {
      return TextDirection.ltr;
    }else if(int.tryParse(value.toString()) == null ) {
      return TextDirection.ltr;
    }
    return null;
  }

}
