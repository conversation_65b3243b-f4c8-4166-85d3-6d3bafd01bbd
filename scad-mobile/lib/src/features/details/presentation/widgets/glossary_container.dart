import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/features/details/presentation/widgets/painter/glossary_cutom_paint.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class GlossaryContiner extends StatelessWidget {
  const GlossaryContiner({required this.theme, super.key});

  final String theme;

  @override
  Widget build(BuildContext context) {
    if(isDemoMode){
      return const SizedBox();
    }
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
              final rtl = DeviceType.isDirectionRTL(context);
    return Stack(
      clipBehavior: Clip.none,
      children: [
        CustomPaint(
          size: const Size(double.infinity, 85),
          painter: GlossaryCustomPaint(isRtl: rtl),
          child: Container(
            padding:const EdgeInsets.symmetric(
                horizontal: 22,
                vertical: 8,
              ) ,
            child: Row(
              children: [
                SvgPicture.asset('assets/images/glossary.svg'),
                const SizedBox(width: 30),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Opacity(
                      opacity: 0.90,
                      child: Text(
                        LocaleKeys.glossary.tr(),
                        style: TextStyle(
                          color: AppColors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.w400,
                        ),
                        textScaler: TextScaler.linear(textScaleFactor.value),
                      ),
                    ),
                    // const SizedBox(height: 3),
                    // Text(
                    //   theme,
                    //   style: TextStyle(
                    //     color: isLightMode
                    //         ? AppColors.blackShade2
                    //         : AppColors.white,
                    //     fontSize: 16,
                    //     fontWeight: FontWeight.w500,
                    //   ),
                    //   textScaler: TextScaler.linear(textScaleFactor.value),
                    // ),
                  ],
                ),
              ],
            ),
          ),
        ),
        Positioned(
          right:  DeviceType.isDirectionRTL(context) ? null: -8.5,
          left:  DeviceType.isDirectionRTL(context) ? (isLightMode ? -8.5 : -22): null,
          top: isLightMode ? -5 :   DeviceType.isDirectionRTL(context) ? -5 : -20,
          height:isLightMode ? 40: 60,
          width: isLightMode ?40: 60,
          child: InkWell(
            onTap: () => AutoRouter.of(context).push(GlossaryScreenRoute()),
            child:RotatedBox(
              quarterTurns: DeviceType.isDirectionRTL(context) ? -1 : 0,
               child: SvgPicture.asset(
              isLightMode ? 'assets/images/arrow_button_light.svg' :  'assets/images/arrow_button_dark.svg',


            ),
            )
          ),
        ),
      ],
    );
  }
}
