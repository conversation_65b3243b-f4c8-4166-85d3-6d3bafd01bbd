import 'dart:convert';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:lottie/lottie.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_response_helper.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/presentation/bloc/indicator_card_bloc.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/intro_widget.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/showcaseview.dart';
import 'package:scad_mobile/src/features/details/presentation/bloc/details_bloc.dart';
import 'package:scad_mobile/src/features/details/presentation/widgets/bottom_sheets/data_presentation_bottom_sheet/data_presentation_bottom_sheet.dart';
import 'package:scad_mobile/src/features/details/presentation/widgets/chart_actions.dart';
import 'package:scad_mobile/src/features/details/presentation/widgets/common_indicator_view.dart';
import 'package:scad_mobile/src/features/details/presentation/widgets/details_page_chart_filter.dart';
import 'package:scad_mobile/src/features/details/presentation/widgets/download_as.dart';
import 'package:scad_mobile/src/features/details/presentation/widgets/icon_and_title_widget.dart';
import 'package:scad_mobile/src/features/details/presentation/widgets/meta_data_widget.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/animation_asset.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class ExpandedIndicatorDetailsView extends StatefulWidget {
  const ExpandedIndicatorDetailsView({
    required this.id,
    required this.isSearch,
    this.scrollController,
    this.index,
    super.key,
    this.indicatorDetails,
    this.isWhatIfDetails = true,
    this.contentType,
    this.nodeIdForResetFilter,
    this.contentTypeForResetFilter,
    this.overviewContentTypeForResetFilter,
    this.originalIndicatorForFilter,
    this.comparedIndicatorName,
    this.selectedVisualization,
    this.computedIndicatorName,
  });

  final String id;
  final int? index;
  final IndicatorDetailsResponseHelper? indicatorDetails;
  final IndicatorDetailsResponse? originalIndicatorForFilter;
  final bool isWhatIfDetails;
  final String? contentType;
  final String? nodeIdForResetFilter;
  final String? contentTypeForResetFilter;
  final String? overviewContentTypeForResetFilter;
  final String? comparedIndicatorName;
  final ScrollController? scrollController;
  final Visualizations? selectedVisualization;
  final String? computedIndicatorName;
  final bool isSearch;

  @override
  State<ExpandedIndicatorDetailsView> createState() =>
      _ExpandedIndicatorDetailsViewState();
}

class _ExpandedIndicatorDetailsViewState
    extends State<ExpandedIndicatorDetailsView> {
  String selectedType = 'line-chart';
  String selectedTypeOld = 'line-chart';
  ValueNotifier<bool> isPreviousActionTriggered = ValueNotifier(false);
  late IndicatorDetailsResponseHelper indicatorDetails;
  IndicatorDetailsResponseHelper? initialIndicatorDetailsFromCompareResponse;

  bool isComparisonActive = false;
  bool isComputeActive = false;
  List<String> seriesTitles = [];
  List<List<Map<String, dynamic>>> forecastSeries = [];
  VisualizationsMeta? vizMeta;
  String? chartType;
  bool donePresentation = false;

  // String selectedMeasurement = '';
  String nameOfIndicator = '';
  final GlobalKey chatButtonKey = GlobalKey(debugLabel: 'chatButtonKey');
  final GlobalKey changeFrequencyButtonKey =
      GlobalKey(debugLabel: 'changeFrequencyButtonKey');
  final GlobalKey changePresentationButtonKey =
      GlobalKey(debugLabel: 'changePresentationButtonKey');
  final GlobalKey compareIndicatorsButtonKey =
      GlobalKey(debugLabel: 'compareIndicatorsButtonKey');
  final GlobalKey computeDataButtonKey =
      GlobalKey(debugLabel: 'computeDataButtonKey');
  final GlobalKey downloadAsButtonKey =
      GlobalKey(debugLabel: 'downloadAsButtonKey');

  List<List<Map<String, dynamic>>>? filteredDataList = [];
  List<List<Map<String, dynamic>>>? oldFilterDataList = [];

  List<List<Map<String, dynamic>>>? seriesForLegend = [];
  List<String> seriesLegendForCompare = [];

  List<List<Map<String, dynamic>>>? solidSeriesList = [];
  List<List<Map<String, dynamic>>>? forecastChartDataDownload = [];
  Visualizations? selectedVisualization;

  String filterKey = '';
  String? selectedFrequencyForFilter;
  List<GlobalKey> steps = [];
  List<Options> yearFilterOptions = [];
  BuildContext? myContext;
  bool forecastVisibility = true;

  bool chartHasData = false;

  @override
  void initState() {
    super.initState();
    steps = [
      chatButtonKey,
      changeFrequencyButtonKey,
      changePresentationButtonKey,
      compareIndicatorsButtonKey,
      computeDataButtonKey,
      downloadAsButtonKey,
    ];
    final List<Options> year =
        widget.originalIndicatorForFilter?.indicatorFilters?.first.options ??
            [];
    yearFilterOptions = List.generate(
      year.length,
      (index) => Options.fromJson(
        jsonDecode(jsonEncode(year[index])) as Map<String, dynamic>,
      ),
    );
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        getData();
        if (HiveUtilsSettings.getUserGuideStatus() == UserGuides.Home) {
          ShowCaseWidget.of(myContext!).startShowCase(steps);
        }
      }
    });
    widget.indicatorDetails?.indicatorDetails.metaData
        ?.removeWhere((value) => value?.value == null);
    super.initState();
  }

  void getData() {
    nameOfIndicator = widget.computedIndicatorName ?? '';
    indicatorDetails = widget.indicatorDetails!;
    vizMeta = indicatorDetails.getFilteredVisualizationMetaList().firstOrNull;
    isComparisonActive = vizMeta?.id == 'compare-chart';
    chartType = vizMeta?.type;
    selectedVisualization = widget.selectedVisualization;

    selectedType =
        chartType == 'tree-map-with-change-chart' ? 'tree-map' : 'line-chart';

    for (final SeriesMeta item in vizMeta?.seriesMeta ?? []) {
      if (item.id!.contains('-forecast')) {
        forecastSeries.add(
          widget.indicatorDetails!.getFilteredSeries(
            seriesMetaIndex: (vizMeta?.seriesMeta ?? []).indexOf(item),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    indicatorDetails = widget.indicatorDetails!;

    vizMeta = indicatorDetails.getFilteredVisualizationMetaList().firstOrNull;
    isComparisonActive = vizMeta?.id == 'compare-chart';
    isComputeActive = vizMeta?.id == 'compute-data';
    final bool isArabic = HiveUtilsSettings.getAppLanguage() == 'ar';
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return ShowCaseWidget(
      builder: Builder(
        builder: (context) {
          myContext = context;
          return CommonIndicatorView(
            isSearch: widget.isSearch,
            indicatorDetails: widget.indicatorDetails,
            nodeId: widget.id,
            index: widget.index ?? 0,
            isWhatIfDetails: widget.isWhatIfDetails,
            comparedIndicatorName: widget.comparedIndicatorName,
            computedIndicatorName: nameOfIndicator,
            originalIndicatorForFilter:
                widget.originalIndicatorForFilter ?? IndicatorDetailsResponse(),
            onClearFilter: isComparisonActive
                ? () {
                    _clearFilters(context);
                  }
                : null,
            chartHasDataFn: (bool b) {
              chartHasData = b;
              setState(() {});
            },
            expandedWidget: Column(
              children: [
                BlocConsumer<DetailsBloc, DetailsState>(
                  listener: (context, state) {
                    if (state is ComputeDataSuccessState) {
                      nameOfIndicator = state.nameOfIndicator ?? '';
                      // selectedMeasurement = state.selectedMeasurement ?? '';
                      indicatorDetails = IndicatorDetailsResponseHelper(
                        state.indicatorDetails,
                      );
                      filteredDataList?.clear();
                      filteredDataList?.add(
                        List.from(
                          state
                                  .indicatorDetails
                                  .indicatorVisualizations
                                  ?.visualizationsMeta
                                  ?.first
                                  .seriesMeta
                                  ?.first
                                  .data ??
                              [],
                        ),
                      );

                      for (var i = 0;
                          i < (filteredDataList ?? []).length;
                          i++) {
                        filteredDataList?[i].sort(
                          (a, b) => a['OBS_DT']
                              .toString()
                              .compareTo(b['OBS_DT'].toString()),
                        );
                      }
                      final List<Options> year = widget
                              .indicatorDetails
                              ?.indicatorDetails
                              .indicatorFilters
                              ?.first
                              .options ??
                          [];
                      yearFilterOptions = List.generate(
                        year.length,
                        (index) => Options.fromJson(
                          jsonDecode(jsonEncode(year[index]))
                              as Map<String, dynamic>,
                        ),
                      );
                    } else if (state is ChangeDriversValueUpdateState) {
                      for (final element in yearFilterOptions) {
                        element.isSelected = false;
                      }
                      if (yearFilterOptions.isNotEmpty) {
                        yearFilterOptions.last.isSelected = false;
                      }
                    } else if (state is FilterApplyState) {
                      filteredDataList =
                          List.from(state.filteredDataList ?? []);
                    } else if (state is CompareIndicatorSuccessState) {
                      final List<Options> year = state.indicatorDetails
                              .indicatorFilters?.first.options ??
                          [];
                      yearFilterOptions = List.generate(
                        year.length,
                        (index) => Options.fromJson(
                          jsonDecode(jsonEncode(year[index]))
                              as Map<String, dynamic>,
                        ),
                      );
                      indicatorDetails = IndicatorDetailsResponseHelper(
                        state.indicatorDetails,
                      );
                      selectedFrequencyForFilter = 'Monthly';
                    } else if (state is ForecastVisibilityUpdateState) {
                      forecastVisibility = state.isVisibilityOn;
                    }
                    if (state is MonthYearFilterState) {
                      yearFilterOptions = state.options;
                    }
                  },
                  builder: (context, state) {
                    return nameOfIndicator.isNotEmpty
                        ? Padding(
                            padding: const EdgeInsets.only(bottom: 8),
                            child: Text.rich(
                              textScaler:
                                  TextScaler.linear(textScaleFactor.value),
                              style: TextStyle(
                                color: AppColors.grey,
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                              ),
                              TextSpan(
                                children: [
                                  TextSpan(text: nameOfIndicator),
                                  // if (selectedMeasurement.isNotEmpty)
                                  //   TextSpan(text: '($selectedMeasurement)'),
                                ],
                              ),
                            ),
                          )
                        : const SizedBox();
                  },
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //monthly filter
                  children: [
                    Visibility(
                      visible: widget.originalIndicatorForFilter
                              ?.indicatorFilters?.first.options !=
                          null,
                      child: Flexible(
                        child: DetailsPageChartFilter(
                          options: yearFilterOptions,
                          originalIndicatorForFilter:
                              indicatorDetails.indicatorDetails,
                          filteredDataList: filteredDataList,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    if (!isComparisonActive && !isComputeActive)
                      ValueListenableBuilder(
                        valueListenable: isPreviousActionTriggered,
                        builder: (context, isPrevious, _) {
                          return IntroWidget(
                            stepKey: chatButtonKey,
                            stepIndex: 5,
                            totalSteps: 10,
                            title: LocaleKeys.chatOption.tr(),
                            description: LocaleKeys.chatOptionGuideDesc.tr(),
                            arrowAlignment: Alignment.bottomRight,
                            isDownArrow: true,
                            position: TooltipPosition.top,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            targetBorderRadius: 50,
                            onNext: () {
                              if (!isPrevious) {
                                scrollToWidget(
                                  context,
                                  changeFrequencyButtonKey,
                                );
                              }
                              Future.delayed(const Duration(milliseconds: 800),
                                  () {
                                setState(() {});
                              });
                            },
                            onPrevious: () {
                              ShowCaseWidget.of(context).dismiss();
                              Navigator.pop(context);
                            },
                            arrowPadding: const EdgeInsets.only(
                              top: 10,
                              right: 14,
                              left: 14,
                            ),
                            targetPadding: const EdgeInsets.all(10),
                            child: InkWell(
                              key: Key(widget.index.toString()),
                              onTap: () {
                                final IndicatorDetailsResponse data =
                                    widget.indicatorDetails!.indicatorDetails;

                                context.read<DetailsBloc>().add(
                                      DetailsCreateChatThreadEvent(
                                        domain: data.domain ?? '',
                                        domainId: int.tryParse(
                                          data.domainId.toString(),
                                        )??0,
                                        theme: data.theme ?? '',
                                        subTheme: data.subtheme ?? '',
                                        indicatorNodeId: data.id ?? '',
                                        indicatorAppType:
                                            data.indicatorType ?? '',
                                        inicatorContentType:
                                            (data.type == 'Internal' &&
                                                    data.multiDrivers == true)
                                                ? 'analytical-apps'
                                                : widget.contentType ?? '',
                                        indicatorKey:
                                            data.contentClassificationKey ?? '',
                                        indicatorName:
                                            data.componentTitle ?? '',
                                      ),
                                    );
                              },
                              child: SvgPicture.asset(
                                isLightMode
                                    ? 'assets/images/chat.svg'
                                    : 'assets/images/chat-dark.svg',
                              ),
                            ),
                          );
                        },
                      ),
                  ],
                ),

                if (isComparisonActive && selectedType != 'table-view')
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // for (final title in vizMeta!.seriesTitles!.values)
                        for (int i = 0; i < seriesLegendForCompare.length; i++)
                          Expanded(
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (i >= 1) const SizedBox(width: 10),
                                Padding(
                                  padding: const EdgeInsets.only(top: 5),
                                  child: i == 0
                                      ? SvgPicture.asset(
                                          AppImages.icLegendGreen,
                                          colorFilter: ColorFilter.mode(
                                            isLightMode
                                                ? AppColors.chartColorSet[0]
                                                : AppColors
                                                    .chartColorSetDark[0],
                                            BlendMode.srcIn,
                                          ),
                                        )
                                      : SvgPicture.asset(
                                          AppImages.icLegendPurple,
                                          colorFilter: ColorFilter.mode(
                                            isLightMode
                                                ? AppColors.chartColorSet[1]
                                                : AppColors
                                                    .chartColorSetDark[1],
                                            BlendMode.srcIn,
                                          ),
                                        ),
                                ),
                                const SizedBox(width: 10),
                                Expanded(
                                  child: Text(
                                    seriesLegendForCompare[i],
                                    textAlign: TextAlign.start,
                                    style: TextStyle(
                                      color: isLightMode
                                          ? AppColors.grey
                                          : AppColors.white,
                                      fontSize: 12,
                                      fontWeight: FontWeight.w400,
                                    ),
                                    textScaler: TextScaler.linear(
                                      textScaleFactor.value,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),
                  ),

                if (!isComparisonActive &&
                    selectedType != 'table-view' &&
                    selectedType != 'tree-map' &&
                    solidSeriesList != null &&
                    (solidSeriesList ?? []).length > 1) ...[
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Align(
                      alignment: isArabic
                          ? Alignment.centerRight
                          : Alignment.centerLeft,
                      child: Wrap(
                        runSpacing: 10,
                        children:
                            List.generate(solidSeriesList?.length ?? 0, (i) {
                          if (solidSeriesList![i].isNotEmpty) {
                            return Padding(
                              padding: EdgeInsets.only(
                                right: isArabic ? 0 : 10,
                                left: isArabic ? 10 : 0,
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(top: 5),
                                    child: SvgPicture.asset(
                                      AppImages.icLegendGreen,
                                      colorFilter: ColorFilter.mode(
                                        isLightMode
                                            ? AppColors.chartColorSet[
                                                solidSeriesList!.indexOf(
                                                        solidSeriesList![i]) %
                                                    AppColors
                                                        .chartColorSet.length]
                                            : AppColors.chartColorSetDark[
                                                solidSeriesList!.indexOf(
                                                        solidSeriesList![i]) %
                                                    AppColors.chartColorSetDark
                                                        .length],
                                        BlendMode.srcIn,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 5),
                                  Flexible(
                                    child: Text(
                                      solidSeriesList?[i]
                                              .first['legend_name']
                                              ?.toString() ??
                                          '',
                                      textAlign: TextAlign.start,
                                      style: TextStyle(
                                        color: isLightMode
                                            ? AppColors.grey
                                            : AppColors.white,
                                        fontSize: 12,
                                        fontWeight: FontWeight.w400,
                                      ),
                                      maxLines: 4,
                                      textScaler: TextScaler.linear(
                                        textScaleFactor.value,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          } else {
                            return const SizedBox.shrink();
                          }
                        }),
                      ),
                    ),
                  ),
                ],

                const SizedBox(height: 20),

                /// The Action buttons of the chat which includes
                /// Data frequency, presentation, compare and compute
                if (!widget.isWhatIfDetails)
                  Column(
                    children: [
                      BlocConsumer<DetailsBloc, DetailsState>(
                        listener: (context, state) {
                          if (state is PresentationTypeSelectionState) {
                            selectedType = state.selectedType;
                          } else if (state is CompareIndicatorSuccessState) {
                            initialIndicatorDetailsFromCompareResponse =
                                state.initialIndicatorDetails;
                          } else if (state is UpdateLegendState) {
                            seriesForLegend = state.seriesList ?? [];
                            filterKey = state.filterKey ?? '';

                            for (var i = 0;
                                i < (seriesForLegend ?? []).length;
                                i++) {
                              seriesLegendForCompare.add(
                                seriesForLegend?[i]
                                        .first[filterKey]
                                        ?.toString() ??
                                    '',
                              );
                            }
                            oldFilterDataList = List.generate(
                              (state.filterList ?? []).length,
                              (outerIndex) => List.generate(
                                (state.filterList?[outerIndex] ?? []).length,
                                (innerIndex) => Map<String, dynamic>.from(
                                  (state.filterList ?? [])[outerIndex]
                                      [innerIndex],
                                ),
                              ),
                            );

                            filteredDataList = List.generate(
                              (state.seriesList ?? []).length,
                              (outerIndex) => List.generate(
                                (state.seriesList?[outerIndex] ?? []).length,
                                (innerIndex) => Map<String, dynamic>.from(
                                  (state.seriesList ?? [])[outerIndex]
                                      [innerIndex],
                                ),
                              ),
                            );
                          } else if (state is DetailsPageFilterResetState) {
                            filteredDataList = oldFilterDataList;
                            seriesLegendForCompare.clear();
                          } else if (state is SolidStateUpdateState) {
                            solidSeriesList = state.seriesList;
                            selectedFrequencyForFilter =
                                state.selectedFrequencyForFilter;
                            // forecastSeries = state.forecastSeriesList ?? [];
                            forecastChartDataDownload =
                                state.forecastSeriesList ?? [];
                          } else if (state is SelectVisualizationState) {
                            selectedVisualization = state.selectedVisualization;
                          }
                          if (state is PresentationTypeSelectionDoneState) {
                            donePresentation = true;
                            // Navigator.pop(context);
                          }
                        },
                        builder: (context, state) {
                          if (isDemoMode) {
                            return const SizedBox();
                          }
                          return ValueListenableBuilder(
                            valueListenable: isPreviousActionTriggered,
                            builder: (context, isPrev, _) {
                              return ChartActions(
                                solidSeries: solidSeriesList ?? [],
                                scrollController: widget.scrollController!,
                                chatButtonKey: chatButtonKey,
                                changeFrequencyKey: changeFrequencyButtonKey,
                                changePresentationKey:
                                    changePresentationButtonKey,
                                compareIndicatorsKey:
                                    compareIndicatorsButtonKey,
                                computeDataKey: computeDataButtonKey,
                                downloadAsKey: downloadAsButtonKey,
                                isComparisonActive: isComparisonActive,
                                isPreviousTriggeredFromDownload: isPrev,
                                onPreviousAction: (value) {
                                  isPreviousActionTriggered.value =
                                      value ?? false;
                                },
                                onDataPresentationSelection: () {
                                  selectedTypeOld = selectedType;
                                  showModalBottomSheet<String>(
                                    isScrollControlled: true,
                                    backgroundColor: Colors.transparent,
                                    context: context,
                                    builder: (BuildContext context) {
                                      return DataPresentationBottomSheet(
                                        selectedChartType: selectedType,
                                        isForecastData:
                                            forecastSeries.isNotEmpty,
                                        isTreeMap: chartType ==
                                            'tree-map-with-change-chart',
                                      );
                                    },
                                  ).whenComplete(() {
                                    if (!donePresentation) {
                                      context.read<DetailsBloc>().add(
                                            PresentationTypeSelectionEvent(
                                              selectType: selectedTypeOld,
                                            ),
                                          );
                                    }
                                    donePresentation = false;
                                  });
                                },
                                indicatorDetails: widget.indicatorDetails,
                                originalIndicatorForFilter:
                                    widget.originalIndicatorForFilter,
                                contentType: widget.contentType,
                                filteredDataList: filteredDataList,
                                forecastDataList: forecastSeries,
                              );
                            },
                          );
                        },
                      ),
                      const SizedBox(height: 20),
                    ],
                  ),

                /// The Download as buttons
                /// as PDF, XLS, IMAGE and PPT
                IntroWidget(
                  stepKey: downloadAsButtonKey,
                  stepIndex: 10,
                  totalSteps: 10,
                  title: LocaleKeys.downloadAs.tr(),
                  description: LocaleKeys.downloadAsGuideDesc.tr(),
                  arrowAlignment: Alignment.bottomLeft,
                  position: TooltipPosition.top,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  targetBorderRadius: 10,
                  isDownArrow: true,
                  arrowPadding: const EdgeInsets.only(top: 10),
                  targetPadding: const EdgeInsets.all(10),
                  onPrevious: () {
                    widget.scrollController?.jumpTo(0);
                    scrollToWidget(context, changeFrequencyButtonKey);

                    Future.delayed(const Duration(milliseconds: 300), () {
                      setState(() {});
                    });
                  },
                  child: DownloadAs(
                    key: Key(
                      'expand.download.$chartHasData.${widget.index}.$solidSeriesList',
                    ),
                    lendendListCompare: seriesLegendForCompare,
                    selectedFrequencyForFilterDownload:
                        selectedFrequencyForFilter ?? '',
                    indicatorDetails: indicatorDetails,
                    chartData: solidSeriesList,
                    whatIfScenarioItemIndex: widget.index,
                    chartType: selectedType,
                    comparedIndicatorName: widget.comparedIndicatorName,
                    isComparisonActive: isComparisonActive,
                    selectedVisualization: selectedVisualization,
                    computedIndicatorName: nameOfIndicator,
                    forecastVisibility: forecastVisibility,
                    enableDownload: chartHasData,
                    forecastChartDataDownload: forecastChartDataDownload,
                  ),
                ),
                const SizedBox(height: 20),

                if ((widget.indicatorDetails?.indicatorDetails.metaData ?? [])
                    .isNotEmpty) ...[
                  MetaDataWidget(
                    metaData:
                        widget.indicatorDetails?.indicatorDetails.metaData,
                  ),
                  const SizedBox(height: 20),
                ],

                if (!isComparisonActive) ...[
                  /// Data shows updated on
                  if ((widget.indicatorDetails?.indicatorDetails
                                      .publicationDate ==
                                  ''
                              ? widget
                                  .indicatorDetails?.indicatorDetails.updated
                              : widget.indicatorDetails?.indicatorDetails
                                  .publicationDate) !=
                          null &&
                      (widget.indicatorDetails?.indicatorDetails
                                          .publicationDate ==
                                      ''
                                  ? widget.indicatorDetails?.indicatorDetails
                                      .updated
                                  : widget.indicatorDetails?.indicatorDetails
                                      .publicationDate)
                              ?.trim() !=
                          '' &&
                      (widget.indicatorDetails?.indicatorDetails
                                      .publicationDate ==
                                  ''
                              ? widget
                                  .indicatorDetails?.indicatorDetails.updated
                              : widget.indicatorDetails?.indicatorDetails
                                  .publicationDate) !=
                          'Invalid date')
                    IconAndTitleWidget(
                      icon: 'assets/images/calendar.svg',
                      title: LocaleKeys.updatedOn.tr(),
                      content: formatDateToUnifrom(
                        widget.indicatorDetails?.indicatorDetails.publicationDate ==
                                ''
                            ? (widget.indicatorDetails?.indicatorDetails
                                    .updated ??
                                '')
                            : (widget.indicatorDetails?.indicatorDetails
                                    .publicationDate ??
                                '-'),
                      ),
                    ),
                  const SizedBox(height: 17),

                  /// Data shows source
                  if (widget.indicatorDetails?.indicatorDetails.dataSource !=
                          null &&
                      widget.indicatorDetails?.indicatorDetails.dataSource
                              ?.trim() !=
                          '')
                    IconAndTitleWidget(
                      icon: 'assets/images/document.svg',
                      title: LocaleKeys.source.tr(),
                      content: widget
                              .indicatorDetails?.indicatorDetails.dataSource ??
                          '-',
                    ),
                  const SizedBox(height: 6),
                ],
              ],
            ),
          );
        },
      ),
    );
  }

  Future<void> _clearFilters(BuildContext context) async {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              contentPadding: const EdgeInsets.all(20),
              insetPadding: const EdgeInsets.all(20),
              backgroundColor: AppColors.white,
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(20)),
              ),
              elevation: 0,
              content: Column(
                mainAxisSize: MainAxisSize.min,
                // crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // SvgPicture.asset(AppImages.icAlertCircleOutline),
                  if (isLightMode)
                    Lottie.asset(AnimationAsset.animationAlert)
                  else
                    Lottie.asset(AnimationAssetDark.animationAlert),
                  const SizedBox(height: 14),
                  Text(
                    LocaleKeys.areYouSure.tr(),
                    style: AppTextStyles.s16w5cBlackShade1,
                    textAlign: TextAlign.center,
                    textScaler: TextScaler.linear(textScaleFactor.value),
                  ),
                  const SizedBox(height: 14),
                  Text(
                    LocaleKeys.clearDataWarning.tr(),
                    style: AppTextStyles.s14w4cBlueTitleText,
                    textAlign: TextAlign.center,
                    textScaler: TextScaler.linear(textScaleFactor.value),
                  ),
                  const SizedBox(height: 24),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      TextButton(
                        style: TextButton.styleFrom(
                          backgroundColor: isLightMode
                              ? AppColors.blueLight
                              : AppColors.blueLightOld,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        onPressed: () {
                          context.read<IndicatorCardBloc>().add(
                                GetIndicatorDetailsEvent(
                                  id: widget.nodeIdForResetFilter!,
                                  contentType:
                                      widget.contentTypeForResetFilter!,
                                  overviewContentType: widget
                                          .overviewContentTypeForResetFilter ??
                                      widget.contentTypeForResetFilter!,
                                  isFromDriverForSearch: true,
                                ),
                              );
                          context.read<DetailsBloc>().add(
                                DetailsPageFilterResetEvent(
                                  indicatorDetails:
                                      initialIndicatorDetailsFromCompareResponse,
                                ),
                              );
                          Navigator.pop(context);
                        },
                        child: Text(
                          LocaleKeys.proceed.tr(),
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: AppColors.white,
                          ),
                          textScaler: TextScaler.linear(textScaleFactor.value),
                        ),
                      ),
                      const SizedBox(height: 14),
                      TextButton(
                        style: TextButton.styleFrom(
                          backgroundColor: AppColors.white,
                          shape: RoundedRectangleBorder(
                            side: BorderSide(color: AppColors.blue),
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        child: Text(
                          LocaleKeys.cancel.tr(),
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: AppColors.blue,
                          ),
                          textScaler: TextScaler.linear(textScaleFactor.value),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  void scrollToWidget(
    BuildContext context,
    GlobalKey key, {
    bool isPrevious = false,
  }) {
    if (key.currentContext != null) {
      final RenderBox renderBox =
          key.currentContext!.findRenderObject()! as RenderBox;
      final position = renderBox.localToGlobal(Offset.zero);
      final screenHeight = MediaQuery.of(context).size.height;
      final scrollOffset =
          position.dy - screenHeight + (isPrevious ? -50 : 180);
      widget.scrollController?.jumpTo(scrollOffset);
    }
  }

  String formatDateToUnifrom(String date) {
    // Define a list of possible date formats
    final List<String> formats = [
      'dd-MM-yyyy',
      'dd/MM/yyyy',
      'yyyy/MM/dd',
      'yyyy-MM-dd',
    ];

    // Initialize parsedDate to null
    DateTime parsedDate = DateTime.now();

    // Attempt to parse the date using each format
    for (final String _ in formats) {
      try {
        parsedDate = DateTime.parse(
          date,
        ); // Try to parse the date using the current format
        break; // If successful, exit the loop
      } catch (e) {
        continue; // If parsing fails, try the next format
      }
    }

    // If parsing was successful, format the date as dd/mm/yyyy

    return '${parsedDate.day.toString().padLeft(2, '0')}/${parsedDate.month.toString().padLeft(2, '0')}/${parsedDate.year}';
  }
}
