import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart' show textScaleFactor;
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

class FilterDriverButton extends StatelessWidget {
  const FilterDriverButton({
    required this.title,
    required this.icon,
    required this.onPressed,
    super.key,
    this.isBorderLess = false,
    this.count,
  });

  final String title;
  final String icon;
  final Function onPressed;
  final bool isBorderLess;
  final int? count;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Align(
      alignment: Alignment.centerRight,
      child: InkWell(
        onTap: () => onPressed(),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: 20,
            vertical: isBorderLess ? 4 : 10,
          ),
          decoration: ShapeDecoration(
            color: isBorderLess
                ? Colors.transparent
                : isLightMode
                    ? Colors.white
                    : AppColors.blueShade36,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(30),
              side: BorderSide(
                color: isLightMode ? Colors.transparent : AppColors.blueShade36,
              ),
            ),
            shadows: [
              if (!isBorderLess)
                const BoxShadow(
                  color: Color(0x0F000000),
                  blurRadius: 10,
                  offset: Offset(0, 4),
                ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              SvgPicture.asset(
                'assets/images/$icon.svg',
                colorFilter: isLightMode ? ColorFilter.mode(
              AppColors.blueShade22,
              BlendMode.srcIn,
            )  :ColorFilter.mode(
              AppColors.white,
              BlendMode.srcIn,
            ),
              ),
              const SizedBox(width: 10),
              Text(
                title,
                style: TextStyle(
                  color: isLightMode ? AppColors.black : AppColors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                textScaler: TextScaler.linear(textScaleFactor.value),
              ),
              if(count!=null)
              Center(
                child: Container(
                  margin: const EdgeInsets.only(left: 10,),
                  padding: const EdgeInsets.symmetric(horizontal: 6),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(50),
                    color: AppColors.red,
                  ),
                  child: Text('$count', style: TextStyle(color: AppColors.white,fontSize: 12),),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
