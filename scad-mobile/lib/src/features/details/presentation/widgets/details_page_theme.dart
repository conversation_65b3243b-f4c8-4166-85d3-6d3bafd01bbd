import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart' show textScaleFactor;
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

class DetailsPageTheme extends StatelessWidget {
  const DetailsPageTheme({
    required this.themeText,
    required this.icon,
    super.key,
  });

  final String themeText;
  final String icon;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(6),
    
          child: SvgPicture.network(
            icon,
            width: 18,
            height: 18,
            colorFilter: 
              isLightMode?
                 const ColorFilter  .mode( Colors.black,BlendMode
                                                                    .srcIn,
                                                              ) :   const ColorFilter
                                                                      .mode(
                                                                Colors.white,
                                                                BlendMode
                                                                    .srcIn,
                                                              ) ,
                                                            
          ),
        ),
        const SizedBox(width: 10),
        Expanded(
          child: Text(
            themeText,
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
            style: TextStyle(
              color: isLightMode ? AppColors.grey : AppColors.greyShade4,
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
            textScaler: TextScaler.linear(textScaleFactor.value),
          ),
        ),
      ],
    );
  }
}
