import 'package:flutter/material.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

class ChartFilterChipWidget extends StatelessWidget {
  const ChartFilterChipWidget({
    required this.index,
    required this.isSelected,
    required this.text,
    required this.onTap,
    this.listLength = 0,
    super.key,
  });

  final int index;
  final bool isSelected;
  final String text;
  final Function onTap;
  final int listLength;

  @override
  Widget build(Object context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return InkWell(
      onTap: () => onTap(),
      child: Container(
        width: 43,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: isSelected
              ? isLightMode
                  ? AppColors.blueLight
                  : AppColors.blueLightOld
              : Colors.transparent,
          borderRadius: BorderRadius.circular(40),
        ),
        child: Directionality(
          textDirection: TextDirection.ltr,
          child: Text(
            text,
            style: TextStyle(
              color: isSelected
                  ? AppColors.white
                  : isLightMode
                      ? AppColors.blackShade4
                      : AppColors.greyShade4,
              fontSize: 12,
              fontWeight: FontWeight.w400,
            ),
            textScaler: TextScaler.linear(textScaleFactor.value),
          ),
        ),
      ),
    );
  }
}
