import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_response_helper.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/intro_widget.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/showcaseview.dart';
import 'package:scad_mobile/src/features/details/domain/usecases/frequency_selector_model.dart';
import 'package:scad_mobile/src/features/details/presentation/bloc/details_bloc.dart';
import 'package:scad_mobile/src/features/details/presentation/widgets/bottom_sheets/compute_data_bottom_sheet.dart';
import 'package:scad_mobile/src/features/details/presentation/widgets/bottom_sheets/data_frequency_bottom_sheet.dart';
import 'package:scad_mobile/src/features/details/presentation/widgets/chart_action_button.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class ChartActions extends StatefulWidget {
   ChartActions({
    required this.onDataPresentationSelection,
    required this.chatButtonKey,
    required this.changeFrequencyKey,
    required this.changePresentationKey,
    required this.compareIndicatorsKey,
    required this.computeDataKey,
    required this.downloadAsKey,
    required this.scrollController,
    this.indicatorDetails,
    this.originalIndicatorForFilter,
    super.key,
    this.contentType,
    this.isComparisonActive = false,
    this.onPreviousAction,
    this.isPreviousTriggeredFromDownload = false,
    this.filteredDataList,
    this.forecastDataList = const [],
    this.solidSeries = const []
  });
  List<List<Map<String, dynamic>>> solidSeries ;
  final VoidCallback onDataPresentationSelection;
  final IndicatorDetailsResponseHelper? indicatorDetails;
  final IndicatorDetailsResponse? originalIndicatorForFilter;
  final String? contentType;
  final bool isComparisonActive;

  final GlobalKey chatButtonKey;
  final GlobalKey changeFrequencyKey;
  final GlobalKey changePresentationKey;
  final GlobalKey compareIndicatorsKey;
  final GlobalKey computeDataKey;
  final GlobalKey downloadAsKey;
  final ScrollController scrollController;
  final ValueChanged<bool?>? onPreviousAction;
  final bool isPreviousTriggeredFromDownload;
  final List<List<Map<String, dynamic>>>? filteredDataList;
  final List<List<Map<String, dynamic>>> forecastDataList;

  @override
  State<ChartActions> createState() => _ChartActionsState();
}

class _ChartActionsState extends State<ChartActions> {
  List<FrequencySelectorModel> freqList = [];

  @override
  void initState() {
    freqList = getFrequencyList();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<DetailsBloc, DetailsState>(
      listener: (context, state) {
        if (state is DataFrequencyApplyState) {
          freqList = state.freqList;
        } else if (state is ChangeDriversValueUpdateState) {
          for (final element in freqList) {
              element.isSelected = false;
          }
            freqList.lastOrNull?.isSelected = true;
        } else if (state is ComputeDataSuccessState) {
           freqList = getFrequencyList();
        } else if (state is PeriodFilterApplyAgainState) {
          Future.delayed(const Duration(milliseconds: 500), () {
            context.read<DetailsBloc>().add(
                  ChangeDataFrequencyApplyEvent(
                    selectedFrequency: freqList
                        .where((element) => element.isSelected)
                        .map((e) => e.title)
                        .first,
                    freqList: freqList,
                    inidicatorData: widget.originalIndicatorForFilter ??
                        IndicatorDetailsResponse(),
                    filteredDataList: List.from(widget.filteredDataList ?? []),
                    forecastSeriesList: List.from(widget.forecastDataList),
                  ),
                );
          });
        }
      },
      builder: (context, state) {
        return Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: IntroWidget(
                stepKey: widget.changeFrequencyKey,
                stepIndex: 6,
                totalSteps: 10,
                title: LocaleKeys.changeDataFrequency.tr(),
                description: LocaleKeys.changeDataFrequencyGuideDesc.tr(),
                arrowAlignment: Alignment.bottomLeft,
                position: TooltipPosition.top,
                targetBorderRadius: 6,
                isDownArrow: true,
                arrowPadding: EdgeInsets.only(
                  top: 10,
                  right: MediaQuery.sizeOf(context).width * 0.12,
                  left: MediaQuery.sizeOf(context).width * 0.12,
                ),
                onPrevious: () {
                  widget.scrollController.jumpTo(0);
                  scrollToWidget(context, widget.chatButtonKey);

                  if (widget.onPreviousAction != null) {
                    widget.onPreviousAction!.call(true);
                  }
                  // scrollToWidget(context, widget.chatButtonKey, isPrevious: true);
                  Future.delayed(const Duration(milliseconds: 500), () {
                    setState(() {});
                  });
                },
                targetPadding: const EdgeInsets.symmetric(vertical: 10),
                child: ChartActionButton(
                  isDisable: (widget.isComparisonActive ||
                      freqList.isEmpty ||
                      freqList.length == 1) || (widget.solidSeries.every((element) => element.isEmpty) && widget.forecastDataList.every((element) => element.isEmpty))  ,
                  text: LocaleKeys.changeDataFrequency.tr(),
                  icon: 'assets/images/data-frequency.svg',
                  onTap: () {
                    showModalBottomSheet<dynamic>(
                      isScrollControlled: true,
                      backgroundColor: Colors.transparent,
                      context: context,
                      builder: (BuildContext context) {
                        return DataFrequencyBottomSheet(
                          forecastDataList:
                              List.from(widget.forecastDataList ?? []),
                          frequencyList: freqList,
                          originalIndicatorData:
                              widget.originalIndicatorForFilter,
                          filteredDataList:
                              List.from(widget.filteredDataList ?? []),
                        );
                      },
                    );
                  },
                ),
              ),
            ),
            Expanded(
              child: IntroWidget(
                stepKey: widget.changePresentationKey,
                stepIndex: 7,
                totalSteps: 10,
                title: LocaleKeys.changeDataPresentation.tr(),
                description: LocaleKeys.changeDataPresentationGuideDesc.tr(),
                arrowAlignment: Alignment.bottomLeft,
                position: TooltipPosition.top,
                targetBorderRadius: 6,
                isDownArrow: true,
                arrowPadding: EdgeInsets.only(
                  top: 10,
                  right: MediaQuery.sizeOf(context).width * 0.34,
                  left: MediaQuery.sizeOf(context).width * 0.34,
                ),
                targetPadding: const EdgeInsets.symmetric(vertical: 10),
                child: ChartActionButton(
                  isDisable:  (widget.solidSeries.every((element) => element.isEmpty)&& widget.forecastDataList.every((element) => element.isEmpty)),
                  text: LocaleKeys.changeDataPresentation.tr(),
                  icon: 'assets/images/data-presentation.svg',
                  onTap: widget.onDataPresentationSelection,
                ),
              ),
            ),
            Expanded(
              child: IntroWidget(
                stepKey: widget.compareIndicatorsKey,
                stepIndex: 8,
                totalSteps: 10,
                title: LocaleKeys.compareIndicators.tr(),
                description: LocaleKeys.compareDataGuideDesc.tr(),
                arrowAlignment: Alignment.bottomRight,
                position: TooltipPosition.top,
                crossAxisAlignment: CrossAxisAlignment.end,
                targetBorderRadius: 6,
                isDownArrow: true,
                arrowPadding: EdgeInsets.only(
                  top: 10,
                  right: MediaQuery.sizeOf(context).width * 0.33,
                  left: MediaQuery.sizeOf(context).width * 0.33,
                ),
                targetPadding: const EdgeInsets.symmetric(vertical: 10),
                child: ChartActionButton(
                  isDisable: (!(widget.indicatorDetails?.indicatorDetails
                              .isMultiDimension !=
                          null &&
                      widget.indicatorDetails?.indicatorDetails
                              .isMultiDimension ==
                          false &&
                      widget.indicatorDetails?.indicatorDetails.indicatorId !=
                          null &&
                      widget.contentType != 'analytical_apps' &&
                      widget.contentType != 'analytical-apps' &&
                      (widget.indicatorDetails?.indicatorDetails
                                  .contentClassificationKey ==
                              'experimental_statistics' ||
                          widget.indicatorDetails?.indicatorDetails
                                  .contentClassificationKey ==
                              'official_statistics'))) ||  (widget.solidSeries.every((element) => element.isEmpty)&& widget.forecastDataList.every((element) => element.isEmpty)),
                  text: LocaleKeys.compareIndicators.tr(),
                  icon: 'assets/images/compare-indicators.svg',
                  onTap: () {
                    context.pushRoute(
                      CompareIndicatorsPageRoute(
                        domainId:
                            '${widget.indicatorDetails?.indicatorDetails.domainId}',
                        domainName:
                            widget.indicatorDetails?.indicatorDetails.domain ??
                                '',
                        contentClassificationKey:
                            '${widget.indicatorDetails?.indicatorDetails.contentClassificationKey}',
                        indicatorId: widget
                            .indicatorDetails?.indicatorDetails.indicatorId,
                        indicatorDetails: widget.indicatorDetails,
                      ),
                    );
                  },
                ),
              ),
            ),
            Expanded(
              child: IntroWidget(
                stepKey: widget.computeDataKey,
                stepIndex: 9,
                totalSteps: 10,
                title: LocaleKeys.computeData.tr(),
                description: LocaleKeys.computeDataGuideDesc.tr(),
                arrowAlignment: Alignment.bottomRight,
                crossAxisAlignment: CrossAxisAlignment.end,
                position: TooltipPosition.top,
                targetBorderRadius: 6,
                onNext: () {
                  // if (!widget.isPreviousTriggeredFromDownload) {
                    scrollToWidget(context, widget.downloadAsKey);
                  // }
                  Future.delayed(const Duration(milliseconds: 300), () {
                    setState(() {});
                  });
                },
                isDownArrow: true,
                arrowPadding: EdgeInsets.only(
                  top: 10,
                  right: MediaQuery.sizeOf(context).width * 0.1,
                  left: MediaQuery.sizeOf(context).width * 0.1,
                ),
                targetPadding: const EdgeInsets.symmetric(vertical: 10),
                child: ChartActionButton(
                  isDisable: (widget.isComparisonActive ||
                      ((widget.indicatorDetails?.indicatorDetails.filterPanel ==
                              null) ||
                          widget.indicatorDetails?.indicatorDetails
                                  .filterPanel ==
                              false)) || (widget.solidSeries.every((element) => element.isEmpty)&& widget.forecastDataList.every((element) => element.isEmpty)),
                  text: LocaleKeys.computeData.tr(),
                  icon: 'assets/images/compute-data.svg',
                  onTap: () {
                    if (widget.indicatorDetails?.indicatorDetails.filterPanel !=
                        false) {
                      context.read<DetailsBloc>().add(
                            ComputeBottomSheetValueEvent(
                              value: LocaleKeys.summation.tr(),
                            ),
                          );
                      showModalBottomSheet<dynamic>(
                        isScrollControlled: true,
                        backgroundColor: Colors.transparent,
                        context: context,
                        builder: (BuildContext context) {
                          return ComputeDataBottomSheet(
                            indicatorId:
                                widget.indicatorDetails?.indicatorDetails.indicatorId ??
                                    '',
                            filterPanel: widget.indicatorDetails
                                ?.indicatorDetails.filterPanel as FilterPanel?,
                          );
                        },
                      );
                    }
                  },
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void scrollToWidget(BuildContext context, GlobalKey key,
      {bool isPrevious = false}) {
    if (key.currentContext != null) {
      final RenderBox renderBox =
          key.currentContext!.findRenderObject()! as RenderBox;
      final position = renderBox.localToGlobal(Offset.zero);
      final screenHeight = MediaQuery.of(context).size.height;
      final scrollOffset =
          position.dy - screenHeight + (isPrevious ? -10 : 300);
      widget.scrollController.jumpTo(scrollOffset);
    }
  }

  String translateToArabic(String englishText) {
    final Map<String, String> translationMap = {
      'Monthly': 'شهري',
      'Quarterly': 'ربع سنوي',
      'Yearly': 'سنوي',
    };

    return translationMap[englishText] ?? englishText;
  }

  List<FrequencySelectorModel> getFrequencyList() {
    final bool isArabic = HiveUtilsSettings.getAppLanguage() == 'ar';
    List<String> dataList = List.from(
      widget.originalIndicatorForFilter?.indicatorVisualizations
              ?.visualizationsMeta?.firstOrNull?.timeUnit ??
          [],
    );

    if (isArabic) {
      dataList = dataList.map((e) => translateToArabic(e)).toList();
    }

    final List<FrequencySelectorModel> frequencyList = [];

    for (final element in dataList) {
      frequencyList.add(
        FrequencySelectorModel(title: element, isSelected: false),
      );
    }

    if ((widget.originalIndicatorForFilter?.indicatorVisualizations
                    ?.visualizationsMeta?.firstOrNull?.showQuarterlyIntervals ==
                'true' ||
            widget.originalIndicatorForFilter?.indicatorVisualizations
                    ?.visualizationsMeta?.firstOrNull?.showQuarterlyIntervals ==
                true) &&
        dataList.isEmpty) {
      frequencyList.addAll([
        FrequencySelectorModel(
          title: isArabic ? 'سنوي' : 'Yearly',
          isSelected: false,
        ),
        FrequencySelectorModel(
          title: isArabic ? 'ربع سنوي' : 'Quarterly',
          isSelected: false,
        ),
      ]);
    }

    if ((dataList.contains('Monthly') || dataList.contains('شهري')) &&
        !(dataList.contains('Quarterly') || dataList.contains('ربع سنوي'))) {
      frequencyList.insert(
        1,
        FrequencySelectorModel(
          title: isArabic ? 'ربع سنوي' : 'Quarterly',
          isSelected: false,
        ),
      );
    }

    bool isSelectedSet = false;

    // for (final element in frequencyList) {
    //   if (element.title == context.read<DetailsBloc>().selectedFrequency) {
    //     element.isSelected = true;
    //     isSelectedSet = true;
    //     break;
    //   }
    // }

    if (!isSelectedSet) {
      for (final element in frequencyList.reversed.toList()) {
        if (element.title == 'Monthly' || element.title == 'شهري') {
          element.isSelected = true;
          break;
        } else if (element.title == 'Quarterly' ||
            element.title == 'ربع سنوي') {
          element.isSelected = true;
          break;
        }
      }
    }

    if (!isSelectedSet &&
        frequencyList.isNotEmpty &&
        frequencyList.every((element) => element.isSelected == false)) {
      frequencyList.first.isSelected = true;
    }

    return frequencyList;
  }
}
