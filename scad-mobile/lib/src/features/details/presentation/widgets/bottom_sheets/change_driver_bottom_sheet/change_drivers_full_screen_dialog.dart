import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/functions/indicator_date_setting.dart';
import 'package:scad_mobile/src/common/widgets/app_sliding_tab.dart';
import 'package:scad_mobile/src/common/widgets/charts/spline_chart.dart';
import 'package:scad_mobile/src/common/widgets/charts/treemap_chart.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_response_helper.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/presentation/bloc/indicator_card_bloc.dart';
import 'package:scad_mobile/src/features/details/presentation/bloc/details_bloc.dart';
import 'package:scad_mobile/src/features/details/presentation/widgets/bottom_sheets/change_driver_bottom_sheet/driver_slider_widget.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/extentions/color_extensions.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';
import 'package:syncfusion_flutter_treemap/treemap.dart';

@RoutePage()
class ChangeDriversFullScreenDialog extends StatefulWidget {
  const ChangeDriversFullScreenDialog({
    required this.indicatorDetails,
    super.key,
    this.index = 0,
    this.isWhatIfDetails = false,
    this.scadProjection,
    this.contentType,
  });

  final IndicatorDetailsResponseHelper indicatorDetails;
  final int index;
  final bool isWhatIfDetails;
  final bool? scadProjection;
  final String? contentType;

  @override
  State<ChangeDriversFullScreenDialog> createState() =>
      _ChangeDriversFullScreenDialogState();
}

class _ChangeDriversFullScreenDialogState
    extends State<ChangeDriversFullScreenDialog> {
  ScrollController scrollController = ScrollController();
  IndicatorDetailsResponseHelper? indicatorDetails;
  List<IndicatorDriver> indicatorDrivers = [];
  List<List<Map<String, dynamic>>> solidSeries = [];
  List<List<Map<String, dynamic>>> forecastSeries = [];

  final Map<String, String> payload = {};
  double minValue = 0;
  double maxValue = 100;
  ValueNotifier<int> selectedDomainTabIndex = ValueNotifier(0);
  ValueNotifier<bool> scadProjectionState = ValueNotifier(false);
  ValueNotifier<List<int>> selectedDrivers = ValueNotifier([]);
  List<VisualizationsMeta> indicatorList = [];
  List<SeriesMeta> seriesMeta = [];
  String? selectedFrequencyForFilter;
  bool isCallingApi = false;
  bool oldScadprojectionState = false;

  @override
  void initState() {
    super.initState();
    selectedDomainTabIndex.value =
        (widget.index == 0 || widget.index == 1) ? 0 : widget.index - 1;
    indicatorDetails = widget.indicatorDetails;
    indicatorList = indicatorDetails
            ?.indicatorDetails.indicatorVisualizations!.visualizationsMeta! ??
        [];
    indicatorDrivers =
        indicatorDetails?.indicatorDetails.indicatorDrivers ?? [];
    seriesMeta = indicatorDetails?.indicatorDetails.indicatorVisualizations!
            .visualizationsMeta?.firstOrNull?.seriesMeta ??
        [];
    for (final item in seriesMeta) {
      if (item.id!.contains('-forecast')) {
        forecastSeries.add(
          indicatorDetails!
              .getFilteredSeries(seriesMetaIndex: seriesMeta.indexOf(item)),
        );
      } else {
        solidSeries.add(
          indicatorDetails!
              .getFilteredSeries(seriesMetaIndex: seriesMeta.indexOf(item)),
        );
      }
    }
    selectedDrivers.value.clear();
    for (int i = 0; i < indicatorDrivers.length; i++) {
      if (indicatorDrivers[i].viewOnly != true) {
        payload[indicatorDrivers[i].id!] = 'medium';
      }
      for (int j = 0; j < (indicatorDrivers[i].options ?? []).length; j++) {
        final option = indicatorDrivers[i].options?[j];
        if (indicatorDrivers[i].viewOnly != true) {
          if (option?.isSelected == true) {
            payload[indicatorDrivers[i].id!] = option!.value!;
          }
        } else {
          if (widget.scadProjection == null) {
            scadProjectionState.value = true;
            oldScadprojectionState = true;
            updateDrivers(payloadData: {});
          } else {
            scadProjectionState.value = widget.scadProjection!;
            oldScadprojectionState = widget.scadProjection!;
          }
        }
      }
      selectedDrivers.value.add(
        (indicatorDrivers[i].options ?? []).indexWhere((e) => e.isSelected!),
      );
    }
    final indicatorDateSetting = IndicatorDateSetting.setFrequancy(
        l: solidSeries, indicatorDetails: indicatorDetails);

    selectedFrequencyForFilter =
        indicatorDateSetting['selectedFrequencyForFilter'] as String;
  }

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    final bool isArabic = HiveUtilsSettings.getAppLanguage() == 'ar';

    return PopScope(
      // canPop: false,
      onPopInvoked: (didPop) {
        resetDriversEvent(true);
      },
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          surfaceTintColor: Colors.transparent,
          title: Text(
            indicatorDetails?.title ?? '',
            style: AppTextStyles.s18w5cBlackShade.copyWith(
              color: !isLightMode ? AppColors.white : null,
            ),
            textScaler: TextScaler.linear(textScaleFactor.value),
          ),
          centerTitle: true,
          leading: Padding(
            padding: EdgeInsets.only(
              left: 16,
              top: 16,
              bottom: 16,
              right: isArabic ? 16 : 0,
            ),
            child: IconButton(
              constraints: const BoxConstraints(),
              padding: EdgeInsets.zero,
              icon: const Icon(Icons.close_rounded),
              onPressed: () {
                resetDriversEvent(true);
                // context.router.pop();
                Navigator.pop(context);
              },
            ),
          ),
          // leadingWidth: 40,
        ),
        body: BlocConsumer<IndicatorCardBloc, IndicatorCardState>(
          listener: (context, state) {
            if (state is IndicatorDetailsSuccessState) {
              indicatorDetails = IndicatorDetailsResponseHelper(
                state.indicatorDetails,
              );
              indicatorDrivers =
                  indicatorDetails?.indicatorDetails.indicatorDrivers ?? [];
              selectedDrivers.value = [];
              for (int i = 0; i < indicatorDrivers.length; i++) {
                selectedDrivers.value.add(
                  (indicatorDrivers[i].options ?? [])
                      .indexWhere((e) => e.isSelected!),
                );
              }

              for (final item in seriesMeta) {
                if (item.id!.contains('-forecast')) {
                  forecastSeries.add(
                    indicatorDetails!.getFilteredSeries(
                      seriesMetaIndex: seriesMeta.indexOf(item),
                    ),
                  );
                } else {
                  solidSeries.add(
                    indicatorDetails!.getFilteredSeries(
                      seriesMetaIndex: seriesMeta.indexOf(item),
                    ),
                  );
                }
              }
              final indicatorDateSetting = IndicatorDateSetting.setFrequancy(
                  l: solidSeries, indicatorDetails: indicatorDetails);

              selectedFrequencyForFilter =
                  indicatorDateSetting['selectedFrequencyForFilter'] as String;
              isCallingApi = false;
            }
          },
          builder: (context, state) {
            return Column(
              children: [
                if (state is IndicatorDetailsLoadingState)
                  LinearProgressIndicator(
                    color: AppColors.blue,
                    backgroundColor: AppColors.blue.withOpacity(0.1),
                  )
                else
                  const SizedBox(height: 4),
                if (widget.isWhatIfDetails)
                  AppSlidingTab(
                    initialTabIndex: selectedDomainTabIndex.value,
                    onTabChange: (int index) {
                      selectedDomainTabIndex.value = index;
                    },
                    tabs: indicatorList
                        .map(
                          (e) => AppSlidingTabItem(
                            label: e.vizLabel ?? '',
                          ),
                        )
                        .toList(),
                  ),
                const SizedBox(height: 10),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 14),
                  child: ValueListenableBuilder(
                    valueListenable: selectedDomainTabIndex,
                    builder: (context, index, _) {
                      return _whatIfChartRepresentation(index);
                    },
                  ),
                ),
                const SizedBox(height: 10),
                Expanded(
                  child: Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color:
                          isLightMode ? AppColors.white : AppColors.blueShade32,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(20),
                        topRight: Radius.circular(20),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const SizedBox(height: 16),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                LocaleKeys.changeDrivers.tr(),
                                style: AppTextStyles.s16w5cBlackShade1.copyWith(
                                  color: !isLightMode ? AppColors.white : null,
                                ),
                                textScaler:
                                    TextScaler.linear(textScaleFactor.value),
                              ),
                              const SizedBox(height: 10),
                              Text(
                                LocaleKeys.modifyDrivers.tr(),
                                style: AppTextStyles.s12w4cGreyShade4.copyWith(
                                  color: isLightMode ? AppColors.grey : null,
                                ),
                                textScaler:
                                    TextScaler.linear(textScaleFactor.value),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 15),
                        driversListView(isLightMode, isArabic),
                        const SizedBox(height: 20),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              minimumSize: const Size.fromHeight(43),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                            onPressed: () {
                              context.read<DetailsBloc>().add(
                                    ChangeDriversValueUpdateEvent(
                                      indicatorDetails: indicatorDetails,
                                    ),
                                  );
                              resetDriversEvent(false);
                              Navigator.pop(context);
                            },
                            child: Text(
                              LocaleKeys.apply.tr(),
                              style: TextStyle(
                                color: AppColors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                              textScaler:
                                  TextScaler.linear(textScaleFactor.value),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  void resetDriversEvent(bool toRestDrivers) {
    context.read<DetailsBloc>().add(
          ToResetChangeDriversEvent(
            toReset: toRestDrivers,
            scadProjectionValue: toRestDrivers
                ? oldScadprojectionState
                : scadProjectionState.value,
            indicatorVal: indicatorDetails,
          ),
        );
  }

  Expanded driversListView(bool isLightMode, bool isArabic) {
    return Expanded(
      child: Scrollbar(
        controller: scrollController,
        thumbVisibility: true,
        child: ListView.builder(
          controller: scrollController,
          shrinkWrap: true,
          itemCount: indicatorDrivers.length,
          itemBuilder: (context, index) {
            final IndicatorDriver item = indicatorDrivers[index];
            final List<DriverOptions> options = item.options ?? [];
            final double interval = maxValue / (options.length - 1);

            int getOptionIndex(double value) {
              return (value / interval).round();
            }

            double getValue() {
              double selectedValue = 0;
              for (int i = 0; i < options.length; i++) {
                print('driver options -> ${options[i].label}');
                if (options[i].isSelected ?? false) {
                  selectedValue = interval * i;
                }
              }
              return selectedValue;
            }

            final List<DriverOptions> transformedOptions = [];

            if (isArabic) {
              for (final DriverOptions option in options) {
                String transformedOption =
                    option.label!.replaceAll('-', '').replaceAll('+', '');
                if (option.label!.startsWith('-')) {
                  transformedOption += '-';
                } else if (option.label!.startsWith('+')) {
                  transformedOption += '+';
                }
                transformedOptions.add(
                  DriverOptions(
                    label: transformedOption,
                    value: option.value,
                    isSelected: option.isSelected,
                  ),
                );
              }
            }

            return ValueListenableBuilder(
              valueListenable: scadProjectionState,
              builder: (context, isScadProjectionActive, _) {
                return item.viewOnly == true
                    ? Column(
                        children: [
                          Padding(
                            padding: const EdgeInsets.fromLTRB(24, 4, 24, 10),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  item.title ?? '',
                                  style: TextStyle(
                                    color: isLightMode
                                        ? AppColors.black
                                        : AppColors.white,
                                  ),
                                  textScaler: TextScaler.linear(
                                    textScaleFactor.value,
                                  ),
                                ),
                                SizedBox(
                                  height: 22,
                                  width: 50,
                                  child: FittedBox(
                                    fit: BoxFit.fitHeight,
                                    child: IgnorePointer(
                                      ignoring: isCallingApi,
                                      child: CupertinoSwitch(
                                        activeColor: AppColors.blueShade22,
                                        trackColor: AppColors.greySwitchOff,
                                        value: isScadProjectionActive,
                                        onChanged: (val) {
                                          scadProjectionState.value = val;
                                          if (val) {
                                            selectedDrivers.value.clear();
                                            for (int i = 0;
                                                i < indicatorDrivers.length;
                                                i++) {
                                              if (indicatorDrivers[i]
                                                      .viewOnly !=
                                                  true) {
                                                payload[indicatorDrivers[i]
                                                    .id!] = 'medium';
                                              }
                                              selectedDrivers.value.add(
                                                (indicatorDrivers[i].options ??
                                                        [])
                                                    .indexWhere(
                                                  (e) => e.isSelected!,
                                                ),
                                              );
                                            }
                                            updateDrivers(payloadData: {});
                                          } else {
                                            for (int i = 0;
                                                i < indicatorDrivers.length;
                                                i++) {
                                              if (indicatorDrivers[i]
                                                      .viewOnly !=
                                                  true) {
                                                payload[indicatorDrivers[i]
                                                    .id!] = 'medium';
                                              }
                                            }
                                            updateDrivers(payloadData: payload);
                                          }
                                        },
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Divider(color: AppColors.greyShade8),
                          const SizedBox(height: 15),
                        ],
                      )
                    : ValueListenableBuilder(
                        valueListenable: selectedDrivers,
                        builder: (context, selectedLabels, _) {
                          return IgnorePointer(
                            ignoring: scadProjectionState.value,
                            child: Opacity(
                              opacity: scadProjectionState.value ? 0.5 : 1,
                              child: DriverSlider(
                                key: ValueKey(
                                    'DriverSlider.${item.id}.${getValue()}'),
                                title: item.title ?? '',
                                interval: interval,
                                minValue: minValue,
                                maxValue: maxValue,
                                value: getValue(),
                                labelFormatterCallback:
                                    (actualValue, formattedText) {
                                  final i =
                                      getOptionIndex(actualValue as double);
                                  return options[i].label?.toLowerCase() ==
                                          'no change'
                                      ? '0'
                                      : isArabic
                                          ? '${transformedOptions[i].label}'
                                          : '${options[i].label}';
                                },
                                onChanged: (value) {
                                  final i = getOptionIndex(value);
                                  payload[indicatorDrivers[index].id!] =
                                      options[i].value!;
                                  selectedDrivers.value[index] = i;
                                  updateDrivers();
                                },
                                selectedLabel: options[selectedLabels[index]]
                                            .label
                                            ?.toLowerCase() ==
                                        'no change'
                                    ? '0'
                                    : isArabic
                                        ? transformedOptions[
                                                selectedLabels[index]]
                                            .label
                                        : options[selectedLabels[index]].label,
                                onMinusIconTap: () {
                                  if (selectedLabels[index] > 0) {
                                    selectedDrivers.value[index]--;
                                    payload[indicatorDrivers[index].id!] =
                                        options[selectedLabels[index]].value!;
                                    updateDrivers();
                                  }
                                },
                                onPlusIconTap: () {
                                  if (selectedLabels[index] <
                                      options.length - 1) {
                                    selectedDrivers.value[index]++;
                                    payload[indicatorDrivers[index].id!] =
                                        options[selectedLabels[index]].value!;
                                    updateDrivers();
                                  }
                                },
                              ),
                            ),
                          );
                        },
                      );
              },
            );
          },
        ),
      ),
    );
  }

  void updateDrivers({Map<String, String>? payloadData}) =>
      context.read<IndicatorCardBloc>().add(
            GetIndicatorDetailsEvent(
              id: indicatorDetails!.indicatorDetails.id!,
              contentType: indicatorDetails!.indicatorDetails.indicatorType ??
                  widget.contentType.toString(),
              overviewContentType:
                  indicatorDetails!.indicatorDetails.contentClassificationKey ??
                      widget.contentType.toString(),
              payload: payloadData ?? payload,
              // isFromChangeDriverCallInitOnce: true,
              isFromDriverForSearch: true,
            ),
          );

  Widget _whatIfChartRepresentation(int index) {
    final List<List<SplineChartData>> nowCast = [];
    final List<List<SplineChartData>> foreCast = [];
    final List<List<SplineChartData>> lowerAreaForecast = [];
    final List<List<SplineChartData>> upperAreaForecast = [];
    List<List<SplineChartData>> areaForecast = [];
    List<List<Map<String, dynamic>>> l =
        indicatorDetails!.getFilteredSeriesForMultiDrivers(
      visualizationsMetaIndex: index,
    );
    final String chartType = indicatorDetails?.indicatorDetails
                .indicatorVisualizations?.visualizationsMeta !=
            null
        ? indicatorDetails?.getFilteredVisualizationMetaList().first.type ?? ''
        : '';
    List<SeriesMeta> treeMapSeriesMeta = [];
    final indicatorDateSetting = IndicatorDateSetting.setFrequancy(
        l: l, indicatorDetails: indicatorDetails);

    selectedFrequencyForFilter =
        indicatorDateSetting['selectedFrequencyForFilter'] as String;
    if (chartType == 'tree-map-with-change-chart') {
      treeMapSeriesMeta = indicatorDetails
              ?.getFilteredVisualizationMetaList()
              .firstOrNull
              ?.seriesMeta ??
          [];
    }
    // final Map<String, dynamic> dataFrequacyChange =
    //     IndicatorDateSetting.setFrequancy(
    //   l: l,
    //   indicatorDetails: indicatorDetails,
    // );
    // final String filterName =
    //     dataFrequacyChange['selectedFrequencyForFilter'] as String;
    // l = dataFrequacyChange['value'] as List<List<Map<String, dynamic>>>;
    for (int i = 0; i < l.length; i++) {
      if (seriesMeta[i].id!.contains('-forecast')) {
        // if (filterName == 'Monthly') {
        //   l[i] = IndicatorDateSetting.setUpNameMonth(list: [l[i]])[0];
        // } else if (filterName == 'Quarterly') {
        //   l[i] = IndicatorDateSetting.setUpName(list: [l[i]])[0];
        // } else if (filterName == 'Yearly') {
        //   l[i] =
        //       IndicatorDateSetting.setUpName(list: [l[i]], type: 'yearly')[0];
        // }

        foreCast.add(
          l[i]
              .map(
                (e) => SplineChartData(
                  e['OBS_DT'].toString(),
                  num.parse('${e['VALUE'] ?? '0'}'),
                ),
              )
              .toList(),
        );
        for (int i = 0; i < l.length; i++) {
          final List<SplineChartData> data = [];

          for (final e in l[i]) {
            if (e['VALUE_LL'] != null) {
              data.add(
                SplineChartData(
                  e['OBS_DT'].toString(),
                  num.parse('${e['VALUE'] ?? '0'}'),
                  y: num.parse('${e['VALUE_LL'] ?? '0'}'),
                ),
              );
            }
          }

          lowerAreaForecast.add(data);
        }

        for (int i = 0; i < l.length; i++) {
          final List<SplineChartData> data = [];

          for (final e in l[i]) {
            if (e['VALUE_UL'] != null) {
              data.add(
                SplineChartData(
                  e['OBS_DT'].toString(),
                  num.parse('${e['VALUE'] ?? '0'}'),
                  y: num.parse('${e['VALUE_UL'] ?? '0'}'),
                ),
              );
            }
          }

          upperAreaForecast.add(data);
        }
        areaForecast = [...lowerAreaForecast, ...upperAreaForecast];
      } else {
        nowCast.add(
          l[i]
              .map(
                (e) => SplineChartData(
                  e['OBS_DT'].toString(),
                  num.parse('${e['VALUE'] ?? '0'}'),
                ),
              )
              .toList()
          /*.reversed
              .take(12)
              .toList()
              .reversed
              .toList()*/
          ,
        );
      }
    }
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return chartType == 'tree-map-with-change-chart'
        ? _treeMapChartRepresentation(treeMapSeriesMeta)
        : SizedBox(
            height: 200,
            child: SplineChart(
              indicatorCard: isLightMode,
              frequance: selectedFrequencyForFilter ?? '',
              isForecast: true,
              chartDataList: nowCast,
              forecastChartDataList: foreCast,
              areaHighlightChartData: areaForecast,
              driver: true,
            ),
          );
  }

  Widget _treeMapChartRepresentation(
    List<SeriesMeta> treeMapSeriesMeta,
  ) {
    // final rtl = DeviceType.isDirectionRTL(context);
    // final List<TreemapColorMapper> colorMappers = [];
    // for (int i = 0; i < treeMapSeriesMeta.length; i++) {
    //   colorMappers.add(
    //     TreemapColorMapper.value(
    //       value: treeMapSeriesMeta[i].data?.first['CHANGE'].toString(),
    //       color: treeMapSeriesMeta[i].color.toString().toColor(),
    //     ),
    //   );
    // }
    return FittedBox(
      child: SizedBox(
        height: 210 * (textScaleFactor.value < 1 ? 1 : textScaleFactor.value),
        width: MediaQuery.sizeOf(context).width * 0.9,
        child: TreemapChart(
          key: Key('treemap.${treeMapSeriesMeta.map((e) => e.color).join()}'),
          chartSeriesData: treeMapSeriesMeta,
          // colorMappers: colorMappers,
          // chartData: treeMapSeriesMeta
          //     .map(
          //       (e) => TreeMapChartData(
          //         sector:
          //             e.data?[0][rtl ? 'SECTOR_AR' : 'SECTOR'].toString() ?? '',
          //         currentValue: double.parse(
          //           '${e.data?[0]['VALUE_CURRENT'] ?? '0'}',
          //         ),
          //         forecastedValue: double.parse(
          //           '${e.data?[0]['VALUE_FORECAST'] ?? '0'}',
          //         ),
          //         qqChange: double.parse(
          //           '${e.data?[0]['CHANGE'] ?? '0'}',
          //         ),
          //         yyChange: double.parse(
          //           '${e.data?[0]['CHANGE_PY'] ?? '0'}',
          //         ),
          //         proportionOfTotalEconomy: double.parse(
          //           '${e.data?[0]['VALUE_PERC_ECO'] ?? '0'}',
          //         ),
          //       ),
          //     )
          //     .toList(),
        ),
      ),
    );
  }
}
