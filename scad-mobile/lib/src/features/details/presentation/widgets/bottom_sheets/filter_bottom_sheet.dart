import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/check_box_text_row.dart';
import 'package:scad_mobile/src/common/functions/indicator_date_setting.dart';
import 'package:scad_mobile/src/common/widgets/bottom_sheet_top_notch.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_response_helper.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/features/details/presentation/bloc/details_bloc.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class FilterBottomSheet extends StatefulWidget {
  const FilterBottomSheet({
    required this.indicatorData,
    required this.filterData,
    required this.originalIndicatorForFilter,
    this.selectedVisualization,
    this.insightToFilterData,
    this.properties,
    super.key,
  });

  final IndicatorDetailsResponseHelper? indicatorData;
  final FilterPanel? filterData;
  final IndicatorDetailsResponse? originalIndicatorForFilter;
  final Visualizations? selectedVisualization;
  final List<Map<String, dynamic>>? insightToFilterData;
  final List<Properties>? properties;

  @override
  State<FilterBottomSheet> createState() => _FilterBottomSheetState();
}

class _FilterBottomSheetState extends State<FilterBottomSheet> {
  List<Properties>? properties = [];

  @override
  void initState() {
    properties = IndicatorDateSetting.removeDuplicates(widget.properties ?? []);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;

    bool isLoading = false;

    return BlocConsumer<DetailsBloc, DetailsState>(
      listener: (context, state) {
        if (state is FilterDropdownExpansionSatte) {
          properties = state.updatedList;
        } else if (state is FilterCheckboxState) {
          properties = state.propertiesList;
        } else if (state is FilterApplyState) {
          final bool isEmpty =
              state.filteredDataList?.every((element) => element.isEmpty) ??
                  false;
          if (isEmpty) {
            AppMessage.showOverlayNotification(
              '',
              LocaleKeys.filterEmptyMessage.tr(),
              msgType: 'error',
            );
          } else {
            Navigator.pop(context);
          }
        } else if (state is FilterApplyLoadingState) {
          isLoading = state.isLoading;
        }
      },
      builder: (context, state) {
        return Container(
          width: double.infinity,
          padding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
          decoration: BoxDecoration(
            color: isLightMode ? AppColors.white : AppColors.blueShade32,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 10),
              const BottomSheetTopNotch(),
              const SizedBox(height: 16),
              Text(
                LocaleKeys.filters.tr(),
                style: TextStyle(
                  color: isLightMode ? AppColors.blackShade1 : AppColors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                textScaler: TextScaler.linear(textScaleFactor.value),
              ),
              const SizedBox(height: 20),
              Flexible(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const SizedBox(height: 15),
                      ...List.generate(properties?.length ?? 0, (index) {
                        final data = properties?[index];
                        return CustomExpandableDropdown(
                          width: (MediaQuery.sizeOf(context).width - 64) / 2.0,
                          isSelected: data?.isOpened ?? false,
                          title: data?.label,
                          dataText: data?.selectedOptionItem,
                          onTap: () {
                            context.read<DetailsBloc>().add(
                                  FilterDropdownExpansionEvent(
                                    index: index,
                                    propertyList: properties ?? [],
                                  ),
                                );
                          },
                          children: List.generate(
                            data?.optionList.length ?? 0,
                            (childIndex) {
                              final item = data?.optionList[childIndex];
                              return Padding(
                                padding: EdgeInsets.only(
                                  bottom: childIndex ==
                                          (data?.optionList.length ?? 0) - 1
                                      ? 0
                                      : 10,
                                ),
                                child: CheckBoxTextRow(
                                  title: item?.title ?? '',
                                  titleColor: isLightMode
                                      ? AppColors.blackTextTile
                                      : AppColors.white,
                                  isSelected: item?.isSelected ?? false,
                                  isRadioButton: data?.type == 'radio',
                                  onChanged: () {
                                    if (data?.type == 'radio') {
                                      context.read<DetailsBloc>().add(
                                            FilterRadioEvent(
                                              index: index,
                                              childIndex: childIndex,
                                              propertiesList: properties ?? [],
                                            ),
                                          );
                                    } else {
                                      context.read<DetailsBloc>().add(
                                            FilterCheckboxEvent(
                                              index: index,
                                              childIndex: childIndex,
                                              propertiesList: properties ?? [],
                                            ),
                                          );
                                    }
                                  },
                                ),
                              );
                            },
                          ),
                        );
                      }),
                      const SizedBox(height: 30),
                      ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          minimumSize: const Size.fromHeight(43),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        onPressed: () {
                          context.read<DetailsBloc>().add(
                                FilterApplyEvent(
                                  originlIndicatorDetails:
                                      widget.originalIndicatorForFilter ??
                                          IndicatorDetailsResponse(),
                                  propertyList: properties ?? [],
                                  selectedVisualization:
                                      widget.selectedVisualization,
                                  insightToFilterData:
                                      widget.insightToFilterData,
                                ),
                              );
                        },
                        child: isLoading
                            ? SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  color: AppColors.white,
                                ),
                              )
                            : Text(
                                LocaleKeys.apply.tr(),
                                style: TextStyle(
                                  color: AppColors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                                textScaler:
                                    TextScaler.linear(textScaleFactor.value),
                              ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

class CustomExpandableDropdown extends StatelessWidget {
  const CustomExpandableDropdown({
    required this.isSelected,
    this.width,
    this.title,
    this.dataText,
    this.children,
    this.onTap,
    super.key,
  });

  final bool isSelected;
  final double? width;
  final String? title;
  final String? dataText;
  final List<Widget>? children;
  final Function? onTap;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch, //CrossAxisAlignment.start
      children: [
        Text(
          title ?? '',
          style: TextStyle(
            color: AppColors.greyShade4,
            fontSize: 14,
            fontWeight: FontWeight.w400,
          ),
          textScaler: TextScaler.linear(textScaleFactor.value),
        ),
        const SizedBox(height: 5),
        InkWell(
          onTap: () => onTap?.call(),
          child: Container(
            width: width ?? MediaQuery.sizeOf(context).width,
            height: 40,
            decoration: BoxDecoration(
              color: isLightMode ? AppColors.white : AppColors.blueShade36,
              borderRadius: BorderRadius.circular(70),
              border: Border.all(
                color: isSelected
                    ? isLightMode
                        ? AppColors.blueLight
                        : AppColors.selectedChipBlue
                    : isLightMode
                        ? AppColors.greyShade1
                        : AppColors.blackShade8,
              ),
            ),

            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 15),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: Text(
                      dataText ?? '',
                      style: TextStyle(
                        color: isLightMode
                            ? AppColors.blackTextTile
                            : AppColors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                      textScaler: TextScaler.linear(textScaleFactor.value),
                    ),
                  ),
                  Icon(
                    !isSelected ? Icons.arrow_drop_down : Icons.arrow_drop_up,
                  ),
                ],
              ),
            ),
            // ),
          ),
        ),
        AnimatedCrossFade(
          firstChild: Container(
            height: 10,
          ),
          secondChild: Container(
            margin: const EdgeInsets.only(top: 7, bottom: 16),
            padding: const EdgeInsets.all(14),
            width: MediaQuery.sizeOf(context).width,
            decoration: BoxDecoration(
              color: isLightMode ? Colors.transparent : AppColors.blueShade32,
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: isLightMode
                    ? AppColors.greyShade1
                    : AppColors.selectedChipBlue,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: children ?? [],
            ),
          ),
          duration: const Duration(milliseconds: 300),
          crossFadeState:
              isSelected ? CrossFadeState.showSecond : CrossFadeState.showFirst,
        ),
      ],
    );
  }
}
