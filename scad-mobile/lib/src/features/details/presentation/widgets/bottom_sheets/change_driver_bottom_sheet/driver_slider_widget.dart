import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:flutter/material.dart';
import 'package:scad_mobile/src/features/details/presentation/widgets/painter/app_slider_thumb_shape.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:syncfusion_flutter_core/theme.dart';
import 'package:syncfusion_flutter_sliders/sliders.dart';

class DriverSlider extends StatefulWidget {
  const DriverSlider({
    required this.title,
    required this.minValue,
    required this.maxValue,
    required this.value,
    required this.interval,
    required this.onChanged,
    required this.onPlusIconTap,
    required this.onMinusIconTap,
    required this.selectedLabel,
    this.showDivider = true,
    super.key,
    this.labelFormatterCallback,
  });

  final String title;
  final String? selectedLabel;
  final double minValue;
  final double maxValue;
  final double value;
  final double interval;
  final bool showDivider;
  final void Function(double) onChanged;
  final void Function() onPlusIconTap;
  final void Function() onMinusIconTap;
  final String Function(dynamic, String)? labelFormatterCallback;

  @override
  State<DriverSlider> createState() => _DriverSliderState();
}

class _DriverSliderState extends State<DriverSlider> {
  double sliderValue = 0;

  @override
  void initState() {
    super.initState();

    sliderValue = widget.value;
  }

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                widget.title,
                style: TextStyle(
                  color: isLightMode
                      ? AppColors.blackShade1
                      : AppColors.greyShade4,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
                textScaler: TextScaler.linear(textScaleFactor.value),
              ),
              Container(
                decoration: BoxDecoration(
                  color:
                      isLightMode ? Colors.transparent : AppColors.blueShade36,
                  borderRadius: BorderRadius.circular(30),
                  border: Border.all(
                    color: isLightMode
                        ? AppColors.greyShade1
                        : AppColors.blueShade36,
                  ),
                ),
                child: SizedBox(
                  height: 40,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      IconButton(
                        icon: Icon(
                          Icons.remove,
                          color:
                              isLightMode ? AppColors.black : AppColors.white,
                        ),
                        padding: const EdgeInsets.all(8),
                        iconSize: 20,
                        constraints: const BoxConstraints(),
                        color: Theme.of(context).primaryColor,
                        onPressed: widget.onMinusIconTap,
                      ),
                      Text(
                        widget.selectedLabel ?? '0',
                        textAlign: TextAlign.center,
                        textScaler: TextScaler.linear(textScaleFactor.value),
                        style: TextStyle(
                          color:
                              isLightMode ? AppColors.black : AppColors.white,
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      IconButton(
                        icon: Icon(
                          Icons.add,
                          color:
                              isLightMode ? AppColors.black : AppColors.white,
                        ),
                        padding: const EdgeInsets.all(8),
                        iconSize: 20,
                        constraints: const BoxConstraints(),
                        onPressed: widget.onPlusIconTap,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 10),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: SfSliderTheme(
            data: SfSliderThemeData(
              activeTrackHeight: 3,
              inactiveTrackHeight: 3,
              activeDividerStrokeColor: Colors.red,
              activeDividerStrokeWidth: 9,
              inactiveDividerStrokeWidth: 9,
              inactiveDividerStrokeColor: Colors.red,
              activeLabelStyle: AppTextStyles.s14w4cBlueTitleText.copyWith(
                color: !isLightMode ? AppColors.greyShade4 : null,
              ),
              inactiveLabelStyle: AppTextStyles.s14w4cBlueTitleText.copyWith(
                color: !isLightMode ? AppColors.greyShade4 : null,
              ),
            ),
            child: MediaQuery(
              data: MediaQuery.of(context).copyWith(
                textScaler: TextScaler.linear(textScaleFactor.value),
              ),
              child: SfSlider(
                min: widget.minValue,
                max: widget.maxValue,
                interval: widget.interval,
                stepSize: widget.interval,
                showDividers: true,
                dividerShape: AppSliderThumbShape(),
                activeColor: AppColors.greyShade13,
                inactiveColor: AppColors.greyShade13,
                value: sliderValue,
                showLabels: true,
                labelFormatterCallback: widget.labelFormatterCallback,
                thumbIcon: SizedBox(
                  height: 30,
                  width: 30,
                  child: FittedBox(
                    child: SvgPicture.asset(AppImages.icSliderThumb,
                    ),
                    
                    
                  ),
                ),
                onChangeEnd: (newValue) {
                  widget.onChanged(newValue as double);
                },
                onChanged: (value) {
                  sliderValue = value as double;
                  setState(() {});
                },
              ),
            ),
          ),
        ),
        const SizedBox(height: 15),
        if (widget.showDivider) ...[
          Divider(
            color: isLightMode ? AppColors.greyShade8 : AppColors.blackShade4,
          ),
          const SizedBox(height: 15),
        ],
      ],
    );
  }
}
