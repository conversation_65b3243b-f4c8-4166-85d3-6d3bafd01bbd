import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/widgets/bottom_sheet_top_notch.dart';
import 'package:scad_mobile/src/features/details/presentation/bloc/details_bloc.dart';
import 'package:scad_mobile/src/features/details/presentation/widgets/bottom_sheets/data_presentation_bottom_sheet/chart_view_selector.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class DataPresentationBottomSheet extends StatefulWidget {
  const DataPresentationBottomSheet({
    required this.selectedChartType,
    required this.isForecastData,
    required this.isTreeMap,
    super.key,
  });

  final String selectedChartType;
  final bool isForecastData;
  final bool isTreeMap;

  @override
  State<DataPresentationBottomSheet> createState() =>
      _DataPresentationBottomSheetState();
}

class _DataPresentationBottomSheetState
    extends State<DataPresentationBottomSheet> {
  ValueNotifier<String> selectedType = ValueNotifier('line-chart');

  List<String> types = ['Line Chart', 'Bar Chart', 'Table View'];

  @override
  void initState() {
    selectedType.value = widget.selectedChartType;
    _onSelectionChartType(selectedType.value);
    if (widget.isForecastData) {
      types = ['Line Chart', 'Table View'];
    }
    if (widget.isTreeMap) {
      types = ['Tree Map', 'Table View'];
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return StatefulBuilder(
      builder: (context, setState) {
        return Container(
          width: double.infinity,
          padding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
          decoration: BoxDecoration(
            color: isLightMode ? AppColors.white : AppColors.blueShade32,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 10),
              const BottomSheetTopNotch(),
              const SizedBox(height: 16),
              Text(
                LocaleKeys.changeDataPresentation.tr(),
                style: TextStyle(
                  color: isLightMode ? AppColors.blackShade1 : AppColors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                textScaler: TextScaler.linear(textScaleFactor.value),
              ),
              const SizedBox(height: 30),
              ValueListenableBuilder(
                valueListenable: selectedType,
                builder: (context, activeType, _) {
                  return Wrap(
                    spacing: 70,
                    runSpacing: 25,
                    children: [
                      for (final String type in types)
                        ChartViewSelector(
                          title: _itemName(type),
                          icon: type.replaceAll(' ', '-').toLowerCase(),
                          isSelected: activeType ==
                              type.replaceAll(' ', '-').toLowerCase(),
                          onTap: () {
                            selectedType.value =
                                type.replaceAll(' ', '-').toLowerCase();
                                 _onSelectionChartType(
                    selectedType.value.replaceAll(' ', '-').toLowerCase(),
                  );
                          },
                        ),
                    ],
                  );
                },
              ),
              const SizedBox(height: 30),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  minimumSize: const Size.fromHeight(43),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                onPressed: () {
                 context.read<DetailsBloc>().add(
          const PresentationTypeSelectionDoneEvent(),
        );

                },
                child: Text(
                  LocaleKeys.done.tr(),
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                  textScaler: TextScaler.linear(textScaleFactor.value),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _onSelectionChartType(String selectedType) {
    context.read<DetailsBloc>().add(
          PresentationTypeSelectionEvent(selectType: selectedType),
        );
  }

  String _itemName(String value) {
    final bool isArabic = HiveUtilsSettings.getAppLanguage() == 'ar';
    String name = '';
    if (value == 'Bar Chart') {
      name = isArabic ? 'شريط الرسم البياني' : value;
    } else if (value == 'Line Chart') {
      name = isArabic ? 'خط الرسم البياني' : value;
    } else if (value == 'Table View') {
      name = isArabic ? 'عرض الجدول' : value;
    } else if (value == 'Tree Map') {
      name = isArabic ? 'خريطة الشجرة' : value;
    }

    return name;
  }
}
