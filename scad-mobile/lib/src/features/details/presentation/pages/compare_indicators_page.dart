import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/common/check_box_text_row.dart';
import 'package:scad_mobile/src/common/widgets/app_back_button.dart';
import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';
import 'package:scad_mobile/src/common/widgets/expandable_widget.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_response_helper.dart';
import 'package:scad_mobile/src/common/widgets/no_data_placeholder.dart';
import 'package:scad_mobile/src/config/firebase_config/firebase_config.dart';
import 'package:scad_mobile/src/features/details/presentation/bloc/details_bloc.dart';
import 'package:scad_mobile/src/features/details/presentation/widgets/details_page_title.dart';
import 'package:scad_mobile/src/features/domains/data/models/domain_classification_model/domain_classification_model.dart';
import 'package:scad_mobile/src/features/domains/data/models/theme_indicator_list_response.dart';
import 'package:scad_mobile/src/features/domains/data/models/theme_subtheme_response.dart';
import 'package:scad_mobile/src/features/domains/presentation/bloc/domains_bloc.dart';
import 'package:scad_mobile/src/features/search/presentation/pages/search/search_screen.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

@RoutePage()
class CompareIndicatorsPage extends StatefulWidget {
  const CompareIndicatorsPage({
    required this.domainId,
    required this.domainName,
    required this.contentClassificationKey,
    required this.indicatorId,
    required this.indicatorDetails,
    super.key,
  });
  final String domainId;
  final String domainName;
  final String? contentClassificationKey;
  final String? indicatorId;
  final IndicatorDetailsResponseHelper? indicatorDetails;

  @override
  State<CompareIndicatorsPage> createState() => _CompareIndicatorsPageState();
}

class _CompareIndicatorsPageState extends State<CompareIndicatorsPage> {
  num indicatorsRefreshedAt = 0;
  // List<DomainModel> domainList = [];
  List<DomainClassificationModel> classificationList = [];
  List<ThemeSubThemeResponse> themeSubThemeList = [];
  String? classificationId;
  Map<String, List<ThemeIndicatorListItem>> indicatorListMap = {};
  ValueNotifier<ThemeIndicatorListItem?> selectedThemeIndicatorItem =
      ValueNotifier(null);
  // String? domainName;
  ValueNotifier<List<bool>> parentExpandedState = ValueNotifier([]);
  ValueNotifier<List<List<bool>>> childExpandedState = ValueNotifier([]);
  ScrollController scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    FirebaseConfig.setScreenToAnalytics('Compare Indicator Page');
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      context.read<DomainsBloc>().add(
            GetClassificationsEvent(domainId: widget.domainId),
          );
    });
  }

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    final rtl = DeviceType.isDirectionRTL(context);

    return AppDrawer(
      child: Scaffold(
        bottomNavigationBar: Padding(
          padding: const EdgeInsets.fromLTRB(24, 10, 24, 30),
          child: BlocConsumer<DetailsBloc, DetailsState>(
            listener: (context, state) {
              if (state is CompareIndicatorSuccessState) {
                Navigator.pop(context);
              } else if (state is CompareIndicatorFailureState) {
                AppMessage.showOverlayNotification(
                  '',
                  state.errorText,
                  msgType: 'error',
                );
              } else if (state is SelectedIndicatorIdFromSearchState) {
                final Map<String, dynamic> payload = {
                  'nodes': [
                    {'indicatorId': widget.indicatorId},
                    {'indicatorId': state.indicatorId},
                  ],
                };

                context.read<DetailsBloc>().add(
                      CompareIndicatorEvent(
                        payload: payload,
                        indicatorDetails: widget.indicatorDetails,
                      ),
                    );
              }
            },
            builder: (context, state) {
              return ValueListenableBuilder(
                valueListenable: selectedThemeIndicatorItem,
                builder: (context, selectedValue, _) {
                  return IgnorePointer(
                    ignoring: selectedValue == null,
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        minimumSize: const Size.fromHeight(43),
                        backgroundColor: selectedValue == null
                            ? isLightMode
                                ? AppColors.blueShade22.withOpacity(0.5)
                                : AppColors.blueLightOld.withOpacity(0.3)
                            : isLightMode
                                ? AppColors.blueShade22
                                : AppColors.blueLightOld,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      onPressed: () async {
                        final Map<String, dynamic> payload = {
                          'nodes': [
                            {'indicatorId': widget.indicatorId},
                            {'indicatorId': selectedValue?.indicatorId},
                          ],
                        };

                        context.read<DetailsBloc>().add(
                              CompareIndicatorEvent(
                                payload: payload,
                                indicatorDetails: widget.indicatorDetails,
                              ),
                            );
                      },
                      child: (state is CompareIndicatorLoadingState)
                          ? SizedBox(
                              height: 18,
                              width: 18,
                              child: CircularProgressIndicator(
                                color: AppColors.white,
                              ),
                            )
                          : Text(
                              LocaleKeys.compare.tr(),
                              style: TextStyle(
                                color: AppColors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                              textScaler:
                                  TextScaler.linear(textScaleFactor.value),
                            ),
                    ),
                  );
                },
              );
            },
          ),
        ),
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: MediaQuery.paddingOf(context).top,
            ),

            /// The title of the page
            Row(
              children: [
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 8),
                  child: AppBackButton(),
                ),
                Expanded(
                  child: Padding(
                    padding: DeviceType.isDirectionRTL(context)
                        ? const EdgeInsets.only(left: 30)
                        : const EdgeInsets.only(right: 30),
                    child: DetailsPageTitle(
                      title: LocaleKeys.compareIndicators.tr(),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            /// Search field
            // GestureDetector(
            //   onTap: () {
            //     context.router.removeWhere(
            //         (route) => route.name == SearchScreenRoute.name);
            //     context.pushRoute(
            //       SearchScreenRoute(
            //         type: SearchTypes.compareIndicators,
            //         contentType: widget.contentClassificationKey,
            //         initialNodeIdForComparison:
            //             widget.indicatorDetails?.indicatorDetails.id,
            //       ),
            //     );
            //   },
            //   child: Hero(
            //     tag: 'search_box',
            //     child: Material(
            //       type: MaterialType.transparency,
            //       child: Container(
            //         margin: const EdgeInsets.symmetric(horizontal: 24),
            //         padding: const EdgeInsets.symmetric(
            //           horizontal: 10,
            //           vertical: 9,
            //         ),
            //         clipBehavior: Clip.antiAlias,
            //         decoration: ShapeDecoration(
            //           color: Colors.white,
            //           shape: RoundedRectangleBorder(
            //             borderRadius: BorderRadius.circular(60),
            //           ),
            //         ),
            //         child: Row(
            //           children: [
            //             Expanded(
            //               child: Padding(
            //                 padding: const EdgeInsets.symmetric(
            //                     horizontal: 12, vertical: 4),
            //                 child: Text(
            //                   LocaleKeys.search.tr(),
            //                   style: AppTextStyles.s14w3cHintColor,
            //                   textScaler: TextScaler.linear(
            //                     textScaleFactor.value,
            //                   ),
            //                 ),
            //               ),
            //             ),
            //             SizedBox(width: 10),
            //             SizedBox(
            //               width: 18,
            //               height: 18,
            //               child: SvgPicture.asset(
            //                 AppImages.iconSearchBlack,
            //               ),
            //             ),
            //             SizedBox(width: 10)
            //           ],
            //         ),
            //       ),
            //     ),
            //   ),
            // ),
            // const SizedBox(height: 20),
            Expanded(
              child: Scrollbar(
                thumbVisibility: true,
                trackVisibility: true,
                controller: scrollController,
                child: BlocConsumer<DomainsBloc, DomainsState>(
                  listener: (context, state) {
                    if (state is GetThemeClassificationState) {
                      classificationList = state.list;
                      final index =
                          widget.contentClassificationKey.toString() != 'null'
                              ? classificationList.indexWhere(
                                  (element) =>
                                      element.key ==
                                      widget.contentClassificationKey,
                                )
                              : 0;

                      classificationId = classificationList[index].id;

                      context.read<DomainsBloc>().add(
                            GetThemesInitEvent(
                              domainId: widget.domainId,
                              classificationId: classificationId!,
                            ),
                          );
                      indicatorsRefreshedAt =
                          DateTime.now().microsecondsSinceEpoch;
                    }
                    if (state is ThemeListShowResponseState) {
                      themeSubThemeList = state.list;
                      for (int i = 0; i < themeSubThemeList.length; i++) {
                        parentExpandedState.value.insert(i, false);
                        for (int j = 0;
                            j < (themeSubThemeList[i].subthemes ?? []).length;
                            j++) {
                          // childExpandedState[i].insert(j, false);
                        }
                      }
                    }
                  },
                  builder: (context, state) {
                    return ListView(
                      padding: EdgeInsets.zero,
                      controller: scrollController,
                      children: [
                        Padding(
                          padding: EdgeInsets.only(
                            left: 24,
                            right: rtl ? 24 : 0,
                          ),
                          child: Text(
                            widget.domainName,
                            textScaler:
                                TextScaler.linear(textScaleFactor.value),
                            style: TextStyle(
                              color: isLightMode
                                  ? AppColors.blackShade1
                                  : AppColors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        const SizedBox(height: 20),
                    
                        /// indicator lists
                        if (state is ThemeLoadingState)
                          const Center(child: CircularProgressIndicator())
                        else
                          themesSubThemesList(isLightMode),
                      ],
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget themesSubThemesList(bool isLightMode) {
    final rtl = DeviceType.isDirectionRTL(context);
    return themeSubThemeList.isEmpty
        ? const Center(
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 100),
              child: NoDataPlaceholder(),
            ),
          )
        : ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: const EdgeInsets.symmetric(
              horizontal: 24,
            ),
            itemCount: themeSubThemeList.length,
            itemBuilder: (BuildContext context, int index) {
              return Container(
                decoration: BoxDecoration(
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.shadow2,
                      blurRadius: 5,
                      offset: const Offset(1, 4),
                    ),
                  ],
                ),
                child: ExpandableWidget(
                  key: ValueKey(themeSubThemeList[index].id),
                  iconPadding: 3,
                  iconSize: 26,
                  title: themeSubThemeList[index].name ?? '',
                  // headerChild: Expanded(
                  //   child: Padding(
                  //     padding: EdgeInsets.only(left: 16, right: rtl ? 16 : 0),
                  //     child: Text(
                  //       themeSubThemeList[index].name ?? '',
                  //       style: TextStyle(
                  //         color: isLightMode
                  //             ? AppColors.blackTextTile
                  //             : AppColors.white,
                  //         fontSize: 14,
                  //         fontWeight: FontWeight.w400,
                  //       ),
                  //       textScaler: TextScaler.linear(textScaleFactor.value),
                  //     ),
                  //   ),
                  // ),
                  expandedChild: Column(
                    children: [
                      const Divider(height: 1),
                      ...List.generate(
                          (themeSubThemeList[index].subthemes ?? []).length,
                          (childIndex) {
                        final item = themeSubThemeList[index].subthemes;
                        return BlocConsumer<DomainsBloc, DomainsState>(
                          listener: (context, state) {
                            if (state is ThemeClassificationShowResponseState) {
                              final classificationList = state.list;

                              final index =
                                  widget.contentClassificationKey != null
                                      ? classificationList.indexWhere(
                                          (element) =>
                                              element.key ==
                                              widget.contentClassificationKey,
                                        )
                                      : 0;

                              classificationId = classificationList[index].id;
                            }
                            if (state is CompareThemeIndicatorListState) {
                              indicatorListMap[state.subThemeId] =
                                  state.themeIndicatorListResponse.results ??
                                      [];
                            }
                          },
                          builder: (context, state) {
                            return ExpandableWidget(
                              iconPadding: 3,
                              iconSize: 26,
                              showDivider: false,
                              key: ValueKey(
                                themeSubThemeList[index]
                                    .subthemes?[childIndex]
                                    .id,
                              ),
                              title: item?[childIndex].name ?? '',
                              // headerChild: Expanded(
                              //   child: Padding(
                              //     padding: EdgeInsets.only(
                              //       left: 30,
                              //       right: rtl ? 30 : 0,
                              //     ),
                              //     child: Text(
                              //       item?[childIndex].name ?? '',
                              //       style: TextStyle(
                              //         color: isLightMode
                              //             ? AppColors.blackTextTile
                              //             : AppColors.white,
                              //         fontSize: 14,
                              //         fontWeight: FontWeight.w400,
                              //       ),
                              //       textScaler: TextScaler.linear(
                              //         textScaleFactor.value,
                              //       ),
                              //     ),
                              //   ),
                              // ),
                              onChanged: (value) {
                                if (value &&
                                    (indicatorListMap[themeSubThemeList[index]
                                                .subthemes![childIndex]
                                                .id!] ??
                                            [])
                                        .isEmpty) {
                                  context.read<DomainsBloc>().add(
                                        CompareIndicatorGetThemesEvent(
                                          subDomainId:
                                              themeSubThemeList[index].id!,
                                          subThemeId: themeSubThemeList[index]
                                              .subthemes![childIndex]
                                              .id!,
                                          pageNo: 0,
                                          domainId: widget.domainId,
                                          classificationId: classificationId!,
                                        ),
                                      );
                                }
                              },
                              expandedChild: Column(
                                children: [
                                  const SizedBox(height: 10),
                                  ...List.generate(
                                      (indicatorListMap[themeSubThemeList[index]
                                                  .subthemes![childIndex]
                                                  .id!] ??
                                              [])
                                          .length, (grandChildIndex) {
                                    return Padding(
                                      padding: const EdgeInsets.only(
                                        left: 16,
                                        right: 16,
                                        bottom: 16,
                                      ),
                                      child: ValueListenableBuilder(
                                        valueListenable:
                                            selectedThemeIndicatorItem,
                                        builder:
                                            (context, selectedIndicator, _) {
                                          final indicator = indicatorListMap[
                                              themeSubThemeList[index]
                                                  .subthemes![childIndex]
                                                  .id!]?[grandChildIndex];
                                          return CheckBoxTextRow(
                                            title: indicator?.title ?? '',
                                            titleColor: isLightMode
                                                ? AppColors.grey
                                                : AppColors.greyShade4,
                                            isDefaultValue:
                                                widget.indicatorId ==
                                                    indicator?.indicatorId,
                                            isDisable:
                                                indicator!.isMultiDimension??true,
                                            isSelected:
                                                selectedIndicator == indicator,
                                            onChanged: () {
                                              if (selectedThemeIndicatorItem
                                                          .value ==
                                                      null ||
                                                  selectedIndicator !=
                                                      indicator) {
                                                selectedThemeIndicatorItem
                                                    .value = indicator;
                                              } else if (selectedIndicator ==
                                                  indicator) {
                                                selectedThemeIndicatorItem
                                                    .value = null;
                                              }
                                            },
                                          );
                                        },
                                      ),
                                    );
                                  }),
                                ],
                              ),
                            );
                          },
                        );
                      }),
                    ],
                  ),
                ),
              );
            },
            separatorBuilder: (_, __) {
              return const SizedBox(height: 20);
            },
          );
  }
}
