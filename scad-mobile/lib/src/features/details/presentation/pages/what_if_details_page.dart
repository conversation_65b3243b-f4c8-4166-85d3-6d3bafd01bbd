import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/common/widgets/appbar/flat_app_bar.dart';
import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_response_helper.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/common/widgets/no_data_placeholder.dart';
import 'package:scad_mobile/src/common/widgets/sliding_tab_filter.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/bloc/chat_with_sme_bloc.dart';
import 'package:scad_mobile/src/features/details/presentation/bloc/details_bloc.dart';
import 'package:scad_mobile/src/features/details/presentation/widgets/expandable_indicator_card_widget.dart';
import 'package:scad_mobile/src/features/details/presentation/widgets/filter_driver_button.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

@RoutePage()
class WhatIfDetailsPage extends StatefulWidget {
  const WhatIfDetailsPage({
    required this.nodeId,
    super.key,
    this.indicatorDetails,
    this.originalIndicatorData,
  });
  final String nodeId;
  final IndicatorDetailsResponseHelper? indicatorDetails;
  final IndicatorDetailsResponse? originalIndicatorData;

  @override
  State<WhatIfDetailsPage> createState() => _WhatIfDetailsPageState();
}

class _WhatIfDetailsPageState extends State<WhatIfDetailsPage> {
  late List<SlidingTabItem> scenarioList;
  late List<VisualizationsMeta> indicatorList;
  // List<VisualizationsMeta> filteredIndicatorList = [];
  ValueNotifier<int> selectedScenarioTabIndex = ValueNotifier(0);
  late IndicatorDetailsResponseHelper? indicatorDetails;
  ScrollController scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    indicatorDetails = widget.indicatorDetails;
    indicatorList = widget.indicatorDetails?.indicatorDetails
            .indicatorVisualizations!.visualizationsMeta! ??
        [];
    scenarioList = [
      ...[SlidingTabItem(name: 'All', icon: '')],
      ...indicatorList.map(
        (e) => SlidingTabItem(name: e.vizLabel ?? '', icon: ''),
      ),
    ];
    return AppDrawer(
      child: Scaffold(
        body: Column(
          children: [
            FlatAppBar(
              title: LocaleKeys.whatIfScenario.tr(),
              scrollController: scrollController,
            ),
            Expanded(
              child: indicatorList.isEmpty
                  ? const Center(
                      child: NoDataPlaceholder(),
                    )
                  : Column(
                      children: [
                        ValueListenableBuilder(
                          valueListenable: selectedScenarioTabIndex,
                          builder: (context, value, _) {
                            return SlidingTab(
                              tabList: scenarioList
                                  .map(
                                    (e) => SlidingTabItem(
                                      name: e.name,
                                      icon: e.icon,
                                    ),
                                  )
                                  .toList(),
                              selectedTabIndex: selectedScenarioTabIndex,
                              onTabChange: (int index) {
                                selectedScenarioTabIndex.value = index;
                              },
                            );
                          },
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(
                                bottom: 14,
                                left: 20,
                                right: 20,
                              ),
                              child: FilterDriverButton(
                                title: LocaleKeys.changeDrivers.tr(),
                                icon: 'driver-icon-blue',
                                onPressed: () {
                                  AutoRouter.of(context).push(
                                    ChangeDriversFullScreenDialogRoute(
                                      indicatorDetails: indicatorDetails!,
                                      isWhatIfDetails: true,
                                      index: selectedScenarioTabIndex.value,
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                        Expanded(
                          child: ValueListenableBuilder(
                            valueListenable: selectedScenarioTabIndex,
                            builder: (context, i, w) {
                              return BlocConsumer<DetailsBloc, DetailsState>(
                                listener: (context, state) {
                                  if (state
                                      is DetailsCreateChatThreadSuccessState) {
                                    context.read<ChatWithSmeBloc>().add(
                                          ChatWithSmeLoadInboxEvent(
                                            chatThreadId:
                                                state.chatThread?.uuid ?? '',
                                          ),
                                        );
                                  
                                    context.pushRoute(
                                      ChatWithSmeInboxPageRoute(
                                        chatThreadId:
                                        state.chatThread?.uuid ?? '',
                                        title: state.domain,
                                        domain: state.domain,
                                        domainId: state.domainId,
                                        theme: state.theme,
                                        subTheme: state.subTheme,
                                        ticketId: state.chatThread?.ticketId,
                                        indicatorNodeId: state.indicatorNodeId,
                                        indicatorAppType:
                                            state.indicatorAppType,
                                        inicatorContentType:
                                            state.inicatorContentType,
                                        indicatorKey: state.indicatorKey,
                                        indicatorName: state.indicatorName,
                                        chatDisabled: state.chatDisabled,
                                        chatThreadClosed: state.chatThreadClosed,
                                      ),
                                    );
                                  } else if (state
                                      is ChangeDriversValueUpdateState) {
                                    indicatorDetails = state.indicatorDetails;
                                  }
                                },
                                builder: (context, state) {
                                  return ListView.builder(
                                    shrinkWrap: true,
                                    controller: scrollController,
                                    padding:
                                        const EdgeInsets.fromLTRB(0, 0, 0, 50),
                                    itemCount: indicatorList.length,
                                    itemBuilder: (context, itemIndex) {
                                      int index = itemIndex;
                                      if (selectedScenarioTabIndex.value != 0) {
                                        index =
                                            selectedScenarioTabIndex.value - 1;
                                      }

                                      return index == itemIndex
                                          ? ExpandableIndicatorCardWidget(
                                              key: Key('${index}aa'),
                                              index: itemIndex,
                                              id: widget.nodeId,
                                              contentType: 'analytical-apps',
                                                  // 'official_statistics',
                                              indicatorDetails:
                                                  indicatorDetails,
                                              originalIndicatorForFilter:
                                                  widget.originalIndicatorData,
                                            )
                                          : const SizedBox();
                                    },
                                  );
                                },
                              );
                            },
                          ),
                        ),
                      ],
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
