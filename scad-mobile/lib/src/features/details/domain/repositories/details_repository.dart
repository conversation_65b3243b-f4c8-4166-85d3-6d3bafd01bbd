part of 'details_repository_imports.dart';

abstract class DetailsRepository {
  /// function for creating chatThread
  Future<RepoResponse<ChatThreadCreateModel>> createChatThread({
    required String domain,
    required int domainId,
    required String theme,
    required String subTheme,
    required String subject,
    required String indicatorNodeId,
    required String indicatorAppType,
    required String inicatorContentType,
    required String indicatorKey,
    required String indicatorName,
  });

  /// functiom for compute data indicators
  Future<RepoResponse<IndicatorDetailsResponse>> computeData({
    required Map<String, dynamic> dataMap,
  });

  /// function to compare indicators
  Future<RepoResponse<IndicatorDetailsResponse>> compareIndicators({
    required Map<String, dynamic> payload,
  });

  /// function for insight discovery filter
  Future<RepoResponse<InsightFilterModelResponse>> getCompleteDataToFilter({
    required Map<String, dynamic> payload,
  });
}
