class FrequencySelectorModel {
  FrequencySelectorModel({
    required this.title,
    required this.isSelected,
  });

  FrequencySelectorModel.fromJson(Map<String, dynamic> json) {
    title = json['title'] as String;
    isSelected = json['isSelected'] as bool;
  }

  late String title;
  late bool isSelected;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['title'] = title;
    json['isSelected'] = isSelected;

    return json;
  }
}
