import 'package:flutter/material.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';

class OnBoardingHeader extends StatelessWidget {
  const OnBoardingHeader({
    required this.title,
    this.description,
    this.index,
    super.key,
  });

  final String title;
  final String? description ;
  final int ? index;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          title,
          textAlign: TextAlign.center,
          style: index == 0 ? AppTextStyles.s20w5cBlackOrWhiteShade.copyWith(
            color: AppColors.white,
          ) :  AppTextStyles.s24w6cWhite,
        //  textScaler: TextScaler.linear(textScaleFactor.value),
        ),
        const SizedBox(height: 10),
        if (description != null)
        Text(
          description ?? '',
          textAlign: TextAlign.center,
          style: AppTextStyles.s14w4cblackShade4
              .copyWith(color: AppColors.white),
          //textScaler: TextScaler.linear(textScaleFactor.value),
        ),
      ],
    );
  }
}
