import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class OnboardingPageIndicators extends StatelessWidget {
  const OnboardingPageIndicators({
    required this.currentStepIndex,
    required this.totalSteps,
    super.key,
    this.isLastScreen = false,
  });

  final int currentStepIndex;
  final int totalSteps;
  final bool isLastScreen;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    // final index = currentPageIndex;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ...List.generate(totalSteps, (index) => Flexible(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildDot(
                  color: currentStepIndex>=index
                      ? AppColors.white
                      : isLightMode? AppColors.blueLight : AppColors.blueLightOld,
                ),
                if(index != totalSteps-1)
                _buildLine(
                  color: currentStepIndex>index ? AppColors.white
                      : isLightMode? AppColors.blueLight : AppColors.blueLightOld,
                ),
              ],
            ),
          ),),

        ],
      ),
    );

  }

  Widget _buildDot({required Color color}) {
    return Container(
      width: 10,
      height: 10,
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: color,
      ),
    );
  }

  Widget _buildLine({
    required Color color,
    bool isVisible = true,
  }) {
    return Flexible(
      child: Visibility(
        visible: isVisible,
        child: Container(
          height: 1,
          color: color,
        ),
      ),
    );
  }
}
