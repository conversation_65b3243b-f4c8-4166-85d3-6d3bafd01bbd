import 'dart:convert';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/widgets/appbar/flat_app_bar.dart';
import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';
import 'package:scad_mobile/src/features/domains/data/models/domain_model/domain_model.dart';
import 'package:scad_mobile/src/features/home/<USER>/bloc/home_bloc/home_bloc.dart';
import 'package:scad_mobile/src/features/onboarding/presentation/bloc/interest_domain_bloc.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/app_utils/flutter_toast.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

import '../../data/models/interest_domain_entity.dart';

@RoutePage()
class SettingInterestPage extends StatefulWidget {
  const SettingInterestPage({super.key});

  @override
  State<SettingInterestPage> createState() => _SettingInterestPageState();
}

class _SettingInterestPageState extends State<SettingInterestPage>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  List<DomainModel> domainList = [];

  ValueNotifier<List<int>> selectedInterest = ValueNotifier([]);

  @override
  void initState() {
    super.initState();

    context.read<InterestDomainBloc>().add(const InterestDomainInitEvent());
  }

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    super.build(context);
    return AppDrawer(
      child: Scaffold(
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            FlatAppBar(
              title: LocaleKeys.chooseYourInterests.tr(),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Expanded(
                      child:
                          BlocConsumer<InterestDomainBloc, InterestDomainState>(
                        listener: (context, state) {
                          if (state is InterestDomainShowResponseState) {
                            domainList = state.domainList;
                            selectedInterest.value = [];
                            final List<InterestDomainEntity> list =
                                state.selectedDomainList;
                            for (int i = 0; i < domainList.length; i++) {
                              for (int j = 0; j < list.length; j++) {
                                if (list[j].domainId == domainList[i].domainId) {
                                  selectedInterest.value.add(i);
                                  break;
                                }
                              }
                            }
                          }
                          if (state is InterestSavedState) {
                            if (state.status) {
                              context.popRoute();
                              context
                                  .read<HomeBloc>()
                                  .add(const RecommendedIndicatorsEvent());
                            }
                            AppMessage.showOverlayNotification(
                              '',
                              state.message,
                            );
                          }
                        },
                        builder: (context, state) {
                          if (state is InterestDomainLoadingState) {
                            return const Center(
                                child: CircularProgressIndicator());
                          } else if (state is InterestDomainErrorState) {
                            return Text(
                              state.error,
                              textScaler:
                                  TextScaler.linear(textScaleFactor.value),
                            );
                          }
                          // else if (state is InterestDomainShowResponseState) {}
                          return ListView.separated(
                            itemCount: domainList.length,
                            shrinkWrap: true,
                            padding: const EdgeInsets.only(top: 12),
                            separatorBuilder: (context, index) =>
                                const SizedBox(height: 14),
                            itemBuilder: (context, int index) {
                              return ValueListenableBuilder(
                                valueListenable: selectedInterest,
                                builder: (context, selectedIndex, w) {
                                  return ClipRRect(
                                    borderRadius: BorderRadius.circular(10),
                                    child: Material(
                                      color:
                                          selectedInterest.value.contains(index)
                                              ? isLightMode
                                                  ? AppColors.blueShade8
                                                  : const Color(0x4C2587FC)
                                              : isLightMode
                                                  ? AppColors.white
                                                  : AppColors.blueShade36,
                                      child: InkWell(
                                        onTap: () {
                                          if (selectedInterest.value
                                              .any((e) => e == index)) {
                                            selectedInterest.value.remove(index);
                                          } else {
                                            selectedInterest.value.add(index);
                                          }
                                          selectedInterest.notifyListeners();
                                        },
                                        child: Container(
                                          padding: const EdgeInsets.all(14),
                                          decoration: BoxDecoration(
                                            border: Border.all(
                                              color: selectedInterest.value
                                                      .contains(index)
                                                  ? isLightMode
                                                      ? AppColors.blueLight
                                                      : const Color(0x4C2587FC)
                                                  : isLightMode
                                                      ? AppColors.white
                                                      : AppColors.blackShade6,
                                            ),
                                            borderRadius:
                                                BorderRadius.circular(10),
                                          ),
                                          child: Row(
                                            children: [
                                              Container(
                                                width: 80,
                                                alignment: Alignment.center,
                                                child: SvgPicture.network(
                                                  domainList[index].domainIcon ??
                                                      '',
                                                  width: 50,
                                                  height: 50,
                                                  colorFilter: ColorFilter.mode(
                                                    isLightMode?  AppColors.blueLight : AppColors.white,
                                                      BlendMode.srcIn),
                                                ),
                                              ),
                                              const SizedBox(width: 10),
                                              Expanded(
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Text(
                                                      HiveUtilsSettings
                                                                  .getAppLanguage() ==
                                                              'en'
                                                          ? domainList[index]
                                                                  .domainName ??
                                                              '-'
                                                          : domainList[index]
                                                                  .domainNameAr ??
                                                              '-',
                                                      style: TextStyle(
                                                        color: isLightMode
                                                            ? AppColors
                                                                .blackShade1
                                                            : AppColors.white,
                                                        fontSize: 16,
                                                        fontWeight:
                                                            FontWeight.w500,
                                                      ),
                                                      // overflow:
                                                      //     TextOverflow.,
                                                      maxLines: 2,
                                                      textScaler:
                                                          TextScaler.linear(
                                                              textScaleFactor
                                                                  .value),
                                                    ),
                                                    // Text(
                                                    //   domainList[index].description??'-',
                                                    //   style: TextStyle(
                                                    //     color: AppColors.greyShade4,
                                                    //     fontSize: 14,
                                                    //     fontWeight: FontWeight.w400,
                                                    //   ),
                                                    //   maxLines: 1,
                                                    //   overflow: TextOverflow.ellipsis,
                                                    //   textScaleFactor: textScaleFactor.value,
                                                    // ),
                                                  ],
                                                ),
                                              ),
                                              Visibility(
                                                visible: selectedInterest.value
                                                    .contains(index),
                                                child: SvgPicture.asset(
                                                  AppImages.icTick,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              );
                            },
                          );
                        },
                      ),
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton(
                      onPressed: () {
                        if (selectedInterest.value.length < 3) {
                          AppMessage.showOverlayNotification(
                            '',
                            LocaleKeys.selectDomains.tr(),
                          );
                        } else {
                          Map<String, dynamic> map = {
                            'data': json.encode(selectedInterest.value.map((e) {
                              DomainModel? model = domainList[e];
                              return {
                                'domain_id': model.domainId,
                                'name': model.domainName,
                              };
                            }).toList())
                          };
                          context
                              .read<InterestDomainBloc>()
                              .add(InterestSaveEvent(domainListMap: map));
                        }
                      },
                      child: Text(
                        LocaleKeys.continueButton.tr(),
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: AppColors.white,
                        ),
                        textScaler: TextScaler.linear(textScaleFactor.value),
                      ),
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
