import 'dart:io';

import 'package:auto_route/annotations.dart';
import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/features/onboarding/presentation/pages/onboarding_screen_1.dart';
import 'package:scad_mobile/src/features/onboarding/presentation/widgets/onboarding_header.dart';
import 'package:scad_mobile/src/features/onboarding/presentation/widgets/onboarding_page_indicators.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';


class OnboardingPageContent{
  OnboardingPageContent({required this.image, required this.title, required this.description, this.onNext, this.contentTransparency = false});

  final Widget image;
  final String title;
  final String description;
  final VoidCallback? onNext;
  final bool contentTransparency;

}

@RoutePage()
class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();

  ValueNotifier<int> currentPageIndex = ValueNotifier(0);

    bool isLightMode = true;

  @override
  void initState() {
    super.initState();

    isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;

    _pageController.addListener(() {
      currentPageIndex.value = _pageController.page!.round();
    });

    pages = [
      // OnboardingPageContent(image: SizedBox(), title: '', description: ''),

      OnboardingPageContent(
        image: Padding(
          padding: const EdgeInsets.only(bottom: 150),
          child: Image.asset(
            isLightMode ?  AppImages.imgOnboarding1 :AppImages.imgOnboardingDark1 ,
            // height: size.height * .50,
          ),
        ),
        title: LocaleKeys.onboardingTitle1.tr(),
        description: LocaleKeys.onboarding1.tr(),
      ),

      OnboardingPageContent(
        image: Padding(
          padding: const EdgeInsets.only(left: 12,right:12),
          child: Image.asset(
            isLightMode ?  AppImages.imgOnboarding2 :AppImages.imgOnboardingDark2 ,
            // height: size.height * .55,
          ),
        ),
        title: LocaleKeys.onboardingTitle2.tr(),
        description: LocaleKeys.onboarding2.tr(),
      ),

      OnboardingPageContent(
        image: Padding(
          padding: const EdgeInsets.only(bottom: 150),
          child: Image.asset(
            isLightMode ?  AppImages.imgOnboarding3 :AppImages.imgOnboardingDark3 ,
            // height: size.height * .55,
          ),
        ),
        title: LocaleKeys.onboardingTitle3.tr(),
        description: LocaleKeys. onboarding3.tr(),
      ),

      OnboardingPageContent(
        image: Image.asset(
          AppImages.imgOnboarding4,
          // height: size.height * .5,
        ),
        title: LocaleKeys.onboardingTitle4.tr(),
        description: LocaleKeys. onboarding4.tr(),
        contentTransparency: true,
      ),

      OnboardingPageContent(
        image: Padding(
          padding: const EdgeInsets.only(top:40,bottom: 150),
          child: Image.asset(
            isLightMode ?  AppImages.imgOnboarding5 :AppImages.imgOnboardingDark5 ,
            // height: size.height * .4,
          ),
        ),
        title: LocaleKeys.onboardingTitle5.tr(),
        description: LocaleKeys.onboarding5 .tr(),
        onNext: (){
          AutoRouter.of(context).replaceAll([HomeNavigationRoute()]);
        },
      ),
    ];
  }


  List<OnboardingPageContent> pages = [];
  
  @override
  void dispose() {
    super.dispose();
    _pageController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (bool didPop) {
        if(currentPageIndex.value > 0) {
          _pageController.animateToPage(
            currentPageIndex.value - 1,
            duration: const Duration(milliseconds: 1),
            curve: Curves.linear,
          );
        } else {
          if(!Platform.isIOS) {
            SystemNavigator.pop();
          }
        }
      },
      child: Scaffold(
        body: Container(
          height: MediaQuery.sizeOf(context).height,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                AppColors.blueGradientShade3,
                AppColors.blueGradientShade4,
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              stops: const [
                0.2,
                0.5,
              ],
            ),
          ),
          child: Stack(
            children: [
              // content
              ValueListenableBuilder(
                valueListenable: currentPageIndex,
                builder: (context,_,__) {
                  return SizedBox(
                    //height: currentPageIndex.value ==0 ?null:MediaQuery.sizeOf(context).height*.66,
                    child: PageView(
                      controller: _pageController,
                      physics: const NeverScrollableScrollPhysics(),
                      children: [
                        OnBoardingScreen1(
                          onDone: (){
                            _pageController.nextPage(
                              duration: const Duration(milliseconds: 1),
                              curve: Curves.linear,
                            );
                          },
                        ),
                        ...List.generate(pages.length, (index) =>
                            SizedBox(
                              height: MediaQuery.sizeOf(context).height*.66,
                              child: OnboardingPage(
                                content: pages[index],
                              ),
                            ),
                        ),
                      ],
                    ),
                  );
                },
              ),

              //gradient
              ValueListenableBuilder(
                valueListenable: currentPageIndex,
                builder: (context,_,__) {
                  return currentPageIndex.value != 4
                      ? const SizedBox()
                  :Positioned(
                    top: MediaQuery.sizeOf(context).height*.26,
                    child: Container(
                      width: MediaQuery.sizeOf(context).width,
                      height: MediaQuery.sizeOf(context).height,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            // Colors.red,
                            AppColors.blueGradientShade4,
                            AppColors.blueGradientShade4,
                            const Color(0x00D9D9D9),
                            Colors.transparent,
                            // Colors.red,
                            // Colors.green,
                            // Colors.amber,
                          ],
                          begin: Alignment.bottomCenter,
                          end: Alignment.topCenter,
                          stops: const [
                            0,
                            0.6,
                            0.8,
                            1,
                          ],
                        ),
                      ),),
                  );
                },
              ),

              //button
              Positioned(
                bottom: 0,
                child: SizedBox(
                  width: MediaQuery.sizeOf(context).width,
                child:                  ValueListenableBuilder(
                  valueListenable: currentPageIndex,
                  builder: (context,_,__) {
                    return currentPageIndex.value == 0
                        ? const SizedBox()
                        : Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.end,
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          OnBoardingHeader(
                            title: pages[currentPageIndex.value-1].title,
                            description: pages[currentPageIndex.value-1].description,
                          ),
                          const SizedBox(height: 24),
                          OnboardingPageIndicators(
                            currentStepIndex: currentPageIndex.value,
                            totalSteps: pages.length+1,
                            isLastScreen: currentPageIndex.value == pages.length+1 ,
                          ),
                          const SizedBox(height: 24),
                          TextButton(
                            style: TextButton.styleFrom(
                              backgroundColor: isLightMode
                                  ? AppColors.blueLight
                                  : AppColors.blueLightOld,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                            onPressed: pages[currentPageIndex.value-1].onNext??(){
                              _pageController.nextPage(
                                duration: const Duration(milliseconds: 1),
                                curve: Curves.linear,
                              );
                            },
                            child: Text(
                              currentPageIndex.value == pages.length
                                  ? LocaleKeys.exploreNow.tr()
                                  : LocaleKeys.next.tr(),
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                color: AppColors.white,
                              ),
                            //  textScaler: TextScaler.linear(textScaleFactor.value),
                            ),
                          ),
                          const SizedBox(height: 10),
                          Center(
                            child: InkWell(
                              onTap: () {
                                _pageController.animateToPage(
                                  pages.length,
                                  duration: const Duration(milliseconds: 200),
                                  curve: Curves.easeIn,
                                );
                              },
                              child:Text(
                                LocaleKeys.skip.tr(),
                                style: AppTextStyles.s16w5cBlackShade1.copyWith(
                                  color: AppColors.blueShade6,
                                ),
                                // textScaler: TextScaler.linear(
                                //   textScaleFactor.value,),
                              ),
                            ),
                          ),
                          const SizedBox(height: 20),
                        ],
                      ),
                    );
                  },
                ),
                      ),
              ),

            ],
          ),
        ),
      ),
    );
  }

}

class OnboardingPage extends StatelessWidget {
  const OnboardingPage({required this.content, super.key});
  final OnboardingPageContent content;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned(
          top: 0,
          right: 0,
          left: 0,
          child: Container(
            alignment: Alignment.topCenter,
            margin: EdgeInsets.only(top: MediaQuery.sizeOf(context).height*.05),
            height: MediaQuery.sizeOf(context).height*.7,
            width: MediaQuery.sizeOf(context).width*.9,
            child: Align(child: content.image),
          ),),
        Positioned.fill(
          child: Container(
            margin: EdgeInsets.only(top: MediaQuery.sizeOf(context).height*.3),
            width: MediaQuery.sizeOf(context).width*.6,
            child: Container(
              height: MediaQuery.sizeOf(context).height*.6,
              // decoration: !content.contentTransparency?null:const BoxDecoration(
              //     gradient: LinearGradient(
              //         colors: [
              //           Color(0xff0131A0),
              //           Colors.transparent,
              //         ],
              //         begin: Alignment.bottomCenter,
              //         end: Alignment.topCenter,
              //         stops: [
              //           0.4,
              //           1,
              //         ],
              //     ),
              // ),
              padding:
              EdgeInsets.only(top:MediaQuery.of(context).padding.top , left: 26, right: 26),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: const [
                  Spacer(),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}

