import 'dart:convert';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/common/widgets/error_reload_placeholder.dart';
import 'package:scad_mobile/src/common/widgets/no_data_placeholder.dart';
import 'package:scad_mobile/src/features/domains/data/models/domain_model/domain_model.dart';
import 'package:scad_mobile/src/features/onboarding/data/models/interest_domain_entity.dart';
import 'package:scad_mobile/src/features/onboarding/presentation/bloc/interest_domain_bloc.dart';
import 'package:scad_mobile/src/features/onboarding/presentation/widgets/onboarding_header.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

import '../widgets/onboarding_page_indicators.dart';

class OnBoardingScreen1 extends StatefulWidget {
  const OnBoardingScreen1({required this.onDone, super.key});

  @override
  State<OnBoardingScreen1> createState() => _OnBoardingScreen1State();

  final VoidCallback onDone;
}

class _OnBoardingScreen1State extends State<OnBoardingScreen1>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  List<DomainModel> domainList = [];

  ValueNotifier<List<int>> selectedInterest = ValueNotifier([]);

  @override
  void initState() {
    super.initState();

    context.read<InterestDomainBloc>().add(const InterestDomainInitEvent());
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Padding(
      padding: EdgeInsets.only(top:  MediaQuery.of(context).padding.top + 40, left: 24, right: 24),
      child: BlocConsumer<InterestDomainBloc, InterestDomainState>(
        listener: (context, state) {
          if (state is InterestDomainShowResponseState) {
            domainList = state.domainList;
            selectedInterest.value = [];
            final List<InterestDomainEntity> list =
                state.selectedDomainList;
            for (int i = 0; i < domainList.length; i++) {
              for (int j = 0; j < list.length; j++) {
                if (list[j].domainId == domainList[i].domainId) {
                  selectedInterest.value.add(i);
                  break;
                }
              }
            }
          }
          if (state is InterestSavedState) {
            if (state.status) {
              widget.onDone();
            }
            AppMessage.showOverlayNotification('', state.message);
          }
          if (state is InterestAlreadySavedState) {
            context.replaceRoute(HomeNavigationRoute());
          }
        },
        builder: (context, state) {
          return IgnorePointer(
            ignoring: state is InterestDomainLoadingState,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                OnBoardingHeader(
                  title: LocaleKeys.chooseYourInterests.tr(),
                  index: 0,
                ),
                Expanded(
                  child: (state is InterestDomainErrorState)
                      ? SizedBox(
                          height: MediaQuery.sizeOf(context).height * 0.8,
                          child: ErrorReloadPlaceholder(
                            error: state.error,
                            onReload: () {
                              context
                                  .read<InterestDomainBloc>()
                                  .add(const InterestDomainInitEvent());
                            },
                          ),
                        )
                      : domainList.isNotEmpty
                          ? ListView.separated(
                            itemCount: domainList.length,
                            shrinkWrap: true,
                            padding: const EdgeInsets.only(top: 12),
                            separatorBuilder: (context, index) =>
                                const SizedBox(height: 14),
                            itemBuilder: (context, int index) {
                              return ValueListenableBuilder(
                                valueListenable: selectedInterest,
                                builder: (context, selectedIndex, w) {
                                  return Material(
                                    borderRadius: BorderRadius.circular(10),
                                    color: selectedInterest.value.contains(index)
                                        ? isLightMode
                                            ? AppColors.blueShade8
                                            : AppColors.greyShade19
                                        : isLightMode
                                            ? AppColors.greyShade7
                                            : AppColors.blueShade32,
                                    child: InkWell(
                                      onTap: () {
                                        if (selectedInterest.value
                                            .any((e) => e == index)) {
                                          selectedInterest.value.remove(index);
                                        } else {
                                          selectedInterest.value.add(index);
                                        }
                                        selectedInterest.notifyListeners();
                                      },
                                      child: Container(
                                        padding: const EdgeInsets.all(14),
                                        decoration: BoxDecoration(
                                          // border: Border.all(
                                          //   color: selectedInterest.value.contains(index)
                                          //       ? isLightMode
                                          //           ? AppColors.blueLight
                                          //           : Colors.transparent
                                          //       : isLightMode
                                          //           ? AppColors.white
                                          //           : Colors.transparent,
                                          // ),
                                          borderRadius: BorderRadius.circular(10),
                                        ),
                                        child: Row(
                                          children: [
                                            Container(
                                              width: 80,
                                              height: 50,
                                              alignment: Alignment.center,
                                              child: SvgPicture.network(
                                                domainList[index].domainIcon ??
                                                    '',
                                                width: 50,
                                                height: 50,
                                                colorFilter: ColorFilter.mode(
                                                  AppColors.blueLightOld,
                                                  BlendMode.srcIn,
                                                ),
                                              ),
                                            ),
                                            const SizedBox(width: 10),
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    HiveUtilsSettings
                                                                .getAppLanguage() ==
                                                            'en'
                                                        ? domainList[index]
                                                                .domainName ??
                                                            '-'
                                                        : domainList[index]
                                                                .domainNameAr ??
                                                            '-',
                                                    style: TextStyle(
                                                      color: isLightMode
                                                          ? AppColors.blackShade1
                                                          : AppColors.white,
                                                      fontSize: 16,
                                                      fontWeight: FontWeight.w500,
                                                    ),
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                    maxLines: 1,
                                                    // textScaler: TextScaler.linear(
                                                    //   textScaleFactor.value,
                                                    // ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            Visibility(
                                              visible: selectedInterest.value
                                                  .contains(index),
                                              child: SvgPicture.asset(
                                                  AppImages.icTick,),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              );
                            },
                          )
                          : state is InterestDomainLoadingState
                              ? const Center(child: CircularProgressIndicator())
                              : const NoDataPlaceholder(),
                ),
                const SizedBox(height: 24),
                if (state is! InterestDomainErrorState && domainList.isNotEmpty)
                  Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const OnboardingPageIndicators(
              currentStepIndex: 0,
              totalSteps: 6,
              ),
                    const SizedBox(height: 24),
                    TextButton(
                      style: TextButton.styleFrom(
                        backgroundColor:
                            isLightMode ? AppColors.blueLight : AppColors.blueLightOld,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      onPressed: () {
                        if (selectedInterest.value.length < 3) {
                          AppMessage.showOverlayNotification(
                            '',
                            LocaleKeys.selectDomains.tr(),
                          );
                        } else {
                          final Map<String, dynamic> map = {
                            'data': json.encode(
                              selectedInterest.value.map((e) {
                                final DomainModel model = domainList[e];
                                return {
                                  'domain_id': model.domainId,
                                  'name': model.domainName,
                                };
                              }).toList(),
                            ),
                          };
                          context
                              .read<InterestDomainBloc>()
                              .add(InterestSaveEvent(domainListMap: map));
                        }
                      },
                      child: (state is InterestDomainLoadingState)
                        ?SizedBox(
                        width: 19,
                        height: 19,
            child: Center(child: CircularProgressIndicator(valueColor: AlwaysStoppedAnimation<Color>(AppColors.white))),)
                : Text(
                        LocaleKeys.next.tr(),
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: AppColors.white,
                        ),
                        // textScaler: TextScaler.linear(textScaleFactor.value),
                      ),
                    ),
                    const SizedBox(height: 52),

            ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
