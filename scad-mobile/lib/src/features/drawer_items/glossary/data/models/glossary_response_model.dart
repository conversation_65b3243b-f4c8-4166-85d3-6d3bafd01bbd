import 'package:json_annotation/json_annotation.dart';
import 'package:scad_mobile/src/features/drawer_items/glossary/data/models/glossary_model.dart';

part 'glossary_response_model.g.dart';

@JsonSerializable()
class GlossaryResponseModel {
  GlossaryResponseModel({
    this.totalCount,
    this.page,
    this.limit,
    this.alphabets,
    this.results,
  });

  factory GlossaryResponseModel.fromJson(Map<String, dynamic> json) =>
      _$GlossaryResponseModelFromJson(json);

  @<PERSON>son<PERSON>ey(name: 'totalCount')
  int? totalCount;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'page')
  int? page;
  @Json<PERSON>ey(name: 'limit')
  int? limit;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'alphabets')
  Map<String, int>? alphabets;
  @Json<PERSON>ey(name: 'results')
  List<GlossaryModel>? results;

  Map<String, dynamic> toJson() => _$GlossaryResponseModelToJson(this);
}
