import 'package:json_annotation/json_annotation.dart';
import 'package:scad_mobile/src/features/drawer_items/glossary/data/models/glossary_filter/glossary_filter_domain_model.dart';

part 'glossary_filter_response_model.g.dart';

@JsonSerializable()
class GlossaryFilterResponseModel {
  GlossaryFilterResponseModel({
    this.domains,
  });

  factory GlossaryFilterResponseModel.fromJson(Map<String, dynamic> json) =>
      _$GlossaryFilterResponseModelFromJson(json);

  @JsonKey(name: 'domains')
  List<GlossaryFilterDomainModel>? domains;

  Map<String, dynamic> toJson() => _$GlossaryFilterResponseModelToJson(this);
}
