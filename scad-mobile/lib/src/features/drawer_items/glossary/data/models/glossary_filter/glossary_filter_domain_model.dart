import 'package:json_annotation/json_annotation.dart';
import 'package:scad_mobile/src/features/drawer_items/glossary/data/models/glossary_filter/glossary_filter_sub_domain_model.dart';

part 'glossary_filter_domain_model.g.dart';

@JsonSerializable()
class GlossaryFilterDomainModel {
  GlossaryFilterDomainModel({
    this.name,
    this.items,
    this.isSelected = false,
  });

  factory GlossaryFilterDomainModel.fromJson(Map<String, dynamic> json) =>
      _$GlossaryFilterDomainModelFromJson(json);

  @<PERSON><PERSON><PERSON><PERSON>(name: 'name')
  String? name;
  @JsonKey(name: 'items')
  List<GlossaryFilterSubDomainModel>? items;
  @JsonKey(name: 'is_selected')
  bool isSelected;

  Map<String, dynamic> toJson() => _$GlossaryFilterDomainModelToJson(this);
}
