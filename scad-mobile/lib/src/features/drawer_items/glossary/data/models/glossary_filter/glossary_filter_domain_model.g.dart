// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'glossary_filter_domain_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GlossaryFilterDomainModel _$GlossaryFilterDomainModelFromJson(
        Map<String, dynamic> json) =>
    GlossaryFilterDomainModel(
      name: json['name'] as String?,
      items: (json['items'] as List<dynamic>?)
          ?.map((e) =>
              GlossaryFilterSubDomainModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      isSelected: json['is_selected'] as bool? ?? false,
    );

Map<String, dynamic> _$GlossaryFilterDomainModelToJson(
        GlossaryFilterDomainModel instance) =>
    <String, dynamic>{
      'name': instance.name,
      'items': instance.items,
      'is_selected': instance.isSelected,
    };
