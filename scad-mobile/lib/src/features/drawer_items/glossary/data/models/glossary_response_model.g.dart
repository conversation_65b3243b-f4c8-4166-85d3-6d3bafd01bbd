// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'glossary_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GlossaryResponseModel _$GlossaryResponseModelFromJson(
        Map<String, dynamic> json) =>
    GlossaryResponseModel(
      totalCount: json['totalCount'] as int?,
      page: json['page'] as int?,
      limit: json['limit'] as int?,
      alphabets: (json['alphabets'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, e as int),
      ),
      results: (json['results'] as List<dynamic>?)
          ?.map((e) => GlossaryModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$GlossaryResponseModelToJson(
        GlossaryResponseModel instance) =>
    <String, dynamic>{
      'totalCount': instance.totalCount,
      'page': instance.page,
      'limit': instance.limit,
      'alphabets': instance.alphabets,
      'results': instance.results,
    };
