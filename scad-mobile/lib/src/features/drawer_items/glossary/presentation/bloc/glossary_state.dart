part of 'glossary_bloc.dart';

abstract class GlossaryState extends Equatable {
  const GlossaryState();

  @override
  List<Object> get props => [];
}

class GlossaryLoadingState extends GlossaryState {
  const GlossaryLoadingState();

  @override
  List<Object> get props => [];
}

/// the state used to get the glossary list
class GlossaryDataState extends GlossaryState {
  const GlossaryDataState({
    required this.glossaryResponse,
  });

  final GlossaryResponseModel? glossaryResponse;

  @override
  List<Object> get props => [
        glossaryResponse ?? GlossaryResponseModel(),
      ];
}

class GlossaryLoadFiltersSuccessState extends GlossaryState {
  const GlossaryLoadFiltersSuccessState({
    required this.glossaryFilterResponse,
  });

  final GlossaryFilterResponseModel? glossaryFilterResponse;

  @override
  List<Object> get props => [
        glossaryFilterResponse ?? GlossaryFilterResponseModel(),
      ];
}

/// the state used tfor common error
class GlossaryErrorState extends GlossaryState {
  const GlossaryErrorState({
    this.errorText,
  });

  final String? errorText;

  @override
  List<Object> get props => [errorText ?? ''];
}

/// the state used to get the error of glossary data
class GlossaryListErrorState extends GlossaryState {
  const GlossaryListErrorState({
    this.errorText,
  });

  final String? errorText;

  @override
  List<Object> get props => [errorText ?? ''];
}

/// the state used to get the error of glossary filter data
class GlossaryFilterErrorState extends GlossaryState {
  const GlossaryFilterErrorState({
    this.errorText,
  });

  final String? errorText;

  @override
  List<Object> get props => [errorText ?? ''];
}

/// the state used to get the domain for filter
// class GlossaryFilterDomainDropdownState extends GlossaryState {
//   const GlossaryFilterDomainDropdownState({
//     required this.domainItems,
//     required this.selectedDomains,
//     required this.subDomainItems,
//     this.selectedSubDomains,
//   });
//
//   final List<GlossaryFilterDomainModel> domainItems;
//   final String selectedDomains;
//   final List<GlossaryFilterSubDomainModel> subDomainItems;
//   final String? selectedSubDomains;
//
//   @override
//   List<Object> get props =>
//       [domainItems, selectedDomains, subDomainItems, selectedDomains];
// }

/// the state used to get the sub domain for filter
// class GlossaryFilterSubDomainDropdownState extends GlossaryState {
//   const GlossaryFilterSubDomainDropdownState({
//     required this.subDomainList,
//     required this.selectedValue,
//   });
//
//   final List<GlossaryFilterSubDomainModel> subDomainList;
//   final String selectedValue;
//
//   @override
//   List<Object> get props => [subDomainList, selectedValue];
// }

/// the state used to get the domain for filter
// class GlossaryFilterDomainDropdownValueUpdatesState extends GlossaryState {
//   const GlossaryFilterDomainDropdownValueUpdatesState({
//     required this.domainItems,
//     required this.subDomainItems,
//   });
//
//   final List<GlossaryFilterDomainModel> domainItems;
//   final List<GlossaryFilterSubDomainModel> subDomainItems;
//
//   @override
//   List<Object> get props =>
//       [domainItems, subDomainItems, ];
// }

/// the state used to get the sub domain for filter
// class GlossaryFilterSubDomainDropdownValueUpdateState extends GlossaryState {
//   const GlossaryFilterSubDomainDropdownValueUpdateState ({
//     required this.subDomainList,
//     required this.selectedValue,
//   });
//
//   final List<GlossaryFilterSubDomainModel> subDomainList;
//   final String selectedValue;
//
//   @override
//   List<Object> get props => [subDomainList, selectedValue];
// }

/// the state used to set selected alphabet
class GlossaryAlphabetSelectedState extends GlossaryState {
  const GlossaryAlphabetSelectedState({required this.selectedAlphabet});

  final String selectedAlphabet;

  @override
  List<Object> get props => [selectedAlphabet];
}

/// the state on done button for both domain, sub domain and also for
/// alphabet selection and ascending/descending
class GlossaryFilterDoneButtonState extends GlossaryState {
  const GlossaryFilterDoneButtonState({
    this.selectedDomains,
    this.selectedSubDomains,
    this.selectedAlphabet,
    this.isAscending,
    required this.glossaryResponse,
    required this.term,
  });

  final String term;

  final List<GlossaryFilterDomainModel>? selectedDomains;
  final List<GlossaryFilterSubDomainModel>? selectedSubDomains;
  final String? selectedAlphabet;
  final bool? isAscending;
  final GlossaryResponseModel glossaryResponse;

  @override
  List<Object> get props => [
        selectedDomains ?? [],
        selectedSubDomains ?? [],
        selectedAlphabet ?? '',
        isAscending ?? true,
        glossaryResponse,
        term,
      ];
}

/// the state used to set arabic/english language switch
// class GlossaryLanguageToggleState extends GlossaryState {
//   const GlossaryLanguageToggleState({required this.updatedList});
//
//   final List<GlossaryModel> updatedList;
//
//   @override
//   List<Object> get props => [updatedList];
// }

/// the state used to reset the filter
class GlossaryFilterResetState extends GlossaryState {
  const GlossaryFilterResetState(
      {this.selectedDomain,
      this.selectedSubDomain,
      this.selectedAlphabet,
      required this.term});

  final String term;
  final String? selectedDomain;
  final String? selectedSubDomain;
  final String? selectedAlphabet;

  @override
  List<Object> get props => [
        selectedDomain ?? '',
        selectedSubDomain ?? '',
        selectedAlphabet ?? '',
        term,
      ];
}
