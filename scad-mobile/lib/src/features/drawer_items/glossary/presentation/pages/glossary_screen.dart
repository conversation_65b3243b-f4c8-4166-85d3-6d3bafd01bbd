import 'dart:async';
import 'dart:convert';

import 'package:auto_route/annotations.dart';
import 'package:easy_localization/easy_localization.dart' as locale;
import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:lottie/lottie.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/widgets/appbar/flat_app_bar.dart';
import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';
import 'package:scad_mobile/src/common/widgets/no_data_placeholder.dart';
import 'package:scad_mobile/src/config/firebase_config/firebase_config.dart';
import 'package:scad_mobile/src/features/drawer_items/glossary/data/models/glossary_filter/glossary_filter_domain_model.dart';
import 'package:scad_mobile/src/features/drawer_items/glossary/data/models/glossary_filter/glossary_filter_response_model.dart';
import 'package:scad_mobile/src/features/drawer_items/glossary/data/models/glossary_filter/glossary_filter_sub_domain_model.dart';
import 'package:scad_mobile/src/features/drawer_items/glossary/data/models/glossary_model.dart';
import 'package:scad_mobile/src/features/drawer_items/glossary/data/models/glossary_response_model.dart';
import 'package:scad_mobile/src/features/drawer_items/glossary/presentation/bloc/glossary_bloc.dart';
import 'package:scad_mobile/src/features/drawer_items/glossary/presentation/widgets/domain_bottom_sheet.dart';
import 'package:scad_mobile/src/features/drawer_items/glossary/presentation/widgets/sub_domain_bottom_sheet.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/animation_asset.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

@RoutePage()
class GlossaryScreen extends StatefulWidget {
  const GlossaryScreen({this.initialSearchTerm = '', super.key});

  final String initialSearchTerm;

  @override
  State<GlossaryScreen> createState() => _GlossaryScreenState();
}

class _GlossaryScreenState extends State<GlossaryScreen> {
  final TextEditingController searchController = TextEditingController();
  ScrollController scrollController = ScrollController();
  List<GlossaryModel> glossaryList = [];
  GlossaryResponseModel? glossaryData;
  GlossaryFilterResponseModel? glossaryFilter;

  // String? selectedDomain;
  // String? selectedSubDomain;
  // List<GlossaryFilterSubDomainModel>? subDomainItems;
  bool isAscending = true;
  String? selectedAlphabet;

  String initialSearchTerm = '';
  bool apiLoading = false;
  bool filter = false;
  List<GlossaryFilterDomainModel>? selectedDomains;
  List<GlossaryFilterSubDomainModel>? selectedSubDomains;

  @override
  void initState() {
    super.initState();
    FirebaseConfig.setScreenToAnalytics('Glossary');
    initialSearchTerm = widget.initialSearchTerm;

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      scrollController.addListener(_loadData);
      context.read<GlossaryBloc>().add(const GlossaryLoadFiltersEvent());
    });
  }

  void _listScrollUp() {
    try {
      scrollController.animateTo(
        scrollController.position.minScrollExtent,
        duration: const Duration(milliseconds: 200),
        curve: Curves.fastOutSlowIn,
      );
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      //error
    }
  }

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return AppDrawer(
      child: Scaffold(
        body: GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
          child: Column(
            children: [
              FlatAppBar(
                title: LocaleKeys.glossary.tr(),
                scrollController: scrollController,
              ),
              Expanded(
                child: BlocConsumer<GlossaryBloc, GlossaryState>(
                  listener: (context, state) {
                    if (state is GlossaryLoadFiltersSuccessState) {
                      if (filter) {
                        filter = false;
                        glossaryList = [];
                      }
                      glossaryFilter = state.glossaryFilterResponse;

                      // context
                      //     .read<GlossaryBloc>()
                      //     .add(GlossaryLoadDataEvent(page: (glossaryData?.page ?? 0) + 1));
                      if (initialSearchTerm.isNotEmpty) {
                        searchController.text = initialSearchTerm;

                        context.read<GlossaryBloc>().add(
                              GlossaryDoneButtonEvent(
                                page: 1,
                                selectedDomains: const [],
                                selectedSubDomains: const [],
                                selectedAlphabet: selectedAlphabet,
                                isAscending: isAscending,
                                term: searchController.text,
                              ),
                            );
                        initialSearchTerm = '';
                      } else {
                        context.read<GlossaryBloc>().add(
                              GlossaryDoneButtonEvent(
                                page: 1,
                                selectedDomains: selectedDomains,
                                selectedSubDomains: selectedSubDomains,
                                isAscending: isAscending,
                              ),
                            );
                      }
                      apiLoading = true;
                    } else if (state is GlossaryDataState) {
                      if (filter) {
                        filter = false;
                        glossaryList = [];
                      }
                      glossaryData = state.glossaryResponse;

                      if (glossaryData?.page == 1) {
                        glossaryList = [];
                        glossaryList.addAll(glossaryData?.results ?? []);
                      } else {
                        (glossaryData?.results ?? []).removeWhere(
                          (element) => glossaryList
                              .any((e) => element.titleEn == e.titleEn),
                        );

                        glossaryList.addAll(glossaryData?.results ?? []);
                      }
                      //todo check filter

                      // } else if (state is GlossaryLanguageToggleState) {
                      //   glossaryData?.results = state.updatedList;
                    } else if (state is GlossaryAlphabetSelectedState) {
                      selectedAlphabet = state.selectedAlphabet;
                    } else if (state is GlossaryFilterDoneButtonState) {
                      if (filter) {
                        filter = false;
                        glossaryList = [];
                        _listScrollUp();
                      }
                      selectedAlphabet = state.selectedAlphabet;
                      isAscending = state.isAscending ?? true;
                      glossaryData = state.glossaryResponse;
                      if (glossaryData?.page == 1) {
                        glossaryList = glossaryData?.results ?? [];
                      } else {
                        (glossaryData?.results ?? []).removeWhere(
                          (element) => glossaryList
                              .any((e) => element.titleEn == e.titleEn),
                        );
                        glossaryList.addAll(glossaryData?.results ?? []);
                      }
                      searchController.text = state.term;

                      selectedDomains = state.selectedDomains;
                      selectedSubDomains = state.selectedSubDomains;
                      apiLoading = false;
                    }
                    // else if (state is GlossaryFilterDomainDropdownValueUpdatesState) {
                    //   subDomainItems = state.subDomainItems;
                    //   glossaryFilter?.domains = state.domainItems;
                    //   searchController.text = '';
                    //   filter = true;
                    // }
                    // else if (state
                    // //todo remove from sub domain sheet
                    //     is GlossaryFilterSubDomainDropdownValueUpdateState) {
                    //   selectedSubDomains = state.subDomainList;
                    //   searchController.text = '';
                    //   filter = true;
                    // }
                    // else if (state is GlossaryFilterDomainDropdownState) {
                    //   selectedDomain = state.selectedDomains;
                    //   subDomainItems = state.subDomainItems;
                    //   selectedSubDomain = state.selectedSubDomains;
                    //   searchController.text = '';
                    //   filter = true;
                    // } else if (state is GlossaryFilterSubDomainDropdownState) {
                    //   selectedSubDomain = state.selectedValue;
                    //   subDomainItems = state.subDomainList;
                    //   searchController.text = '';
                    //    filter = true;
                    // }
                    else if (state is GlossaryFilterResetState) {
                      // selectedDomain = state.selectedDomain;
                      selectedAlphabet = state.selectedAlphabet;
                      searchController.text = state.term;
                      // subDomainItems?.clear();
                      glossaryList = [];
                      selectedDomains = [];

                      for (final element in glossaryFilter?.domains ??
                          <GlossaryFilterDomainModel>[]) {
                        element.isSelected = false;

                        for (final data in element.items ??
                            <GlossaryFilterSubDomainModel>[]) {
                          data.isSelected = false;
                        }
                      }

                      selectedSubDomains = [];
                    }
                  },
                  builder: (context, state) {
                    if (state is GlossaryLoadingState &&
                        (glossaryList.isEmpty && !filter)) {
                      return const Center(
                        child: CircularProgressIndicator(),
                      );
                    } else if (state is GlossaryListErrorState) {
                      return Expanded(
                        child: Center(
                          child: Text(
                            state.errorText ??
                                LocaleKeys.somethingWentWrong.tr(),
                            textScaler:
                                TextScaler.linear(textScaleFactor.value),
                          ),
                        ),
                      );
                    } else if (state is GlossaryFilterErrorState) {
                      return Expanded(
                        child: Center(
                          child: Text(
                            state.errorText ??
                                LocaleKeys.somethingWentWrong.tr(),
                            textScaler:
                                TextScaler.linear(textScaleFactor.value),
                          ),
                        ),
                      );
                    } else if (state is GlossaryErrorState) {
                      return Expanded(
                        child: Center(
                          child: Text(
                            state.errorText ??
                                LocaleKeys.somethingWentWrong.tr(),
                            textScaler:
                                TextScaler.linear(textScaleFactor.value),
                          ),
                        ),
                      );
                    }
                    return Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 24),
                          child: SizedBox(
                            child: TextField(
                              controller: searchController,
                              style: TextStyle(
                                  color: AppColors.black,
                                  fontSize: 16 * textScaleFactor.value),
                              onChanged: (value) {},
                              decoration: InputDecoration(
                                suffixIcon: InkWell(
                                  borderRadius: BorderRadius.circular(25),
                                  onTap: () {
                                    context.read<GlossaryBloc>().add(
                                          GlossaryFilterResetEvent(
                                            selectedDomain: '',
                                            selectedSubDomain: '',
                                            selectedAlphabet: '',
                                            term: searchController.text,
                                          ),
                                        );
                                    context.read<GlossaryBloc>().add(
                                          GlossaryDoneButtonEvent(
                                            page: 1,
                                            selectedDomains: const [],
                                            selectedSubDomains: const [],
                                            selectedAlphabet: selectedAlphabet,
                                            isAscending: isAscending,
                                            term: searchController.text,
                                          ),
                                        );
                                    apiLoading = true;
                                  },
                                  child: SizedBox(
                                    width: 27,
                                    height: 27,
                                    child: Padding(
                                      padding: const EdgeInsets.all(14),
                                      child: SvgPicture.asset(
                                        AppImages.iconSearchBlack,
                                      ),
                                    ),
                                  ),
                                ),
                                hintText: LocaleKeys.search.tr(),
                                contentPadding:
                                    const EdgeInsets.symmetric(horizontal: 24),
                                hintStyle: TextStyle(
                                  color: AppColors.grey,
                                  fontSize: 16 * textScaleFactor.value,
                                  fontWeight: FontWeight.w400,
                                ),
                                isDense: true,
                                border: OutlineInputBorder(
                                  borderSide: const BorderSide(
                                    color: Colors.blueAccent,
                                    width: 32,
                                  ),
                                  borderRadius: BorderRadius.circular(25),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderSide: const BorderSide(
                                    color: Colors.white,
                                    width: 32,
                                  ),
                                  borderRadius: BorderRadius.circular(25),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderSide: const BorderSide(
                                    color: Colors.white,
                                    width: 32,
                                  ),
                                  borderRadius: BorderRadius.circular(25),
                                ),
                              ),
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(
                            vertical: 8,
                            horizontal: 24,
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              TextButton(
                                style: TextButton.styleFrom(
                                  backgroundColor: Colors.transparent,
                                  shape: const RoundedRectangleBorder(
                                    side: BorderSide(
                                      color: Colors.transparent,
                                    ),
                                  ),
                                ),
                                onPressed: !_isFilterApplied()
                                    ? null
                                    : () {
                                        context.read<GlossaryBloc>().add(
                                              const GlossaryFilterResetEvent(
                                                selectedDomain: '',
                                                selectedSubDomain: '',
                                                selectedAlphabet: '',
                                                term: '',
                                              ),
                                            );
                                        glossaryList = [];
                                        glossaryData = null;
                                        selectedDomains = [];
                                        selectedSubDomains = [];
                                        selectedAlphabet = '';
                                        for (final element
                                            in glossaryFilter?.domains ?? []) {
                                          element.isSelected = false;
                                        }

                                        // context.read<GlossaryBloc>().add(
                                        //     GlossaryLoadDataEvent(
                                        //         page: (glossaryData?.page ?? -1) + 1));

                                        context.read<GlossaryBloc>().add(
                                              GlossaryDoneButtonEvent(
                                                page:
                                                    (glossaryData?.page ?? 0) +
                                                        1,
                                                selectedDomains:
                                                    selectedDomains,
                                                selectedSubDomains:
                                                    selectedSubDomains,
                                                selectedAlphabet:
                                                    selectedAlphabet,
                                                isAscending: isAscending,
                                                term: '',
                                              ),
                                            );
                                        // context.read<GlossaryBloc>().add(
                                        //       GlossaryDoneButtonEvent(
                                        //         selectedDomains: const [],
                                        //         selectedSubDomains: const [],
                                        //         selectedAlphabet: selectedAlphabet,
                                        //         isAscending: isAscending,
                                        //         term: '',
                                        //       ),
                                        //     );
                                        apiLoading = true;
                                      },
                                child: Row(
                                  children: [
                                    Text(
                                      LocaleKeys.reset.tr(),
                                      textScaler: TextScaler.linear(
                                          textScaleFactor.value),
                                    ),
                                    const SizedBox(
                                      width: 8,
                                    ),
                                    if (!_isFilterApplied())
                                      SvgPicture.asset(
                                        'assets/images/ic_reset.svg',
                                        colorFilter: ColorFilter.mode(
                                          _isFilterApplied()
                                              ? AppColors.blueLight
                                              : AppColors.grey.withOpacity(0.6),
                                          BlendMode.srcIn,
                                        ),
                                      )
                                    else
                                      isLightMode
                                          ? Lottie.asset(
                                              AnimationAsset.animationSync,
                                              width: 16,
                                              height: 16,
                                              fit: BoxFit.cover,
                                            )
                                          : Lottie.asset(
                                              AnimationAssetDark.animationSync,
                                              width: 16,
                                              height: 16,
                                              fit: BoxFit.cover,
                                            ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        // Padding(
                        //   padding: const EdgeInsets.symmetric(horizontal: 24),
                        //   child: Wrap(
                        //     crossAxisAlignment: WrapCrossAlignment.center,
                        //     alignment: WrapAlignment.center,
                        //     spacing: 12,
                        //     runSpacing: 12,
                        //     children: [
                        //       for (final String suggestion in suggestions)
                        //         GestureDetector(
                        //           onTap: () {
                        //             searchController.text = suggestion;
                        //             suggestionsNotifier.value = suggestions
                        //                 .where(
                        //                   (element) => element
                        //                       .toLowerCase()
                        //                       .contains(suggestion.toLowerCase()),
                        //                 )
                        //                 .toList();
                        //           },
                        //           child: Container(
                        //             decoration: BoxDecoration(
                        //               color: AppColors.blueLight.withOpacity(0.1),
                        //               borderRadius: BorderRadius.circular(30),
                        //             ),
                        //             padding: const EdgeInsets.symmetric(
                        //               horizontal: 10,
                        //               vertical: 4,
                        //             ),
                        //             child: Text(
                        //               suggestion,
                        //               style: AppTextStyles.s14w4cBlue
                        //                   .copyWith(fontWeight: FontWeight.w500),
                        //             ),
                        //           ),
                        //         ),
                        //     ],
                        //   ),
                        // ),
                        // const SizedBox(height: 30),
                        Expanded(
                          child: Container(
                            decoration: BoxDecoration(
                              color: isLightMode
                                  ? AppColors.white
                                  : AppColors.blueShade32,
                              borderRadius: const BorderRadius.only(
                                topRight: Radius.circular(20),
                                topLeft: Radius.circular(20),
                              ),
                            ),
                            padding: const EdgeInsets.only(top: 24),
                            child: Column(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 24),
                                  child: Row(
                                    children: [
                                      BlocBuilder<GlossaryBloc, GlossaryState>(
                                        builder: (context, state) {
                                          return InkWell(
                                            onTap: () {
                                              isAscending = !isAscending;
                                              filter = true;
                                              context.read<GlossaryBloc>().add(
                                                    GlossaryDoneButtonEvent(
                                                      page: 1,
                                                      selectedDomains:
                                                          glossaryFilter
                                                              ?.domains,
                                                      selectedSubDomains:
                                                          selectedSubDomains,
                                                      selectedAlphabet:
                                                          selectedAlphabet,
                                                      isAscending: isAscending,
                                                      term:
                                                          searchController.text,
                                                    ),
                                                  );
                                              apiLoading = true;
                                            },
                                            child: SvgPicture.asset(
                                              isAscending
                                                  ? isLightMode
                                                      ? 'assets/images/sort-enable-light.svg'
                                                      : 'assets/images/ic-sorting-dark-light.svg'
                                                  : isLightMode
                                                      ? AppImages.icSorting
                                                      : 'assets/images/ic-sorting-dark.svg',
                                              height: 36,
                                            ),
                                          );
                                        },
                                      ),
                                      const SizedBox(width: 14),
                                      Expanded(
                                        child: InkWell(
                                          onTap: () {
                                            if (searchController
                                                .text.isNotEmpty) {
                                              for (final GlossaryFilterDomainModel item
                                                  in glossaryFilter?.domains ??
                                                      []) {
                                                item.isSelected = false;
                                              }
                                            }
                                            showModalBottomSheet<dynamic>(
                                              backgroundColor:
                                                  Colors.transparent,
                                              isScrollControlled: false,
                                              context: context,
                                              builder: (BuildContext context) {
                                                return DomainBottomSheet(
                                                  oldSubDomainItems: List.generate(
                                                      selectedSubDomains
                                                              ?.length ??
                                                          0,
                                                      (index) => GlossaryFilterSubDomainModel
                                                          .fromJson(jsonDecode(
                                                              jsonEncode(
                                                                  selectedSubDomains?[
                                                                      index])) as Map<
                                                              String,
                                                              dynamic>)),
                                                  domainList: List.generate(
                                                      glossaryFilter?.domains
                                                              ?.length ??
                                                          0,
                                                      (index) => glossaryFilter!
                                                          .domains![index]),
                                                );
                                              },
                                            );
                                          },
                                          child: Container(
                                            height: 40,
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 15,
                                            ),
                                            decoration: BoxDecoration(
                                              color: isLightMode
                                                  ? AppColors.white
                                                  : AppColors.blueShade36,
                                              borderRadius:
                                                  BorderRadius.circular(70),
                                              border: Border.all(
                                                color: isLightMode
                                                    ? AppColors.greyShade1
                                                    : AppColors.blackShade8,
                                              ),
                                            ),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Flexible(
                                                  child: Text(
                                                    (selectedDomains ?? [])
                                                            .firstWhere(
                                                              (element) => element
                                                                  .isSelected,
                                                              orElse: () =>
                                                                  GlossaryFilterDomainModel(),
                                                            )
                                                            .name ??
                                                        LocaleKeys.select.tr(),
                                                    // (selectedDomain == null ||
                                                    //         selectedDomain == '')
                                                    //     ? LocaleKeys.select.tr()
                                                    //     : (selectedDomain ?? ''),
                                                    style: TextStyle(
                                                      color: isLightMode
                                                          ? !(selectedDomains?.any(
                                                                      (element) =>
                                                                          element
                                                                              .isSelected) ??
                                                                  false)
                                                              ? AppColors
                                                                  .greyShade4
                                                              : AppColors.black
                                                          : AppColors.white,
                                                      fontSize: 14,
                                                      fontWeight:
                                                          FontWeight.w400,
                                                    ),
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                    maxLines: 1,
                                                    textScaler:
                                                        TextScaler.linear(
                                                      textScaleFactor.value,
                                                    ),
                                                  ),
                                                ),
                                                const Icon(
                                                    Icons.arrow_drop_down),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 14),
                                      Expanded(
                                        child: Opacity(
                                          opacity: (selectedSubDomains ?? [])
                                                  .isNotEmpty
                                              ? 1
                                              : 0.4,
                                          child: InkWell(
                                            onTap: () {
                                              if ((selectedSubDomains ?? [])
                                                  .isNotEmpty) {
                                                if (searchController
                                                    .text.isNotEmpty) {
                                                  for (final GlossaryFilterSubDomainModel item
                                                      in selectedSubDomains ??
                                                          []) {
                                                    item.isSelected = false;
                                                  }
                                                }
                                                showModalBottomSheet<dynamic>(
                                                  backgroundColor:
                                                      Colors.transparent,
                                                  isScrollControlled: false,
                                                  context: context,
                                                  builder:
                                                      (BuildContext context) {
                                                    return SubDomainBottomSheet(
                                                      domainList:
                                                          selectedDomains ?? [],
                                                      subDomainList:
                                                          List.generate(
                                                        (selectedSubDomains ??
                                                                [])
                                                            .length,
                                                        (index) =>
                                                            GlossaryFilterSubDomainModel
                                                                .fromJson(
                                                          jsonDecode(
                                                            jsonEncode(
                                                              selectedSubDomains?[
                                                                  index],
                                                            ),
                                                          ) as Map<String,
                                                              dynamic>,
                                                        ),
                                                      ),
                                                    );
                                                  },
                                                );
                                              }
                                            },
                                            child: Container(
                                              height: 40,
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                horizontal: 15,
                                              ),
                                              decoration: BoxDecoration(
                                                color: isLightMode
                                                    ? AppColors.white
                                                    : AppColors.blueShade36,
                                                borderRadius:
                                                    BorderRadius.circular(70),
                                                border: Border.all(
                                                  color: isLightMode
                                                      ? AppColors.greyShade1
                                                      : AppColors.blackShade8,
                                                ),
                                              ),
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Flexible(
                                                    child: Text(
                                                      (selectedSubDomains ?? [])
                                                              .firstWhere(
                                                                (element) => element
                                                                    .isSelected,
                                                                orElse: () =>
                                                                    GlossaryFilterSubDomainModel(),
                                                              )
                                                              .name ??
                                                          LocaleKeys.select
                                                              .tr(),
                                                      style: TextStyle(
                                                        color: isLightMode
                                                            ? !(selectedSubDomains?.any(
                                                                        (element) =>
                                                                            element
                                                                                .isSelected) ??
                                                                    false)
                                                                ? AppColors
                                                                    .greyShade4
                                                                : AppColors
                                                                    .black
                                                            : AppColors.white,
                                                        fontSize: 14,
                                                        fontWeight:
                                                            FontWeight.w400,
                                                      ),
                                                      overflow:
                                                          TextOverflow.ellipsis,
                                                      maxLines: 1,
                                                      textScaler:
                                                          TextScaler.linear(
                                                        textScaleFactor.value,
                                                      ),
                                                    ),
                                                  ),
                                                  const Icon(
                                                    Icons.arrow_drop_down,
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 20),
                                SizedBox(
                                  height: 30,
                                  child: SingleChildScrollView(
                                    scrollDirection: Axis.horizontal,
                                    child: Row(
                                      children: glossaryData?.alphabets?.keys
                                              .map((e) {
                                            final bool isSelectable =
                                                glossaryData?.alphabets?[e] !=
                                                    0;
                                            return Padding(
                                              padding: EdgeInsets.only(
                                                left: 24,
                                                right: e ==
                                                        glossaryData?.alphabets
                                                            ?.keys.last
                                                    ? 24
                                                    : 0,
                                              ),
                                              child: InkWell(
                                                onTap: () {
                                                  if (isSelectable) {
                                                    context
                                                        .read<GlossaryBloc>()
                                                        .add(
                                                          GlossaryAlphabetSelectedEvent(
                                                            selectedAlphabet: e,
                                                          ),
                                                        );

                                                    if (selectedAlphabet == e) {
                                                      filter = true;
                                                      glossaryList = [];
                                                      context
                                                          .read<GlossaryBloc>()
                                                          .add(
                                                            GlossaryDoneButtonEvent(
                                                              page: 1,
                                                              selectedDomains:
                                                                  selectedDomains,
                                                              selectedSubDomains:
                                                                  selectedSubDomains,
                                                              isAscending:
                                                                  isAscending,
                                                              term:
                                                                  searchController
                                                                      .text,
                                                            ),
                                                          );
                                                      apiLoading = true;
                                                    } else {
                                                      filter = true;
                                                      glossaryList = [];
                                                      context
                                                          .read<GlossaryBloc>()
                                                          .add(
                                                            GlossaryDoneButtonEvent(
                                                              page: 1,
                                                              selectedDomains:
                                                                  selectedDomains,
                                                              selectedSubDomains:
                                                                  selectedSubDomains,
                                                              selectedAlphabet:
                                                                  e,
                                                              isAscending:
                                                                  isAscending,
                                                              term:
                                                                  searchController
                                                                      .text,
                                                            ),
                                                          );
                                                      apiLoading = true;
                                                    }
                                                  }
                                                },
                                                child: Container(
                                                  height: 30,
                                                  width: 30,
                                                  alignment: Alignment.center,
                                                  decoration: BoxDecoration(
                                                    shape: BoxShape.circle,
                                                    color: selectedAlphabet == e
                                                        ? AppColors.blueLight
                                                        : Colors.transparent,
                                                  ),
                                                  child: Text(
                                                    e,
                                                    style: AppTextStyles
                                                        .s12w4cblueGreyShade1
                                                        .copyWith(
                                                      color: selectedAlphabet ==
                                                              e
                                                          ? AppColors.white
                                                          : (isSelectable
                                                              ? isLightMode
                                                                  ? AppColors
                                                                      .blueGreyShade1
                                                                  : AppColors
                                                                      .white
                                                              : isLightMode
                                                                  ? AppColors
                                                                      .greyShade1
                                                                  : AppColors
                                                                      .blackShade8),
                                                    ),
                                                    textScaler:
                                                        TextScaler.linear(
                                                      textScaleFactor.value,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            );
                                          }).toList() ??
                                          [],
                                    ),
                                  ),
                                ),
                                if (state is GlossaryLoadingState && filter)
                                  const Padding(
                                    padding: EdgeInsets.all(8),
                                    child: CircularProgressIndicator(),
                                  ),
                                if ((glossaryData?.results ?? []).isEmpty)
                                  const Expanded(
                                    child: Center(
                                      child: NoDataPlaceholder(),
                                    ),
                                  )
                                else
                                  Expanded(
                                    child: buildList(),
                                  ),
                              ],
                            ),
                          ),
                        ),

                        if (state is GlossaryLoadingState && !filter)
                          const Padding(
                            padding: EdgeInsets.all(8),
                            child: CircularProgressIndicator(),
                          ),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _loadData() {
    if (scrollController.position.pixels >=
        scrollController.position.maxScrollExtent) {
      if (apiLoading) {
        return;
      }
      if (glossaryList.length >= (glossaryData?.totalCount ?? 0)) {
        return;
      }

      // context
      //     .read<GlossaryBloc>()
      //     .add(GlossaryLoadDataEvent(page: (glossaryData?.page ?? 0) + 1));

      context.read<GlossaryBloc>().add(
            GlossaryDoneButtonEvent(
              page: (glossaryData?.page ?? 0) + 1,
              selectedDomains: selectedDomains,
              selectedSubDomains: selectedSubDomains,
              selectedAlphabet: selectedAlphabet,
              isAscending: isAscending,
              term: '',
            ),
          );
      apiLoading = true;
    }
  }

  Widget buildList() {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return ListView.separated(
      shrinkWrap: true,
      itemCount: glossaryList.length,
      controller: scrollController,
      padding: const EdgeInsets.symmetric(
        horizontal: 16,
        vertical: 16,
      ),
      itemBuilder: (context, index) {
        return Container(
          decoration: BoxDecoration(
            color: !isLightMode ? AppColors.blueShade36 : null,
            borderRadius: BorderRadius.circular(15),
            border: Border.all(
              color: isLightMode ? AppColors.greyShade1 : Colors.transparent,
            ),
          ),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      HiveUtilsSettings.getAppLanguage() == 'ar'
                          ? glossaryList[index].titleAr ?? ''
                          : glossaryList[index].titleEn ?? '',
                      style: AppTextStyles.s16w5cBlackShade1.copyWith(
                        color: !isLightMode ? Colors.white : null,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                      textScaler: TextScaler.linear(textScaleFactor.value),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 10),
              ExpandableNotifier(
                child: ScrollOnExpand(
                  scrollOnCollapse: false,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ExpandablePanel(
                        theme: const ExpandableThemeData(
                          headerAlignment:
                              ExpandablePanelHeaderAlignment.center,
                          tapBodyToCollapse: true,
                          hasIcon: false,
                        ),
                        header: const SizedBox(),
                        collapsed: Row(
                          children: [
                            Expanded(
                              child: Text(
                                HiveUtilsSettings.getAppLanguage() == 'ar'
                                    ? glossaryList[index].descriptionAr ?? ''
                                    : glossaryList[index].descriptionEn ?? '',
                                softWrap: true,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                style: AppTextStyles.s14w4cblackShade4.copyWith(
                                  color: isLightMode
                                      ? AppColors.grey
                                      : AppColors.greyShade4,
                                ),
                                textScaler: TextScaler.linear(textScaleFactor.value),
                              ),
                            ),
                          ],
                        ),
                        expanded: Text(
                          HiveUtilsSettings.getAppLanguage() == 'ar'
                              ? glossaryList[index].descriptionAr ?? ''
                              : glossaryList[index].descriptionEn ?? '',
                          maxLines: 100,
                          style: AppTextStyles.s14w4cblackShade4.copyWith(
                            color: isLightMode
                                ? AppColors.grey
                                : AppColors.greyShade4,
                          ),
                          overflow: TextOverflow.ellipsis,
                          textScaler: TextScaler.linear(textScaleFactor.value),
                        ),
                      ),
                      const SizedBox(height: 6),
                      if (getLineLength(
                            context,
                            HiveUtilsSettings.getAppLanguage() == 'ar'
                                ? glossaryList[index].descriptionAr ?? ''
                                : glossaryList[index].descriptionEn ?? '',
                            HiveUtilsSettings.getAppLanguage() == 'ar',
                          ) >
                          2)
                        Builder(
                          builder: (context) {
                            final controller = ExpandableController.of(
                              context,
                              required: true,
                            )!;
                            return InkWell(
                              onTap: controller.toggle,
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    controller.expanded
                                        ? LocaleKeys.showLess.tr()
                                        : LocaleKeys.showMore.tr(),
                                    style: AppTextStyles.s14w4cBlue.copyWith(
                                        color: !isLightMode
                                            ? AppColors.blueLightOld
                                            : null),
                                    overflow: TextOverflow.ellipsis,
                                    textScaler: TextScaler.linear(
                                      textScaleFactor.value,
                                    ),
                                  ),
                                  Icon(
                                    controller.expanded
                                        ? Icons.keyboard_arrow_up_rounded
                                        : Icons.keyboard_arrow_down_rounded,
                                    size: 18,
                                    color: isLightMode
                                        ? AppColors.blueLight
                                        : AppColors.blueLightOld,
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
      separatorBuilder: (context, index) => const SizedBox(height: 14),
    );
  }

  int getLineLength(BuildContext context, String text, bool isArabic) {
    final span = TextSpan(text: text, style: AppTextStyles.s14w4cblackShade4);
    final tp = TextPainter(
      text: span,
      textScaler: TextScaler.linear(textScaleFactor.value),
      textDirection: isArabic ? TextDirection.rtl : TextDirection.ltr,
    )..layout(maxWidth: MediaQuery.of(context).size.width - 76);
    final numLines = tp.computeLineMetrics().length;
    return numLines;
  }

  bool _isFilterApplied() {
    return (selectedDomains ?? []).any((e) => e.isSelected) ||
        (selectedSubDomains ?? []).any((e) => e.isSelected) ||
        (selectedAlphabet ?? '').isNotEmpty ||
        searchController.text.isNotEmpty ||
        (glossaryFilter?.domains ?? []).any((element) => element.isSelected);
  }
}
