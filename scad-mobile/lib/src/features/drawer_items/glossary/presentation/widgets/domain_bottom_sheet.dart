import 'dart:convert';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/check_box_text_row.dart';
import 'package:scad_mobile/src/common/widgets/bottom_sheet_top_notch.dart';
import 'package:scad_mobile/src/features/drawer_items/glossary/data/models/glossary_filter/glossary_filter_domain_model.dart';
import 'package:scad_mobile/src/features/drawer_items/glossary/data/models/glossary_filter/glossary_filter_sub_domain_model.dart';
import 'package:scad_mobile/src/features/drawer_items/glossary/presentation/bloc/glossary_bloc.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class DomainBottomSheet extends StatefulWidget {
  const DomainBottomSheet(
      {required this.domainList, required this.oldSubDomainItems, super.key});

  final List<GlossaryFilterDomainModel> domainList;

  final List<GlossaryFilterSubDomainModel> oldSubDomainItems;

  @override
  State<DomainBottomSheet> createState() => _DomainBottomSheetState();

  List<int> initialSelection() {
    final List<int> list = [];
    for (int i = 0; i < domainList.length; i++) {
      if (domainList[i].isSelected) {
        list.add(i);
      }
    }
    return list;
  }
}

class _DomainBottomSheetState extends State<DomainBottomSheet> {
  // List<GlossaryFilterDomainModel>? selectedDomains;
  // List<GlossaryFilterSubDomainModel>? selectedSubDomains;
  // List<GlossaryFilterSubDomainModel>? subDomainItems;
  // String? selectedSubDomain;
  // String? selectedSubDomainName = '';
  // String? selectedAlphabet = '';
  // bool isAscending = true;

  List<int> selectedIndexList = [];

  bool get isSelectionChanged => !listEquals(
      (widget.initialSelection()..sort()), (selectedIndexList..sort()));

  List<GlossaryFilterDomainModel> domainList = [];

  @override
  void initState() {
    super.initState();

    domainList = List.generate(
        widget.domainList.length, (index) => widget.domainList[index]);

     for (int i = 0; i < domainList.length; i++) {
      if (domainList[i].isSelected) {
        selectedIndexList.add(i);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;

    // return BlocBuilder<GlossaryBloc, GlossaryState>(
    //   builder: (context, state) {
    //     if (state is GlossaryFilterDomainDropdownState) {
    //       selectedDomains = state.domainItems;
    //       subDomainItems = state.subDomainItems;
    //       selectedSubDomain = state.selectedSubDomains;
    //       selectedSubDomainName = state.selectedDomains;
    //       selectedSubDomains = state.subDomainItems;
    //
    //     // } else if (state is GlossaryFilterSubDomainDropdownState) {
    //     //   selectedSubDomains = state.subDomainList;
    //     //   selectedSubDomain = state.selectedValue;
    //     } else if (state is GlossaryFilterDoneButtonState) {
    //       selectedDomains = state.selectedDomains;
    //       selectedSubDomains = state.selectedSubDomains;
    //       selectedAlphabet = state.selectedAlphabet;
    //       isAscending = state.isAscending ?? true;
    //     }
        return Container(
          width: double.infinity,
          padding: const EdgeInsets.fromLTRB(
            24,
            0,
            24,
            24,
          ),
          decoration: BoxDecoration(
            color: isLightMode ? AppColors.white : AppColors.blueShade32,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 10),
              const BottomSheetTopNotch(),
              const SizedBox(height: 16),
              Flexible(
                child: ListView.builder(
                  shrinkWrap: true,
                  padding: EdgeInsets.zero,
                  itemCount: domainList.length ?? 0,
                  itemBuilder: (context, index) {
                    final domain = domainList[index];
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 15),
                      child: CheckBoxTextRow(
                        title: domain.name ?? '',
                        titleColor: isLightMode
                            ? AppColors.blackTextTile
                            : AppColors.white,
                        isSelected: selectedIndexList.contains(index),
                        //domain.isSelected,
                        onChanged: () {
                          if (selectedIndexList.contains(index)) {
                            selectedIndexList.remove(index);
                          } else {
                            selectedIndexList.add(index);
                          }
                          setState(() {});
                          // context.read<GlossaryBloc>().add(
                          //       GlossaryFilterDomainDropdownEvent(
                          //         index: index,
                          //         domainItems: domainList,
                          //       ),
                          //     );
                        },
                      ),
                    );
                  },
                ),
              ),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  minimumSize: const Size.fromHeight(43),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(
                      10,
                    ),
                  ),
                ),
                onPressed: !isSelectionChanged
                    ? null
                    : () {
                        Navigator.pop(context);

                        final List<GlossaryFilterDomainModel> selectedDomains = [];

                        for(int i =0;i<domainList.length;i++) {
                          if(selectedIndexList.contains(i)){
                            domainList[i].isSelected = true;
                          }else{
                            domainList[i].isSelected = false;
                          }
                          selectedDomains.add(domainList[i]);
                        }


                        final List<GlossaryFilterSubDomainModel> selectedSubDomains = [];// widget.oldSubDomainItems;

                        // for(int i = 0; i<selectedSubDomains.length; i++) {
                          // if(selected.map((e) => e.name))
                        // }



                        // for (final GlossaryFilterSubDomainModel element in selectedSubDomains ?? []) {
                        //   for (final GlossaryFilterSubDomainModel old in widget.oldSubDomainItems) {
                        //     if (element.name == old.name && old.isSelected) {
                        //       element.isSelected = true;
                        //     }
                        //   }
                        // }



                        // void _setSubDomainDropdownList(){
                        //   if (selectedDomains.isNotEmpty) {

                            for(int i =0;i<selectedDomains.length;i++){
                              if(selectedDomains[i].isSelected) {
                                // for(int j =0;j<selectedSubDomains.length;j++) {
                                //   if(!selectedSubDomains.any((e)=> e.name == (selectedDomains[i].items??[])[j].name)){
                                //     selectedSubDomains.removeWhere((element) => selectedSubDomains[j].name == element.name);
                                //   }
                                // }
                                selectedSubDomains.addAll(selectedDomains[i].items??[]);
                              }
                              // else {
                              //   selectedSubDomains.removeWhere((element) =>
                              //       selectedDomains[i].items!.map((e)=>e.name).toList().contains(element.name));
                              // }
                            }

                            for(int i =0;i<widget.oldSubDomainItems.length;i++) {
                              int index = selectedSubDomains.indexWhere((element) => element.name == widget.oldSubDomainItems[i].name);
                              if(index>=0){
                                selectedSubDomains[index].isSelected = widget.oldSubDomainItems[i].isSelected;
                              }
                            }

    // selectedDomains[event.index].isSelected = !selectedDomains[event.index].isSelected;
                            // Update subDomainList for all selected domains
                            // for (final element in selectedDomains!) {
                            //   if (element.isSelected) {
                            //     subDomainList.addAll(element.items ?? []);
                            //     selectedDomains += '${element.name ?? ''}, ';
                            //
                            //     // Update selectedSubDomains for the selected domain
                            //     selectedSubDomains += (element.items ?? [])
                            //         .where((subDomain) => subDomain.isSelected)
                            //         .map((subDomain) => '${subDomain.name ?? ''}, ')
                            //         .join();
                            //   } else {
                            //     // Unselecting the domain, clear its associated subdomains
                            //     for (final subDomain in element.items ?? []) {
                            //       subDomain.isSelected = false;
                            //     }
                            //   }
                            // }
                          // } else {
                          //   selectedSubDomains.clear();
                          // }
                        // }


                        // context.read<GlossaryBloc>().add(
                        //   GlossaryFilterDomainDropdownValueUpdateEvent(
                        //     domainItems: selected,
                        //     selectedDomains: '',
                        //     subDomainItems: [],
                        //     selectedSubDomains: '',
                        //   ),
                        // );
                        context.read<GlossaryBloc>().add(
                              GlossaryDoneButtonEvent(
                                page: 1,
                                selectedDomains: selectedDomains,
                                selectedSubDomains: selectedSubDomains,
                                selectedAlphabet: '',
                                isAscending: true,
                                term: '',
                              ),
                            );
                      },
                child: Text(
                  LocaleKeys.done.tr(),
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                  textScaler: TextScaler.linear(textScaleFactor.value),
                ),
              ),
            ],
          ),
        );
    //   },
    // );
  }
}
