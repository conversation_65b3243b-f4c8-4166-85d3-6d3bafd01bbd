part of 'glossary_repository_imports.dart';

abstract class GlossaryRepository {
  /// function for getting glossary list
  Future<RepoResponse<GlossaryResponseModel>> getGlossarylist({
    required int page,
    List<String>? domainList,
    List<String>? subDomainList,
    List<String>? alphabetList,
    bool? isAscending = true,
    String? term,
  });

  ///
  Future<RepoResponse<GlossaryFilterResponseModel>> glossaryFilter();
}
