import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

import '../../../../../utils/hive_utils/hive_utils_settings.dart';

class FeedbackField extends StatelessWidget {
  const FeedbackField({
    required this.feedbackController,
    this.hintText,
    super.key,
    this.showError = true,
  });

  final TextEditingController feedbackController;
  final String? hintText;
  final bool showError;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Column(
      children: [
        TextFormField(
          controller: feedbackController,
          minLines: 5,
          keyboardType: TextInputType.multiline,
          maxLines: null,
          style:
          AppTextStyles.s14w4cblackShade4.copyWith(
            color: isLightMode ? AppColors.blackShade4 :AppColors.white ,
            fontSize: 16 * textScaleFactor.value,
          )  ,
          decoration: InputDecoration(
            fillColor:isLightMode ?  AppColors.white :  AppColors.blueShade36,
            filled: true,
            focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(
                color: showError ? AppColors.red : AppColors.greyShade1,
                width: 1
              ),
              borderRadius: BorderRadius.circular(10),
            ),
            enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(
                color: showError ? AppColors.red : AppColors.greyShade1,
                width: 1,
              ),
              borderRadius: BorderRadius.circular(10),
            ),
            errorMaxLines: 2,
            hintText: hintText ?? LocaleKeys.enterYourFeedback.tr(),
            hintStyle: AppTextStyles.s14w4cblackShade4
                .copyWith(color: AppColors.greyShade4,
              fontSize: 16 * textScaleFactor.value,
            ),
          ),
        ),
        const SizedBox(height: 6),
        if (showError)
          Text(
            LocaleKeys.feedbackFieldError.tr(),
            style: AppTextStyles.s14w4cBlue.copyWith(color: AppColors.red),
            textScaler: TextScaler.linear(textScaleFactor.value),
            maxLines: 2,
          ),
        const SizedBox(height: 8),
      ],
    );
  }
}
