import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/common/widgets/appbar/common_app_bar.dart';
import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';
import 'package:scad_mobile/src/config/firebase_config/firebase_config.dart';
import 'package:scad_mobile/src/features/drawer_items/feedback/data/models/feedback_model/request/feedback_request.dart';
import 'package:scad_mobile/src/features/drawer_items/feedback/data/models/feedback_model/response/feedback_content/feedback_content_result_response.dart';
import 'package:scad_mobile/src/features/drawer_items/feedback/data/models/feedback_model/response/user_feedback/user_feedback_result_response.dart';
import 'package:scad_mobile/src/features/drawer_items/feedback/presentation/bloc/feedback_bloc.dart';
import 'package:scad_mobile/src/features/drawer_items/feedback/presentation/widgets/feedback_field.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

@RoutePage()
class FeedbackScreen extends StatefulWidget {
  const FeedbackScreen({super.key});

  @override
  State<FeedbackScreen> createState() => _FeedbackScreenState();
}

class _FeedbackScreenState extends State<FeedbackScreen> {
  FeedbackContentResultResponseModel? feedbackContent;
  UserFeedbackResultResponseModel? userFeedback;
  ValueNotifier<double> selectedRating = ValueNotifier(0);
  ValueNotifier<bool> feedbackError = ValueNotifier(false);
  TextEditingController feedbackController = TextEditingController();
  // final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    FirebaseConfig.setScreenToAnalytics('Feedback');
    context.read<FeedbackBloc>().add(const CombinedFeedbackLoadEvent());
  }

  @override
  Widget build(BuildContext context) {
    final rtl = DeviceType.isDirectionRTL(context);
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return AppDrawer(
      child: Scaffold(
        body: GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              CommonAppBar(title: LocaleKeys.feedback.tr()),
              Expanded(
                child: BlocConsumer<FeedbackBloc, FeedbackState>(
                  listener: (context, state) {
                    if (state is FeedbackSuccessState) {
                      context.replaceRoute(const FeedbackSuccessScreenRoute());
                    }
                  },
                  builder: (context, state) {
                    if (state is FeedbackLoadingState) {
                      return const Padding(
                        padding: EdgeInsets.only(top: 50),
                        child: Center(child: CircularProgressIndicator()),
                      );
                    } else if (state is CombinedFeedbackSuccessState) {
                      feedbackContent = state.contentResponse.results?.first;
          
                      // userFeedback = state.userFeedbacks.results?.first;
                      // feedbackController.text = userFeedback?.feedback ?? '';
                      // selectedRating.value = userFeedback?.rating ?? 0;
                      return Padding(
                        padding: const EdgeInsets.all(24),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            Text(
                              rtl
                                  ? feedbackContent?.textContentAr ?? ''
                                  : feedbackContent?.textContent ?? '',
                              style: AppTextStyles.s14w4cblackShade4.copyWith(
                                color: !isLightMode ? AppColors.white : null,
                              ),
                              textScaler:
                                  TextScaler.linear(textScaleFactor.value),
                            ),
                            const SizedBox(height: 14),
                            Expanded(
                              child: ValueListenableBuilder(
                                valueListenable: selectedRating,
                                builder: (context, rating, _) {
                                  return Column(
                                    crossAxisAlignment: CrossAxisAlignment.stretch,
                                    children: [
                                      Expanded(
                                        child: ListView(
                                          shrinkWrap: true,
                                          padding: EdgeInsets.zero,
                                          children: [
                                            RatingBar(
                                              initialRating: userFeedback?.rating ?? 0,
                                              itemSize: 24,
                                              ratingWidget: RatingWidget(
                                                full: SvgPicture.asset(
                                                    AppImages.icFullStar),
                                                half: const SizedBox(),
                                                empty: SvgPicture.asset(
                                                    AppImages.icEmptyStar),
                                              ),
                                              itemPadding:
                                              const EdgeInsets.only(right: 20),
                                              onRatingUpdate: (value) {
                                                selectedRating.value = value;
                                              },
                                            ),
                                            const SizedBox(height: 24),
                                            ValueListenableBuilder(
                                              valueListenable: feedbackError,
                                              builder: (context, isError, _) {
                                                return FeedbackField(
                                                  showError: isError,
                                                  feedbackController: feedbackController,
                                                  hintText: LocaleKeys.writeFeedback.tr(),
                                                );
                                              },
                                            ),
                                          ],
                                        ),
                                      ),
                                      ElevatedButton(
                                        onPressed: _submitFeedback,
                                        child: Text(
                                          LocaleKeys.submit.tr(),
                                          style: TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.w500,
                                            color: AppColors.white,
                                          ),
                                          textScaler: TextScaler.linear(textScaleFactor.value),
                                        ),
                                      ),
                                    ],
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      );
                    } else if (state is FeedbackFailureState) {
                      return Center(
                        child: Text(
                          state.error,
                          textScaler: TextScaler.linear(textScaleFactor.value),
                        ),
                      );
                    } else if (state is FeedbackSuccessState) {
                      return const SizedBox();
                    } else {
                      return Center(
                        child: Text(
                          LocaleKeys.errorLoadingData.tr(),
                          textScaler: TextScaler.linear(textScaleFactor.value),
                        ),
                      );
                    }
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _submitFeedback() {
    if (selectedRating.value <= 3) {
      feedbackError.value = true;
      if (feedbackController.text.isNotEmpty) {
        context.read<FeedbackBloc>().add(
              FeedbackSubmitEvent(
                feedbackRequest: FeedbackRequestModel(
                  rating: selectedRating.value,
                  feedback: feedbackController.text,
                ),
              ),
            );
      }
    } else {
      feedbackError.value = false;
      context.read<FeedbackBloc>().add(
            FeedbackSubmitEvent(
              feedbackRequest: FeedbackRequestModel(
                rating: selectedRating.value,
                feedback: feedbackController.text,
              ),
            ),
          );
    }
  }
}
