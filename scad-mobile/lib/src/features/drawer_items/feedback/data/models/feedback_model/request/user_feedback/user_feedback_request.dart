import 'package:json_annotation/json_annotation.dart';

part 'user_feedback_request.g.dart';

@JsonSerializable()
class UserFeedbackRequestModel {
  UserFeedbackRequestModel({
    required this.offset,
    required this.limit,
  });

  factory UserFeedbackRequestModel.fromJson(Map<String, dynamic> json) =>
      _$UserFeedbackRequestModelFromJson(json);
  final int offset;
  final int limit;

  Map<String, dynamic> toJson() => _$UserFeedbackRequestModelToJson(this);
}
