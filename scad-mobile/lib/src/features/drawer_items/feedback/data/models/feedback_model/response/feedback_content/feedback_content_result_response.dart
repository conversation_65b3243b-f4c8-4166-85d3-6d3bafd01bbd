import 'package:json_annotation/json_annotation.dart';

part 'feedback_content_result_response.g.dart';

@JsonSerializable()
class FeedbackContentResultResponseModel {
  FeedbackContentResultResponseModel({
    this.uuid,
    this.textContent,
    this.textContentAr,
    this.placeholderText,
    this.active,
  });

  factory FeedbackContentResultResponseModel.fromJson(
    Map<String, dynamic> json,
  ) =>
      _$FeedbackContentResultResponseModelFromJson(json);
  final String? uuid;
  @JsonKey(name: 'text_content')
  final String? textContent;
  @Json<PERSON>ey(name: 'text_content_ar')
  final String? textContentAr;
  @J<PERSON><PERSON>ey(name: 'placeholder_text')
  final String? placeholderText;
  final bool? active;

  Map<String, dynamic> toJson() =>
      _$FeedbackContentResultResponseModelToJson(this);
}
