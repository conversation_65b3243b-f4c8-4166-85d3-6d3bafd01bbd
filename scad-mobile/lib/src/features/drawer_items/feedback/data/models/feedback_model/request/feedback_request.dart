import 'package:json_annotation/json_annotation.dart';

part 'feedback_request.g.dart';

@JsonSerializable()
class FeedbackRequestModel {
  FeedbackRequestModel({
    required this.rating,
    this.feedback,
  });

  factory FeedbackRequestModel.fromJson(Map<String, dynamic> json) =>
      _$FeedbackRequestModelFromJson(json);
  final double rating;
  final String? feedback;

  Map<String, dynamic> toJson() => _$FeedbackRequestModelToJson(this);
}
