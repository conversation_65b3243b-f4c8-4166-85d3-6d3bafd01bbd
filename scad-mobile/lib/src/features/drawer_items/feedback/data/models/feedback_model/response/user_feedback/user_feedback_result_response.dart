import 'package:json_annotation/json_annotation.dart';

part 'user_feedback_result_response.g.dart';

@JsonSerializable()
class UserFeedbackResultResponseModel {
  UserFeedbackResultResponseModel({
    this.uuid,
    this.rating,
    this.feedback,
    this.active,
  });

  factory UserFeedbackResultResponseModel.fromJson(
    Map<String, dynamic> json,
  ) =>
      _$UserFeedbackResultResponseModelFromJson(json);
  final String? uuid;
  final double? rating;
  final String? feedback;
  final bool? active;

  Map<String, dynamic> toJson() =>
      _$UserFeedbackResultResponseModelToJson(this);
}
