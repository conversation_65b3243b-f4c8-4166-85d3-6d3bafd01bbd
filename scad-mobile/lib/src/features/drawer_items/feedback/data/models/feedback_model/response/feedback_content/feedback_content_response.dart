import 'package:json_annotation/json_annotation.dart';
import 'package:scad_mobile/src/features/drawer_items/feedback/data/models/feedback_model/response/feedback_content/feedback_content_result_response.dart';

part 'feedback_content_response.g.dart';

@JsonSerializable()
class FeedbackContentResponseModel {
  FeedbackContentResponseModel({
    this.count,
    this.results,
  });

  factory FeedbackContentResponseModel.fromJson(Map<String, dynamic> json) =>
      _$FeedbackContentResponseModelFromJson(json);
  final int? count;
  final List<FeedbackContentResultResponseModel>? results;

  Map<String, dynamic> toJson() => _$FeedbackContentResponseModelToJson(this);
}
