import 'package:json_annotation/json_annotation.dart';

part 'feedback_response.g.dart';

@JsonSerializable()
class FeedbackResponseModel {
  FeedbackResponseModel({
    this.status,
    this.message,
  });

  factory FeedbackResponseModel.fromJson(Map<String, dynamic> json) =>
      _$FeedbackResponseModelFromJson(json);
  final String? status;
  final String? message;

  Map<String, dynamic> toJson() => _$FeedbackResponseModelToJson(this);
}
