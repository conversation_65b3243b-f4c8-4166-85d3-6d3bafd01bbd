import 'package:json_annotation/json_annotation.dart';
import 'package:scad_mobile/src/features/drawer_items/feedback/data/models/feedback_model/response/user_feedback/user_feedback_result_response.dart';

part 'user_feedback_response.g.dart';

@JsonSerializable()
class UserFeedbackResponseModel {
  UserFeedbackResponseModel({
    this.count,
    this.results,
  });

  factory UserFeedbackResponseModel.fromJson(Map<String, dynamic> json) =>
      _$UserFeedbackResponseModelFromJson(json);
  final int? count;
  final List<UserFeedbackResultResponseModel>? results;

  Map<String, dynamic> toJson() => _$UserFeedbackResponseModelToJson(this);
}
