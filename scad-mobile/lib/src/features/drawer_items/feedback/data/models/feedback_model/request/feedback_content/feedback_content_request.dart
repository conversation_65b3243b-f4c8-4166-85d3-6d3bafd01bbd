import 'package:json_annotation/json_annotation.dart';

part 'feedback_content_request.g.dart';

@JsonSerializable()
class FeedbackContentRequestModel {
  FeedbackContentRequestModel({
    required this.offset,
    required this.limit,
  });

  factory FeedbackContentRequestModel.fromJson(Map<String, dynamic> json) =>
      _$FeedbackContentRequestModelFromJson(json);
  final int offset;
  final int limit;

  Map<String, dynamic> toJson() => _$FeedbackContentRequestModelToJson(this);
}
