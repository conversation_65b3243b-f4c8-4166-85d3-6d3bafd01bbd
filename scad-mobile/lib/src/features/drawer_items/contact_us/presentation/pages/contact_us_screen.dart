import 'dart:async';
import 'dart:convert';

import 'package:auto_route/auto_route.dart';
import 'package:dio/dio.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/common/models/response_models/common_api_response/common_api_response.dart';
import 'package:scad_mobile/src/common/widgets/appbar/common_app_bar.dart';
import 'package:scad_mobile/src/common/widgets/contact_card.dart';
import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';
import 'package:scad_mobile/src/config/app_config/api_config.dart';
import 'package:scad_mobile/src/config/firebase_config/firebase_config.dart';
import 'package:scad_mobile/src/features/drawer_items/contact_us/data/models/highlight_model.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_auth.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';
import 'package:url_launcher/url_launcher.dart';

@RoutePage()
class ContactUsScreen extends StatefulWidget {
  const ContactUsScreen({super.key});

  @override
  State<ContactUsScreen> createState() => _ContactUsScreenState();
}

class _ContactUsScreenState extends State<ContactUsScreen>
    with TickerProviderStateMixin {
  late AnimationController _controller;

  List<HighlightModel> suggestionsToHighlight = [];
  ValueNotifier<String> selectedSuggestion = ValueNotifier('');

  final TextEditingController explanationController = TextEditingController();

  ValueNotifier<String> attachmentPath = ValueNotifier('');
  PlatformFile? pickedFile;

  bool isLoadingHighlight = true;
  bool isLoadingDomains = true;
  bool isSubmitting = false;
  bool showLinearprogressIndicator = true;

  @override
  void initState() {
    FirebaseConfig.setScreenToAnalytics('Contact Us');
    _controller = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    _controller.forward();
    if (isDemoMode) {
      isLoadingDomains = isLoadingHighlight = false;
    }else{
      fetchApis();
    }
    super.initState();
  }

  Future<void> fetchApis() async {
    await getHighlightList();
  }

  /// fetch higlight list
  Future<void> getHighlightList() async {
    try {
      final String apiToken = HiveUtilsAuth.getToken();
      HighlightResponseModel? data;

      final Response<List<dynamic>> response = await Dio().get(
        '${ApiConfig.appApiPath}/contactus/highlight/list/',
        options: Options(
          headers: {
            'Content-type': 'application/json',
            'Accept-Language': HiveUtilsSettings.getAppLanguage(),
            if (apiToken.isNotEmpty) 'Authorization': 'Bearer $apiToken',
          },
        ),
      );
      if (response.statusCode == 200 ||
          response.statusCode == 201 && response.data != null) {
        data = HighlightResponseModel.fromJson({'data': response.data});

        setState(() {
          suggestionsToHighlight = data?.highlightList ?? [];
          isLoadingHighlight = false;
        });
      } else {
        setState(() {
          isLoadingHighlight = false;
        });
        if (mounted) {
          AppMessage.showOverlayNotification(
            '',
            LocaleKeys.somethingWentWrong.tr(),
            msgType: 'error',
          );
        }
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      setState(() {
        isLoadingHighlight = false;
      });
      if (mounted) {
        AppMessage.showOverlayNotification(
          '',
           LocaleKeys.somethingWentWrong.tr(),
          msgType: 'error',
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final rtl = DeviceType.isDirectionRTL(context);
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return AppDrawer(
      child: Scaffold(
        body: isLoadingHighlight && isLoadingDomains
            ? const Center(
                child: CircularProgressIndicator(),
              )
            : GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
              child: Column(
                children: [
                  CommonAppBar(title: LocaleKeys.contactSCAD.tr()),
                  Expanded(
                    child: ListView(
                      padding: EdgeInsets.zero,
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(24),
                          child: Row(
                            children: [
                              Expanded(
                                child: ContactCard(
                                  iconPath: isLightMode?
                                   AppImages.icCall :'assets/images/ic_call_dark.svg',
                                  contact: '+971 2 123 45 67',
                                  onTap: () async {
                                    await launchToCall(
                                      'tel',
                                      '+971 2 123 45 67',
                                    );
                                  },
                                ),
                              ),
                              const SizedBox(width: 24),
                              Expanded(
                                child: ContactCard(
                                    iconPath: isLightMode
                                        ? AppImages.icMail
                                        : 'assets/images/ic_mail_dark.svg',
                                  contact: '<EMAIL>',
                                  onTap: () async {
                                    await launchToMail(
                                      'mailto',
                                      '<EMAIL>',
                                    );
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(20),
                              topRight: Radius.circular(20),
                            ),
                            color: isLightMode
                                ? AppColors.greyShade7
                                : AppColors.blueShade32,
                          ),
                          padding: const EdgeInsets.fromLTRB(24, 24, 24, 10),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              Text(
                                LocaleKeys.whatINeedToHighlight.tr(),
                                style: AppTextStyles.s16w5cBlackShade1.copyWith(
                                  fontSize: 18,
                                  color: !isLightMode ? AppColors.white : null,
                                ),
                                textScaler:
                                    TextScaler.linear(textScaleFactor.value),
                              ),
                              const SizedBox(height: 24),
                              ...List.generate(suggestionsToHighlight.length,
                                  (index) {
                                return ValueListenableBuilder(
                                  valueListenable: selectedSuggestion,
                                  builder: (context, value, _) {
                                    return GestureDetector(
                                      onTap: () {
                                        selectedSuggestion.value =
                                            suggestionsToHighlight[index]
                                                    .text ??
                                                '';
                                        FocusManager.instance.primaryFocus?.unfocus();
                                      },
                                      child: Container(
                                        decoration: BoxDecoration(
                                          color: value ==
                                                  suggestionsToHighlight[index]
                                                      .text
                                              ? isLightMode ?  AppColors.blueLight
                                                  .withOpacity(0.1) : AppColors.blueLightOld.withOpacity(0.3)
                                              : isLightMode
                                                  ? AppColors.white
                                                  : AppColors.blueShade36,
                                          borderRadius:
                                              BorderRadius.circular(10),
                                          border: Border.all(
                                            color: value ==
                                                    suggestionsToHighlight[
                                                            index]
                                                        .text
                                                ? AppColors.blueLight
                                                : isLightMode
                                                    ? AppColors.greyShade1
                                                    : Colors.transparent,
                                          ),
                                        ),
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 10,
                                          vertical: 12,
                                        ),
                                        margin:
                                            const EdgeInsets.only(bottom: 14),
                                        child: Row(
                                          children: [
                                            Container(
                                              height: 18,
                                              width: 18,
                                              alignment: Alignment.center,
                                              decoration: BoxDecoration(
                                                shape: BoxShape.circle,
                                                border: Border.all(
                                                  color: value ==
                                                          suggestionsToHighlight[
                                                                  index]
                                                              .text
                                                      ? isLightMode ?  AppColors.blueLight :   AppColors.blueLightOld
                                                      : AppColors.greyShade1,
                                                ),
                                              ),
                                              child: Icon(
                                                Icons.circle,
                                                size: 12,
                                                color: value ==
                                                        suggestionsToHighlight[
                                                                index]
                                                            .text
                                                    ?  isLightMode ?  AppColors.blueLight :   AppColors.blueLightOld
                                                    : Colors.transparent,
                                              ),
                                            ),
                                            const SizedBox(width: 10),
                                            Expanded(
                                              child: Text(
                                                rtl
                                                    ? suggestionsToHighlight[
                                                                index]
                                                            .textAr ??
                                                        ''
                                                    : suggestionsToHighlight[
                                                                index]
                                                            .text ??
                                                        '',
                                                style: AppTextStyles.s14w4cBlue
                                                    .copyWith(
                                                  color: isLightMode
                                                      ? AppColors.grey
                                                      : AppColors.white,
                                                ),
                                                textScaler: TextScaler.linear(
                                                  textScaleFactor.value,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    );
                                  },
                                );
                              }),
                              const SizedBox(height: 20),
                              TextFormField(

                                controller: explanationController,
                                minLines: 5,
                                keyboardType: TextInputType.multiline,
                                maxLines: null,
                                textCapitalization:
                                    TextCapitalization.sentences,
                                style: AppTextStyles.s14w4cblackShade4.copyWith(
                                  color: isLightMode ? AppColors.blackShade4 :AppColors.white ,
                                  fontSize: 16 * textScaleFactor.value,
                                ),
                                decoration: InputDecoration(
                                  fillColor:isLightMode ? AppColors.white :AppColors.blueShade36 ,
                                  filled: true,
                                  focusedBorder: OutlineInputBorder(
                                    borderSide:
                                        BorderSide(color: AppColors.greyShade1),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderSide:
                                        BorderSide(color: AppColors.greyShade1),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  hintText: LocaleKeys.pleaseExplain.tr(),
                                  hintStyle: AppTextStyles.s14w4cblackShade4
                                      .copyWith(color: AppColors.greyShade4,
                                    fontSize: 16 * textScaleFactor.value,),
                                ),
                              ),
                              const SizedBox(height: 20),
                              Text(
                                LocaleKeys.attachments.tr(),
                                style: AppTextStyles.s14w4cblackShade4
                                    .copyWith(color: AppColors.greyShade4),
                                textScaler:
                                    TextScaler.linear(textScaleFactor.value),
                              ),
                              const SizedBox(height: 12),
                              if (attachmentPath.value.isNotEmpty)
                                Container(
                                  width: MediaQuery.of(context).size.width,
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 14,
                                    vertical: 8,
                                  ),
                                  decoration: BoxDecoration(
                                    color: isLightMode
                                        ? AppColors.greyShade8
                                        : AppColors.blackShade8,
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Row(
                                    children: [
                                      SvgPicture.asset(
                                        'assets/images/image-outline.svg',
                                        colorFilter: ColorFilter.mode(
                                          isLightMode
                                              ? AppColors.greyShade4
                                              : AppColors.white,
                                          BlendMode.srcIn,
                                        ),
                                      ),
                                      const SizedBox(width: 20),
                                      Expanded(
                                        child: Column(
                                          mainAxisSize: MainAxisSize.min,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              pickedFile?.path
                                                      ?.split('/')
                                                      .last ??
                                                  '',
    textScaler: TextScaler.linear(textScaleFactor.value),
                                              style: TextStyle(
                                                color: isLightMode
                                                    ? AppColors.blueGreyShade1
                                                    : AppColors.white,
                                                fontSize: 14,
                                                fontWeight: FontWeight.w400,
                                              ),
                                            ),
                                            if (showLinearprogressIndicator) ...[
                                              const SizedBox(height: 5),
                                              SizedBox(
                                                width:
                                                    MediaQuery.sizeOf(context)
                                                            .width /
                                                        3,
                                                height: 3,
                                                child: TweenAnimationBuilder(
                                                  tween: Tween<double>(
                                                    begin: 0,
                                                    end: 1,
                                                  ),
                                                  duration: const Duration(
                                                    seconds: 1,
                                                  ),
                                                  onEnd: () {
                                                    setState(() {
                                                      showLinearprogressIndicator =
                                                          false;
                                                    });
                                                  },
                                                  builder: (
                                                    BuildContext context,
                                                    double value,
                                                    Widget? child,
                                                  ) {
                                                    return LinearProgressIndicator(
                                                      backgroundColor:
                                                          AppColors.greyShade9,
                                                      color: Color(
                                                        isLightMode
                                                            ? 0xFF697D92
                                                            : 0xFF6A7180,
                                                      ),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                        40,
                                                      ),
                                                      value: value,
                                                      valueColor:
                                                          AlwaysStoppedAnimation<
                                                              Color>(
                                                        Color(
                                                          isLightMode
                                                              ? 0xFF697D92
                                                              : 0xFFFFFFFF,
                                                        ),
                                                      ),
                                                    );
                                                  },
                                                ),
                                              ),
                                            ],
                                          ],
                                        ),
                                      ),
                                      InkWell(
                                        onTap: () {
                                          setState(() {
                                            pickedFile = null;
                                            attachmentPath.value = '';
                                            showLinearprogressIndicator = true;
                                          });
                                        },
                                        child: SvgPicture.asset(
                                          'assets/images/trash-outline.svg',
                                          colorFilter: ColorFilter.mode(
                                            isLightMode
                                                ? AppColors.greyShade4
                                                : AppColors.white,
                                            BlendMode.srcIn,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              else
                                DottedBorder(
                                  borderType: BorderType.RRect,
                                  color: isLightMode
                                      ? AppColors.blueLight
                                      : AppColors.greyShade4,
                                  radius: const Radius.circular(12),
                                  child: ClipRRect(
                                    borderRadius: const BorderRadius.all(
                                      Radius.circular(12),
                                    ),
                                    child: InkWell(
                                      onTap: () async {
                                        pickedFile = await pickSingleFile();
                                        if (pickedFile != null) {
                                          final String? path = pickedFile?.path;
                                          attachmentPath.value = path!;
                                        }
                                      },
                                      child: Container(
                                        height: 130,
                                        width: MediaQuery.sizeOf(context).width,
                                        color: isLightMode
                                            ? AppColors.greyShade3
                                            : const Color(0x19E9F3FF),
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            SvgPicture.asset(
                                              isLightMode?
                                              AppImages.icFolder
                                                    : 'assets/images/ic_folder-dark.svg',
                                            ),
                                            const SizedBox(height: 14),
                                            Text(
                                              LocaleKeys.filePickerPlaceholder
                                                  .tr(),
                                              style: AppTextStyles
                                                  .s14w4cblackShade4
                                                  .copyWith(
                                                color: isLightMode
                                                    ? AppColors.grey
                                                    : AppColors.white,
                                              ),
                                              overflow: TextOverflow.ellipsis,
                                              maxLines: 1,
                                              textScaler: TextScaler.linear(
                                                textScaleFactor.value,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              const SizedBox(height: 30),
                              TextButton(
                                style: TextButton.styleFrom(
                                    backgroundColor: isLightMode
                                        ? AppColors.blueLight
                                        : AppColors.blueLightOld,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                ),
                                onPressed: () {
                                  if (!isSubmitting) {
                                    if (selectedSuggestion.value == '') {
                                      AppMessage.showOverlayNotification(
                                        '',
                                        LocaleKeys.selectSuggestion.tr(),
                                        msgType: 'error',
                                      );
                                    } else if (explanationController.text
                                        .trim()
                                        .isEmpty) {
                                      AppMessage.showOverlayNotification(
                                        '',
                                        LocaleKeys.enterExplanation.tr(),
                                        msgType: 'error',
                                      );
                                    } else {
                                      onSubmit();
                                    }
                                  }
                                },
                                child: isSubmitting
                                    ? SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(
                                          color: AppColors.white,
                                        ),
                                      )
                                    : Text(
                                        LocaleKeys.submit.tr(),
                                        textScaler: TextScaler.linear(
                                          textScaleFactor.value,
                                        ),
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w500,
                                          color: AppColors.white,
                                        ),
                                      ),
                              ),
                              const SizedBox(height: 30),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
        ),
      ),
    );
  }

  Future<PlatformFile?> pickSingleFile() async {
    try {
      final FilePickerResult? result = await FilePicker.platform.pickFiles(
        allowedExtensions: ['jpeg', 'png', 'jpg'],
        type: FileType.custom,
      );
      if (result != null) {
        setState(() {
          pickedFile = result.files.first;
        });
        if ((pickedFile?.size ?? 0) > 100 * 1024) {
          AppMessage.showOverlayNotification(
            '',
            LocaleKeys.selectFileSize.tr(),
            msgType: 'error',
          );

          return null;
        }
        return pickedFile;
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      if (kDebugMode) {
   
      }
    }
    return null;
  }

  Future<void> onSubmit() async {
    setState(() {
      isSubmitting = true;
    });
    try {
      if(isDemoMode) {
        context.replaceRoute(const ContactUsSuccessPageRoute());
      }

      final String apiToken = HiveUtilsAuth.getToken();
      CommonApiResponse? data;

      final jsonPayload = pickedFile == null
          ? {
              'data': json.encode(
                {
                  'highlight': selectedSuggestion.value,
                  'explanation': explanationController.text,
                },
              ),
            }
          : {
              'data': json.encode(
                {
                  'highlight': selectedSuggestion.value,
                  'explanation': explanationController.text,
                },
              ),
              'attachment': await MultipartFile.fromFile(
                pickedFile?.path ?? '',
                filename: pickedFile?.path?.split('/').last,
              ),
            };

      final Response<Map<String, dynamic>> response = await Dio().post(
        '${ApiConfig.appApiPath}/mycontact-reason/create/',
        data: FormData.fromMap(jsonPayload),
        options: Options(
          headers: {
            'Content-type': 'multipart/form-data',
            'Accept-Language': 'en',
            if (apiToken.isNotEmpty) 'Authorization': 'Bearer $apiToken',
          },
        ),
      );

      if (response.statusCode == 200 ||
          response.statusCode == 201 && response.data != null) {
        data = CommonApiResponse.fromJson(response.data!);

        if (mounted) {
          await context.replaceRoute(const ContactUsSuccessPageRoute());
        }

        if(mounted){
          setState(() {
            selectedSuggestion.value = '';
            explanationController.clear();
            pickedFile = null;
            attachmentPath.value = '';
            isSubmitting = false;
            showLinearprogressIndicator = true;
          });
        }
      } else {
        setState(() {
          isSubmitting = false;
        });
        if (mounted) {
          AppMessage.showOverlayNotification(
            '',
            LocaleKeys.somethingWentWrong.tr(),
            msgType: 'error',
          );
        }
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      setState(() {
        isSubmitting = false;
      });
      if (mounted) {
        AppMessage.showOverlayNotification(
          '',
           LocaleKeys.somethingWentWrong.tr(),
          msgType: 'error',
        );
      }
    }
  }

  Future<void> launchToCall(String scheme, String path) async {
    try {
      final Uri launchUri = Uri(
        scheme: scheme,
        path: path,
      );
      await canLaunchUrl(launchUri).then((bool result) async {
        if (result) {
          await launchUrl(launchUri);
        }
      });
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      AppMessage.showOverlayNotification(
        '',
        LocaleKeys.somethingWentWrong.tr(),
        msgType: 'error',
      );
    }
  }

  Future<void> launchToMail(String scheme, String path) async {
    try {
      final Uri emailLaunchUri = Uri(
        scheme: scheme,
        path: path,
      );
      await launchUrl(emailLaunchUri);
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      AppMessage.showOverlayNotification(
        '',
         LocaleKeys.somethingWentWrong.tr(),
        msgType: 'error',
      );
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
