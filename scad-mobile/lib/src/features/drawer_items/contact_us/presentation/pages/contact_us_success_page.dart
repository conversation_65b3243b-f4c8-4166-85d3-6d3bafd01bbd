import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/animation_asset.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

@RoutePage()
class ContactUsSuccessPage extends StatelessWidget {
  const ContactUsSuccessPage({super.key});

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Scaffold(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  if (isLightMode)
                    Lottie.asset(
                      AnimationAsset.animationSubmitSuccess,
                      height: 140,
                      width: 140,
                    )
                  else
                    Lottie.asset(
                      AnimationAssetDark.animationSubmitSuccess,
                      height: 140,
                      width: 140,
                    ),
                  const SizedBox(height: 50),
                  Text(
                    LocaleKeys.contactUsSuccess.tr(),
                    style: AppTextStyles.s24w5cBlackShade1.copyWith(
                      color: !isLightMode ? AppColors.white : null,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    textScaler: TextScaler.linear(textScaleFactor.value),
                  ),
                ],
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(24),
            child: TextButton(
              style: TextButton.styleFrom(
                backgroundColor:
                    isLightMode ? AppColors.blueLight : AppColors.blueLightOld,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              onPressed: () {
                AutoRouter.of(context).pop();
              },
              child: Text(
                LocaleKeys.back.tr(),
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: AppColors.white,
                ),
                textScaler: TextScaler.linear(textScaleFactor.value),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
