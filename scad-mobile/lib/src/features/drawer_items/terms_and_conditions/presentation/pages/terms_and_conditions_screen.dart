import 'package:auto_route/annotations.dart';
import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/widgets/appbar/common_app_bar.dart';
import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';
import 'package:scad_mobile/src/common/widgets/no_data_placeholder.dart';
import 'package:scad_mobile/src/config/firebase_config/firebase_config.dart';
import 'package:scad_mobile/src/features/drawer_items/terms_and_conditions/presentation/bloc/t_and_c_bloc.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/constants/webstyle/webstye.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

@RoutePage()
class TermsAndConditionsScreen extends StatefulWidget {
  const TermsAndConditionsScreen({super.key});

  @override
  State<TermsAndConditionsScreen> createState() =>
      _TermsAndConditionsScreenState();
}

class _TermsAndConditionsScreenState extends State<TermsAndConditionsScreen> {
  PageController pageController = PageController();

  ValueNotifier<int> currentPageIndex = ValueNotifier(0);

  @override
  void initState() {
    FirebaseConfig.setScreenToAnalytics('Terms and Conditions');
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final rtl = DeviceType.isDirectionRTL(context);
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return AppDrawer(
      child: Scaffold(
        body: Stack(
          alignment: Alignment.bottomCenter,
          children: [
            Column(
              children: [
                CommonAppBar(title: LocaleKeys.termsAndConditions.tr()),
                BlocConsumer<TAndCBloc, TAndCState>(
                  listener: (context, state) {
                    if (state is TAndCFailureState) {
                      AppMessage.showOverlayNotification(
              '',
              state.error,
              msgType: 'error',
            );
                    }
                  },
                  builder: (context, state) {
                    if (state is TAndCLoadingState) {
                      return const Center(
                        child: Padding(
                          padding: EdgeInsets.symmetric(vertical: 100),
                          child: CircularProgressIndicator(),
                        ),
                      );
                    } else if (state is TAndCSuccessState) {
                      return Expanded(
                        child: state.tAndCList.isEmpty
                            ? const NoDataPlaceholder()
                            : Column(
                          children: [
                            Container(
                              height: 45,
                              margin: const EdgeInsets.only(
                                top: 24,
                                left: 24,
                                right: 24,
                              ),
                              decoration: BoxDecoration(
                                color: isLightMode
                                    ? AppColors.white
                                    : AppColors.blueShade32,
                                borderRadius: BorderRadius.circular(10),
                                boxShadow: [
                                  BoxShadow(
                                    offset: const Offset(0, 4),
                                    blurRadius: 10,
                                    color: AppColors.black.withOpacity(0.06),
                                  ),
                                ],
                              ),
                              child: ValueListenableBuilder(
                                valueListenable: currentPageIndex,
                                builder: (context, value, child) {
                                  return Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      IconButton(
                                        onPressed: () {
                                          if (value != 0) {
                                            pageController.previousPage(
                                              duration: const Duration(
                                                milliseconds: 200,
                                              ),
                                              curve: Curves.ease,
                                            );
                                          }
                                        },
                                        icon: Icon(
                                          Icons.chevron_left_rounded,
                                          size: 20,
                                          color: value != 0
                                              ? AppColors.blue
                                              : AppColors.greyShade4,
                                        ),
                                      ),
                                      Expanded(
                                        child: Text(
                                          rtl
                                              ? state
                                                      .tAndCList[
                                                          currentPageIndex
                                                              .value]
                                                      .titleAr ??
                                                  ''
                                              : state
                                                      .tAndCList[
                                                          currentPageIndex
                                                              .value]
                                                      .title ??
                                                  '',
                                          style: AppTextStyles.s18w5cBlackShade1
                                              .copyWith(
                                            color: !isLightMode
                                                ? AppColors.white
                                                : null,
                                          ),
                                          maxLines: 1,
                                          textAlign: TextAlign.center,
                                          overflow: TextOverflow.ellipsis,
                                          textScaler: TextScaler.linear(
                                            textScaleFactor.value,
                                          ),
                                        ),
                                      ),
                                      IconButton(
                                        onPressed: () {
                                          if (value + 1 <
                                              state.tAndCList.length) {
                                            pageController.nextPage(
                                              duration: const Duration(
                                                milliseconds: 200,
                                              ),
                                              curve: Curves.ease,
                                            );
                                          }
                                        },
                                        icon: Icon(
                                          Icons.chevron_right_rounded,
                                          size: 20,
                                          color:
                                              value + 1 < state.tAndCList.length
                                                  ? AppColors.blue
                                                  : AppColors.greyShade4,
                                        ),
                                      ),
                                    ],
                                  );
                                },
                              ),
                            ),
                            Expanded(
                              child: Container(
                                height: 400,
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 24),
                                child: PageView(
                                  onPageChanged: (index) {
                                    currentPageIndex.value = index;
                                  },
                                  physics: const NeverScrollableScrollPhysics(),
                                  controller: pageController,
                                  children: [
                                    for (int i = 0;
                                        i < state.tAndCList.length;
                                        i++)
                                      buildTermsAndConditions(
                                        rtl
                                            ? state.tAndCList[i]
                                                    .textContentAr ??
                                                ''
                                            : state.tAndCList[i].textContent ??
                                                '',
                                        isLightMode,
                                      ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    } else if (state is TAndCFailureState) {
                      return Center(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 100),
                          child: Text(
                            state.error,
                            textScaler:
                                TextScaler.linear(textScaleFactor.value),
                          ),
                        ),
                      );
                    } else {
                      return Center(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 100),
                          child: Text(
                            LocaleKeys.errorLoadingData.tr(),
                            textScaler:
                                TextScaler.linear(textScaleFactor.value),
                          ),
                        ),
                      );
                    }
                  },
                ),
              ],
            ),
            // Visibility(
            //   visible: !HiveUtilsSettings.getTermsAndConditionStatus(),
            //   child: ValueListenableBuilder(
            //     valueListenable: isChecked,
            //     builder: (context, accepted, _) {
            //       return Container(
            //         height: bottomSize,
            //         padding: const EdgeInsets.symmetric(horizontal: 24),
            //         decoration: BoxDecoration(
            //           color: AppColors.scaffoldBackgroundLight,
            //           border:
            //               Border(top: BorderSide(color: AppColors.greyShade1)),
            //         ),
            //         child: Column(
            //           crossAxisAlignment: CrossAxisAlignment.stretch,
            //           children: [
            //             Row(
            //               children: [
            //                 SizedBox(
            //                   width: 20,
            //                   child: Checkbox(
            //                     checkColor: AppColors.white,
            //                     activeColor: AppColors.blueLight,
            //                     side: BorderSide(color: AppColors.greyShade1),
            //                     value: accepted,
            //                     onChanged: (value) {
            //                       isChecked.value = value!;
            //                     },
            //                   ),
            //                 ),
            //                 const SizedBox(width: 14),
            //                 Text(
            //                   'I accept the terms and conditions',
            //                   style: AppTextStyles.s14w4cBlue
            //                       .copyWith(color: AppColors.grey),
            //                   textScaleFactor: textScaleFactor.value,
            //                 ),
            //               ],
            //             ),
            //             TextButton(
            //               style: TextButton.styleFrom(
            //                 backgroundColor: accepted
            //                     ? AppColors.blueLight
            //                     : AppColors.blueLight.withOpacity(0.6),
            //                 shape: RoundedRectangleBorder(
            //                   borderRadius: BorderRadius.circular(10),
            //                 ),
            //               ),
            //               onPressed: accepted
            //                   ? () async {
            //                       await HiveUtilsSettings
            //                           .saveTermsAndConditionStatus(
            //                         value: accepted,
            //                       );
            //                       await AutoRouter.of(context).pop();
            //                     }
            //                   : null,
            //               child: Text(
            //                 'Accept',
            //                 textScaleFactor: textScaleFactor.value,
            //                 style: TextStyle(
            //                   fontSize: 16,
            //                   fontWeight: FontWeight.w500,
            //                   color: AppColors.white,
            //                 ),
            //               ),
            //             ),
            //             const SizedBox(height: 10),
            //             TextButton(
            //               style: TextButton.styleFrom(
            //                 backgroundColor: AppColors.blueLight.withOpacity(0.1),
            //                 shape: RoundedRectangleBorder(
            //                   borderRadius: BorderRadius.circular(10),
            //                 ),
            //               ),
            //               onPressed: () async {
            //                 await HiveUtilsSettings.saveTermsAndConditionStatus(
            //                   value: false,
            //                 );
            //                 await AutoRouter.of(context).pop();
            //               },
            //               child: Text(
            //                 'Decline',
            //                 textScaleFactor: textScaleFactor.value,
            //                 style: TextStyle(
            //                   fontSize: 16,
            //                   fontWeight: FontWeight.w500,
            //                   color: AppColors.blueLight,
            //                 ),
            //               ),
            //             ),
            //             const SizedBox(height: 30),
            //           ],
            //         ),
            //       );
            //     },
            //   ),
            // ),
          ],
        ),
      ),
    );
  }

  SingleChildScrollView buildTermsAndConditions(
    String content,
    bool isLightMode,
  ) {

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            HtmlWidget(
              WebStyle.webSyle+  content  ,
              textStyle: AppTextStyles.s14w4cblackShade4.copyWith(
                color: isLightMode ? AppColors.grey : AppColors.greyShade4,
              ),
            ),
            // Visibility(
            //   visible: !HiveUtilsSettings.getTermsAndConditionStatus(),
            //   child: SizedBox(height: bottomSize),
            // ),
          ],
        ),
      ),
    );
  }
}
