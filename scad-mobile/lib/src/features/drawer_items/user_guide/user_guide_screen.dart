import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/common/widgets/appbar/common_app_bar.dart';
import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';
import 'package:scad_mobile/src/common/widgets/sliding_tab_filter.dart';
import 'package:scad_mobile/src/config/firebase_config/firebase_config.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

@RoutePage()
class UserGuideScreen extends StatefulWidget {
  const UserGuideScreen({super.key});

  @override
  State<UserGuideScreen> createState() => _UserGuideScreenState();
}

class _UserGuideScreenState extends State<UserGuideScreen> {
  List<SlidingTabItem> menuList = [
    SlidingTabItem(name: LocaleKeys.home.tr(), icon: AppImages.icHomeThin),
    SlidingTabItem(
      name: LocaleKeys.domains.tr(),
      icon: AppImages.icDomainsThin,
    ),
    SlidingTabItem(
      name: LocaleKeys.myPanel.tr(),
      icon: AppImages.icMyAppsThin,
    ),
    SlidingTabItem(
      name: LocaleKeys.products.tr(),
      icon: AppImages.icProductsThin,
    ),
    SlidingTabItem(
      name: LocaleKeys.askUs.tr(),
      icon: AppImages.icContactUsThin,
    ),
  ];

  @override
  void initState() {
    FirebaseConfig.setScreenToAnalytics('User Guide');
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return AppDrawer(
      child: Scaffold(
        body: Column(
          children: [
            CommonAppBar(title: LocaleKeys.userGuide.tr()),
            Expanded(
              child: GridView.builder(
                itemCount: menuList.length,
                shrinkWrap: true,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
                itemBuilder: (context, index) => Container(
                  decoration: BoxDecoration(
                    color: isLightMode
                        ? AppColors.white
                        : AppColors.blueShade32,
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      Column(
                        children: [
                          Container(
                            height: 56,
                            width: 56,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(40),
                              color: isLightMode
                                  ? AppColors.blueLight.withOpacity(.1)
                                  : AppColors.blueShade36,
                            ),
                            padding: const EdgeInsets.all(10),
                            child: SvgPicture.asset(
                              menuList[index].icon,
                              height: 28,
                              colorFilter: ColorFilter.mode(
                                isLightMode
                                    ? AppColors.blueLight
                                    : AppColors.white,
                                BlendMode.srcIn,
                              ),
                            ),
                          ),
                          const SizedBox(height: 14),
                          Text(
                            menuList[index].name,
                            style: AppTextStyles.s14w5cBlack.copyWith(
                              color: isLightMode
                                  ? AppColors.blueGreyShade1
                                  : AppColors.white,
                            ),
                            textScaler:
                                TextScaler.linear(textScaleFactor.value),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 30,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: TextButton(
                            onPressed: () async {
                              /// HOME
                              if (index == 0) {
                                await HiveUtilsSettings.saveUserGuideStatus(
                                  value: UserGuides.Home,
                                );

                                /// Domains
                              } else if (index == 1) {
                                await HiveUtilsSettings.saveUserGuideStatus(
                                  value: UserGuides.Domains,
                                );

                                /// My Dashboards
                              } else if (index == 2) {
                                await HiveUtilsSettings.saveUserGuideStatus(
                                  value: UserGuides.MyDashboards,
                                );

                                /// Products
                              } else if (index == 3) {
                                await HiveUtilsSettings.saveUserGuideStatus(
                                  value: UserGuides.Products,
                                );

                                /// Ask Us
                              } else if (index == 4) {
                                await HiveUtilsSettings.saveUserGuideStatus(
                                  value: UserGuides.AskUs,
                                );
                              }
                              AutoRouter.of(context).removeWhere((route) => route.name == HomeNavigationRoute.name);
                              await AutoRouter.of(context).push(
                                HomeNavigationRoute(screenTabIndex: index),
                              );
                            },
                            style: TextButton.styleFrom(
                              backgroundColor: isLightMode
                                  ? AppColors.blueLight
                                  : AppColors.blueLightOld,
                              padding: DeviceType.isDirectionRTL(context) ?
                               const EdgeInsets.only(left: 6, right: 18,)
                              :const EdgeInsets.only(left: 18, right: 6,) ,
                              shape:   RoundedRectangleBorder(
                                side: isLightMode
                                    ? BorderSide.none
                                    : BorderSide(
                                        color: AppColors.blueShade22,
                                        width: 1,
                                      ),
                                      borderRadius: BorderRadius.circular(10),
                              ),),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Expanded(
                                  child: Text(
                                    LocaleKeys.takeATour.tr(),
                                    style: AppTextStyles.s14w4cBlue
                                        .copyWith(color: AppColors.white),
                                    overflow: TextOverflow.ellipsis,
                                    textScaler:
                                        TextScaler.linear(textScaleFactor.value),
                                  ),
                                ),
                                Icon(
                                  Icons.chevron_right_rounded,
                                  size: 18,
                                  color: AppColors.white,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 24,
                  mainAxisSpacing: 24,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
