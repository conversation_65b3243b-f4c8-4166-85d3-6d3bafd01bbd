import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/widgets/card_custom_clipper.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_response_helper.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_status_response.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/presentation/bloc/indicator_card_bloc.dart';
import 'package:scad_mobile/src/features/my_apps/presentation/bloc/myapps_bloc.dart';
import 'package:scad_mobile/src/features/my_apps/presentation/pages/add_to_myapps.dart';
import 'package:scad_mobile/src/features/notification/data/models/request/subscription_request.dart';
import 'package:scad_mobile/src/features/notification/presentation/bloc/notification_bloc.dart';
import 'package:scad_mobile/src/features/search/data/models/search_ifp_response.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_api_cache.dart';

class SearchIndicatorItem extends StatelessWidget {
  const SearchIndicatorItem({
    required this.searchItemData,
    required this.index,
    super.key,
    this.indicatorDetails,
  });

  final Items searchItemData;
  final IndicatorDetailsResponseHelper? indicatorDetails;
  final int index;

  @override
  Widget build(BuildContext context) {
    return _SearchIndicatorItem(
      searchItemData: searchItemData,
      index: index,
      indicatorDetails: indicatorDetails,
      key: key,
    );
  }
}

class _SearchIndicatorItem extends StatefulWidget {
  const _SearchIndicatorItem({
    required this.searchItemData,
    required this.index,
    super.key,
    this.indicatorDetails,
  });

  final Items searchItemData;
  final IndicatorDetailsResponseHelper? indicatorDetails;
  final int index;

  @override
  State<_SearchIndicatorItem> createState() => _SearchIndicatorItemState();
}

class _SearchIndicatorItemState extends State<_SearchIndicatorItem> {
  NodeIdUuid? subscription;
  NodeIdUuid? myApps;

  Map<String, String> classificationKeyMap = {
    'official_statistics': 'Official Statistics',
    'experimental_statistics': 'Experimental Statistics',
    'analytical_apps': 'Analytical Apps',
  };

  @override
  void initState() {
    super.initState();
    context.read<IndicatorCardBloc>().add(
          GetIndicatorStatusEvent(
            id: widget.searchItemData.id!,
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    final rtl = DeviceType.isDirectionRTL(context);
    return Padding(
      padding: const EdgeInsets.only(
        right: 24,
        left: 24,
        top: 8,
        bottom: 8,
      ),
      child: Stack(
        children: [
          Positioned(
            right: rtl ? null : 0,
            left: rtl ? 0 : null,
            top: 0,
            height: 40,
            width: 40,
            child: openDetailsButton(context),
          ),
          ClipPath(
            clipper: CardCustomClipper(isRtl: rtl),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
              ),
              padding: const EdgeInsets.fromLTRB(15, 0, 15, 15),
              child: Container(
                constraints: const BoxConstraints(minHeight: 70),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            margin: const EdgeInsets.only(
                              top: 15,
                            ),
                            child: Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(
                                    8,
                                  ),
                                  decoration: BoxDecoration(
                                    color: AppColors.green,
                                    borderRadius: BorderRadius.circular(100),
                                  ),
                                  child: SvgPicture.network(
                                    HiveUtilsApiCache.getDomainImageByName(
                                      widget.indicatorDetails?.indicatorDetails
                                          .domain,
                                      rtl,
                                    ),
                                    width: 10,
                                    height: 10,
                                    colorFilter: const ColorFilter.mode(
                                      Colors.white,
                                      BlendMode.srcIn,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 10),
                                Expanded(
                                  child: Text(
                                    widget.indicatorDetails?.domainName ?? '',
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                      color: AppColors.grey,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w400,
                                    ),
                                    textScaler: TextScaler.linear(
                                      textScaleFactor.value,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 10),
                              ],
                            ),
                          ),
                        ),
                        BlocConsumer<IndicatorCardBloc, IndicatorCardState>(
                          listener: (context, state) {
                            // if (state is IndicatorStatusLoadingState) {
                            //   subscription = null;
                            //   myApps = null;
                            // } else
                            if (state is IndicatorStatusErrorState) {
                              subscription = NodeIdUuid();
                              myApps = NodeIdUuid();
                            } else if (state is IndicatorStatusSuccessState) {
                              subscription = state.subscription;
                              myApps = state.myApps;
                            }
                          },
                          builder: (context, state) {
                            return subscription == null || myApps == null
                                ? const SizedBox(
                                    height: 16,
                                    width: 16,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 1,
                                    ),
                                  )
                                : actionButtons();
                          },
                        ),
                        const SizedBox(width: 40),
                      ],
                    ),
                    const SizedBox(height: 10),
                    Padding(
                      padding: const EdgeInsets.only(right: 46),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Flexible(
                            child: Text(
                              widget.searchItemData.title ?? '',
                              style: TextStyle(
                                color: AppColors.black,
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              textScaler: TextScaler.linear(
                                textScaleFactor.value,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          if (classificationKeyMap.keys.firstWhere(
                                (k) =>
                                    classificationKeyMap[k] ==
                                    widget.searchItemData.contentClassification,
                                orElse: () => '',
                              ) !=
                              'official_statistics')
                            SvgPicture.asset(
                              AppImages.icOfficialActive,
                            )
                          else
                            SvgPicture.asset(
                              AppImages.icExperimentalActive,
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget actionButtons() {
    if (isDemoMode) {
      return const SizedBox();
    }
    return Padding(
      padding: const EdgeInsets.only(
        top: 6,
      ),
      child: Row(
        children: [
          BlocConsumer<NotificationBloc, NotificationState>(
            key: Key(subscription?.uuid ?? Random().nextInt(1000).toString()),
            listener: (context, state) {
              if (state is SubscriptionSuccessState) {
                if (state.id == widget.searchItemData.id) {
                  // AppMessage.showOverlayNotification(
                  //   widget.searchItemData.title ?? '',
                  //   state.message,
                  // );
                  if (mounted) {
                    context.read<IndicatorCardBloc>().add(
                          GetIndicatorStatusEvent(
                            id: widget.searchItemData.id!,
                          ),
                        );
                  }
                }
              }
            },
            builder: (context, state) {
              return Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(20),
                  onTap: () {
                    if (subscription?.nodeId == null) {
                      context.read<NotificationBloc>().add(
                            CreateSubscriptionEvent(
                              title: widget.searchItemData.title,
                              request: SubscriptionRequest(
                                nodeId: widget.searchItemData.id,
                                appType: widget.searchItemData.type,
                                contentType:
                                    classificationKeyMap.keys.firstWhere(
                                  (k) =>
                                      classificationKeyMap[k] ==
                                      widget
                                          .searchItemData.contentClassification,
                                  orElse: () => '',
                                ),
                              ),
                            ),
                          );
                    } else {
                      context.read<NotificationBloc>().add(
                            RemoveSubscriptionEvent(
                              title: widget.searchItemData.title,
                              uuid: subscription!.uuid!,
                              id: subscription!.nodeId!,
                            ),
                          );
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    child: subscription?.nodeId == widget.searchItemData.id
                        ? SvgPicture.asset(AppImages.icBellSolid)
                        : SvgPicture.asset(
                            AppImages.icBell,
                            colorFilter: ColorFilter.mode(
                              AppColors.black,
                              BlendMode.srcIn,
                            ),
                          ),
                  ),
                ),
              );
            },
          ),
          BlocConsumer<MyAppsBloc, MyAppsState>(
            // key: Key(myApps?.uuid ?? Random().nextInt(1000).toString()),
            key: widget.key,
            listener: (context, state) {
              if (state is MyAppsStatusSuccessResponseState) {
                if (state.id == widget.searchItemData.id) {
                  // AppMessage.showOverlayNotification(
                  //   widget.searchItemData.title ?? '',
                  //   state.message,
                  // );
                  if (mounted) {
                    context.read<IndicatorCardBloc>().add(
                          GetIndicatorStatusEvent(
                            id: widget.searchItemData.id!,
                          ),
                        );
                  }
                }
              }
            },
            builder: (context, state) {
              return Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(20),
                  onTap: () async {
                    if (myApps?.nodeId == null) {
                      // add to my apps
                      await showModalBottomSheet<void>(
                        isScrollControlled: true,
                        useRootNavigator: true,
                        constraints: BoxConstraints(
                          minHeight: 100,
                          maxHeight: MediaQuery.sizeOf(context).height * .90,
                        ),
                        shape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(20),
                            topRight: Radius.circular(20),
                          ),
                        ),
                        backgroundColor: AppColors.white,
                        context: context,
                        builder: (context) {
                          return AddToMyApps(
                            indicatorDetails: widget.indicatorDetails!,
                            nodeId: widget.searchItemData.id!,
                            contentType: classificationKeyMap.keys.firstWhere(
                              (k) =>
                                  classificationKeyMap[k] ==
                                  widget.searchItemData.contentClassification,
                              orElse: () => '',
                            ),
                          );
                        },
                      );
                    } else {
                      // remove from my apps
                      context.read<MyAppsBloc>().add(
                            RemoveFromMyAppsEvent(
                              title: widget.indicatorDetails!.indicatorDetails
                                      .componentTitle ??
                                  '',
                              id: widget.searchItemData.id!,
                              uuid: myApps!.uuid!,
                            ),
                          );
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    child: myApps?.nodeId == widget.searchItemData.id
                        ? SvgPicture.asset(
                            AppImages.icAddToMyAppsOn,
                          )
                        : SvgPicture.asset(
                            AppImages.icAddToMyAppsOff,
                            colorFilter: ColorFilter.mode(
                              AppColors.black,
                              BlendMode.srcIn,
                            ),
                          ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget openDetailsButton(BuildContext context) {
    return Container(
      decoration: ShapeDecoration(
        gradient: LinearGradient(
          begin: const Alignment(-0.65, -0.76),
          end: const Alignment(0.65, 0.76),
          colors: [AppColors.blueShade11, AppColors.blueShade12],
        ),
        shape: const OvalBorder(),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(100),
          onTap: () {
            context.read<IndicatorCardBloc>().add(
                  GetIndicatorDetailsEvent(
                    id: widget.searchItemData.id!,
                    contentType: classificationKeyMap.keys.firstWhere(
                      (k) =>
                          classificationKeyMap[k] ==
                          widget.searchItemData.contentClassification,
                      orElse: () => '',
                    ),

                    //TODO: overviewContentType
                    overviewContentType: '',
                  ),
                );
          },
          child: Padding(
            padding: const EdgeInsets.all(5),
            child: RotatedBox(
              quarterTurns: DeviceType.isDirectionRTL(context) ? -1 : 0,
              child: Icon(
                Icons.arrow_outward_rounded,
                color: AppColors.white,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
