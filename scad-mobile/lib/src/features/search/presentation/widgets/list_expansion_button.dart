import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class ListExpansionButton extends StatelessWidget {
  const ListExpansionButton({
    required this.onTap,
    super.key,
    this.seeMore = false,
    this.showDivider = true,
  });
  final bool seeMore;
  final bool showDivider;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            InkWell(
              onTap: onTap,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    seeMore ? LocaleKeys.seeLess.tr() : LocaleKeys.seeMore.tr(),
                    style: AppTextStyles.s14w4cBlue,
                    textScaler: TextScaler.linear(textScaleFactor.value),
                  ),
                  Icon(
                    seeMore
                        ? Icons.expand_less_rounded
                        : Icons.expand_more_rounded,
                    size: 18,
                    color: AppColors.blue,
                  ),
                ],
              ),
            ),
            const SizedBox(width: 20),
          ],
        ),
        if (showDivider) const Divider(endIndent: 24, indent: 24),
      ],
    );
  }
}
