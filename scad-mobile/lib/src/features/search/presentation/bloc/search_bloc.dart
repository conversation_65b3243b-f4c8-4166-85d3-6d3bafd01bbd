import 'dart:async';
import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/config/dependancy_injection/injection_container.dart';
import 'package:scad_mobile/src/features/products/data/models/authntification_response.dart';
import 'package:scad_mobile/src/features/products/data/models/web_report_response.dart';
import 'package:scad_mobile/src/features/search/data/models/search_dashboard_response_item.dart';
import 'package:scad_mobile/src/features/search/data/models/search_ifp_response.dart';
import 'package:scad_mobile/src/features/search/data/models/search_publications_response.dart';
import 'package:scad_mobile/src/features/search/domain/repositories/search_repository_imports.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_keys.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_api_cache.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

part 'search_event.dart';

part 'search_state.dart';

class SearchBloc extends Bloc<SearchEvent, SearchState> {
  SearchBloc() : super(SearchInitial()) {
    on<SearchInitialEvent>(_onSearchInitialEvent);
    on<OnSearchDashboardEvent>(_onSearchDashboard);
    on<OnSearchIfpEvent>(_onSearchIfp);
    on<OnSearchWebReportsEvent>(_onSearchWebReports);
    on<OnSearchPublicationsEvent>(_onSearchPublications);
    on<OnSearchDashboardToggleEvent>(_onDashboardToggle);
    on<OnSearchIfpToggleEvent>(_onIfpToggle);
    on<OnSearchPublicationsToggleEvent>(_onPublicationsToggle);
    on<OnSearchWebReportsToggleEvent>(_onWebReportsToggle);
    on<AuthenticationsEvent>(_onAuthentication);
  }

  void _onSearchInitialEvent(
    SearchInitialEvent event,
    Emitter<SearchState> emit,
  ) {
    emit(SearchInitial());
  }

  Future<void> _onSearchDashboard(
    OnSearchDashboardEvent event,
    Emitter<SearchState> emit,
  ) async {
    try {
      emit(SearchLoadingBeginState());
      final RepoResponse<List<SearchDashboardResponseItem>> response =
          await servicelocator<SearchRepository>()
              .searchDashboardQuery(query: event.query);

      if (response.isSuccess) {
        emit(SearchDashboardSuccessState(searchResult: response.response!));
      } else {
        emit(
          SearchErrorState(error: response.errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(SearchErrorState(error: LocaleKeys.somethingWentWrong.tr()));
    } finally {
      emit(SearchLoadingEndState());
    }
  }

  Future<void> _onSearchIfp(
    OnSearchIfpEvent event,
    Emitter<SearchState> emit,
  ) async {
    try {
      emit(SearchLoadingBeginState());
      final RepoResponse<SearchIfpResponse> response =
          await servicelocator<SearchRepository>()
              .searchIfpQuery(query: event.query);

      if (response.isSuccess) {
        emit(
          SearchIfpSuccessState(
            searchResult: response.response?.result?.contentTypes ?? [],
          ),
        );
      } else {
        emit(
          SearchErrorState(error: response.errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(SearchErrorState(error: LocaleKeys.somethingWentWrong.tr()));
    } finally {
      emit(SearchLoadingEndState());
    }
  }

  Future<void> _onAuthentication(
    AuthenticationsEvent event,
    Emitter<SearchState> emit,
  ) async {
    try {
      final dynamic res =
          HiveUtilsApiCache.get(HiveKeys.keyAuthWebReportAndPublication);
      if (res != null) {
        emit(
          AuthenticationSetSuccessState(
            data: Authentification.fromJson(res as Map<String, dynamic>),
          ),
        );
      } else {
        // emit(const AuthenticationLoadingState());
        final RepoResponse<Authentification> response =
            await servicelocator<SearchRepository>().authentication();
        if (response.isSuccess) {
          await HiveUtilsApiCache.set(
            HiveKeys.keyAuthWebReportAndPublication,
            response.response?.toJson(),
          );
          emit(AuthenticationSetSuccessState(data: response.response));
          // } else {
          //   emit(AuthenticationErrorState(errorText: response.errorMessage));
        }
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      // emit(AuthenticationErrorState(errorText: LocaleKeys.somethingWentWrong.tr()));
    } finally {
      emit(SearchInitial());
    }
  }

  Future<void> _onSearchPublications(
    OnSearchPublicationsEvent event,
    Emitter<SearchState> emit,
  ) async {
    try {
      emit(SearchLoadingBeginState());
      final RepoResponse<PublicationsResult> response =
          await servicelocator<SearchRepository>().searchPublicationQuery(
        query: event.query,
        pageNo: event.pageNo,
        pageSize: event.pageSize,
        token: event.authToken,
      );

      if (response.isSuccess) {
        emit(SearchPublicationsSuccessState(searchResult: response.response));
      } else {
        emit(
          SearchErrorState(error: response.errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(SearchErrorState(error: LocaleKeys.somethingWentWrong.tr()));
    } finally {
      emit(SearchLoadingEndState());
    }
  }

  Future<void> _onSearchWebReports(
    OnSearchWebReportsEvent event,
    Emitter<SearchState> emit,
  ) async {
    try {
      emit(SearchLoadingBeginState());
      final RepoResponse<Webreports> response =
          await servicelocator<SearchRepository>().searchWebReportQuery(
        query: event.query,
        pageNo: event.pageNo,
        pageSize: event.pageSize,
        token: event.authToken,
      );

      if (response.isSuccess) {
        emit(
          SearchWebReportsSuccessState(searchResult: response.response),
        );
      } else {
        emit(
          SearchErrorState(error: response.errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(SearchErrorState(error: LocaleKeys.somethingWentWrong.tr()));
    } finally {
      emit(SearchLoadingEndState());
    }
  }

  Future<void> _onDashboardToggle(
    OnSearchDashboardToggleEvent event,
    Emitter<SearchState> emit,
  ) async {
    emit(SearchDashboardToggleState(seeMore: event.seeMore));
  }

  Future<void> _onIfpToggle(
    OnSearchIfpToggleEvent event,
    Emitter<SearchState> emit,
  ) async {
    emit(SearchIfpToggleState(seeMoreStatusList: event.seeMoreStatusList));
  }

  Future<void> _onPublicationsToggle(
    OnSearchPublicationsToggleEvent event,
    Emitter<SearchState> emit,
  ) async {
    emit(SearchPublicationsToggleState(seeMore: event.seeMore));
  }

  Future<void> _onWebReportsToggle(
    OnSearchWebReportsToggleEvent event,
    Emitter<SearchState> emit,
  ) async {
    emit(SearchDashboardToggleState(seeMore: event.seeMore));
  }
}
