part of 'search_bloc.dart';

abstract class SearchState extends Equatable {
  const SearchState();

  @override
  List<Object> get props => [];
}

class SearchInitial extends SearchState {
  SearchInitial() {
    rnd = Random().nextInt(10000);
  }

  late final int rnd;

  @override
  List<Object> get props => [rnd];
}

class SearchLoadingBeginState extends SearchState {
  SearchLoadingBeginState() {
    rnd = Random().nextInt(10000);
  }

  late final int rnd;

  @override
  List<Object> get props => [rnd];
}

class SearchLoadingEndState extends SearchState {
  SearchLoadingEndState() {
    rnd = Random().nextInt(10000);
  }

  late final int rnd;

  @override
  List<Object> get props => [rnd];
}

class SearchErrorState extends SearchState {
  const SearchErrorState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}

class SearchDashboardSuccessState extends SearchState {
  const SearchDashboardSuccessState({required this.searchResult});

  final List<SearchDashboardResponseItem> searchResult;

  @override
  List<Object> get props => [searchResult];
}

class SearchDashboardToggleState extends SearchState {
  const SearchDashboardToggleState({required this.seeMore});

  final bool seeMore;

  @override
  List<Object> get props => [seeMore];
}

class SearchIfpSuccessState extends SearchState {
  const SearchIfpSuccessState({required this.searchResult});

  final List<ResultContentTypes> searchResult;

  @override
  List<Object> get props => [searchResult];
}

class SearchIfpToggleState extends SearchState {
  const SearchIfpToggleState({required this.seeMoreStatusList});

  final List<bool> seeMoreStatusList;

  @override
  List<Object> get props => [seeMoreStatusList];
}

class SearchWebReportsSuccessState extends SearchState {
  const SearchWebReportsSuccessState({required this.searchResult});

  final Webreports? searchResult;

  @override
  List<Object> get props => [searchResult!];
}

class SearchWebReportsToggleState extends SearchState {
  const SearchWebReportsToggleState({required this.seeMore});

  final bool seeMore;

  @override
  List<Object> get props => [seeMore];
}

class SearchPublicationsSuccessState extends SearchState {
  const SearchPublicationsSuccessState({required this.searchResult});

  final PublicationsResult? searchResult;

  @override
  List<Object> get props => [searchResult!];
}

class SearchPublicationsToggleState extends SearchState {
  const SearchPublicationsToggleState({required this.seeMore});

  final bool seeMore;

  @override
  List<Object> get props => [seeMore];
}

// authentication
class AuthenticationInitialState extends SearchState {
  const AuthenticationInitialState();

  @override
  List<Object> get props => [];
}

// class AuthenticationLoadingState extends SearchState {
//   const AuthenticationLoadingState();
//
//   @override
//   List<Object> get props => [];
// }

// class AuthenticationErrorState extends SearchState {
//   const AuthenticationErrorState({this.errorText});
//
//   final String? errorText;
//
//   @override
//   List<Object> get props => [errorText ?? ''];
// }

class AuthenticationSetSuccessState extends SearchState {
  const AuthenticationSetSuccessState({this.data});

  final Authentification? data;

  @override
  List<Object> get props => [data ?? ''];
}
