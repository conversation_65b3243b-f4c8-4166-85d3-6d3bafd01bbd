import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:scad_mobile/main.dart' show textScaleFactor;
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class TicketIdChip extends StatelessWidget {
  const TicketIdChip({
    required this.ticketId,
    super.key,
  });

  final String? ticketId;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          LocaleKeys.ticketID.tr(),
          style: TextStyle(
            color: AppColors.greyShade4,
            fontSize: 14,
            fontWeight: FontWeight.w400,
          ),
          textScaler: TextScaler.linear(textScaleFactor.value),
        ),
        const SizedBox(width: 11),
        Container(
          padding: const EdgeInsets.symmetric(
            horizontal: 10,
            vertical: 5,
          ),
          decoration: BoxDecoration(
            color: isLightMode ? AppColors.greyShade8 : AppColors.blueShade32,
            borderRadius: BorderRadius.circular(10),
          ),
          child: Text(
            ticketId ?? '',
            style: TextStyle(
              color: isLightMode ? AppColors.blackTextTile : AppColors.white,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
            textScaler: TextScaler.linear(textScaleFactor.value),
          ),
        ),
      ],
    );
  }
}
