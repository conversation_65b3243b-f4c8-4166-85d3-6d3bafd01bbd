import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/chat_thread/chat_thread_model.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/widgets/landing/chat_thread_list.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class ChatWidget extends StatelessWidget {
  const ChatWidget({
    required this.chatThreadList,
    super.key,
  });

  final List<ChatThreadModel> chatThreadList;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          LocaleKeys.questionThreads.tr(),
          style: TextStyle(
            color: isLightMode ? AppColors.blackShade1 : AppColors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
          textScaler: TextScaler.linear(textScaleFactor.value),
        ),
        const SizedBox(height: 20),
        ...List.generate(chatThreadList.length, (index) {
          final chatData = chatThreadList[index];
          return ChatThreadList(
            chatData: chatData,
          );
        }),
        const SizedBox(height: 5),
      ],
    );
  }
}
