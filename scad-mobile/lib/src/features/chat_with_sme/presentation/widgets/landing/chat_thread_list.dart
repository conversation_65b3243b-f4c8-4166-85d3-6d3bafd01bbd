import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart' as locale;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart' show textScaleFactor;
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/chat_thread/chat_thread_model.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/bloc/chat_with_sme_bloc.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/widgets/ticket_id_chip.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/extentions/string_extentions.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_auth.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class ChatThreadList extends StatelessWidget {
  const ChatThreadList({
    required this.chatData,
    super.key,
  });

  final ChatThreadModel chatData;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Padding(
      padding: const EdgeInsets.only(bottom: 15),
      child: InkWell(
        onTap: () {
          context.read<ChatWithSmeBloc>().add(
                ChatWithSmeLoadInboxEvent(
                  chatThreadId: chatData.uuid ?? '',
                ),
              );
          context
              .pushRoute(
            ChatWithSmeInboxPageRoute(
              title: chatData.subject,
              domain: chatData.domain,
              domainId: int.parse(chatData.domainId ?? '0'),
              theme: chatData.theme,
              subTheme: chatData.subTheme,
              indicatorNodeId: chatData.indicatorNodeId,
              indicatorAppType: chatData.indicatorAppType,
              inicatorContentType: chatData.indicatorContentType,
              indicatorKey: chatData.indicatorKey,
              indicatorName: chatData.indicatorName,
              ticketId: chatData.ticketId,
              chatThreadId: chatData.uuid,
              chatDisabled: chatData.chatDisabled,
              chatThreadClosed: chatData.chatThreadClosed,
            ),
          )
              .whenComplete(() {
            context
                .read<ChatWithSmeBloc>()
                .add(const ChatWithSmeLoadDataEvent());
          });
        },
        borderRadius: BorderRadius.circular(15),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(14),
          decoration: BoxDecoration(
            color:
                isLightMode ? AppColors.greyShade7 : AppColors.blueShade32,
            borderRadius: BorderRadius.circular(15),
            // boxShadow: [
            //   BoxShadow(
            //     color: AppColors.shadow1,
            //     blurRadius: 5,
            //     offset: const Offset(1, 4),
            //   ),
            // ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        SvgPicture.asset(
                          'assets/images/population.svg',
                          colorFilter: ColorFilter.mode(
                            isLightMode
                                ? AppColors.blueLight
                                : AppColors.blueLightOld,
                            BlendMode.srcIn,
                          ),
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          child: Text(
                            chatData.subject ?? '',
                            style: TextStyle(
                              color: isLightMode
                                  ? AppColors.blackTextTile
                                  : AppColors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textScaler:
                                TextScaler.linear(textScaleFactor.value),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 5),
                  Container(
                    width: 10,
                    height: 10,
                    decoration: BoxDecoration(
                      color: (chatData.latestMessage?.isRead == true) ||
                              (chatData.latestMessage == null)
                          ? isLightMode
                              ? AppColors.readMessageDot
                              : AppColors.readMessageDot.withOpacity(0.5)
                          : isLightMode
                              ? AppColors.blueLight
                              : AppColors.blueLightOld,
                      shape: BoxShape.circle,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text.rich(
                textScaler: TextScaler.linear(textScaleFactor.value),
                TextSpan(
                  children: [
                    TextSpan(
                      text: chatData.latestMessage?.sender?.uuid ==
                              HiveUtilsAuth.getUserId()
                          ? '${LocaleKeys.you.tr()}: '
                          : '${LocaleKeys.SME.tr()}: ',
                      style: TextStyle(
                        color: isLightMode
                            ? AppColors.blackChatText
                            : AppColors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    TextSpan(
                      text: chatData.latestMessage == null
                          ? LocaleKeys.askForHelp.tr()
                          : chatData.latestMessage?.message,
                      style: TextStyle(
                        color: AppColors.greyShade4,
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 15),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TicketIdChip(
                    ticketId: chatData.ticketId,
                  ),
                  const SizedBox(width: 10),
                  Visibility(
                    visible: chatData.latestMessage != null,
                    child: Flexible(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Flexible(
                            child: Directionality(
                              textDirection: TextDirection.ltr,
                              child: Text(
                                chatData.latestMessage?.createdTime
                                        ?.formatDateTimeForChat(
                                      showDateOnly: true,
                                    ) ??
                                    '',
                                style: TextStyle(
                                  color: AppColors.greyShade4,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                                textScaler:
                                    TextScaler.linear(textScaleFactor.value),
                              ),
                            ),
                          ),
                          const SizedBox(width: 6),
                          SvgPicture.asset(
                            'assets/images/pending.svg',
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
