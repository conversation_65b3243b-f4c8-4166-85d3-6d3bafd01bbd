// import 'package:scad_mobile/main.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:scad_mobile/src/common/widgets/landing_top_button.dart';
// import 'package:scad_mobile/src/features/chat_with_sme/presentation/bloc/chat_with_sme_bloc.dart';
// import 'package:scad_mobile/src/features/chat_with_sme/presentation/widgets/landing/new_chat_bottom_sheet.dart';
// import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
//
// class StartNewChatWidgetSmall extends StatelessWidget {
//   const StartNewChatWidgetSmall({super.key});
//
//   @override
//   Widget build(BuildContext context) {
//     return InkWell(
//       borderRadius: BorderRadius.circular(10),
//       ,
//       child:
//     );
//   }
// }
