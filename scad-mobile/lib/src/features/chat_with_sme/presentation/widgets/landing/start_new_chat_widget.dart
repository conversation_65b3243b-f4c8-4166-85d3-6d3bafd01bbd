// import 'package:scad_mobile/main.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:scad_mobile/src/features/chat_with_sme/presentation/bloc/chat_with_sme_bloc.dart';
// import 'package:scad_mobile/src/features/chat_with_sme/presentation/widgets/landing/new_chat_bottom_sheet.dart';
// import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
//
// class StartNewChatWidget extends StatelessWidget {
//   const StartNewChatWidget({super.key});
//
//   @override
//   Widget build(BuildContext context) {
//     return InkWell(
//       borderRadius: BorderRadius.circular(20),
//       child: Container(
//         width: double.infinity,
//         height: MediaQuery.sizeOf(context).height / 5.0,
//         decoration: ShapeDecoration(
//           color: AppColors.lightBlueContainer,
//           shape: RoundedRectangleBorder(
//             borderRadius: BorderRadius.circular(20),
//           ),
//         ),
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             SvgPicture.asset(
//               'assets/images/chat_bubble.svg',
//               height: 31,
//               width: 31,
//             ),
//             const SizedBox(height: 10),
//             Text(
//               'Submit a new question',
//               style: TextStyle(
//                 color: AppColors.selectedChipBlue,
//                 fontSize: 16,
//                 fontWeight: FontWeight.w500,
//               ),
//               textScaler: TextScaler.linear(textScaleFactor.value),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
