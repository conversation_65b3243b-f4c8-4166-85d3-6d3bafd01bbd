import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/faq/faq_model.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/bloc/chat_with_sme_bloc.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

class FaqListWidget extends StatelessWidget {
  const FaqListWidget({
    required this.faqList,
    super.key,
  });

  final List<FaqModel> faqList;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isLightMode ? AppColors.white : AppColors.blueShade32,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        children: List.generate(faqList.length, (index) {
          return Column(
            children: [
              Theme(
                data: ThemeData(
                  dividerColor: Colors.transparent,
                  listTileTheme: ListTileTheme.of(context).copyWith(
                    dense: true,
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                child: ExpansionTile(
                  key: Key('faq_tile_$index'),
                  title: Text(
                    (HiveUtilsSettings.getAppLanguage() == 'en'
                        ? (faqList[index].title ?? '')
                        : (faqList[index].titleAr == ''
                            ? (faqList[index].title ?? '')
                            : (faqList[index].titleAr ?? ''))),
                    style: TextStyle(
                      color: isLightMode
                          ? AppColors.blackTextTile
                          : AppColors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    textScaler: TextScaler.linear(textScaleFactor.value),
                  ),
                  tilePadding: EdgeInsets.zero,
                  trailing: faqList[index].isOpened == true
                      ? SvgPicture.asset(
                          'assets/images/${isLightMode ? 'minus' : 'minus_dark'}.svg',
                        )
                      : SvgPicture.asset(
                          'assets/images/${isLightMode ? 'plus' : 'plus_dark'}.svg',
                        ),
                  onExpansionChanged: (value) {
                    context.read<ChatWithSmeBloc>().add(
                          ChatWithSmeToggleExpansionEvent(
                            updatedList: faqList,
                            index: index,
                          ),
                        );
                  },
                  expandedAlignment: HiveUtilsSettings.getAppLanguage() == 'en'
                      ? Alignment.centerLeft
                      : Alignment.centerRight,
                  children: [
                    Text(
                      (HiveUtilsSettings.getAppLanguage() == 'en'
                          ? (faqList[index].description ?? '')
                          : faqList[index].descriptionAr == ''
                              ? (faqList[index].description ?? '')
                              : (faqList[index].descriptionAr ?? '')),
                      style: TextStyle(
                        color: AppColors.greyShade4,
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                      ),
                      textScaler: TextScaler.linear(textScaleFactor.value),
                    ),
                  ],
                ),
              ),
              if (index == faqList.length - 1)
                const SizedBox()
              else
                Divider(
                  color: isLightMode
                      ? AppColors.greyF3F4F6
                      : AppColors.blackShade4,
                  thickness: 1,
                ),
            ],
          );
        }),
      ),
    );
  }
}
