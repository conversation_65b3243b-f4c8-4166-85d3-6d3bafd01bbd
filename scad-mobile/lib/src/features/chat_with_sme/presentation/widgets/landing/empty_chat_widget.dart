import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/animation_asset.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class EmptyChatWidget extends StatelessWidget {
  const EmptyChatWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Column(
      children: [
        if (isLightMode)
          Lottie.asset(AnimationAsset.animationNoConversations)
        else
          Lottie.asset(AnimationAssetDark.animationNoConversations),
        const SizedBox(height: 20),
        Text(
          LocaleKeys.youDontHaveAnyConversationsStartedYet.tr(),
          textAlign: TextAlign.center,
          style: TextStyle(
            color: isLightMode ? AppColors.grey : AppColors.white,
            fontSize: 12,
            fontWeight: FontWeight.w400,
          ),
          textScaler: TextScaler.linear(textScaleFactor.value),
        ),
      ],
    );
  }
}
