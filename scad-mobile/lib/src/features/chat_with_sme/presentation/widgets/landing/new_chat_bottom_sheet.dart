import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/main.dart' show textScaleFactor;
import 'package:scad_mobile/src/common/widgets/bottom_sheet_top_notch.dart';
import 'package:scad_mobile/src/common/widgets/rounded_dropdown_widget.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/domain_theme_sub_theme_model.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/bloc/chat_with_sme_bloc.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class NewChatBottomSheet extends StatefulWidget {
  const NewChatBottomSheet({super.key});

  @override
  State<NewChatBottomSheet> createState() => _NewChatBottomSheetState();
}

class _NewChatBottomSheetState extends State<NewChatBottomSheet> {
  bool? isLoading;
  List<Domain>? domainList;
  Domain? selectedDomain;
  List<Subdomain>? subDomainList;
  Subdomain? selectedSubDomain;
  List<Subtheme>? subThemeList;
  Subtheme? selectedSubTheme;
  String? charLength = '0';
  TextEditingController subjectController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return BlocConsumer<ChatWithSmeBloc, ChatWithSmeState>(
      listener: (context, state) {
        if (state is ChatWithSmeDomainLoadingState) {
          isLoading = true;
        } else if (state is ChatWithSmeDomainDataState) {
          isLoading = false;
          domainList = state.domainList;
        } else if (state is ChatWithSmeDomainDropdownState) {
          selectedDomain = state.selectedValue;
          subDomainList = state.subDomainList;
          subThemeList = null;
          selectedSubDomain = null;
          selectedSubTheme = null;
        } else if (state is ChatWithSmeThemeDropdownState) {
          selectedSubDomain = state.selectedValue;
          subThemeList = state.subThemeList;
          selectedSubTheme = null;
        } else if (state is ChatWithSmeSubThemeDropdownState) {
          selectedSubTheme = state.selectedValue;
        } else if (state is Max60CharacterState) {
          charLength = state.count;
          subjectController.text = state.actualText ?? '';
        }
      },
      builder: (context, state) {
        if (state is ChatWithSmeDomainErrorState) {
          return Center(
            child: Text(
              state.errorText ?? 'Domain List Fetch Error!',
              textScaler: TextScaler.linear(textScaleFactor.value),
            ),
          );
        }
        return Padding(
          padding: MediaQuery.of(context).viewInsets,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
            decoration: BoxDecoration(
              color: isLightMode ? AppColors.white : AppColors.blueShade32,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: isLoading == true
                ? ConstrainedBox(
                    constraints: const BoxConstraints(
                      maxHeight: 200,
                    ),
                    child: const Center(
                      child: CircularProgressIndicator(),
                    ),
                  )
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const SizedBox(height: 10),
                      const BottomSheetTopNotch(),
                      const SizedBox(height: 16),
                      Text(
                        LocaleKeys.selectCategoryYouWantToTalkAbout.tr(),
                        style: TextStyle(
                          color: isLightMode
                              ? AppColors.blackShade1
                              : AppColors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                        textScaler: TextScaler.linear(textScaleFactor.value),
                      ),
                      const SizedBox(height: 20),
                      Row(
                        children: [
                          RoundedDropDownWidget<Domain>(
                            constraintWidth:
                                (MediaQuery.sizeOf(context).width - 64) / 2,
                            width: (MediaQuery.sizeOf(context).width - 64) / 2,
                            title: LocaleKeys.changeDomain.tr(),
                            items: domainList,
                            value: selectedDomain,
                            onChanged: (val) {
                              context.read<ChatWithSmeBloc>().add(
                                    ChatWithSmeDomainDropdownEvent(
                                      domainList: domainList ?? [],
                                      selectedValue: val ?? Domain(),
                                    ),
                                  );
                            },
                          ),
                          const SizedBox(width: 16),
                          RoundedDropDownWidget<Subdomain>(
                            constraintWidth:
                                (MediaQuery.sizeOf(context).width - 64) / 2,
                            width: (MediaQuery.sizeOf(context).width - 64) / 2,
                            title: LocaleKeys.changeTheme.tr(),
                            items: subDomainList,
                            value: selectedSubDomain,
                            onChanged: (val) {
                              context.read<ChatWithSmeBloc>().add(
                                    ChatWithSmeThemeDropdownEvent(
                                      subDomainList: subDomainList ?? [],
                                      selectedValue: val ?? Subdomain(),
                                    ),
                                  );
                            },
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      RoundedDropDownWidget<Subtheme>(
                        width: MediaQuery.sizeOf(context).width,
                        title: LocaleKeys.changeSubTheme.tr(),
                        items: subThemeList,
                        value: selectedSubTheme,
                        onChanged: (val) {
                          context.read<ChatWithSmeBloc>().add(
                                ChatWithSmeSubThemeDropdownEvent(
                                  selectedValue: val!,
                                ),
                              );
                        },
                      ),
                      const SizedBox(height: 20),
                      Text.rich(
                        textScaler: TextScaler.linear(textScaleFactor.value),
                        TextSpan(
                          children: [
                            TextSpan(
                              text: LocaleKeys.addSubjectLine.tr(),
                              style: TextStyle(
                                color: AppColors.greyShade4,
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                            const WidgetSpan(
                              child: SizedBox(width: 5),
                            ),
                            const TextSpan(
                              text: '*',
                              style: TextStyle(
                                color: Color(0xFFBA0202),
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 5),
                      SizedBox(
                        height: 49,
                        child: TextField(
                          controller: subjectController,
                          textCapitalization: TextCapitalization.sentences,
                          maxLength: 60,
                          decoration: InputDecoration(
                            counterText: '',
                            contentPadding: const EdgeInsets.symmetric(
                              vertical: 14,
                              horizontal: 12,
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: AppColors.greyShade1,
                              ),
                            ),
                            disabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: AppColors.greyShade1,
                              ),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: AppColors.greyShade1,
                              ),
                            ),
                            suffix: Text(
                              '$charLength/60',
                              style: TextStyle(
                                color: isLightMode
                                    ? AppColors.black
                                    : AppColors.white,
                              ),
                              textScaler: TextScaler.linear(
                                textScaleFactor.value,
                              ),
                            ),
                            fillColor: isLightMode
                                ? AppColors.white
                                : AppColors.blueShade36,
                            filled: true,
                          ),
                          style: TextStyle(
                            color:
                                isLightMode ? AppColors.black : AppColors.white,
                            fontSize: 16 * textScaleFactor.value,
                          ),
                          onChanged: (val) {
                            context
                                .read<ChatWithSmeBloc>()
                                .add(Max60CharacterEvent(value: val));
                          },
                          onSubmitted: (val) {
                            context
                                .read<ChatWithSmeBloc>()
                                .add(Max60CharacterEvent(value: val));
                          },
                        ),
                      ),
                      const SizedBox(height: 30),
                      ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          minimumSize: const Size.fromHeight(43),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        onPressed: () {
                          if (selectedDomain == null) {
                            AppMessage.showOverlayNotification(
                              '',
                              LocaleKeys.domainRequired.tr(),
                              msgType: 'error',
                            );
                          } else if (selectedSubDomain == null) {
                            AppMessage.showOverlayNotification(
                              '',
                              LocaleKeys.themeRequired.tr(),
                              msgType: 'error',
                            );
                          } else if (selectedSubTheme == null) {
                            AppMessage.showOverlayNotification(
                              '',
                              LocaleKeys.subThemeRequired.tr(),
                              msgType: 'error',
                            );
                          } else if (subjectController.text.trim().isEmpty) {
                            AppMessage.showOverlayNotification(
                              '',
                              LocaleKeys.subjectRequired.tr(),
                              msgType: 'error',
                            );
                          } else {
                            Navigator.pop(context);
                            context.read<ChatWithSmeBloc>().add(
                                  ChatWithSmeCreateChatThreadEvent(
                                    domain: selectedDomain!.name!,
                                    domainId: int.parse(selectedDomain!.id!),
                                    theme: selectedSubDomain?.name ?? '',
                                    subTheme: selectedSubTheme?.name ?? '',
                                    subject: subjectController.text,
                                  ),
                                );
                          }
                        },
                        child: Text(
                          LocaleKeys.submitYourQuery.tr(),
                          style: TextStyle(
                            color: AppColors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                          textScaler: TextScaler.linear(textScaleFactor.value),
                        ),
                      ),
                    ],
                  ),
          ),
        );
      },
    );
  }
}
