import 'package:flutter/material.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/message/message_model.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/widgets/inbox/chat_bubble_and_time_widget.dart';
import 'package:scad_mobile/src/utils/extentions/string_extentions.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_auth.dart';

class InboxList extends StatelessWidget {
  const InboxList({
    required this.messageList,
    super.key,
  });

  final List<MessageModel>? messageList;

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.only(bottom: 25, top: 10),
      itemCount: messageList?.length ?? 0,
      itemBuilder: (BuildContext context, int index) {
        final MessageModel? currentMessage = messageList?[index];
        MessageModel? nextMessage;
        if (index < (messageList?.length ?? 0) - 1) {
          nextMessage = messageList?[index + 1];
        }
        final bool isSender =
            currentMessage?.sender?.uuid == HiveUtilsAuth.getUserId();
        final bool nextMessageISender =
            nextMessage?.sender?.uuid == HiveUtilsAuth.getUserId();

        bool isGrouped = false;

        if (nextMessage != null) {
          isGrouped = (isSender == nextMessageISender) &&
              (currentMessage?.createdTime?.toFormattedDateTimeString(
                    'll:mm a',
                  ) ==
                  nextMessage.createdTime?.toFormattedDateTimeString(
                    'll:mm a',
                  ));
        }
        return ChatBubbleAndTimeWidget(
          message: currentMessage,
          nextMessage: nextMessage,
          isSender: isSender,
          isGrouped: isGrouped,
          isLastMessage: index == (messageList?.length ?? 0) - 1 &&
              messageList?[index].sender != null,
        );
      },
      separatorBuilder: (_, seperatorIndex) {
        final MessageModel? currentMessage = messageList?[seperatorIndex];
        MessageModel? nextMessage;
        if (seperatorIndex < (messageList?.length ?? 0) - 1) {
          nextMessage = messageList?[seperatorIndex + 1];
        }
        final bool isSender =
            currentMessage?.sender?.uuid == HiveUtilsAuth.getUserId();
        final bool nextMessageISender =
            nextMessage?.sender?.uuid == HiveUtilsAuth.getUserId();

        bool isGrouped = false;

        if (nextMessage != null) {
          isGrouped = (isSender == nextMessageISender) &&
              (currentMessage?.createdTime?.toFormattedDateTimeString(
                    'll:mm a',
                  ) ==
                  nextMessage.createdTime?.toFormattedDateTimeString(
                    'll:mm a',
                  ));
        }
        return Visibility(
          visible: !isGrouped,
          child: const SizedBox(height: 15),
        );
      },
    );
  }
}
