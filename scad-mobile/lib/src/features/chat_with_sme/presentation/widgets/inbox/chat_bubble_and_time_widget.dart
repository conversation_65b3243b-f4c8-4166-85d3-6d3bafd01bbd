import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_chat_bubble/chat_bubble.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/message/message_model.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/bloc/chat_with_sme_bloc.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/widgets/inbox/feedback_pop_up.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/extentions/string_extentions.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

class ChatBubbleAndTimeWidget extends StatelessWidget {
  const ChatBubbleAndTimeWidget({
    required this.message,
    required this.nextMessage,
    required this.isSender,
    this.isGrouped,
    this.isLastMessage = false,
    super.key,
  });

  final MessageModel? message;
  final MessageModel? nextMessage;
  final bool isSender;
  final bool? isGrouped;
  final bool? isLastMessage;

  @override
  Widget build(BuildContext context) {
    bool isDisabled = message?.feedback != null;
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    final bool isArabic = HiveUtilsSettings.getAppLanguage() == 'ar';
    return BlocBuilder<ChatWithSmeBloc, ChatWithSmeState>(
      builder: (context, state) {
        if (state is ChatWithSmeSendFeedbackState) {
          isDisabled = state.chatThreadClosed;
        }
        return Column(
          crossAxisAlignment: isSender
              ? isArabic
                  ? CrossAxisAlignment.start
                  : CrossAxisAlignment.end
              : isArabic
                  ? CrossAxisAlignment.end
                  : CrossAxisAlignment.start,
          children: [
            if (isGrouped == true)
              Container(
                padding: _padding(
                  message?.attachment != '' ? true : false,
                  message?.message != '' ? true : false,
                ),
                margin: EdgeInsets.only(
                  right: isSender ? 5 : 100,
                  left: isSender ? 100 : 7,
                ),
                decoration: BoxDecoration(
                  color: isSender
                      ? isLightMode
                          ? AppColors.blueLight
                          : AppColors.blueLightOld
                      : isLightMode
                          ? AppColors.white
                          : AppColors.blueShade32,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (message?.attachment != '') ...[
                      ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: Image.network(message?.attachment ?? ''),
                      ),
                      const SizedBox(height: 5),
                    ],
                    Text(
                      message?.message ?? '',
                      style: TextStyle(
                        color: isSender
                            ? AppColors.white
                            : isLightMode
                                ? AppColors.blackChatText
                                : AppColors.white,
                        fontSize: 14,
                      ),
                      textScaler: TextScaler.linear(textScaleFactor.value),
                    ),
                  ],
                ),
              )
            else
              Stack(
                children: [
                  ChatBubble(
                    alignment:
                        isSender ? Alignment.centerRight : Alignment.centerLeft,
                    clipper: ChatBubbleClipper6(
                      type: isSender
                          ? BubbleType.sendBubble
                          : BubbleType.receiverBubble,
                    ),
                    elevation: 0,
                    backGroundColor: isSender
                        ? isLightMode
                            ? AppColors.blueLight
                            : AppColors.blueLightOld
                        : isLightMode
                            ? AppColors.white
                            : AppColors.blueShade32,
                    padding: _padding(
                      message?.attachment != '' ? true : false,
                      message?.message != '' ? true : false,
                    ),
                    margin: EdgeInsets.only(
                      right: isSender ? 0 : 100,
                      left: isSender ? 100 : 0,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (message?.attachment != '') ...[
                          ClipRRect(
                            borderRadius: BorderRadius.circular(10),
                            child: Image.network(message?.attachment ?? ''),
                          ),
                          const SizedBox(height: 5),
                        ],
                        if (message?.message != '') ...[
                          Directionality(
                            textDirection: TextDirection.ltr,
                            child: Text(
                              message?.message ?? '',
                              style: TextStyle(
                                color: isSender
                                    ? AppColors.white
                                    : isLightMode
                                        ? AppColors.blackChatText
                                        : AppColors.white,
                                fontSize: 14,
                              ),
                              textScaler:
                                  TextScaler.linear(textScaleFactor.value),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  if (!isSender && isLastMessage == true) ...[
                    Positioned(
                      right: 0,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          InkWell(
                            onTap: () {
                              if (isDisabled == false) {
                                context.read<ChatWithSmeBloc>().add(
                                      ChatWithSmeSendFeedbackEvent(
                                        messageId: message?.uuid ?? '',
                                        isSatisfied: true,
                                      ),
                                    );
                              }
                            },
                            child: isLightMode
                                ? SvgPicture.asset(
                                    message?.feedback?.satisfied == true
                                        ? 'assets/images/thumbs-up-blue.svg'
                                        : 'assets/images/thumbs-up-grey.svg',
                                  )
                                : SvgPicture.asset(
                                    message?.feedback?.satisfied == true
                                        ? 'assets/images/thumbs-up-blue.svg'
                                        : 'assets/images/thumbs-up-grey.svg',
                                    colorFilter: ColorFilter.mode(
                                      message?.feedback?.satisfied == true
                                          ? AppColors.white
                                          : AppColors.greyShade4,
                                      BlendMode.srcIn,
                                    ),
                                  ),
                          ),
                          const SizedBox(height: 10),
                          InkWell(
                            onTap: () {
                              if (isDisabled == false) {
                                showDialog<dynamic>(
                                  context: context,
                                  builder: (BuildContext context) {
                                    return FeedbackPopUp(
                                      message: message,
                                      isSatisfied: false,
                                    );
                                  },
                                );
                              }
                            },
                            child: isLightMode
                                ? SvgPicture.asset(
                                    message?.feedback?.satisfied == false
                                        ? 'assets/images/thumbs-down-blue.svg'
                                        : 'assets/images/thumbs-down-grey.svg',
                                  )
                                : SvgPicture.asset(
                                    message?.feedback?.satisfied == false
                                        ? 'assets/images/thumbs-down-blue.svg'
                                        : 'assets/images/thumbs-down-grey.svg',
                                    colorFilter: ColorFilter.mode(
                                      message?.feedback?.satisfied == false
                                          ? AppColors.white
                                          : AppColors.greyShade4,
                                      BlendMode.srcIn,
                                    ),
                                  ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            Visibility(
              visible:
                  isGrouped == false && message?.feedback?.comments != null,
              child: Directionality(
                textDirection: TextDirection.ltr,
                child: Padding(
                  padding: const EdgeInsets.only(top: 10, bottom: 10),
                  child: Text(
                    isGrouped == true
                        ? (nextMessage?.createdTime ?? '')
                            .formatDateTimeForChat()
                        : (message?.createdTime ?? '').formatDateTimeForChat(),
                    style: TextStyle(
                      color: AppColors.greyShade4,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                    ),
                    textScaler: TextScaler.linear(textScaleFactor.value),
                  ),
                ),
              ),
            ),
            if (message?.feedback?.comments != null &&
                message?.feedback?.comments != '') ...[
              Column(
                crossAxisAlignment: !isArabic
                    ? CrossAxisAlignment.end
                    : CrossAxisAlignment.start,
                children: [
                  ChatBubble(
                    alignment: Alignment.centerRight,
                    clipper: ChatBubbleClipper6(
                      type: BubbleType.sendBubble,
                    ),
                    elevation: 0,
                    backGroundColor: isLightMode
                        ? AppColors.blueLight
                        : AppColors.blueLightOld,
                    margin: const EdgeInsets.only(
                      left: 100,
                    ),
                    padding: const EdgeInsets.only(
                      left: 22,
                      top: 12,
                      right: 22,
                      bottom: 20,
                    ),
                    child: Directionality(
                      textDirection: TextDirection.ltr,
                      child: Text(
                        message?.feedback?.comments ?? '',
                        style: TextStyle(
                          color: AppColors.white,
                          fontSize: 14,
                        ),
                        textScaler: TextScaler.linear(textScaleFactor.value),
                      ),
                    ),
                  ),
                  const SizedBox(height: 10),
                  Directionality(
                    textDirection: TextDirection.ltr,
                    child: Text(
                      (message?.feedback?.createdTime ?? '')
                          .formatDateTimeForChat(),
                      style: TextStyle(
                        color: AppColors.greyShade4,
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                      ),
                      textScaler: TextScaler.linear(textScaleFactor.value),
                    ),
                  ),
                ],
              ),
            ],
            const SizedBox(height: 10),
            Visibility(
              visible:
                  isGrouped == false && message?.feedback?.comments == null,
              child: Directionality(
                textDirection: TextDirection.ltr,
                child: Text(
                  isGrouped == true
                      ? (nextMessage?.createdTime ?? '').formatDateTimeForChat()
                      : (message?.createdTime ?? '').formatDateTimeForChat(),
                  style: TextStyle(
                    color: AppColors.greyShade4,
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                  textScaler: TextScaler.linear(textScaleFactor.value),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  EdgeInsetsGeometry _padding(bool isAttachment, bool isMessageText) {
    if (isAttachment) {
      return EdgeInsets.only(
        left: isGrouped == true
            ? isSender
                ? 5
                : 5
            : isSender
                ? 5
                : 13,
        top: isGrouped == true
            ? isSender
                ? 5
                : 5
            : 5,
        right: isGrouped == true
            ? isSender
                ? 5
                : 5
            : isSender
                ? 13
                : 5,
        bottom: isMessageText ? 20 : 9,
      );
    } else {
      return EdgeInsets.only(
        left: isSender ? 22 : 27,
        top: isGrouped == true
            ? isSender
                ? 0
                : 12
            : 12,
        right: isSender ? 27 : 22,
        bottom: isGrouped == true ? 13 : 20,
      );
    }
  }
}
