import 'package:flutter/material.dart';
import 'package:scad_mobile/main.dart' show textScaleFactor;
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';

class EndOfChatWidget extends StatelessWidget {
  const EndOfChatWidget({required this.text, super.key});

  final String text;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Divider(
            color: AppColors.greyShade4,
          ),
        ),
        Expanded(
          flex: 5,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: Text(
              text,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: AppColors.greyShade4,
                fontSize: 14,
                fontWeight: FontWeight.w400,
                height: 0,
              ),
              textScaler: TextScaler.linear(textScaleFactor.value),
            ),
          ),
        ),
        Expanded(
          child: Divider(
            color: AppColors.greyShade4,
          ),
        ),
      ],
    );
  }
}
