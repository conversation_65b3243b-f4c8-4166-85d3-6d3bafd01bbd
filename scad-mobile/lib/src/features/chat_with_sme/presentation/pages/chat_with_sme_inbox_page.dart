import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:overlay_support/overlay_support.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/common/widgets/appbar/flat_app_bar.dart';
import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_response_helper.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/presentation/bloc/indicator_card_bloc.dart';
import 'package:scad_mobile/src/common/widgets/no_data_placeholder.dart';
import 'package:scad_mobile/src/config/firebase_config/firebase_config.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/message/message_model.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/bloc/chat_with_sme_bloc.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/widgets/inbox/end_of_chat_widget.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/widgets/inbox/inbox_list.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/widgets/ticket_id_chip.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_auth.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

@RoutePage()
class ChatWithSmeInboxPage extends StatefulWidget {
  const ChatWithSmeInboxPage({
    this.title,
    this.domain,
    this.domainId,
    this.theme,
    this.subTheme,
    this.ticketId,
    this.indicatorNodeId,
    this.indicatorAppType,
    this.inicatorContentType,
    this.indicatorKey,
    this.indicatorName,
    this.chatThreadId,
    this.chatDisabled,
    this.chatThreadClosed,
    super.key,
  });

  final String? title;
  final String? domain;
  final int? domainId;
  final String? theme;
  final String? subTheme;
  final String? ticketId;
  final String? indicatorNodeId;
  final String? indicatorAppType;
  final String? inicatorContentType;
  final String? indicatorKey;
  final String? indicatorName;
  final String? chatThreadId;
  final bool? chatDisabled;
  final bool? chatThreadClosed;

  @override
  State<ChatWithSmeInboxPage> createState() => _ChatWithSmeInboxPageState();
}

class _ChatWithSmeInboxPageState extends State<ChatWithSmeInboxPage> {
  List<MessageModel>? messageList = [];
  final ScrollController scrollController = ScrollController();
  bool isSendingMessage = false;

  bool isLoading = false;

  bool isChatClosed = false;
  bool isChatDisabled = false;
  File? file;

  final TextEditingController chatController = TextEditingController();

  @override
  void initState() {
    FirebaseConfig.setScreenToAnalytics('Ask Us Inbox');
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    final bool isArabic = HiveUtilsSettings.getAppLanguage() == 'ar';

    return PopScope(
      canPop: false,
      onPopInvoked: (bool didPop) {
        if (appDrawerController.value.visible) {
          appDrawerController.hideDrawer();
        } else {
          Navigator.pop(context);
        }
      },
      child: AppDrawer(
        child: Scaffold(
          body: GestureDetector(
            onTap: () {
              FocusManager.instance.primaryFocus?.unfocus();
            },
            child: BlocConsumer<ChatWithSmeBloc, ChatWithSmeState>(
              listener: (context, state) {
                if (state is ChatWithSmeSendFeedbackState) {
                  if (state.isSuccess) {
                    showSimpleNotification(
                      Container(
                        padding: const EdgeInsets.only(
                          top: 15,
                          left: 20,
                          right: 14,
                          bottom: 15,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xFFEAF7D9),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              LocaleKeys.feedbackSubmittedSuccessfully.tr(),
                              style: const TextStyle(
                                color: Color(0xFF4B7044),
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            SvgPicture.asset(
                              'assets/images/green-circle-tick.svg',
                            ),
                          ],
                        ),
                      ),
                      elevation: 0,
                      position: NotificationPosition.bottom,
                      background: Colors.transparent,
                    );
                    context.read<ChatWithSmeBloc>().add(
                          ChatWithSmeLoadInboxEvent(
                            chatThreadId: widget.chatThreadId ?? '',
                          ),
                        );
                  }
                  isChatClosed = state.chatThreadClosed;
                }
                if (state is ChatWithSmeSendLoadingMessageState) {
                  isSendingMessage = state.isLoading ?? false;
                } else if (state is ChatWithSmeSendMessageState) {
                  messageList = state.messageList;
                  isChatDisabled = state.chatDisabled ?? false;
                  // setState(() {});
                } else if (state is ChatWithSmeInboxDataState) {
                  messageList = (state.messageList ?? []).isEmpty
                      ? [
                          MessageModel(
                            message: 'Hi ${HiveUtilsSettings.getUsersName()}',
                            uuid: 'admin',
                            attachment: '',
                          ),
                          MessageModel(
                            message: 'How can we help you today?',
                            uuid: 'admin',
                            attachment: '',
                          ),
                        ]
                      : state.messageList?.reversed.toList() ?? [];
                }
              },
              builder: (context, state) {
                if (state is ChatWithSmeInboxLoadingState) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                } else if (state is ChatWithSmeChatInboxErrorState) {
                  return ConstrainedBox(
                    constraints: const BoxConstraints(maxHeight: 300),
                    child: const Center(
                      child: NoDataPlaceholder(),
                    ),
                  );
                }

                return Stack(
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        FlatAppBar(
                          title: widget.title ?? '',
                          scrollController: scrollController,
                          fromChatScreen: true,
                          onBack: () {
                            context
                                .read<ChatWithSmeBloc>()
                                .add(const ChatWithSmeLoadDataEvent());
                          },
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 24),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              if (widget.indicatorName == '') ...[
                                Text(
                                  '${LocaleKeys.reference.tr()}:',
                                  style: TextStyle(
                                    color: AppColors.greyShade4,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400,
                                  ),
                                  textScaler:
                                      TextScaler.linear(textScaleFactor.value),
                                ),
                                const SizedBox(height: 4),
                                Wrap(
                                  children: [
                                    _referenceItem(
                                      isLightMode,
                                      widget.domain,
                                      true,
                                    ),
                                    _referenceItem(
                                      isLightMode,
                                      widget.theme,
                                      true,
                                    ),
                                    _referenceItem(
                                      isLightMode,
                                      widget.subTheme,
                                      false,
                                    ),
                                  ],
                                ),
                              ],
                              if (widget.indicatorName != '') ...[
                                Row(
                                  children: [
                                    Text(
                                      '${LocaleKeys.reference.tr()}:',
                                      style: TextStyle(
                                        color: AppColors.greyShade4,
                                        fontSize: 14,
                                        fontWeight: FontWeight.w400,
                                      ),
                                      textScaler: TextScaler.linear(
                                        textScaleFactor.value,
                                      ),
                                    ),
                                    const SizedBox(width: 10),
                                    BlocConsumer<IndicatorCardBloc,
                                        IndicatorCardState>(
                                      listener: (context, state) {
                                        if (state
                                            is IndicatorDetailsSuccessState) {
                                          if (state.indicatorDetails.type ==
                                                  'Internal' &&
                                              state.indicatorDetails
                                                      .multiDrivers ==
                                                  true) {
                                            context.router.removeWhere(
                                                (route) =>
                                                    route.name ==
                                                    WhatIfDetailsPageRoute
                                                        .name);
                                            context
                                                .pushRoute(
                                              WhatIfDetailsPageRoute(
                                                nodeId:
                                                    widget.indicatorNodeId ??
                                                        '',
                                                indicatorDetails:
                                                    IndicatorDetailsResponseHelper(
                                                        state.indicatorDetails),
                                                originalIndicatorData:
                                                    state.indicatorDetails,
                                              ),
                                            )
                                                .whenComplete(() {
                                              setState(() {
                                                isSendingMessage = false;
                                              });
                                            });
                                          } else {
                                            context.router.removeWhere(
                                                (route) =>
                                                    route.name ==
                                                    DetailsPageRoute.name);
                                            context
                                                .pushRoute(
                                              DetailsPageRoute(
                                                indicatorDetails:
                                                    IndicatorDetailsResponseHelper(
                                                        state.indicatorDetails),
                                                originalIndicatorForFilter:
                                                    state.indicatorDetails,
                                                contentType:
                                                    widget.inicatorContentType,
                                              ),
                                            )
                                                .whenComplete(() {
                                              setState(() {
                                                isSendingMessage = false;
                                              });
                                            });
                                          }

                                          // context
                                          //     .pushRoute(
                                          //   DetailsPageRoute(
                                          //     indicatorDetails:
                                          //         IndicatorDetailsResponseHelper(
                                          //       state.indicatorDetails,
                                          //     ),
                                          //     originalIndicatorForFilter:
                                          //         state.indicatorDetails,
                                          //   ),
                                          // )
                                        }
                                      },
                                      builder: (context, state) {
                                        return Flexible(
                                          child: InkWell(
                                            borderRadius:
                                                BorderRadius.circular(70),
                                            onTap: () {
                                              setState(() {
                                                isSendingMessage = true;
                                              });
                                              context
                                                  .read<IndicatorCardBloc>()
                                                  .add(
                                                    GetIndicatorDetailsEvent(
                                                      id: widget
                                                              .indicatorNodeId ??
                                                          '',
                                                      contentType: widget
                                                              .inicatorContentType ??
                                                          '',
                                                      overviewContentType:
                                                          // widget
                                                          //         .indicatorAppType ??
                                                          '',
                                                    ),
                                                  );
                                            },
                                            child: Container(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                horizontal: 15,
                                                vertical: 8,
                                              ),
                                              decoration: BoxDecoration(
                                                color: isLightMode
                                                    ? AppColors.blueShade28
                                                    : AppColors.blueShade16,
                                                borderRadius:
                                                    BorderRadius.circular(70),
                                              ),
                                              child: Text(
                                                widget.indicatorName ?? '',
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                                style: TextStyle(
                                                  color: isLightMode
                                                      ? AppColors.blueLight
                                                      : AppColors.white,
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                                textScaler: TextScaler.linear(
                                                  textScaleFactor.value,
                                                ),
                                              ),
                                            ),
                                          ),
                                        );
                                      },
                                    ),
                                  ],
                                ),
                              ],
                              const SizedBox(height: 15),
                              TicketIdChip(
                                ticketId: widget.ticketId,
                              ),
                              const SizedBox(height: 15),
                              Container(
                                width: double.infinity,
                                height: 2,
                                color: isLightMode
                                    ? AppColors.white
                                    : AppColors.blackShade4,
                              ),
                            ],
                          ),
                        ),
                        Expanded(child: _body(state, isLightMode)),
                      ],
                    ),
                    if (isSendingMessage) ...[
                      Center(
                        child: Container(
                          width: 50,
                          height: 50,
                          padding: const EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            color: Colors.transparent.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: CircularProgressIndicator(
                            color: AppColors.selectedChipBlue,
                          ),
                        ),
                      ),
                    ],
                  ],
                );
              },
            ),
          ),
          bottomNavigationBar: BlocConsumer<ChatWithSmeBloc, ChatWithSmeState>(
            listener: (context, state) {
              if (state is ChatWithSmeInboxDataState) {
                file = null;
                chatController.clear();
              } else if (state is ChatWithSmeAttachmentAddState) {
                file = state.file;
              } else if (state is ChatWithSmeSendMessageState) {
                file = null;
                chatController.clear();
                FocusManager.instance.primaryFocus?.unfocus();
              }
            },
            builder: (context, state) {
              return widget.chatDisabled == true ||
                      widget.chatThreadClosed == true ||
                      isChatDisabled
                  ? const SizedBox.shrink()
                  : Padding(
                      padding: EdgeInsets.only(
                        bottom: MediaQuery.of(context).viewInsets.bottom,
                      ),
                      child: Container(
                        height: 99,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 15, vertical: 25),
                        decoration: BoxDecoration(
                          color: isLightMode
                              ? AppColors.white
                              : AppColors.blueShade32,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: SizedBox(
                          height: 49,
                          child: TextField(
                            controller: chatController,
                            textCapitalization: TextCapitalization.sentences,
                            style: TextStyle(
                              color: isLightMode
                                  ? AppColors.black
                                  : AppColors.white,
                            ),
                            decoration: InputDecoration(
                              fillColor: isLightMode
                                  ? AppColors.white
                                  : AppColors.blueShade36,
                              contentPadding: const EdgeInsets.symmetric(
                                vertical: 14,
                                horizontal: 12,
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(50),
                                borderSide: BorderSide(
                                  color: AppColors.greyShade1,
                                ),
                              ),
                              disabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(50),
                                borderSide: BorderSide(
                                  color: AppColors.greyShade1,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(50),
                                borderSide: BorderSide(
                                  color: AppColors.greyShade1,
                                ),
                              ),
                              prefixIcon: file != null
                                  ? Padding(
                                      padding: const EdgeInsets.only(left: 8),
                                      child: InkWell(
                                        onTap: () {
                                          context.read<ChatWithSmeBloc>().add(
                                                const ChatWithSmeAttachmentAddEvent(
                                                  isRemove: true,
                                                ),
                                              );
                                        },
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            SizedBox(
                                              width: MediaQuery.sizeOf(context)
                                                      .width /
                                                  4,
                                              child: Text(
                                                file?.path.split('/').last ??
                                                    '',
                                                overflow: TextOverflow.ellipsis,
                                                maxLines: 1,
                                                style: TextStyle(
                                                  color: isLightMode
                                                      ? AppColors.black
                                                      : AppColors.white,
                                                ),
                                                textScaler: TextScaler.linear(
                                                  textScaleFactor.value,
                                                ),
                                              ),
                                            ),
                                            Icon(
                                              Icons.delete,
                                              color: isLightMode
                                                  ? AppColors.black
                                                  : AppColors.white,
                                            ),
                                          ],
                                        ),
                                      ),
                                    )
                                  : InkWell(
                                      onTap: () {
                                        context.read<ChatWithSmeBloc>().add(
                                              const ChatWithSmeAttachmentAddEvent(),
                                            );
                                      },
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 12),
                                        child: SvgPicture.asset(
                                          'assets/images/attach.svg',
                                          colorFilter: ColorFilter.mode(
                                            file != null
                                                ? AppColors.selectedChipBlue
                                                : AppColors.greyShade1,
                                            BlendMode.srcIn,
                                          ),
                                        ),
                                      ),
                                    ),
                              suffixIcon: InkWell(
                                onTap: () {
                                  FocusManager.instance.primaryFocus?.unfocus();
                                  if (chatController.text.trim().isEmpty &&
                                      file == null) {
                                    AppMessage.showOverlayNotification(
                                      '',
                                      LocaleKeys
                                          .pleaseEnterAMessageOrAttachAFile
                                          .tr(),
                                      msgType: 'error',
                                    );
                                  } else {
                                    context.read<ChatWithSmeBloc>().add(
                                          ChatWithSmeSendMessageEvent(
                                            chatThreadId:
                                                widget.chatThreadId ?? '',
                                            message: chatController.text,
                                            attachment: file,
                                          ),
                                        );
                                  }
                                },
                                child: Container(
                                  width: 33,
                                  height: 33,
                                  margin: const EdgeInsets.all(9),
                                  padding: const EdgeInsets.all(7),
                                  decoration: ShapeDecoration(
                                    color: isLightMode
                                        ? AppColors.blueLight
                                        : AppColors.blueLightOld,
                                    shape: const OvalBorder(),
                                  ),
                                  child: Transform.flip(
                                    flipX: isArabic,
                                    child: SvgPicture.asset(
                                        'assets/images/send.svg'),
                                  ),
                                ),
                              ),
                              filled: true,
                            ),
                          ),
                        ),
                      ),
                    );
            },
          ),
        ),
      ),
    );
  }

  Widget _referenceItem(bool isLightMode, String? label, bool showArrow) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          '$label ',
          style: TextStyle(
            color: isLightMode ? AppColors.grey : AppColors.white,
            fontSize: 14,
            fontWeight: FontWeight.w400,
          ),
          textScaler: TextScaler.linear(
            textScaleFactor.value,
          ),
        ),
        if (showArrow)
          Padding(
            padding: const EdgeInsets.only(top: 2),
            child: SvgPicture.asset(
              'assets/images/reference_arrow.svg',
              colorFilter: ColorFilter.mode(
                isLightMode ? AppColors.grey : AppColors.white,
                BlendMode.srcIn,
              ),
            ),
          ),
      ],
    );
  }

  Widget _body(ChatWithSmeState state, bool isLightMode) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (scrollController.hasClients) {
        scrollController.animateTo(
          scrollController.position.maxScrollExtent,
          curve: Curves.easeOut,
          duration: const Duration(milliseconds: 500),
        );
      }
    });

    return SingleChildScrollView(
      controller: scrollController,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        child: Column(
          children: [
            InboxList(
              messageList: messageList,
            ),
            if (_showAwaitingMessage() || isChatDisabled) ...[
              EndOfChatWidget(
                text: LocaleKeys.awaitingResponseFromSme.tr(),
              ),
              const SizedBox(height: 26),
            ],
            if (widget.chatThreadClosed == true || isChatClosed) ...[
              EndOfChatWidget(
                text: LocaleKeys.chatEnded.tr(),
              ),
              const SizedBox(height: 26),
            ],
          ],
        ),
      ),
    );
  }

  bool _showAwaitingMessage() {
    if ((widget.chatDisabled == true) &&
        (widget.chatThreadClosed == false) &&
        !isChatClosed) {
      if (messageList?.last.sender == null) {
        return true;
      } else if (messageList?.last.sender?.uuid != HiveUtilsAuth.getUserId()) {
        if (messageList?.last.feedback == null) {
          return false;
        } else if (messageList?.last.feedback?.satisfied == true) {
          return false;
        } else if (messageList?.last.feedback?.satisfied == false) {
          return true;
        }
      }
      return true;
    } else {
      return false;
    }
  }
}
