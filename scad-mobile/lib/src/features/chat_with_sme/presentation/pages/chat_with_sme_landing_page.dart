import 'dart:convert';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/main.dart' show is<PERSON><PERSON><PERSON><PERSON><PERSON>, textScaleFactor;
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/common/widgets/appbar/flat_app_bar.dart';
import 'package:scad_mobile/src/common/widgets/landing_top_button.dart';
import 'package:scad_mobile/src/common/widgets/no_data_placeholder.dart';
import 'package:scad_mobile/src/common/widgets/rounded_dropdown_widget.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/intro_widget.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/showcaseview.dart';
import 'package:scad_mobile/src/config/firebase_config/firebase_config.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/chat_thread/chat_thread_model.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/faq/faq_model.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/bloc/chat_with_sme_bloc.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/widgets/landing/chat_widget.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/widgets/landing/empty_chat_widget.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/widgets/landing/faq_list_widget.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/widgets/landing/new_chat_bottom_sheet.dart';
import 'package:scad_mobile/src/features/domains/data/models/domain_model/domain_model.dart';
import 'package:scad_mobile/src/features/home/<USER>/widgets/home_page_list_toggler/home_page_list_toggler.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/animation_asset.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

import '../../../../../demo/demo_api_responses.dart';

@RoutePage()
class ChatWithSmeLandingPage extends StatefulWidget {
  const ChatWithSmeLandingPage({super.key});

  @override
  State<ChatWithSmeLandingPage> createState() => _ChatWithSmeLandingPageState();
}

class _ChatWithSmeLandingPageState extends State<ChatWithSmeLandingPage> {
  @override
  Widget build(BuildContext context) {
    return ShowCaseWidget(
      builder: Builder(
        builder: (context) {
          return const ChatWithSmeLanding();
        },
      ),
    );
  }
}

class ChatWithSmeLanding extends StatefulWidget {
  const ChatWithSmeLanding({super.key});

  @override
  State<ChatWithSmeLanding> createState() => _ChatWithSmeLandingState();
}

class _ChatWithSmeLandingState extends State<ChatWithSmeLanding>
    with TickerProviderStateMixin {
  final ScrollController chatThreadScrollController = ScrollController();
  final ScrollController faqScrollController = ScrollController();
  late final TabController askAsTabController;
  ValueNotifier<int> faqTabIndex = ValueNotifier(1);
  List<DomainModel>? domainList;
  DomainModel? selectedDomain;
  DomainModel general =
      DomainModel(domainId: 'general', domainName: 'general', domainIcon: '');
  ValueNotifier<String> selectedDomainId = ValueNotifier('');

  final GlobalKey chatThreadKey = GlobalKey(debugLabel: 'chatThreadKey');
  final GlobalKey mostPopularFAQsKey = GlobalKey(debugLabel: 'mostPopularFAQsKey');
  final GlobalKey submitQuestionKey = GlobalKey(debugLabel: 'submitQuestionKey');
  List<GlobalKey> steps = [];

  @override
  void initState() {
    super.initState();
    if(!isDemoMode) {
      steps = [chatThreadKey, mostPopularFAQsKey, submitQuestionKey];
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        if (HiveUtilsSettings.getUserGuideStatus() == UserGuides.AskUs) {
          ShowCaseWidget.of(context).startShowCase(steps);
        }
      });
      askAsTabController = TabController(length: 2, vsync: this);

      context.read<ChatWithSmeBloc>().add(const ChatWithSmeLoadDataEvent());
    }
  }

  @override
  void dispose() {
    askAsTabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    List<ChatThreadModel> chatThreadList = [];
    List<FaqModel> faqList = [];
    return Scaffold(
      body: Column(
        children: [
          FlatAppBar(
            title: LocaleKeys.askUs.tr(),
            mainScreen: true,
            appBarHeight: 10,
            scrollController: askAsTabController.index == 0
                ? chatThreadScrollController
                : faqScrollController,
          ),
          if(isDemoMode)
              _demoModeContent()
          else
            ...[
            TabBar(
            controller: askAsTabController,
            overlayColor: MaterialStateProperty.all<Color>(
              AppColors.blueLight.withOpacity(0.1),
            ),
            indicatorSize: TabBarIndicatorSize.tab,
            labelColor:
                isLightMode ? AppColors.blueLight : AppColors.blueLightOld,
            indicator: UnderlineTabIndicator(
              borderSide: BorderSide(
                width: 3,
                color: isLightMode
                    ? AppColors.blueLight
                    : AppColors.blueLightOld,
              ),
              insets: const EdgeInsets.symmetric(horizontal: 24),
            ),
            onTap: (index) {
              if (index == 1) {
                faqTabIndex.value = 1;
                context
                    .read<ChatWithSmeBloc>()
                    .add(const FaqLoadDomainDataEvent());
              } else {
                context
                    .read<ChatWithSmeBloc>()
                    .add(const ChatWithSmeLoadDataEvent());
              }
            },
            tabs: [
              IntroWidget(
                stepKey: chatThreadKey,
                stepIndex: 1,
                totalSteps: steps.length,
                title: LocaleKeys.chatThreads.tr(),
                description: LocaleKeys.chatThreadsGuideDesc.tr(),
                arrowAlignment: Alignment.topLeft,
                targetBorderRadius: 6,
                arrowPadding: EdgeInsets.only(
                  right: MediaQuery.sizeOf(context).width * 0.2,
                  left: MediaQuery.sizeOf(context).width * 0.2,
                  bottom: 10,
                ),
                targetPadding: const EdgeInsets.symmetric(horizontal: 44),
                child: Tab(
                  child: Text(
                    LocaleKeys.chatThreads.tr(),
                    textScaler: TextScaler.linear(textScaleFactor.value),
                  ),
                ),
              ),
              IntroWidget(
                stepKey: mostPopularFAQsKey,
                stepIndex: 2,
                totalSteps: steps.length,
                title: LocaleKeys.mostPopularFAQ.tr(),
                description: LocaleKeys.mostPopularFAQGuideDesc.tr(),
                arrowAlignment: Alignment.topRight,
                crossAxisAlignment: CrossAxisAlignment.end,
                targetBorderRadius: 6,
                arrowPadding: EdgeInsets.only(
                  right: MediaQuery.sizeOf(context).width * 0.18,
                  left: MediaQuery.sizeOf(context).width * 0.18,
                  bottom: 10,
                ),
                targetPadding: const EdgeInsets.symmetric(horizontal: 30),
                child: Tab(
                  child: Text(
                    LocaleKeys.mostPopularFAQ.tr(),
                    textScaler: TextScaler.linear(textScaleFactor.value),
                  ),
                ),
              ),
            ],
          ),
          Expanded(
            child: TabBarView(
              controller: askAsTabController,
              children: [
                if(!isDemoMode)
                  Padding(
                  padding: const EdgeInsets.only(bottom: 50),
                  child: BlocConsumer<ChatWithSmeBloc, ChatWithSmeState>(
                    listener: (context, state) {
                      if (state is ChatWithSmeLoadDataState) {
                        chatThreadList = state.chatThreadList ?? [];
                      } else if (state is ChatWithSmeFaqToggleState) {
                        faqList = state.updatedList;
                      } else if (state
                          is ChatWithSmeCreateChatThreadSuccessState) {
                        context.read<ChatWithSmeBloc>().add(
                              ChatWithSmeLoadInboxEvent(
                                chatThreadId: state.chatThreadId ?? '',
                              ),
                            );
                        context
                            .pushRoute(
                          ChatWithSmeInboxPageRoute(
                            chatThreadId: state.chatThreadId,
                            title: state.subject,
                            domain: state.domain,
                            domainId: state.domainId,
                            theme: state.theme,
                            subTheme: state.subTheme,
                            ticketId: state.ticketId,
                            indicatorName: '',
                            chatDisabled: state.chatDisabled,
                            chatThreadClosed: state.chatThreadClosed,
                          ),
                        )
                            .whenComplete(() {
                          context
                              .read<ChatWithSmeBloc>()
                              .add(const ChatWithSmeLoadDataEvent());
                        });
                      }
                    },
                    builder: (context, state) {
                      if (state is ChatWithSmeLoadingState) {
                        return const Center(
                          child: CircularProgressIndicator(),
                        );
                      } else if (state
                          is ChatWithSmeChatThreadListErrorState) {
                        return NoDataPlaceholder(msg: state.errorText);
                      } else if (state is ChatWithSmeErrorState) {
                        return NoDataPlaceholder(msg: state.errorText);
                      }
                      return SingleChildScrollView(
                        controller: chatThreadScrollController,
                        padding: const EdgeInsets.all(24),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (chatThreadList.isEmpty) ...[
                              submitNewQuestionButton(context),
                              const SizedBox(height: 55),
                              const EmptyChatWidget(),
                              const SizedBox(height: 65),
                            ],
                            if (chatThreadList.isNotEmpty) ...[
                              submitNewQuestionButton(
                                context,
                                isSmallWidget: true,
                              ),
                              const SizedBox(height: 30),
                              ChatWidget(chatThreadList: chatThreadList),
                              const SizedBox(height: 110),
                            ],
                          ],
                        ),
                      );
                    },
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    children: [
                      ValueListenableBuilder(
                        valueListenable: faqTabIndex,
                        builder: (context, i, w) {
                          return Container(
                            decoration: BoxDecoration(
                              color: isLightMode
                                  ? AppColors.blueShadeTabInset
                                  : AppColors.blueShade32,
                              borderRadius: BorderRadius.circular(40),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: HomePageListToggler(
                                    title: LocaleKeys.general.tr(),
                                    isSelected: faqTabIndex.value == 0,
                                    onTap: () {
                                      faqTabIndex.value = 0;
                                      selectedDomain = general;
                                      selectedDomainId.value =
                                          general.domainId ?? '';
                                      context.read<ChatWithSmeBloc>().add(
                                            FaqStateLoadDataEvent(
                                              domainFilter:
                                                  selectedDomain?.domainId ??
                                                      '',
                                            ),
                                          );
                                    },
                                  ),
                                ),
                                Expanded(
                                  child: HomePageListToggler(
                                    title: LocaleKeys.domains.tr(),
                                    isSelected: faqTabIndex.value == 1,
                                    onTap: () {
                                      faqTabIndex.value = 1;
                                      selectedDomain = domainList?.first;
                                      selectedDomainId.value =
                                          domainList?.first.domainId ?? '';
                                      context.read<ChatWithSmeBloc>().add(
                                            FaqStateLoadDataEvent(
                                              domainFilter:
                                                  selectedDomain?.domainId ??
                                                      '',
                                            ),
                                          );
                                    },
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                      const SizedBox(height: 25),
                      Align(
                        alignment: HiveUtilsSettings.getAppLanguage() == 'en'
                            ? Alignment.centerLeft
                            : Alignment.centerRight,
                        child: SizedBox(
                          width: MediaQuery.sizeOf(context).width * .5,
                          child:
                              BlocBuilder<ChatWithSmeBloc, ChatWithSmeState>(
                            builder: (context, state) {
                              if (state is FaqDomainLoadingState) {
                                return const Center(
                                  child: CircularProgressIndicator(),
                                );
                              } else if (state is FaqDomainDataState) {
                                domainList = state.domainList;
                                selectedDomain = state.domainList?.firstOrNull;
                                selectedDomainId.value =
                                    selectedDomain?.domainId ?? '';
                                context.read<ChatWithSmeBloc>().add(
                                      FaqStateLoadDataEvent(
                                        domainFilter:
                                            selectedDomain?.domainId ?? '',
                                      ),
                                    );
                              }
                              return ValueListenableBuilder(
                                valueListenable: selectedDomainId,
                                builder: (context, i, w) {
                                  if (faqTabIndex.value == 0) {
                                    return const SizedBox();
                                  }
                                  return Column(
                                    children: [
                                      RoundedDropDownWidget<DomainModel>(
                                        boxShadow: true,
                                        showBorder: false,
                                        isSquare: true,
                                        title: LocaleKeys.selectDomain.tr(),
                                        items: domainList,
                                        value: selectedDomain,
                                        onChanged: (val) {
                                          selectedDomain = val;
                                          selectedDomainId.value =
                                              val?.domainId ?? '';
                                          context.read<ChatWithSmeBloc>().add(
                                                FaqStateLoadDataEvent(
                                                  domainFilter:
                                                      val?.domainId ?? '',
                                                ),
                                              );
                                        },
                                      ),
                                      const SizedBox(height: 25),
                                    ],
                                  );
                                },
                              );
                            },
                          ),
                        ),
                      ),
                      Expanded(
                        child:
                            BlocConsumer<ChatWithSmeBloc, ChatWithSmeState>(
                          listener: (context, state) {
                            if (state is FaqStateLoadDataState) {
                              faqList = state.faqList ?? [];
                            }
                          },
                          builder: (context, state) {
                            if (state is FaqStateLoadingState) {
                              return const Center(
                                child: CircularProgressIndicator(),
                              );
                            } else if (state
                                is ChatWithSmeFaqListErrorState) {
                              return NoDataPlaceholder(msg: state.errorText);
                            }
                            return faqList.isEmpty
                                ? const Center(
                                    child: Padding(
                                      padding: EdgeInsets.only(bottom: 150),
                                      child: NoDataPlaceholder(),
                                    ),
                                  )
                                : SingleChildScrollView(
                                    controller: faqScrollController,
                                    padding:
                                        const EdgeInsets.only(bottom: 150),
                                    child: FaqListWidget(faqList: faqList),
                                  );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          ],
        ],
      ),
    );
  }

  Widget _demoModeContent(){
    final List<dynamic> list = demoFaqResponse as List<dynamic>;

    final List<FaqModel> faqList = list.map((e)=> FaqModel.fromJson(e as Map<String, dynamic>)).toList();

    return Expanded(
      child: SingleChildScrollView(
        controller: faqScrollController,
        padding:
        const EdgeInsets.fromLTRB(16, 12, 16, 150),
        child: FaqListWidget(faqList: faqList),
      ),
    );
  }

  Widget submitNewQuestionButton(
    BuildContext context, {
    bool isSmallWidget = false,
  }) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return IntroWidget(
      stepKey: submitQuestionKey,
      stepIndex: 3,
      totalSteps: steps.length,
      title: LocaleKeys.submitANewQuestion.tr(),
      description: LocaleKeys.startChatGuideDesc.tr(),
      arrowAlignment: Alignment.topRight,
      crossAxisAlignment: CrossAxisAlignment.center,
      targetBorderRadius: 20,
      arrowPadding: EdgeInsets.only(
        right: MediaQuery.sizeOf(context).width * 0.2,
        left: MediaQuery.sizeOf(context).width * 0.2,
        bottom: 10,
      ),
      targetPadding: const EdgeInsets.all(10),
      child: LandingTopButton(
        isSmallWidget: isSmallWidget,
        animatedIcon: isLightMode
            ? AnimationAsset.animationChatBubble
            : AnimationAssetDark.animationChatBubble,
        label: LocaleKeys.submitANewQuestion.tr(),
        onTap: () {
          context.read<ChatWithSmeBloc>().add(
                const ChatWithSmeLoadDomainDataEvent(),
              );
          showModalBottomSheet<dynamic>(
            backgroundColor: Colors.transparent,
            isScrollControlled: true,
            useRootNavigator: true,
            context: context,
            builder: (BuildContext context) {
              return const NewChatBottomSheet();
            },
          );
        },
      ),
    );
  }
}
