part of 'chat_with_sme_bloc.dart';

abstract class ChatWithSmeState extends Equatable {
  const ChatWithSmeState();

  @override
  List<Object> get props => [];
}

class ChatWithSmeInitial extends ChatWithSmeState {
  const ChatWithSmeInitial();
}

class ChatWithSmeLoadingState extends ChatWithSmeState {
  const ChatWithSmeLoadingState();

  @override
  List<Object> get props => [];
}

/// the state used to get both the chat thread the faq list
class ChatWithSmeLoadDataState extends ChatWithSmeState {
  const ChatWithSmeLoadDataState({this.chatThreadList});

  final List<ChatThreadModel>? chatThreadList;

  @override
  List<Object> get props => [chatThreadList ?? []];
}

class FaqStateInitial extends ChatWithSmeState {
  const FaqStateInitial();
}

class FaqStateLoadingState extends ChatWithSmeState {
  const FaqStateLoadingState();

  @override
  List<Object> get props => [];
}

/// the state used to get both the chat thread list
class FaqStateLoadDataState extends ChatWithSmeState {
  const FaqStateLoadDataState({this.faqList});

  final List<FaqModel>? faqList;

  @override
  List<Object> get props => [faqList ?? []];
}

class FaqErrorState extends ChatWithSmeState {
  const FaqErrorState({
    this.errorText,
  });

  final String? errorText;

  @override
  List<Object> get props => [errorText ?? ''];
}

/// the state used to get the error of chat thread list
class ChatWithSmeChatThreadListErrorState extends ChatWithSmeState {
  const ChatWithSmeChatThreadListErrorState({
    this.errorText,
  });

  final String? errorText;

  @override
  List<Object> get props => [errorText ?? ''];
}

/// the state used to get the error of faq list
class ChatWithSmeFaqListErrorState extends ChatWithSmeState {
  const ChatWithSmeFaqListErrorState({
    this.errorText,
  });

  final String? errorText;

  @override
  List<Object> get props => [errorText ?? ''];
}

/// the state used to get the updated faq list
class ChatWithSmeFaqToggleState extends ChatWithSmeState {
  const ChatWithSmeFaqToggleState({required this.updatedList});

  final List<FaqModel> updatedList;

  @override
  List<Object> get props => [updatedList];
}

/// the loading state for the domain for creating chat thread
class ChatWithSmeDomainLoadingState extends ChatWithSmeState {
  const ChatWithSmeDomainLoadingState();

  @override
  List<Object> get props => [];
}

/// the state used to get the domain for creating chat thread
class ChatWithSmeDomainDataState extends ChatWithSmeState {
  const ChatWithSmeDomainDataState({this.domainList});

  final List<Domain>? domainList;

  @override
  List<Object> get props => [domainList ?? []];
}

/// the error state for the domain for creating chat thread
class ChatWithSmeDomainErrorState extends ChatWithSmeState {
  const ChatWithSmeDomainErrorState({this.errorText});

  final String? errorText;

  @override
  List<Object> get props => [errorText ?? ''];
}

// the loading state for the domain for creating chat thread
class FaqDomainLoadingState extends ChatWithSmeState {
  const FaqDomainLoadingState();

  @override
  List<Object> get props => [];
}

/// the state used to get the domain for creating chat thread
class FaqDomainDataState extends ChatWithSmeState {
  const FaqDomainDataState({this.domainList});

  final List<DomainModel>? domainList;

  @override
  List<Object> get props => [domainList ?? []];
}

/// the error state for the domain for creating chat thread
class FaqDomainErrorState extends ChatWithSmeState {
  const FaqDomainErrorState({this.errorText});

  final String? errorText;

  @override
  List<Object> get props => [errorText ?? ''];
}

/// the state used to get the selected domain for creating chat thread
class ChatWithSmeDomainDropdownState extends ChatWithSmeState {
  const ChatWithSmeDomainDropdownState({
    required this.selectedValue,
    required this.subDomainList,
  });

  final Domain selectedValue;
  final List<Subdomain> subDomainList;

  @override
  List<Object> get props => [selectedValue, subDomainList];
}

/// the state used to get the theme for creating chat thread
class ChatWithSmeThemeDropdownState extends ChatWithSmeState {
  const ChatWithSmeThemeDropdownState({
    required this.selectedValue,
    required this.subThemeList,
  });

  final Subdomain selectedValue;
  final List<Subtheme> subThemeList;

  @override
  List<Object> get props => [selectedValue, subThemeList];
}

/// the state used to get the sub theme for creating chat thread
class ChatWithSmeSubThemeDropdownState extends ChatWithSmeState {
  const ChatWithSmeSubThemeDropdownState({
    required this.selectedValue,
  });

  final Subtheme selectedValue;

  @override
  List<Object> get props => [selectedValue];
}

/// the state used to get the success of create that thread
class ChatWithSmeCreateChatThreadSuccessState extends ChatWithSmeState {
  const ChatWithSmeCreateChatThreadSuccessState({
    this.chatThreadId,
    this.domain,
    this.domainId,
    this.theme,
    this.subTheme,
    this.subject,
    this.ticketId,
    this.chatDisabled,
    this.chatThreadClosed,
  });

  final String? chatThreadId;
  final String? domain;
  final int? domainId;
  final String? theme;
  final String? subTheme;
  final String? subject;
  final String? ticketId;
  final bool? chatDisabled;
  final bool? chatThreadClosed;

  @override
  List<Object> get props => [
        chatThreadId ?? '',
        domain ?? '',
        domainId ?? 0,
        theme ?? '',
        subTheme ?? '',
        subject ?? '',
        ticketId ?? '',
      ];
}

/// the state used to get the error of create chat thread
class ChatWithSmeCreateChatThreadErrorState extends ChatWithSmeState {
  const ChatWithSmeCreateChatThreadErrorState({
    this.errorText,
  });

  final String? errorText;

  @override
  List<Object> get props => [errorText ?? ''];
}

/// the state for max 60 in subject line while creating chat thread
class Max60CharacterState extends ChatWithSmeState {
  const Max60CharacterState({required this.count, this.actualText});

  final String count;
  final String? actualText;

  @override
  List<Object> get props => [count, actualText ?? ''];
}

class ChatWithSmeErrorState extends ChatWithSmeState {
  const ChatWithSmeErrorState({
    this.errorText,
  });

  final String? errorText;

  @override
  List<Object> get props => [errorText ?? ''];
}

/// for inbox
/// loading
class ChatWithSmeInboxLoadingState extends ChatWithSmeState {
  const ChatWithSmeInboxLoadingState();

  @override
  List<Object> get props => [];
}

/// the state used to get the inbox list
class ChatWithSmeInboxDataState extends ChatWithSmeState {
  const ChatWithSmeInboxDataState({this.messageList, this.chatThreadId});

  final List<MessageModel>? messageList;
  final String? chatThreadId;

  @override
  List<Object> get props => [messageList ?? []];
}

/// the state used to get the inbox error
class ChatWithSmeChatInboxErrorState extends ChatWithSmeState {
  const ChatWithSmeChatInboxErrorState({
    this.errorText,
  });

  final String? errorText;

  @override
  List<Object> get props => [errorText ?? ''];
}

/// the state used to add attachment to the message
class ChatWithSmeAttachmentAddState extends ChatWithSmeState {
  const ChatWithSmeAttachmentAddState({this.file});

  final File? file;

  @override
  List<Object> get props => [];
}

/// the state used when sending message process to show a loader
class ChatWithSmeSendLoadingMessageState extends ChatWithSmeState {
  const ChatWithSmeSendLoadingMessageState({this.isLoading});

  final bool? isLoading;

  @override
  List<Object> get props => [isLoading ?? false];
}

/// the state used to add sending message
class ChatWithSmeSendMessageState extends ChatWithSmeState {
  const ChatWithSmeSendMessageState({
    this.messageList,
    this.uuid,
    this.chatDisabled,
    this.chatThreadClosed,
  });

  final List<MessageModel>? messageList;
  final String? uuid;
  final bool? chatDisabled;
  final bool? chatThreadClosed;

  @override
  List<Object> get props => [
        messageList ?? [],
        uuid ?? '',
        chatDisabled!,
        chatThreadClosed!,
      ];
}

/// the state used to add sending feedback
class ChatWithSmeSendFeedbackState extends ChatWithSmeState {
  const ChatWithSmeSendFeedbackState({
    required this.isSuccess,
    required this.text,
    required this.chatDisabled,
    required this.chatThreadClosed,
  });

  final bool isSuccess;
  final String text;
  final bool chatDisabled;
  final bool chatThreadClosed;

  @override
  List<Object> get props => [isSuccess, text, chatDisabled, chatThreadClosed];
}

/// empty state
class ChatwithSmeEmptyState extends ChatWithSmeState {
  @override
  List<Object> get props => [];
}
