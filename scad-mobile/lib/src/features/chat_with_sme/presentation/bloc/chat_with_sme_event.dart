part of 'chat_with_sme_bloc.dart';

abstract class ChatWithSmeEvent extends Equatable {
  const ChatWithSmeEvent();

  @override
  List<Object> get props => [];
}

/// the event used to load both the chat thread list and the faq list
class ChatWithSmeLoadDataEvent extends ChatWithSmeEvent {
  const ChatWithSmeLoadDataEvent();

  @override
  List<Object> get props => [];
}

/// the event used to open and close faq list
class ChatWithSmeToggleExpansionEvent extends ChatWithSmeEvent {
  const ChatWithSmeToggleExpansionEvent({
    required this.updatedList,
    required this.index,
  });

  final List<FaqModel> updatedList;
  final int index;

  @override
  List<Object> get props => [updatedList, index];
}

/// the event used to get the domain for creating chat thread
class ChatWithSmeLoadDomainDataEvent extends ChatWithSmeEvent {
  const ChatWithSmeLoadDomainDataEvent();

  @override
  List<Object> get props => [];
}

/// the event used to get the domain for creating chat thread
class FaqLoadDomainDataEvent extends ChatWithSmeEvent {
  const FaqLoadDomainDataEvent();

  @override
  List<Object> get props => [];
}

/// the event used for domain droopdown
class ChatWithSmeDomainDropdownEvent extends ChatWithSmeEvent {
  const ChatWithSmeDomainDropdownEvent({
    required this.domainList,
    required this.selectedValue,
  });

  final List<Domain> domainList;
  final Domain selectedValue;

  @override
  List<Object> get props => [domainList, selectedValue];
}

/// the event used for theme droopdown
class ChatWithSmeThemeDropdownEvent extends ChatWithSmeEvent {
  const ChatWithSmeThemeDropdownEvent({
    required this.subDomainList,
    required this.selectedValue,
  });

  final List<Subdomain> subDomainList;
  final Subdomain selectedValue;

  @override
  List<Object> get props => [subDomainList, selectedValue];
}

/// the event used for sub theme droopdown
class ChatWithSmeSubThemeDropdownEvent extends ChatWithSmeEvent {
  const ChatWithSmeSubThemeDropdownEvent({required this.selectedValue});

  final Subtheme selectedValue;

  @override
  List<Object> get props => [selectedValue];
}

/// the event for max 60 in subject line while creating chat thread
class Max60CharacterEvent extends ChatWithSmeEvent {
  const Max60CharacterEvent({this.value});

  final String? value;

  @override
  List<Object> get props => [value ?? ''];
}

/// the event used to create chat thread
class ChatWithSmeCreateChatThreadEvent extends ChatWithSmeEvent {
  const ChatWithSmeCreateChatThreadEvent({
    required this.domain,
    required this.domainId,
    required this.theme,
    required this.subTheme,
    required this.subject,
  });

  final String domain;
  final int domainId;
  final String theme;
  final String subTheme;
  final String subject;

  @override
  List<Object> get props => [domain, domainId, theme, subTheme, subject];
}

/// the event used to load the inbox data
class ChatWithSmeLoadInboxEvent extends ChatWithSmeEvent {
  const ChatWithSmeLoadInboxEvent({required this.chatThreadId});

  final String chatThreadId;

  @override
  List<Object> get props => [chatThreadId];
}

/// the event used to add/remove attachment
class ChatWithSmeAttachmentAddEvent extends ChatWithSmeEvent {
  const ChatWithSmeAttachmentAddEvent({this.isRemove});

  final bool? isRemove;

  @override
  List<Object> get props => [isRemove ?? false];
}

/// the event used to send message
class ChatWithSmeSendMessageEvent extends ChatWithSmeEvent {
  const ChatWithSmeSendMessageEvent({
    required this.chatThreadId,
    required this.message,
    this.attachment,
  });

  final String chatThreadId;
  final String message;
  final File? attachment;

  @override
  List<Object> get props => [chatThreadId, message];
}

/// the event used to send feedback
class ChatWithSmeSendFeedbackEvent extends ChatWithSmeEvent {
  const ChatWithSmeSendFeedbackEvent({
    required this.messageId,
    required this.isSatisfied,
    this.comment,
  });

  final String messageId;
  final bool isSatisfied;
  final String? comment;

  @override
  List<Object> get props => [messageId, isSatisfied];
}

abstract class FaqEvent extends Equatable {
  const FaqEvent();

  @override
  List<Object> get props => [];
}

class FaqStateLoadDataEvent extends ChatWithSmeEvent {
  const FaqStateLoadDataEvent({
    required this.domainFilter,
  });
  final String domainFilter;
  @override
  List<Object> get props => [domainFilter];
}
