import 'package:json_annotation/json_annotation.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/faq/faq_model.dart';

part 'faq_list_model.g.dart';

@JsonSerializable()
class FaqListModel {
  FaqListModel({
    this.faqList,
  });

  factory FaqListModel.fromJson(Map<String, dynamic> json) =>
      _$FaqListModelFromJson(json);

  @JsonKey(name: 'data')
  List<FaqModel>? faqList;

  Map<String, dynamic> toJson() => _$FaqListModelToJson(this);
}
