import 'package:json_annotation/json_annotation.dart';
part 'faq_model.g.dart';

@JsonSerializable()
class FaqModel {
  FaqModel({
    this.uuid,
    this.title,
    this.titleAr,
    this.description,
    this.descriptionAr,
    this.domainId,
    this.domainName,
    this.domainNameAr,
    this.general,
    this.order,
    this.active,
    this.createdTime,
    this.updatedTime,
    this.isOpened = false,
  });

  factory FaqModel.fromJson(Map<String, dynamic> json) =>
      _$FaqModelFromJson(json);

  @<PERSON><PERSON><PERSON>ey(name: 'uuid')
  String? uuid;
  @Json<PERSON>ey(name: 'title')
  String? title;
  @J<PERSON><PERSON><PERSON>(name: 'title_ar')
  String? titleAr;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'description')
  String? description;
  @Json<PERSON>ey(name: 'description_ar')
  String? descriptionAr;
  @Json<PERSON>ey(name: 'domain_id')
  String? domainId;
  @JsonKey(name: 'domain_name')
  String? domainName;
  @Json<PERSON>ey(name: 'domain_name_ar')
  String? domainNameAr;
  @Json<PERSON>ey(name: 'general')
  bool? general;
  @J<PERSON><PERSON><PERSON>(name: 'order')
  int? order;
  @Json<PERSON><PERSON>(name: 'active')
  bool? active;
  @JsonKey(name: 'created_time')
  DateTime? createdTime;
  @JsonKey(name: 'updated_time')
  DateTime? updatedTime;
  @JsonKey(name: 'is_opened')
  bool isOpened;

  Map<String, dynamic> toJson() => _$FaqModelToJson(this);
}
