import 'package:json_annotation/json_annotation.dart';

part 'faq_added_by_model.g.dart';

@JsonSerializable()
class FaqAddedByModel {
  FaqAddedByModel({
    this.uuid,
    this.name,
  });

  factory FaqAddedByModel.fromJson(Map<String, dynamic> json) =>
      _$FaqAddedByModelFromJson(json);

  @Json<PERSON>ey(name: 'uuid')
  String? uuid;
  @Json<PERSON><PERSON>(name: 'name')
  String? name;

  Map<String, dynamic> toJson() => _$FaqAddedByModelToJson(this);
}
