// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'faq_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FaqModel _$FaqModelFromJson(Map<String, dynamic> json) => FaqModel(
      uuid: json['uuid'] as String?,
      title: json['title'] as String?,
      titleAr: json['title_ar'] as String?,
      description: json['description'] as String?,
      descriptionAr: json['description_ar'] as String?,
      domainId: json['domain_id'] as String?,
      domainName: json['domain_name'] as String?,
      domainNameAr: json['domain_name_ar'] as String?,
      general: json['general'] as bool?,
      order: json['order'] as int?,
      active: json['active'] as bool?,
      createdTime: json['created_time'] == null
          ? null
          : DateTime.parse(json['created_time'] as String),
      updatedTime: json['updated_time'] == null
          ? null
          : DateTime.parse(json['updated_time'] as String),
      isOpened: json['is_opened'] as bool? ?? false,
    );

Map<String, dynamic> _$FaqModelToJson(FaqModel instance) => <String, dynamic>{
      'uuid': instance.uuid,
      'title': instance.title,
      'title_ar': instance.titleAr,
      'description': instance.description,
      'description_ar': instance.descriptionAr,
      'domain_id': instance.domainId,
      'domain_name': instance.domainName,
      'domain_name_ar': instance.domainNameAr,
      'general': instance.general,
      'order': instance.order,
      'active': instance.active,
      'created_time': instance.createdTime?.toIso8601String(),
      'updated_time': instance.updatedTime?.toIso8601String(),
      'is_opened': instance.isOpened,
    };
