import 'package:json_annotation/json_annotation.dart';

part 'chat_thread_create_model.g.dart';

@JsonSerializable()
class ChatThreadCreateModel {
  ChatThreadCreateModel({
    this.status,
    this.message,
    this.uuid,
    this.createdTime,
    this.ticketId,
    this.chatDisabled,
    this.chatThreadClosed,
  });

  factory ChatThreadCreateModel.fromJson(Map<String, dynamic> json) =>
      _$ChatThreadCreateModelFromJson(json);

  @Json<PERSON>ey(name: 'status')
  String? status;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'message')
  String? message;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'uuid')
  String? uuid;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_time')
  String? createdTime;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'ticket_id')
  String? ticketId;
  @J<PERSON><PERSON><PERSON>(name: 'chat_disabled')
  bool? chatDisabled;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'chat_thread_closed')
  bool? chatThreadClosed;

  Map<String, dynamic> toJson() => _$ChatThreadCreateModelToJson(this);
}
