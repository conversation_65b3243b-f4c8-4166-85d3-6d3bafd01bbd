import 'package:json_annotation/json_annotation.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/chat_thread/chat_thread_model.dart';

part 'chat_thread_list_model.g.dart';

@JsonSerializable()
class ChatThreadListModel {
  ChatThreadListModel({
    this.chatThreadList,
  });

  factory ChatThreadListModel.fromJson(Map<String, dynamic> json) =>
      _$ChatThreadListModelFromJson(json);

  @JsonKey(name: 'data')
  List<ChatThreadModel>? chatThreadList;

  Map<String, dynamic> toJson() => _$ChatThreadListModelToJson(this);
}
