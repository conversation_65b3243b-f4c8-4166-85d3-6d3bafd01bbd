// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_thread_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ChatThreadModel _$ChatThreadModelFromJson(Map<String, dynamic> json) =>
    ChatThreadModel(
      domain: json['domain'] as String?,
      domainId: json['domain_id'] as String?,
      theme: json['theme'] as String?,
      subTheme: json['sub_theme'] as String?,
      subject: json['subject'] as String?,
      latestMessage: json['latest_message'] == null
          ? null
          : MessageModel.fromJson(
              json['latest_message'] as Map<String, dynamic>),
      uuid: json['uuid'] as String?,
      indicatorNodeId: json['indicator_node_id'] as String?,
      indicatorAppType: json['indicator_app_type'] as String?,
      indicatorContentType: json['indicator_content_type'] as String?,
      indicatorKey: json['indicator_key'] as String?,
      indicatorName: json['indicator_name'] as String?,
      createdTime: json['created_time'] as String?,
      updatedTime: json['updated_time'] as String?,
      ticketId: json['ticket_id'] as String?,
    )
      ..chatDisabled = json['chat_disabled'] as bool?
      ..chatThreadClosed = json['chat_thread_closed'] as bool?;

Map<String, dynamic> _$ChatThreadModelToJson(ChatThreadModel instance) =>
    <String, dynamic>{
      'domain': instance.domain,
      'domain_id': instance.domainId,
      'theme': instance.theme,
      'sub_theme': instance.subTheme,
      'subject': instance.subject,
      'latest_message': instance.latestMessage,
      'uuid': instance.uuid,
      'indicator_node_id': instance.indicatorNodeId,
      'indicator_app_type': instance.indicatorAppType,
      'indicator_content_type': instance.indicatorContentType,
      'indicator_key': instance.indicatorKey,
      'indicator_name': instance.indicatorName,
      'created_time': instance.createdTime,
      'updated_time': instance.updatedTime,
      'ticket_id': instance.ticketId,
      'chat_disabled': instance.chatDisabled,
      'chat_thread_closed': instance.chatThreadClosed,
    };
