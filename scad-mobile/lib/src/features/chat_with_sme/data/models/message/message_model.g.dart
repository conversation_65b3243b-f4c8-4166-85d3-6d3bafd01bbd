// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'message_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MessageModel _$MessageModelFromJson(Map<String, dynamic> json) => MessageModel(
      uuid: json['uuid'] as String?,
      sender: json['sender'] == null
          ? null
          : Recipient.fromJson(json['sender'] as Map<String, dynamic>),
      message: json['message'] as String?,
      recipient: json['recipient'] == null
          ? null
          : Recipient.fromJson(json['recipient'] as Map<String, dynamic>),
      isRead: json['is_read'] as bool?,
      createdTime: json['created_time'] as String?,
      updatedTime: json['updated_time'] as String?,
      attachment: json['attachment'] as String?,
      feedback: json['feedback'] == null
          ? null
          : FeedbackModel.fromJson(json['feedback'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$MessageModelToJson(MessageModel instance) =>
    <String, dynamic>{
      'uuid': instance.uuid,
      'sender': instance.sender,
      'message': instance.message,
      'recipient': instance.recipient,
      'is_read': instance.isRead,
      'created_time': instance.createdTime,
      'updated_time': instance.updatedTime,
      'attachment': instance.attachment,
      'feedback': instance.feedback,
    };
