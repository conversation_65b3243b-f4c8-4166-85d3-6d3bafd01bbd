import 'package:json_annotation/json_annotation.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/message/feedback_model.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/message/recipient_model.dart';

part 'message_model.g.dart';

@JsonSerializable()
class MessageModel {
  MessageModel({
    this.uuid,
    this.sender,
    this.message,
    this.recipient,
    this.isRead,
    this.createdTime,
    this.updatedTime,
    this.attachment,
    this.feedback,
  });

  factory MessageModel.fromJson(Map<String, dynamic> json) =>
      _$MessageModelFromJson(json);

  @<PERSON><PERSON><PERSON><PERSON>(name: 'uuid')
  String? uuid;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'sender')
  Recipient? sender;
  @Json<PERSON>ey(name: 'message')
  String? message;
  @<PERSON>son<PERSON>ey(name: 'recipient')
  Recipient? recipient;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_read')
  bool? isRead;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_time')
  String? createdTime;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_time')
  String? updatedTime;
  @J<PERSON><PERSON><PERSON>(name: 'attachment')
  String? attachment;
  @Json<PERSON>ey(name: 'feedback')
  FeedbackModel? feedback;

  Map<String, dynamic> toJson() => _$MessageModelToJson(this);
}
