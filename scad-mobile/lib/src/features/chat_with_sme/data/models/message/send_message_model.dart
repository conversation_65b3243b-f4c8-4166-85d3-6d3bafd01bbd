import 'package:json_annotation/json_annotation.dart';

part 'send_message_model.g.dart';

@JsonSerializable()
class SendMessageModel {
  SendMessageModel({
    this.status,
    this.message,
    this.uuid,
    this.attachment,
    this.chatDisabled,
    this.chatThreadClosed,
  });

  factory SendMessageModel.fromJson(Map<String, dynamic> json) =>
      _$SendMessageModelFromJson(json);

  @Json<PERSON>ey(name: 'status')
  String? status;
  @<PERSON>son<PERSON>ey(name: 'message')
  String? message;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'uuid')
  String? uuid;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'attachment')
  String? attachment;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'chat_disabled')
  bool? chatDisabled;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'chat_thread_closed')
  bool? chatThreadClosed;

  Map<String, dynamic> toJson() => _$SendMessageModelToJson(this);
}
