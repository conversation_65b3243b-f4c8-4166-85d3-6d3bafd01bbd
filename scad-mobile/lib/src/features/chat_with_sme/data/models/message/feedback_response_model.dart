import 'package:json_annotation/json_annotation.dart';
part 'feedback_response_model.g.dart';

@JsonSerializable()
class FeedbackResponseModel {
  FeedbackResponseModel({
    this.status,
    this.message,
    this.chatDisabled,
    this.chatThreadClosed,
  });

  factory FeedbackResponseModel.fromJson(Map<String, dynamic> json) =>
      _$FeedbackResponseModelFromJson(json);

  @Json<PERSON>ey(name: 'status')
  String? status;
  @Json<PERSON>ey(name: 'message')
  String? message;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'chat_disabled')
  bool? chatDisabled;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'chat_thread_closed')
  bool? chatThreadClosed;

  Map<String, dynamic> toJson() => _$FeedbackResponseModelToJson(this);
}
