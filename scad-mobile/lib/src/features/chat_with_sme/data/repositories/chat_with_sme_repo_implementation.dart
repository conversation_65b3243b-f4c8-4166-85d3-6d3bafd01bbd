import 'dart:async';
import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/datasources/chat_with_sme_end_points.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/chat_thread/chat_thread_create_model.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/chat_thread/chat_thread_list_model.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/faq/faq_list_model.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/message/feedback_response_model.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/message/message_list_model.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/message/send_message_model.dart';
import 'package:scad_mobile/src/features/chat_with_sme/domain/repositories/chat_with_sme_repository_imports.dart';
import 'package:scad_mobile/src/services/http_service_impl.dart';
import 'package:scad_mobile/src/services/http_services.dart';
import 'package:scad_mobile/src/utils/extentions/string_extentions.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class ChatWithSmeRepoImplementation extends ChatWithSmeRepository {
  ChatWithSmeRepoImplementation() {
    _httpService = HttpServiceRequests();
  }

  late HttpService _httpService;

  @override
  Future<RepoResponse<FaqListModel>> getFaqList({
    required String domainFilter,
  }) async {
    try {
      final response = await _httpService.get(
          '${ChatWithSmeEndPoints.faqListEndPoint}?domain_filter=$domainFilter');
      if (response.isSuccess) {
        return RepoResponse<FaqListModel>.success(
          response: FaqListModel.fromJson(response.response),
        );
      } else {
        return RepoResponse<FaqListModel>.error(
          errorMessage: response.message,
        );
      }
    } catch (e,s) {
      Completer<dynamic>().completeError(e,s);
      return RepoResponse<FaqListModel>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  // @override
  // Future<RepoResponse<DomainThemeSubThemeModelResponse>> getDomainList() async {
  //   try {
  //     if(isDemoMode){
  //       return RepoResponse<DomainThemeSubThemeModelResponse>.success(
  //         response:
  //         DomainThemeSubThemeModelResponse.fromJson({'data': demoIfpNavigationSuccess} as Map<String,dynamic>),
  //       );
  //     }
  //
  //     final response =
  //         await _httpService.get(ChatWithSmeEndPoints.getDomainList);
  //     if (response.isSuccess) {
  //       return RepoResponse<DomainThemeSubThemeModelResponse>.success(
  //         response:
  //             DomainThemeSubThemeModelResponse.fromJson(response.response),
  //       );
  //     } else {
  //       return RepoResponse<DomainThemeSubThemeModelResponse>.error(
  //         errorMessage: response.message,
  //       );
  //     }
  //   } catch (e,s) {
  //     Completer<dynamic>().completeError(e,s);
  //     return RepoResponse<DomainThemeSubThemeModelResponse>.error(
  //       errorMessage: LocaleKeys.somethingWentWrong.tr(),
  //     );
  //   }
  // }

  @override
  Future<RepoResponse<ChatThreadCreateModel>> createChatThread({
    required String domain,
    required int domainId,
    required String theme,
    required String subTheme,
    required String subject,
  }) async {
    try {
      final response = await _httpService.postJson(
        ChatWithSmeEndPoints.createChatThreadEndPoint,
        jsonPayloadMap: {
          'domain': domain,
          'domain_id': domainId,
          'theme': theme,
          'sub_theme': subTheme,
          'subject': subject,
        },
      );
      if (response.isSuccess) {
        return RepoResponse<ChatThreadCreateModel>.success(
          response: ChatThreadCreateModel.fromJson(response.response),
        );
      } else {
        return RepoResponse<ChatThreadCreateModel>.error(
          errorMessage: response.message,
        );
      }
    } catch (e,s) {
      Completer<dynamic>().completeError(e,s);
      return RepoResponse<ChatThreadCreateModel>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<ChatThreadListModel>> getChatThreadList() async {
    try {
      final response =
          await _httpService.get(ChatWithSmeEndPoints.chatThreadListEndPoint);
      if (response.isSuccess) {
        return RepoResponse<ChatThreadListModel>.success(
          response: ChatThreadListModel.fromJson(response.response),
        );
      } else {
        return RepoResponse<ChatThreadListModel>.error(
          errorMessage: response.message,
        );
      }
    } catch (e,s) {
      Completer<dynamic>().completeError(e,s);
      return RepoResponse<ChatThreadListModel>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<MessageListModel>> getMessageList({
    required String chatThreadId,
  }) async {
    final String endPoint =
        ChatWithSmeEndPoints.messageListEndPoint.setUrlParams(
      {'chatThreadId': chatThreadId},
    );

    try {
      final response = await _httpService.get(endPoint);
      if (response.isSuccess) {
        return RepoResponse<MessageListModel>.success(
          response: MessageListModel.fromJson(response.response),
        );
      } else {
        return RepoResponse<MessageListModel>.error(
          errorMessage: response.message,
        );
      }
    } catch (e,s) {
      Completer<dynamic>().completeError(e,s);
      return RepoResponse<MessageListModel>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<SendMessageModel>> sendMessage({
    required String chatThreadId,
    required Map<String, dynamic> messageData,
    File? attachment,
  }) async {
    final String endPoint =
        ChatWithSmeEndPoints.sendMessageEndPoint.setUrlParams(
      {'chatThreadId': chatThreadId},
    );

    try {
      final response = await _httpService.postMultipart(
        endPoint,
        formDataPayload: messageData,
        filePayload: attachment == null ? {} : {'attachment': attachment},
      );
      if (response.isSuccess) {
        return RepoResponse<SendMessageModel>.success(
          response: SendMessageModel.fromJson(response.response),
        );
      } else {
        return RepoResponse<SendMessageModel>.error(
          errorMessage: response.message,
        );
      }
    } catch (e,s) {
      Completer<dynamic>().completeError(e,s);
      return RepoResponse<SendMessageModel>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<FeedbackResponseModel>> sendFeedback({
    required String messageId,
    required Map<String, dynamic> messageData,
  }) async {
    final String endPoint =
        ChatWithSmeEndPoints.sendFeedbackEndPoint.setUrlParams(
      {'messageId': messageId},
    );

    try {
      final response = await _httpService.postJson(
        endPoint,
        jsonPayloadMap: messageData,
      );
      if (response.isSuccess) {
        return RepoResponse<FeedbackResponseModel>.success(
          response: FeedbackResponseModel.fromJson(response.response),
        );
      } else {
        return RepoResponse<FeedbackResponseModel>.error(
          errorMessage: response.message,
        );
      }
    } catch (e,s) {
      Completer<dynamic>().completeError(e,s);
      return RepoResponse<FeedbackResponseModel>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  // @override
  // Future<RepoResponse<List<DomainModel>>> getDomainListFaq() async {
  //   try {
  //     final response =
  //         await _httpService.get(ChatWithSmeEndPoints.getDomainListFaq);
  //     if (response.isSuccess) {
  //       final List<DomainModel> list = [];
  //       final List<dynamic> data = response.response['data'] as List<dynamic>;
  //
  //       for (int i = 0; i < data.length; i++) {
  //         list.add(DomainModel.fromJson(data[i] as Map<String, dynamic>));
  //       }
  //       return RepoResponse<List<DomainModel>>.success(
  //         response: list,
  //       );
  //     } else {
  //       return RepoResponse<List<DomainModel>>.error(
  //         errorMessage: response.message,
  //       );
  //     }
  //   } catch (e,s) {
  //     Completer<dynamic>().completeError(e,s);
  //     return RepoResponse<List<DomainModel>>.error(
  //       errorMessage:LocaleKeys.somethingWentWrong.tr(),
  //     );
  //   }
  // }
}
