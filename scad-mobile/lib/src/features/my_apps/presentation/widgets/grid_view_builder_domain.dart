import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/features/domains/data/models/domain_model/domain_model.dart';
import 'package:scad_mobile/src/features/domains/presentation/pages/themes_page.dart';
import 'package:scad_mobile/src/features/home/<USER>/widgets/persistent_bottom_nav_bar/persistent_bottom_nav_bar_v2.dart';
import 'package:scad_mobile/src/features/my_apps/data/models/response/collection_list_response.dart';
import 'package:scad_mobile/src/features/my_apps/presentation/widgets/domain_button_and_name.dart';

class GridViewBuilderDomain extends StatefulWidget {
  const GridViewBuilderDomain({
    required this.domains,
    super.key,
    this.orientation = Orientation.portrait,
    this.collection,
  });

  final Orientation orientation;
  final List<DomainModel> domains;
  final CollectionResult? collection;

  @override
  State<GridViewBuilderDomain> createState() => _GridViewBuilderDomainState();
}

class _GridViewBuilderDomainState extends State<GridViewBuilderDomain> {
  @override
  Widget build(BuildContext context) {
    final double width = (MediaQuery.sizeOf(context).width - 48) / 3;
    return Padding(
      padding: const EdgeInsets.only(top: 20),
      child: Wrap(
          crossAxisAlignment: WrapCrossAlignment.center,
          alignment: WrapAlignment.spaceBetween,
          runSpacing: 14,
          children: [
            ...List.generate(
              widget.domains.length,
              (index) => SizedBox(
                width: width,
                child: DomainButtonAndName(
                  name: widget.domains[index].domainName ?? '',
                  icon: widget.domains[index].domainIcon ?? '',
                  onTap: () {
                    Navigator.pop(context);
                    pushScreen(
                      context,
                      withNavBar: true,
                      screen: ThemesPage(
                        domainId: widget.domains[index].domainId ?? '',
                        collection: widget.collection,
                      ),
                    );
                    // AutoRouter.of(context).push(
                    //   ThemesPageRoute(
                    //     domainId: widget.domains[index].domainId ?? '',
                    //     collection: widget.collection,
                    //   ),
                    // );
                  },
                ),
              ),
            ),
          ]),
    );
    return GridView.builder(
      padding: const EdgeInsets.only(top: 20),
      shrinkWrap: true,
      itemCount: widget.domains.length,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisSpacing: 14,
        mainAxisSpacing: 14,
        crossAxisCount: widget.orientation == Orientation.portrait ? 3 : 4,
      ),
      itemBuilder: (_, int index) {
        return DomainButtonAndName(
          name: widget.domains[index].domainName ?? '',
          icon: widget.domains[index].domainIcon ?? '',
          onTap: () {
            Navigator.pop(context);
            AutoRouter.of(context).push(
              ThemesPageRoute(
                domainId: widget.domains[index].domainId ?? '',
                collection: widget.collection,
              ),
            );
          },
        );
      },
    );
  }
}
