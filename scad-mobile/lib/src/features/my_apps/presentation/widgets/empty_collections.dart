// import 'package:flutter/material.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:scad_mobile/main.dart';
// import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
//
// class EmptyCollections extends StatelessWidget {
//   const EmptyCollections({super.key});
//
//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       mainAxisAlignment: MainAxisAlignment.center,
//       crossAxisAlignment: CrossAxisAlignment.center,
//       children: [
//         SvgPicture.asset(
//           'assets/images/myapps_icons.svg',
//           alignment: Alignment.center,
//         ),
//         const SizedBox(height: 14),
//           Text('You don\'t have any apps added',
//             style: TextStyle(
//                 color: AppColors.grey, fontSize: 16, fontWeight: FontWeight.w500),
//             textScaler: TextScaler.linear(textScaleFactor.value),
//           ),
//       ],
//     );
//   }
// }
