import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/widgets/bottom_sheet_top_notch.dart';
import 'package:scad_mobile/src/features/my_apps/data/models/request/create_collection_request.dart';
import 'package:scad_mobile/src/features/my_apps/presentation/bloc/myapps_bloc.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class CreateNewCollection extends StatefulWidget {
  const CreateNewCollection({super.key, this.uuid, this.currentCollectionName});

  final String? uuid;
  final String? currentCollectionName;

  @override
  State<CreateNewCollection> createState() => _CreateNewCollectionState();
}

class _CreateNewCollectionState extends State<CreateNewCollection> {
  TextEditingController controller = TextEditingController();

  @override
  void initState() {
    super.initState();

    if (widget.currentCollectionName != null) {
      controller.text = widget.currentCollectionName ?? '';
    }
  }

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Container(
      padding: const EdgeInsets.only(left: 24, right: 24, bottom: 30),
      width: double.infinity,
      decoration: BoxDecoration(
        color: isLightMode ? AppColors.white : AppColors.blueShade32,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 10),
              const BottomSheetTopNotch(),
              const SizedBox(height: 16),
              Padding(
                padding: const EdgeInsets.only(bottom: 6),
                child: InkWell(
                  onTap: () {
                    Navigator.of(context).maybePop();
                  },
                  borderRadius: BorderRadius.circular(8),
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(0, 10, 10, 10),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.arrow_back_ios_new_rounded,
                          color: AppColors.blue,
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 10),
                          child: Text(
                            widget.uuid == null
                                ? LocaleKeys.createStartNewCollection.tr()
                                : LocaleKeys.updateCollection.tr(),
                            style: AppTextStyles.s16w5cBlackShade1.copyWith(
                              color: isLightMode
                                  ? AppColors.blackShade1
                                  : AppColors.white,
                            ),
                            textScaler:
                                TextScaler.linear(textScaleFactor.value),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
          TextField(
            autofocus: true,
            controller: controller,
            style: isLightMode ? AppTextStyles.s14w4cblackShade4.copyWith(
              fontSize: 16 * textScaleFactor.value,
            ) :  TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w400,
            color: AppColors.white,
          ),
            decoration: InputDecoration(
              contentPadding: const EdgeInsets.only(
                left: 10,
                right: 10,
                bottom: 15,
                top: 15,
              ),
              fillColor: isLightMode ? AppColors.whiteShade5 : AppColors.blueShade36,
              filled: true,
              border: OutlineInputBorder(
                borderSide: BorderSide(color: AppColors.greyShade1),
                borderRadius: const BorderRadius.all(Radius.circular(12)),
              ),
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: AppColors.greyShade1),
                borderRadius: const BorderRadius.all(Radius.circular(12)),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(color: AppColors.greyShade1),
                borderRadius: const BorderRadius.all(Radius.circular(12)),
              ),
              hintStyle: isLightMode ? AppTextStyles.s14w3cGrey.copyWith(
                fontSize: 16 * textScaleFactor.value,
              ) :   TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: AppColors.white,
            ),
              hintText: LocaleKeys.enterCollectionName.tr(),
            ),
          ),
          const SizedBox(height: 24),
          BlocConsumer<MyAppsBloc, MyAppsState>(
            listener: (context, state) {
              if (state is CreateCollectionSuccessState) {
                Navigator.pop(context, 'success');
                AppMessage.showOverlayNotification(
                  controller.text.trim(),
                  LocaleKeys.collectionCreated.tr(),
                );
              } else if (state is CreateCollectionFailureState) {
                AppMessage.showOverlayNotification(
                  '',
                  state.error,
                  msgType: 'error',
                );
              } else if (state is UpdateCollectionFailureState) {
                AppMessage.showOverlayNotification(
                  '',
                  state.error,
                  msgType: 'error',
                );
              } else if (state is UpdateCollectionSuccessState) {
                Navigator.pop(context, 'success');
                AppMessage.showOverlayNotification(
                  controller.text.trim(),
                  LocaleKeys.collectionUpdated.tr(),
                );
              }
            },
            builder: (context, state) {
              return TextButton(
                style: TextButton.styleFrom(
                  backgroundColor: isLightMode
                      ? AppColors.blueLight
                      : AppColors.blueLightOld,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                onPressed: () {
                  createCollection();
                },
                child: Text(
                  widget.uuid == null
                      ? LocaleKeys.save.tr()
                      : LocaleKeys.update.tr(),
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: AppColors.white,
                  ),
                  textScaler: TextScaler.linear(textScaleFactor.value),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  void createCollection() {
    if (controller.text.trim().isEmpty) {
      AppMessage.showOverlayNotification(
        '',
        LocaleKeys.emptyCollectionNameError.tr(),
        msgType: 'error',
      );
    } else if (controller.text.trim().toLowerCase() == LocaleKeys.all.tr()) {
      AppMessage.showOverlayNotification(
        '',
        LocaleKeys.collectionError.tr(),
        msgType: 'error',
      );
    } else if (widget.uuid == null) {
      context.read<MyAppsBloc>().add(
            CreateCollectionEvent(
              request: CreateCollectionRequest(name: controller.text),
            ),
          );
    } else if (controller.text.trim() == widget.currentCollectionName) {
      AppMessage.showOverlayNotification(
        '',
        LocaleKeys.duplicateCollectionName.tr(),
        msgType: 'error',
      );
    } else {
      context.read<MyAppsBloc>().add(
            UpdateCollectionEvent(
              uuid: widget.uuid!,
              name: controller.text,
            ),
          );
    }
  }
}
