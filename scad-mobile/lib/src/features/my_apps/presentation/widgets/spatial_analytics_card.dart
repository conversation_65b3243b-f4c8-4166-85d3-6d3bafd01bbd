import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/common/widgets/app_box_shadow.dart';
import 'package:scad_mobile/src/common/widgets/card_custom_clipper.dart';
import 'package:scad_mobile/src/features/spatial_analytics/presentation/pages/spatial_analytics_screen.dart';
import 'package:scad_mobile/src/features/spatial_analytics/presentation/widgets/my_apps_and_notifications.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class SpatialAnalyticsCard extends StatelessWidget {
  SpatialAnalyticsCard({
    required String id,
    super.key,
    this.postMyAppButtonTap,
  }) {
    this.id = id.split('-')[1];
  }

  final void Function(bool isRemoved)? postMyAppButtonTap;
  late final String id;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    final rtl = DeviceType.isDirectionRTL(context);
    return GestureDetector(
      onTap: () {
        AutoRouter.of(context).push(
          SpatialAnalyticsScreenRoute(initialModuleKey: id),
        );
      },
      child: SizedBox(
        child: Stack(
          children: [
            Positioned(
              right: rtl ? null : 0,
              left: rtl ? 0 : null,
              top: 0,
              height: 40,
              width: 40,
              child: Container(
                decoration: ShapeDecoration(
                  gradient: LinearGradient(
                    begin: const Alignment(-0.65, -0.76),
                    end: const Alignment(0.65, 0.76),
                    colors: [AppColors.blueShade11, AppColors.blueShade12],
                  ),
                  shape: const OvalBorder(),
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(100),
                    onTap: () {},
                    child: Padding(
                      padding: const EdgeInsets.all(5),
                      child: RotatedBox(
                        quarterTurns:
                            DeviceType.isDirectionRTL(context) ? -1 : 0,
                        child: Icon(
                          Icons.arrow_outward_rounded,
                          color: AppColors.white,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
            Container(
              decoration: const BoxDecoration(
                boxShadow: [
                  BoxShadow(
                    color: Color(0x0A4E4F51),
                    blurRadius: 5,
                    offset: Offset(4, 1),
                  ),
                ],
              ),
              child: ClipPath(
                clipper: CardCustomClipper(isRtl: rtl),
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: isLightMode
                        ? AppColors.greyShade7
                        : AppColors.blueShade32,
                    boxShadow: isLightMode ? AppBox.shadow() : null,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: Row(
                                    children: [
                                      ClipRRect(
                                        borderRadius: BorderRadius.circular(20),
                                        child: SvgPicture.asset(
                                          AppImages.icGeoSpatial,
                                          width: 18,
                                          height: 18,
                                          colorFilter: ColorFilter.mode(AppColors.green, BlendMode.color,),
                                        ),
                                      ),
                                      const SizedBox(
                                        width: 10,
                                      ),
                                      Text(
                                        LocaleKeys.spatialAnalysis.tr(),
                                        style: TextStyle(
                                          color: isLightMode
                                              ? AppColors.grey
                                              : AppColors.white,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w400,
                                        ),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        textScaler: TextScaler.linear(
                                          textScaleFactor.value,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                MyAppsAndNotification(
                                  postMyAppButtonTap: postMyAppButtonTap,
                                  moduleKey: id,
                                ),
                                const SizedBox(
                                  width: 44,
                                ),
                              ],
                            ),
                            _spatialModule(isLightMode),
                            const SizedBox(
                              height: 4,
                            ),
                          ],
                        ),
                      ),
                      ClipRRect(
                        borderRadius: BorderRadius.circular(15),
                        child: SizedBox(
                          height: 100,
                          child: Image.asset(
                            AppImages.imgPlaceholderArcGIS,
                            fit: BoxFit.fitWidth,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _spatialModule(bool isLightMode) {
    String selectedSubTheme = '--';
    for (final theme in SpatialAnalyticsScreen.modules.data.values) {
      final int i = theme.sub.indexWhere((element) => element.key == id);
      if (i >= 0) {
        selectedSubTheme = (HiveUtilsSettings.getAppLanguage() == 'en'
                ? theme.sub[i].titleEn
                : theme.sub[i].titleAr) ??
            '--';
        break;
      }
    }

    return Text(
      selectedSubTheme,
      style: TextStyle(
        color: isLightMode ? AppColors.black : AppColors.white,
        fontSize: 14,
        fontWeight: FontWeight.w500,
      ),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
      textScaler: TextScaler.linear(
        textScaleFactor.value,
      ),
    );
  }
}
