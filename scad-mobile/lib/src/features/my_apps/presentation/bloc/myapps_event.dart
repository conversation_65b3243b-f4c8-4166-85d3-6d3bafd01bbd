part of 'myapps_bloc.dart';

abstract class MyAppsEvent extends Equatable {
  const MyAppsEvent();

  @override
  List<Object> get props => [];
}

class MyAppsLoadEvent extends MyAppsEvent {
  const MyAppsLoadEvent({required this.pageNo});

  final int pageNo;

  @override
  List<Object> get props => [pageNo];
}

class CollectionsLoadEvent extends MyAppsEvent {
  const CollectionsLoadEvent({this.showLoader = true});

  final bool showLoader;

  @override
  List<Object> get props => [];
}

class AddToMyAppsEvent extends MyAppsEvent {
  const AddToMyAppsEvent({required this.myAppsRequest});

  final AddToMyAppsRequest myAppsRequest;

  @override
  List<Object> get props => [myAppsRequest];
}

class RemoveFromMyAppsEvent extends MyAppsEvent {
  const RemoveFromMyAppsEvent({
    required this.uuid,
    required this.id,
    this.title,
  });

  final String id;
  final String uuid;
  final String? title;

  @override
  List<Object> get props => [id, uuid, title!];
}

class ReloadIndicatorListEvent extends MyAppsEvent {
  const ReloadIndicatorListEvent({required this.collectionUuid});

  final String collectionUuid;

  @override
  List<Object> get props => [collectionUuid];
}

class RemoveIndicatorFromListEvent extends MyAppsEvent {
  const RemoveIndicatorFromListEvent({required this.id});

  final String id;

  @override
  List<Object> get props => [id];
}

class CreateCollectionEvent extends MyAppsEvent {
  const CreateCollectionEvent({required this.request});

  final CreateCollectionRequest request;

  @override
  List<Object> get props => [];
}

class UpdateCollectionEvent extends MyAppsEvent {
  const UpdateCollectionEvent({required this.name, required this.uuid});

  final String uuid;
  final String name;

  @override
  List<Object> get props => [];
}

class DeleteCollectionEvent extends MyAppsEvent {
  const DeleteCollectionEvent({required this.title, required this.uuid});

  final String uuid;
  final String title;

  @override
  List<Object> get props => [];
}

class CollectionSelectionEvent extends MyAppsEvent {
  const CollectionSelectionEvent({required this.selectedCollection});

  final CollectionResult selectedCollection;

  @override
  List<Object> get props => [];
}

class GetCollectionIndicatorsEvent extends MyAppsEvent {
  const GetCollectionIndicatorsEvent({
    required this.pageNo,
    required this.collectionUuid,
  });

  final String collectionUuid;
  final int pageNo;

  @override
  List<Object> get props => [pageNo, collectionUuid];
}
