part of 'myapps_bloc.dart';

abstract class MyAppsState extends Equatable {
  const MyAppsState();

  @override
  List<Object> get props => [];
}

class MyAppsLoadingState extends MyAppsState {}

class ReloadIndicatorListState extends MyAppsState {
  const ReloadIndicatorListState({required this.collectionUuid});

  final String collectionUuid;

  @override
  List<Object> get props => [collectionUuid];
}

class RemoveIndicatorFromListState extends MyAppsState {
  const RemoveIndicatorFromListState({required this.id});

  final String id;

  @override
  List<Object> get props => [id];
}

class MyAppsSuccessState extends MyAppsState {
  const MyAppsSuccessState({required this.myAppsResponse});

  final MyAppsListResponse myAppsResponse;

  @override
  List<Object> get props => [myAppsResponse];
}

class MyAppsFailureState extends MyAppsState {
  const MyAppsFailureState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}

class CollectionsLoadingState extends MyAppsState {}

class CollectionsShowResponseState extends MyAppsState {
  const CollectionsShowResponseState({required this.collectionListResponse});

  final List<CollectionResult> collectionListResponse;

  @override
  List<Object> get props => [collectionListResponse];
}

class CollectionSelectionState extends MyAppsState {
  const CollectionSelectionState({required this.selectedCollection});

  final CollectionResult selectedCollection;

  @override
  List<Object> get props => [selectedCollection];
}

class CollectionsFailureState extends MyAppsState {
  const CollectionsFailureState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}

class AddToMyAppsLoadingState extends MyAppsState {}

class MyAppsStatusSuccessResponseState extends MyAppsState {
  const MyAppsStatusSuccessResponseState({ required this.isRemoved, required this.id, required this.message});

  final bool isRemoved;
  final String id;
  final String message;

  @override
  List<Object> get props => [id,message];
}

class AddToMyAppsFailureState extends MyAppsState {
  const AddToMyAppsFailureState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}

class CreateCollectionLoadingState extends MyAppsState {}

class CreateCollectionSuccessState extends MyAppsState {
  const CreateCollectionSuccessState({required this.createCollectionResponse});

  final CreateCollectionResponse createCollectionResponse;

  @override
  List<Object> get props => [createCollectionResponse];
}

class CreateCollectionFailureState extends MyAppsState {
  const CreateCollectionFailureState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}

class UpdateCollectionLoadingState extends MyAppsState {
  const UpdateCollectionLoadingState({required this.uuid});

  final String uuid;

  @override
  List<Object> get props => [uuid];
}

class UpdateCollectionSuccessState extends MyAppsState {
  const UpdateCollectionSuccessState({required this.createCollectionResponse});

  final CreateCollectionResponse createCollectionResponse;

  @override
  List<Object> get props => [createCollectionResponse];
}

class UpdateCollectionFailureState extends MyAppsState {
  const UpdateCollectionFailureState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}

class DeleteCollectionLoadingState extends MyAppsState {
  const DeleteCollectionLoadingState({required this.uuid});

  final String uuid;

  @override
  List<Object> get props => [uuid];
}

class DeleteCollectionSuccessState extends MyAppsState {
  const DeleteCollectionSuccessState(
      {required this.title, required this.deleteCollectionResponseMessage});

  final String deleteCollectionResponseMessage;
  final String title;

  @override
  List<Object> get props => [deleteCollectionResponseMessage];
}

class DeleteCollectionFailureState extends MyAppsState {
  const DeleteCollectionFailureState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}
