import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/models/response_models/api_response.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/features/my_apps/data/data_sources/api_end_points.dart';
import 'package:scad_mobile/src/features/my_apps/data/models/request/add_to_myapps_request.dart';
import 'package:scad_mobile/src/features/my_apps/data/models/request/create_collection_request.dart';
import 'package:scad_mobile/src/features/my_apps/data/models/response/add_to_myapps_response.dart';
import 'package:scad_mobile/src/features/my_apps/data/models/response/collection_list_response.dart';
import 'package:scad_mobile/src/features/my_apps/data/models/response/create_collection_response.dart';
import 'package:scad_mobile/src/features/my_apps/data/models/response/myapps_list_response.dart';
import 'package:scad_mobile/src/features/my_apps/domain/repositories/myapps_repository_imports.dart';
import 'package:scad_mobile/src/services/http_service_impl.dart';
import 'package:scad_mobile/src/services/http_services.dart';
import 'package:scad_mobile/src/utils/extentions/string_extentions.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class MyAppsRepositoryImpl implements MyAppsRepository {
  MyAppsRepositoryImpl() {
    _httpService = HttpServiceRequests();
  }

  late HttpService _httpService;

  @override
  Future<RepoResponse<MyAppsListResponse>> myAppsList(
    RequestParamsMap requestParams,
  ) async {
    try {
      if (isDemoMode) {
        return RepoResponse<MyAppsListResponse>.success(
          response: MyAppsListResponse.fromJson(
              {"count": 0, "next": null, "previous": null, "results": []}),
        );
      }

      final String endpoint =
          MyAppsEndPoints.getMyAppsList.setUrlParams(requestParams);
      final ApiResponse response = await _httpService.get(
        endpoint,
        server: ApiServer.ifp,
      );
      if (response.isSuccess) {
        return RepoResponse<MyAppsListResponse>.success(
          response: MyAppsListResponse.fromJson(response.response),
        );
      } else {
        return RepoResponse<MyAppsListResponse>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<MyAppsListResponse>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<List<CollectionResult>>> collectionsList() async {
    try {
      if(isDemoMode){
        return RepoResponse<List<CollectionResult>>.success(
          response: [],
        );
      }

      final String endpoint = MyAppsEndPoints.getMyAppsCollectionList;
      final ApiResponse response = await _httpService.get(
        endpoint,
        server: ApiServer.ifp,
      );
      if (response.isSuccess) {
        List<CollectionResult> list = (response.response['data']
                as List<dynamic>)
            .map((e) => CollectionResult.fromJson(e as Map<String, dynamic>))
            .toList();

        return RepoResponse<List<CollectionResult>>.success(
          response: list,
        );
      } else {
        return RepoResponse<List<CollectionResult>>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<List<CollectionResult>>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<AddToMyAppsResponse>> addToMyApps(
    AddToMyAppsRequest payload,
  ) async {
    try {
      final String endpoint = MyAppsEndPoints.addToMyApps;
      final ApiResponse response = await _httpService.postJson(
        endpoint,
        server: ApiServer.ifp,
        jsonPayloadMap: payload.toJson(),
      );

      if (response.isSuccess) {
        return RepoResponse<AddToMyAppsResponse>.success(
          response: AddToMyAppsResponse.fromJson(response.response),
        );
      } else {
        return RepoResponse<AddToMyAppsResponse>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<AddToMyAppsResponse>.error(
        errorMessage:  LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<AddToMyAppsResponse>> removeFromMyApps(
    String uuid,
  ) async {
    try {
      final String endpoint = '${MyAppsEndPoints.removeFromMyApps}$uuid/';
      final ApiResponse response = await _httpService.delete(
        endpoint,
      );

      if (response.isSuccess) {
        return RepoResponse<AddToMyAppsResponse>.success(
          response: AddToMyAppsResponse.fromJson(response.response),
        );
      } else {
        return RepoResponse<AddToMyAppsResponse>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<AddToMyAppsResponse>.error(
        errorMessage:  LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<CreateCollectionResponse>> createCollection(
    CreateCollectionRequest payload,
  ) async {
    try {
      final String endpoint = MyAppsEndPoints.createMyAppsCollection;
      final ApiResponse response = await _httpService.postJson(
        endpoint,
        jsonPayloadMap: payload.toJson(),
      );

      if (response.isSuccess) {
        return RepoResponse<CreateCollectionResponse>.success(
          response: CreateCollectionResponse.fromJson(response.response),
        );
      } else {
        return RepoResponse<CreateCollectionResponse>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<CreateCollectionResponse>.error(
        errorMessage:  LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<CreateCollectionResponse>> updateCollection(
    String uuid,
    String name,
  ) async {
    try {
      final String endpoint = '${MyAppsEndPoints.updateMyAppsCollection}$uuid/';
      final ApiResponse response =
          await _httpService.putMultipart(endpoint, formDataPayload: {
        'name': name,
      });

      if (response.isSuccess) {
        return RepoResponse<CreateCollectionResponse>.success(
          response: CreateCollectionResponse.fromJson(response.response),
        );
      } else {
        return RepoResponse<CreateCollectionResponse>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<CreateCollectionResponse>.error(
        errorMessage:  LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<CreateCollectionResponse>> deleteCollection(
    String uuid,
  ) async {
    try {
      final String endpoint = '${MyAppsEndPoints.deleteMyAppsCollection}$uuid/';
      final ApiResponse response = await _httpService.delete(
        endpoint,
      );

      if (response.isSuccess) {
        return RepoResponse<CreateCollectionResponse>.success(
            response: CreateCollectionResponse.fromJson(response.response));
      } else {
        return RepoResponse<CreateCollectionResponse>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<CreateCollectionResponse>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<MyAppsListResponse>> collectionIndicatorsList(
    RequestParamsMap requestParams,
  ) async {
    try {
      final ApiResponse response = await _httpService.get(
        MyAppsEndPoints.getMyAppsCollectionIndicatorsList
            .setUrlParams(requestParams),
      );

      if (response.isSuccess) {
        return RepoResponse<MyAppsListResponse>.success(
          response: MyAppsListResponse.fromJson(response.response),
        );
      } else {
        return RepoResponse<MyAppsListResponse>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<MyAppsListResponse>.error(
        errorMessage:  LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }
}
