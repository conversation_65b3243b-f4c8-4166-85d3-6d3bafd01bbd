part of 'myapps_repository_imports.dart';

abstract class MyAppsRepository {
  Future<RepoResponse<MyAppsListResponse>> myAppsList(
    RequestParamsMap requestParams,
  );

  Future<RepoResponse<List<CollectionResult>>> collectionsList();

  Future<RepoResponse<CreateCollectionResponse>> createCollection(
    CreateCollectionRequest payload,
  );

  Future<RepoResponse<CreateCollectionResponse>> updateCollection(
    String uuid,
    String name,
  );

  Future<RepoResponse<CreateCollectionResponse>> deleteCollection(
    String uuid,
  );

  Future<RepoResponse<AddToMyAppsResponse>> addToMyApps(
    AddToMyAppsRequest payload,
  );

  Future<RepoResponse<AddToMyAppsResponse>> removeFromMyApps(
    String uuid,
  );

  Future<RepoResponse<MyAppsListResponse>> collectionIndicatorsList(
    RequestParamsMap requestParams,
  );
}
