import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/features/my_apps/data/models/request/add_to_myapps_request.dart';
import 'package:scad_mobile/src/features/my_apps/data/models/request/create_collection_request.dart';
import 'package:scad_mobile/src/features/my_apps/data/models/response/add_to_myapps_response.dart';
import 'package:scad_mobile/src/features/my_apps/data/models/response/collection_list_response.dart';
import 'package:scad_mobile/src/features/my_apps/data/models/response/create_collection_response.dart';
import 'package:scad_mobile/src/features/my_apps/data/models/response/myapps_list_response.dart';
import 'package:scad_mobile/src/services/http_services.dart';

part 'myapps_repository.dart';
