import 'dart:async';
import 'dart:ui' as ui;

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/common/widgets/app_sliding_tab.dart';
import 'package:scad_mobile/src/common/widgets/appbar/flat_app_bar.dart';
import 'package:scad_mobile/src/common/widgets/error_reload_placeholder.dart';
import 'package:scad_mobile/src/common/widgets/expandable_widget.dart';
import 'package:scad_mobile/src/common/widgets/expandable_widget_list_item.dart';
import 'package:scad_mobile/src/common/widgets/no_data_placeholder.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/showcaseview.dart';
import 'package:scad_mobile/src/config/app_config/api_config.dart';
import 'package:scad_mobile/src/features/domains/presentation/bloc/domains_bloc.dart';
import 'package:scad_mobile/src/features/products/data/models/forecast_response.dart';
import 'package:scad_mobile/src/features/products/data/models/product_dashboard.dart';
import 'package:scad_mobile/src/features/products/data/models/reports_response.dart';
import 'package:scad_mobile/src/features/products/data/models/response/product_response1.dart';
import 'package:scad_mobile/src/features/products/data/models/web_report_response.dart';
import 'package:scad_mobile/src/features/products/presentation/bloc/products_bloc.dart';
import 'package:scad_mobile/src/features/products/presentation/pages/forecast_list_page.dart';
import 'package:scad_mobile/src/features/products/presentation/pages/product_indicator_list_page.dart';
import 'package:scad_mobile/src/features/search/data/models/search_publications_response.dart'
    as publication;
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/app_utils/downloadHelper.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_api_cache.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/webview_flutter.dart';

@RoutePage()
class Products extends StatefulWidget {
  const Products({super.key, this.isFromDetailsScreen = false});

  final bool isFromDetailsScreen;

  @override
  State<Products> createState() => _ProductsState();
}

class _ProductsState extends State<Products>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  List<AppSlidingTabItem> slidingTabList = [];
  List<ItemsWebReport> listWebReport = [];
  List<publication.PublicationItem> listPublication = [];
  TabController? tabController;
  PageController pageController = PageController();

  ScrollController scrollControllerPublications = ScrollController();
  ScrollController dashboardScrollController = ScrollController();
  ScrollController webReportsScrollController = ScrollController();
  ScrollController scenarioScrollController = ScrollController();
  ScrollController insightsDiscoveryScrollController = ScrollController();
  ScrollController forecastScrollController = ScrollController();
  ScrollController reportsScrollController = ScrollController();

  int? countPublication;
  String? preselectTabType;
  bool publicationLoading = false;

  WebViewController? demoDashboardWebViewController;
  WebViewController? demoWebReportsWebViewController;

  @override
  bool get wantKeepAlive => true;
  int pageNoPublication = 1;
  String authToken = '';

  List<GlobalKey> steps = [];
  BuildContext? myContext;
  String tabKey = '0';

  @override
  void initState() {
    super.initState();

    if (isDemoMode) {
      demoDashboardWebViewController = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..loadRequest(Uri.parse('https://scad.gov.ae'));
      demoWebReportsWebViewController = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..loadRequest(
          Uri.parse(
            'https://scad.gov.ae/web/guest/w/abu-dhabi-s-non-oil-economy-expands-7-7-in-q3-and-8-6-in-first-9-months-of-20',
          ),
        );
    }

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _pageResetPublication();
      scrollControllerPublications.addListener(_loadDataPublication);
    });
    reload();
  }

  Future<void> reload() async {
    slidingTabList = [];

    context.read<ProductsBloc>().add(const ProductsLoadEvent());
    context.read<DomainsBloc>().add(const DomainsInitEvent());
  }

  ValueNotifier<int> selectedIndex = ValueNotifier(0);

  @override
  void dispose() {
    scrollControllerPublications.removeListener(_loadDataPublication);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    super.build(context);
    return ShowCaseWidget(
      builder: Builder(
        builder: (context) {
          myContext = context;
          return Scaffold(
            body: Column(
              children: [
                ValueListenableBuilder(
                  valueListenable: selectedIndex,
                  builder: (context, index, _) {
                    return FlatAppBar(
                      key: ValueKey(index.toString()),
                      title: LocaleKeys.disseminationProducts.tr(),
                      mainScreen: true,
                    );
                  },
                ),
                Expanded(
                  child: BlocConsumer<ProductsBloc, ProductsState>(
                    listener: (context, state) {
                      if(state is ProductsResetState){
                        tabController?.animateTo(0);
                        pageController.jumpToPage(0);
                        Future.delayed(const Duration(milliseconds: 240), () {
                          setState(() {});
                        });
                      }
                      if (state is ProductsSelectTabState) {
                        if ((tabController?.length ?? 0) > 0) {
                          for (int i = 0; i < slidingTabList.length; i++) {
                            if (slidingTabList[i].object.type == state.type) {
                              tabController?.animateTo(i);
                              pageController.jumpToPage(i);
                              break;
                            }
                          }
                        } else {
                          preselectTabType = state.type;
                        }
                      } else if (state is ProductsSuccessState) {
                        int initialIndex = 0;
                        for (int i = 0; i < state.products.length; i++) {
                          final ProductDashboard item = state.products[i];
                          if (item.type == 'geoSpatial') {
                            slidingTabList.add(
                              AppSlidingTabItem(
                                label: LocaleKeys.geoSpatial.tr(),
                                onTap: () async {
                                  await AutoRouter.of(context).push(
                                    SpatialAnalyticsScreenRoute(),
                                  );
                                },
                              ),
                            );
                          } else {
                            slidingTabList.add(
                              AppSlidingTabItem(
                                label: item.title,
                                object: item,
                              ),
                            );
                            if (item.type == preselectTabType) {
                              tabKey = '$preselectTabType';
                              initialIndex = i;
                              preselectTabType = null;
                              selectedIndex.value = i;
                            }
                          }
                        }

                        tabController = TabController(
                          length: slidingTabList.length,
                          vsync: this,
                          initialIndex: initialIndex,
                        );

                        if (HiveUtilsSettings.getUserGuideStatus() == UserGuides.Products) {
                          steps = [];
                          for (var i = 0; i <  slidingTabList.length; i++) {
                            steps.add(GlobalKey(debugLabel: 'steps-slidingTabList-$i'));
                          }
                          ShowCaseWidget.of(myContext!).startShowCase(steps);
                        }
                      }
                    },
                    builder: (context, state) {
                      if (state is ProductsLoadingState) {
                        return const Center(
                          child: CircularProgressIndicator(),
                        );
                      } else if (state is ProductsFailureState) {
                        return Center(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 100),
                            child: ErrorReloadPlaceholder(
                              error: state.error,
                              onReload: reload,
                            ),
                          ),
                        );
                      }
                      return Column(
                        children: [
                          AppSlidingTab(
                            key: Key('tab.$tabKey'),
                            userGuideStepsKeyList: steps,
                            horizontalPadding: 24,
                            onTabChange: (i) {
                              pageController.jumpToPage(i);
                              selectedIndex.value = i;
                            },
                            initialTabIndex: selectedIndex.value,
                            tabs: slidingTabList,
                            pageController: pageController,
                            tabController: tabController,
                            overrideTabOnTap:
                                HiveUtilsSettings.getUserGuideStatus() !=
                                    UserGuides.Products,
                          ),
                          const SizedBox(height: 20),
                          Divider(
                            height: 1,
                            color: isLightMode
                                ? AppColors.greyShade1
                                : AppColors.blackShade4,
                          ),
                          Expanded(
                            child: PageView(
                              controller: pageController,
                              onPageChanged: (i) {
                                selectedIndex.value = i;
                                if(slidingTabList[i].object != null) {
                                if (slidingTabList[i].object.type == 'pb' ||
                                    slidingTabList[i].object.type == 'wr') {
                                  context
                                      .read<ProductsBloc>()
                                      .add(const AuthentificationsEvent());
                                }
                                if (slidingTabList[i].object.type == 'pb') {
                                  _pageResetPublication();
                                }
                                // if (slidingTabList[i].object.type == 'Forecast') {
                                //   context
                                //       .read<ProductsBloc>()
                                //       .add(const GetForecastEvent());
                                // }
                                }
                                tabController?.animateTo(i);
                              },
                              children: [
                                ...slidingTabList
                                    .where(
                                  (element) =>
                                      (HiveUtilsSettings.getUserGuideStatus() ==
                                              UserGuides.Products)
                                          ? true
                                          : element.object != null,
                                ).map((e) {
                                  if(e.label == LocaleKeys.geoSpatial.tr()) {
                                    return const SizedBox();
                                  } else if ((e.object as ProductDashboard).type == 'td') {
                                    return _tableauDashboardsView(
                                      e.object.value
                                          as List<ProductDashboardResponseItem>,
                                      isLightMode,
                                    );
                                  } else if ((e.object as ProductDashboard)
                                          .type ==
                                      'pb') {
                                    return _publicationsView(
                                      e.object.value
                                          as List<ProductDashboardResponseItem>,
                                      isLightMode,
                                    );
                                  } else if ((e.object as ProductDashboard)
                                          .type ==
                                      'wr') {
                                    return _webReportsView(
                                      e.object.value
                                          as List<ProductDashboardResponseItem>,
                                      isLightMode,
                                    );
                                  } else if ((e.object as ProductDashboard)
                                          .type ==
                                      'Forecast') {
                                    final List<ForecastResponse> forcastList =
                                        (e.object.value)
                                            as List<ForecastResponse>;
                                    for (var i = 0;
                                        i < forcastList.length;
                                        i++) {
                                      forcastList.removeWhere(
                                        (element) =>
                                            element.nodes?.isEmpty ?? false,
                                      );
                                    }
                                    return _indicatorForecastList(
                                      forcastList,
                                    );
                                  } else if ((e.object as ProductDashboard)
                                          .type ==
                                      'Reports') {
                                    // return _indicatorReportList(
                                    //   e.object.value as List<ReportResponse>,
                                    // );
                                    return _reportsView(
                                      e.object.value as List<ReportResponse>,
                                      isLightMode,
                                    );
                                  } else {
                                    for (var i = 0;
                                        i <
                                            ((e.object.value as ProductResponse)
                                                        .domains ??
                                                    [])
                                                .length;
                                        i++) {
                                      final List<DomainsResponse> ele =
                                          ((e.object as ProductDashboard).value
                                                      as ProductResponse)
                                                  .domains ??
                                              [];

                                      ele[i].items?.removeWhere(
                                            (element) => [
                                              'eci_insights',
                                              'eci-insights',
                                              'basket-insights',
                                              'basket_insights',
                                            ].contains(
                                              element.appType?.toLowerCase(),
                                            ),
                                          );
                                      ele.removeWhere(
                                        (element) =>
                                            (element.items ?? []).isEmpty,
                                      );
                                    }

                                    return _indicatorProductList(
                                      e.object as ProductDashboard,
                                      isLightMode,
                                      (e.object as ProductDashboard).type ==
                                              'Forecasts'
                                          ? forecastScrollController
                                          : (e.object as ProductDashboard)
                                                      .type ==
                                                  'Scenario Drivers'
                                              ? scenarioScrollController
                                              : insightsDiscoveryScrollController,
                                    );
                                  }
                                  // return const SizedBox();
                                }),
                              ],
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _indicatorForecastList(List<ForecastResponse> forecasts) {
    return ForecastListPage(
      forecast: forecasts,
      scrollController: forecastScrollController,
    );
  }

  // Widget _indicatorReportList(List<ReportResponse> reports) {
  //   return ReportListPage(reports: reports);
  // }

  Widget _indicatorProductList(
    ProductDashboard product,
    bool isLightMode,
    ScrollController scrollController,
  ) {
    return ProductIndicatorListPage(
      product: product.value as ProductResponse,
      scrollController: scrollController,
    );
    /*return Column(
      children: [
        Container(
          height: 45,
          margin: const EdgeInsets.only(top: 24, left: 24, right: 24),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(10),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              IconButton(
                onPressed: () {
                  // if (value != 0) {
                  //   selectedDomainIndex.value--;
                  //   if (products.length <= 3) {
                  //     _onDomainChanged(
                  //       products[selectedProductIndex.value]
                  //           .domains[selectedDomainIndex.value],
                  //     );
                  //   }
                  // }
                },
                icon: Icon(
                  Icons.chevron_left_rounded,
                  size: 26,
                  color:
                      // value != 0 ? AppColors.blue :
                      AppColors.greyShade4,
                ),
              ),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SvgPicture.network(
                    '',
                    // HiveUtilsApiCache.getDomainImage(product.value[selectedProductIndex.value].domains[value].darkIcon),
                    height: 22,
                    width: 22,
                    color: AppColors.blueLight,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'products[selectedProductIndex.value].domains[value].name',
                    style: AppTextStyles.s18w5cBlackShade1,
                    textScaler: TextScaler.linear(textScaleFactor.value),
                  ),
                ],
              ),
              IconButton(
                onPressed: () {
                  // if (value + 1 <
                  //     products[selectedProductIndex.value]
                  //         .domains
                  //         .length) {
                  //   selectedDomainIndex.value++;
                  //
                  //   if (products.length <= 3) {
                  //     _onDomainChanged(
                  //       products[selectedProductIndex.value]
                  //           .domains[selectedDomainIndex.value],
                  //     );
                  //   }
                  // }
                },
                icon: Icon(
                  Icons.chevron_right_rounded,
                  size: 26,
                  color:
                      // value + 1 <
                      //     products[selectedProductIndex.value]
                      //         .domains
                      //         .length
                      //     ? AppColors.blueLight
                      //     :
                      AppColors.greyShade4,
                ),
              ),
            ],
          ),
        ),
      ],
    );*/
  }

  Widget _tableauDashboardsView(
    List<ProductDashboardResponseItem> dashboards,
    bool isLightMode,
  ) {
    if (isDemoMode) {
      return _tableauDashboardsViewDemo();
    }

    final rtl = DeviceType.isDirectionRTL(context);
    final List<Widget> list = [];

    final Map<String, List<ProductDashboardResponseItem>> map = {};

    for (int i = 0; i < dashboards.length; i++) {
      map[dashboards[i].domainId!] = [];
    }

    for (int i = 0; i < map.keys.length; i++) {
      map[map.keys.toList()[i]] =
          dashboards.where((e) => e.domainId == map.keys.toList()[i]).toList();

      list.add(
        Padding(
          padding: const EdgeInsets.only(bottom: 10),
          child: ExpandableWidget(
            title: rtl
                ? map[map.keys.toList()[i]]?.firstOrNull?.domainNameAr ?? ''
                : map[map.keys.toList()[i]]?.firstOrNull?.domainName ?? '',
            leadingIcon: Row(
              children: [
                // if (rtl) const SizedBox(width: 16),
                SizedBox(
                  width: 40,
                  child: Padding(
                    padding: EdgeInsets.only(left: rtl ? 10 : 0),
                    child: BlocBuilder<DomainsBloc, DomainsState>(
                      builder: (context, state) {
                        return SvgPicture.network(
                          HiveUtilsApiCache.getDomainImage(
                            map[map.keys.toList()[i]]?.firstOrNull?.domainId,
                          ),
                          height: 22,
                          width: 22,
                          colorFilter: isLightMode
                              ? null
                              : ColorFilter.mode(AppColors.white, BlendMode.srcIn),
                          // color: AppColors.blueLight,
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),

            // headerChild: Expanded(
            //   child: Row(
            //     children: [
            //       if (rtl) const SizedBox(width: 16),
            //       SizedBox(
            //         width: 40,
            //         child: Padding(
            //           padding: EdgeInsets.only(left: rtl ? 10 : 14),
            //           child: SvgPicture.network(
            //             HiveUtilsApiCache.getDomainImage(
            //               map[map.keys.toList()[i]]?.firstOrNull?.domainId,
            //             ),
            //             height: 22,
            //             width: 22,
            //             // color: AppColors.blueLight,
            //           ),
            //         ),
            //       ),
            //       Expanded(
            //         child: Padding(
            //           padding: const EdgeInsets.only(left: 16),
            //           child: Text(
            //             rtl
            //                 ? map[map.keys.toList()[i]]
            //                         ?.firstOrNull
            //                         ?.domainNameAr ??
            //                     ''
            //                 : map[map.keys.toList()[i]]
            //                         ?.firstOrNull
            //                         ?.domainName ??
            //                     '',
            //             style: const TextStyle(
            //               fontSize: 14,
            //               fontWeight: FontWeight.w500,
            //             ),
            //             textScaler: TextScaler.linear(textScaleFactor.value),
            //           ),
            //         ),
            //       ),
            //     ],
            //   ),
            // ),
            expandedChild: ListView.separated(
              itemCount: map[map.keys.toList()[i]]!.length,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              padding: const EdgeInsets.symmetric(
                vertical: 14,
                horizontal: 14,
              ),
              separatorBuilder: (context, index) => const SizedBox(height: 10),
              itemBuilder: (context, index) {
                final ProductDashboardResponseItem dbs =
                    map[map.keys.toList()[i]]![index];
                return ExpandableWidgetListItem(
                  title: rtl ? dbs.nameAr! : dbs.name!,
                  leadingIcon: rtl ? const SizedBox(width: 16) : null,
                  trailingIcon: Padding(
                    padding: rtl
                        ? const EdgeInsets.fromLTRB(0, 16, 16, 16)
                        : const EdgeInsets.all(16),
                    child: SvgPicture.asset(
                      AppImages.icLink,
                      colorFilter: ColorFilter.mode(
                        isLightMode ? AppColors.blueShade22 : Colors.white,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                  onTap: () {
                    context.pushRoute(
                      DashboardWebViewPageRoute(
                        title: rtl ? dbs.nameAr! : dbs.name!,
                        uuid: dbs.uuid!,
                      ),
                    );
                  },
                );
                // return Material(
                //   color:
                //   isLightMode ? AppColors.whiteShade8 : AppColors.greyBorder,
                //   borderRadius: BorderRadius.circular(10),
                //   child: InkWell(
                //     borderRadius: BorderRadius.circular(10),
                //     onTap: () {
                //       context.pushRoute(
                //         DashboardWebViewPageRoute(
                //           title: rtl ? dbs.nameAr! : dbs.name!,
                //           uuid: dbs.uuid!,
                //         ),
                //       );
                //     },
                //     child: Container(
                //       clipBehavior: Clip.antiAlias,
                //       decoration:BoxDecoration(
                //         borderRadius: BorderRadius.circular(10),
                //       ),
                //       padding: const EdgeInsets.only(
                //         left: 14,
                //         top: 5,
                //         bottom: 5,
                //       ),
                //       child: Row(
                //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //         children: [
                //           if (rtl) const SizedBox(width: 16),
                //           Expanded(
                //             child: Text(
                //               rtl ? dbs.nameAr! : dbs.name!,
                //               maxLines: 3,
                //               style: TextStyle(
                //                 color: isLightMode
                //                     ? AppColors.blueGreyShade1
                //                     : AppColors.white,
                //                 fontWeight: FontWeight.w500,
                //               ),
                //               textScaler:
                //                   TextScaler.linear(textScaleFactor.value),
                //             ),
                //           ),
                //           Padding(
                //             padding: rtl
                //                 ? const EdgeInsets.fromLTRB(0, 16, 16, 16)
                //                 : const EdgeInsets.all(16),
                //             child: SvgPicture.asset(AppImages.icLink,
                //             colorFilter:  ColorFilter.mode(
                //                isLightMode ?  AppColors.blueShade22 : Colors.white,
                //                 BlendMode.srcIn,
                //             ),
                //             ),
                //           ),
                //         ],
                //       ),
                //     ),
                //   ),
                // );
              },
            ),
          ),
        ),
      );
    }

    if (list.isEmpty) {
      return _noDataPlaceholder();
    } else {
      return ListView(
        shrinkWrap: true,
        controller: dashboardScrollController,
        padding: const EdgeInsets.fromLTRB(24, 24, 24, 150),
        children: list,
      );
    }
  }

  Widget _tableauDashboardsViewDemo() {
    // shows scad website for demo user
    return WebViewWidget(controller: demoDashboardWebViewController!);
  }

  Widget _webReportsDashboardsViewDemo() {
    // shows a news from scad website for demo user
    return WebViewWidget(controller: demoWebReportsWebViewController!);
  }

  Widget _reportsView(
    List<ReportResponse> reports,
    bool isLightMode,
  ) {
    final rtl = DeviceType.isDirectionRTL(context);
    return ListView.builder(
      controller: reportsScrollController,
      shrinkWrap: true,
      itemCount: reports.length,
      padding: const EdgeInsets.fromLTRB(24, 24, 24, 150),
      itemBuilder: (context, index) => Padding(
        padding: const EdgeInsets.only(bottom: 10),
        child: ExpandableWidget(
          title: reports[index].domain ?? '-',
          leadingIcon: SizedBox(
            width: 40,
            child: SvgPicture.network(
              HiveUtilsApiCache.getDomainImageByName(
                reports[index].domain,
                rtl,
              ),
              height: 22,
              width: 22,
              colorFilter: ColorFilter.mode(
                AppColors.blueLight,
                BlendMode.srcIn,
              ),
            ),
          ),
          // headerChild: Expanded(
          //   child: Row(
          //     children: [
          //       SizedBox(
          //         width: 40,
          //         child: Padding(
          //           padding: rtl ? const EdgeInsets.only(right: 14) : const EdgeInsets.only(left: 14),
          //           child: SvgPicture.network(
          //             HiveUtilsApiCache.getDomainImageByName(
          //               reports[index].domain,
          //               rtl,
          //             ),
          //             height: 22,
          //             width: 22,
          //             colorFilter: ColorFilter.mode(AppColors.blueLight, BlendMode.srcIn,),
          //           ),
          //         ),
          //       ),
          //       Expanded(
          //         child: Padding(
          //           padding:  rtl ? const EdgeInsets.only(right: 16) : const EdgeInsets.only(left: 16) ,
          //           child: Text(
          //             reports[index].domain ?? '-',
          //             style: const TextStyle(
          //               fontSize: 14,
          //               fontWeight: FontWeight.w500,
          //             ),
          //             textScaler: TextScaler.linear(textScaleFactor.value),
          //           ),
          //         ),
          //       ),
          //     ],
          //   ),
          // ),
          expandedChild: ListView.separated(
            itemCount: (reports[index].nodes ?? []).length,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: const EdgeInsets.symmetric(
              vertical: 14,
              horizontal: 14,
            ),
            separatorBuilder: (context, childIndex) =>
                const SizedBox(height: 10),
            itemBuilder: (context, childIndex) {
              final ReportNode node = (reports[index].nodes ?? [])[childIndex];

              return Material(
                color: AppColors.scaffoldBackgroundLight,
                borderRadius: BorderRadius.circular(10),
                child: InkWell(
                  borderRadius: BorderRadius.circular(10),
                  onTap: () {
                    context.pushRoute(
                      ReportWebViewPageRoute(
                        title: node.title!,
                        url: node.newsletterURL!,
                      ),
                    );
                  },
                  child: Container(
                    clipBehavior: Clip.antiAlias,
                    decoration: ShapeDecoration(
                      color: isLightMode ? Colors.white : AppColors.greyBorder,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    padding: rtl
                        ? const EdgeInsets.only(
                            right: 14,
                            top: 5,
                            bottom: 5,
                          )
                        : const EdgeInsets.only(
                            left: 14,
                            top: 5,
                            bottom: 5,
                          ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            node.title!,
                            maxLines: 3,
                            style: TextStyle(
                              color: isLightMode
                                  ? AppColors.blueGreyShade1
                                  : AppColors.white,
                              fontWeight: FontWeight.w500,
                            ),
                            textScaler:
                                TextScaler.linear(textScaleFactor.value),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.all(16),
                          child: SvgPicture.asset(
                            AppImages.icLink,
                            colorFilter: ColorFilter.mode(
                              isLightMode
                                  ? AppColors.blueShade22
                                  : Colors.white,
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _publicationsView(
    List<ProductDashboardResponseItem> dashboards,
    bool isLightMode,
  ) {
    return BlocConsumer<ProductsBloc, ProductsState>(
      listener: (BuildContext context, state) {
        if (state is AuthentificationSetSucessState) {
          authToken = state.data?.accessToken ?? '';
          context.read<ProductsBloc>().add(
                GetPublicationsEvent(
                  pageno: pageNoPublication.toString(),
                  token: state.data?.accessToken ?? '',
                ),
              );
        } else if (state is PublicationSetSucessState) {
          listPublication.addAll(state.data?.items ?? []);
          countPublication = state.data?.totalCount;
          publicationLoading = false;
        }
      },
      builder: (context, state) {
        if (state is AuthentificationLoadingState) {
          return const Center(child: CircularProgressIndicator());
        } else if (state is PublicationErrorState) {
          return _noDataPlaceholder(
            msg: state.errorText ?? LocaleKeys.noDataAvailable.tr(),
          );
        } else if (state is AuthentificationErrorState) {
          return _noDataPlaceholder(
            msg: state.errorText ?? LocaleKeys.noDataAvailable.tr(),
          );
        } else if (state is PublicationsLoadingState) {
          publicationLoading = true;
        }
        final ValueNotifier<bool> controllerValue = ValueNotifier<bool>(true);
        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            if (listPublication.isEmpty)
              state is PublicationsLoadingState
                  ? const Expanded(
                      child: Center(child: CircularProgressIndicator()),
                    )
                  : Expanded(
                      child: _noDataPlaceholder(),
                    )
            else
             ...[
              Expanded(
                child: ListView.builder(
                  shrinkWrap: true,
                  padding: const EdgeInsets.fromLTRB(24, 24, 24, 150),
                  itemCount: listPublication.length,
                  controller: scrollControllerPublications,
                  itemBuilder: (e, i) {
                    return Container(
                      padding: const EdgeInsets.all(15),
                      margin: const EdgeInsets.only(bottom: 20),
                      decoration: BoxDecoration(
                        color: isLightMode
                            ? AppColors.greyShade7
                            : AppColors.blueShade32,
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: ExpandableNotifier(
                        child: ScrollOnExpand(
                          scrollOnCollapse: false,
                          child: ValueListenableBuilder(
                            valueListenable: controllerValue,
                            builder: (context, value, _) {
                              return Row(
                                crossAxisAlignment: listPublication[i].open
                                    ? CrossAxisAlignment.center
                                    : CrossAxisAlignment.start,
                                children: <Widget>[
                                  Flexible(
                                    child: ExpandablePanel(
                                      theme: ExpandableThemeData(
                                        bodyAlignment: HiveUtilsSettings
                                                    .getAppLanguage() ==
                                                'ar'
                                            ? ExpandablePanelBodyAlignment.right
                                            : ExpandablePanelBodyAlignment.left,
                                        headerAlignment:
                                            ExpandablePanelHeaderAlignment
                                                .center,
                                        tapBodyToCollapse: true,
                                        hasIcon: false,
                                      ),
                                      header: const SizedBox(),
                                      collapsed: Text(
                                        listPublication[i].title ?? '',
                                        softWrap: true,
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        style: AppTextStyles.s14w4cblackShade4
                                            .copyWith(
                                          color: isLightMode
                                              ? AppColors.grey
                                              : AppColors.white,
                                        ),
                                        textScaler: TextScaler.linear(
                                          textScaleFactor.value,
                                        ),
                                      ),
                                      expanded: Text(
                                        listPublication[i].title ?? '',
                                        maxLines: 100,
                                        style: AppTextStyles.s14w4cblackShade4
                                            .copyWith(
                                          color: isLightMode
                                              ? AppColors.grey
                                              : AppColors.greyShade4,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                        textScaler: TextScaler.linear(
                                          textScaleFactor.value,
                                        ),
                                      ),
                                    ),
                                  ),
                                  // const SizedBox(width: 6),
                                  Row(
                                    children: [
                                      if (getLineLength(
                                            context,
                                            listPublication[i].title ?? '',
                                            HiveUtilsSettings
                                                    .getAppLanguage() ==
                                                'ar',
                                          ) >
                                          1)
                                        Builder(
                                          builder: (context) {
                                            final controller =
                                                ExpandableController.of(
                                              context,
                                              required: true,
                                            )!;
                                            return InkWell(
                                              onTap: () {
                                                controllerValue.value =
                                                    controller.expanded;
                                                controller.toggle();
                                                listPublication[i].open =
                                                    controllerValue.value;
                                              },
                                              child: Padding(
                                                padding: EdgeInsets.only(
                                                  right: HiveUtilsSettings
                                                              .getAppLanguage() ==
                                                          'ar'
                                                      ? 0
                                                      : 12,
                                                  left: HiveUtilsSettings
                                                              .getAppLanguage() ==
                                                          'ar'
                                                      ? 12
                                                      : 0,
                                                ),
                                                child: Icon(
                                                  controller.expanded
                                                      ? Icons
                                                          .keyboard_arrow_up_rounded
                                                      : Icons
                                                          .keyboard_arrow_down_rounded,
                                                  size: 18,
                                                  color: isLightMode
                                                      ? AppColors.greyShade11
                                                      : AppColors.blueLightOld,
                                                ),
                                              ),
                                            );
                                          },
                                        ),
                                      if ((listPublication[i].contentFields ??
                                              [])
                                          .isNotEmpty)
                                        getDownloadWidgets(
                                          listPublication[i].contentFields ??
                                              [],
                                        ),
                                    ],
                                  ),
                                ],
                              );
                            },
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
              if (publicationLoading)
                const Padding(
                  padding: EdgeInsets.only(bottom: 150),
                  child: Center(child: CircularProgressIndicator()),
                ),
            ],
          ],
        );
      },
    );
  }

  Widget _noDataPlaceholder({String? msg}) {
    return NoDataPlaceholder(
      msg: msg,
      padding: const EdgeInsets.only(bottom: 150),
    );
  }

  void _pageResetPublication() {
    listPublication = [];
    countPublication = null;
    pageNoPublication = 1;
  }

  void _loadDataPublication() {
    if (scrollControllerPublications.position.pixels >=
        scrollControllerPublications.position.maxScrollExtent) {
      if ((countPublication != null &&
              listPublication.length >= countPublication! * 5) ||
          publicationLoading) {
        return;
      }
      pageNoPublication++;
      context.read<ProductsBloc>().add(
            GetPublicationsEvent(
              pageno: pageNoPublication.toString(),
              token: authToken,
            ),
          );
    }
  }

  int getLineLength(BuildContext context, String text, bool isArabic) {
    final span = TextSpan(text: text, style: AppTextStyles.s14w4cblackShade4);
    final tp = TextPainter(
      text: span,
      textScaler: TextScaler.linear(textScaleFactor.value),
      textDirection: isArabic ? ui.TextDirection.rtl : ui.TextDirection.ltr,
    )..layout(maxWidth: MediaQuery.of(context).size.width - 160);
    final numLines = tp.computeLineMetrics().length;
    return numLines;
  }

  Widget _webReportsView(
    List<ProductDashboardResponseItem> dashboards,
    bool isLightMode,
  ) {
    if (isDemoMode) {
      return _webReportsDashboardsViewDemo();
    }

    return BlocConsumer<ProductsBloc, ProductsState>(
      listener: (BuildContext context, state) {
        if (state is AuthentificationSetSucessState) {
          context.read<ProductsBloc>().add(
                GetWebReportsEvent(
                  pageno: '10',
                  token: state.data?.accessToken ?? '',
                ),
              );
        } else if (state is WebReportsSetSucessState) {
          listWebReport = state.data?.items ?? [];
          for (final (_, item) in listWebReport.indexed) {
            for (final (_, content) in (item.contentFields ?? []).indexed) {
              if (content.label == 'iframeURL English') {
                item.darshboardEn = content.contentFieldValue?.data ?? '';
              } else if (content.label == 'iframeURL Arabic') {
                item.darshboardAr = content.contentFieldValue?.data ?? '';
              }
            }
          }
        }
      },
      builder: (context, state) {
        if (state is AuthentificationLoadingState) {
          return const Center(child: CircularProgressIndicator());
        } else if (state is WebReportsLoadingState) {
          return const Center(child: CircularProgressIndicator());
        } else if (state is WebReportsErrorState) {
          return _noDataPlaceholder(
            msg: state.errorText ?? LocaleKeys.noDataAvailable.tr(),
          );
        } else if (state is AuthentificationErrorState) {
          return _noDataPlaceholder(
            msg: state.errorText ?? LocaleKeys.noDataAvailable.tr(),
          );
        }
        return ListView.builder(
          controller: webReportsScrollController,
          shrinkWrap: true,
          padding: const EdgeInsets.fromLTRB(24, 24, 24, 150),
          itemCount: listWebReport.length,
          itemBuilder: (e, i) {
            return Container(
              margin: const EdgeInsets.only(bottom: 20),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
              ),
              child: Material(
                borderRadius: BorderRadius.circular(15),
                color:
                    isLightMode ? AppColors.greyShade7 : AppColors.blueShade32,
                child: InkWell(
                  borderRadius: BorderRadius.circular(15),
                  onTap: () {
                    HiveUtilsSettings.getAppLanguage() == 'en'
                        ? launchToCall(listWebReport[i].darshboardEn)
                        : launchToCall(listWebReport[i].darshboardAr);
                  },
                  child: Container(
                    padding: const EdgeInsets.all(15),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          flex: 5,
                          child: Row(
                            children: [
                              // SvgPicture.network(
                              //   'https://scad.gov.ae/documents/d/guest/web-reports',
                              //   width: 30,
                              //   height: 30,
                              // ),
                              const SizedBox(width: 9),
                              Flexible(
                                child: Text(
                                  listWebReport[i].title ?? '',
                                  textScaler:
                                      TextScaler.linear(textScaleFactor.value),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 2),
                        SvgPicture.asset(
                          AppImages.icLink,
                          colorFilter: ColorFilter.mode(
                            isLightMode ? AppColors.blueShade22 : Colors.white,
                            BlendMode.srcIn,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget getDownloadWidgets(List<publication.ContentFields> strings) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: strings
          .map(
            (item) => item.contentFieldValue?.document?.contentUrl != null &&
                    item.contentFieldValue?.document?.fileExtension != null
                ? DownloadItemWidget(
                    icon:
                        item.contentFieldValue?.document?.fileExtension == 'pdf'
                            ? 'ic_pdf'
                            : item.contentFieldValue?.document?.fileExtension ==
                                    'xlsx'
                                ? 'ic_excel'
                                : 'ic_word',
                    title:
                        (item.contentFieldValue?.document?.fileExtension ?? '')
                            .toUpperCase(),
                    url:
                        '${ApiConfig.scadApiPath}${item.contentFieldValue?.document?.contentUrl}',
                    fileName:
                        '${item.contentFieldValue?.document?.contentUrl?.split('/').last}.${item.contentFieldValue?.document?.fileExtension}',
                  )
                : const SizedBox.shrink(),
          )
          .toList(),
    );
  }

  Future<void> launchToCall(String path) async {
    try {
      final Uri launchUri = Uri.parse(path);
      await launchUrl(launchUri);
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      AppMessage.showOverlayNotification(
        '',
        LocaleKeys.somethingWentWrong.tr(),
        msgType: 'error',
      );
    }
  }
}

class DownloadItemWidget extends StatelessWidget {
  const DownloadItemWidget({
    required this.icon,
    required this.title,
    this.url,
    this.fileName,
    super.key,
  });

  final String icon;
  final String title;
  final String? url;
  final String? fileName;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.getThemeMode() == ThemeMode.light;
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Material(
          color: isLightMode
              ? AppColors.blueShade24.withOpacity(.1)
              : AppColors.blueLight,
          borderRadius: BorderRadius.circular(100),
          child: InkWell(
            borderRadius: BorderRadius.circular(100),
            onTap: () async {
              // final bool hasPermission = await AppPermissions.checkPermissions(
              //   context,
              //   [AppPermission.storageAndroid],
              // );
              // if (hasPermission) {
              // }
              unawaited(
                DownloadHelper
                    .downloadFileToDownloadsDir(
                  context,
                  url.toString(),
                  fileName: fileName,
                ),
              );
            },
            child: Padding(
              padding: const EdgeInsets.all(10),
              child: SvgPicture.asset(
                width: 15,
                height: 15,
                'assets/images/$icon.svg',
                colorFilter: isLightMode
                    ? ColorFilter.mode(AppColors.blueShade30, BlendMode.srcIn)
                    : ColorFilter.mode(AppColors.white, BlendMode.srcIn),
              ),
            ),
          ),
        ),

        const SizedBox(width: 8),
        // Text(
        //   title,
        //   style: TextStyle(
        //     color: AppColors.grey,
        //     fontSize: 14,
        //     fontWeight: FontWeight.w400,
        //   ),
        // ),
      ],
    );
  }
}
