class ProductDashboardResponseItem {
  String? uuid;
  String? domainId;
  String? domainName;
  String? domainNameAr;
  String? name;
  String? nameAr;
  String? productType;

  ProductDashboardResponseItem({
    this.uuid,
    this.domainId,
    this.domainName,
    this.domainNameAr,
    this.name,
    this.nameAr,
    this.productType,
  });

  ProductDashboardResponseItem.fromJson(Map<String, dynamic> json) {
    uuid = json['uuid'] as String?;
    domainId = json['domain_id'] as String?;
    domainName = json['domain_name'] as String?;
    domainNameAr = json['domain_name_ar'] as String?;
    name = json['name'] as String?;
    nameAr = json['name_ar'] as String?;
    productType = json['product_type'] as String?;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['uuid'] = uuid;
    json['domain_id'] = domainId;
    json['domain_name'] = domainName;
    json['domain_name_ar'] = domainNameAr;
    json['name'] = name;
    json['name_ar'] = nameAr;
    json['product_type'] = productType;
    return json;
  }
}
