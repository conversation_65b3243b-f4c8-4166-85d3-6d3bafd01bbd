class Publications {
  final Actions? actions;
  final List<dynamic>? facets;
  final List<ItemsPublications>? items;
  final int? lastPage;
  final int? page;
  final int? pageSize;
  final int? totalCount;

  Publications({
    this.actions,
    this.facets,
    this.items,
    this.lastPage,
    this.page,
    this.pageSize,
    this.totalCount,
  });

  Publications.fromJson(Map<String, dynamic> json)
      : actions = (json['actions'] as Map<String, dynamic>?) != null
            ? Actions.fromJson(json['actions'] as Map<String, dynamic>)
            : null,
        facets = json['facets'] as List?,
        items = (json['items'] as List?)
            ?.map((dynamic e) =>
                ItemsPublications.fromJson(e as Map<String, dynamic>))
            .toList(),
        lastPage = json['lastPage'] as int?,
        page = json['page'] as int?,
        pageSize = json['pageSize'] as int?,
        totalCount = json['totalCount'] as int?;

  Map<String, dynamic> toJson() => {
        'actions': actions?.toJson(),
        'facets': facets,
        'items': items?.map((e) => e.toJson()).toList(),
        'lastPage': lastPage,
        'page': page,
        'pageSize': pageSize,
        'totalCount': totalCount
      };
}

class Actions {
  final Get? get;

  Actions({
    this.get,
  });

  Actions.fromJson(Map<String, dynamic> json)
      : get = (json['get'] as Map<String, dynamic>?) != null
            ? Get.fromJson(json['get'] as Map<String, dynamic>)
            : null;

  Map<String, dynamic> toJson() => {'get': get?.toJson()};
}

class Get {
  final String? method;
  final String? href;

  Get({
    this.method,
    this.href,
  });

  Get.fromJson(Map<String, dynamic> json)
      : method = json['method'] as String?,
        href = json['href'] as String?;

  Map<String, dynamic> toJson() => {'method': method, 'href': href};
}

class ItemsPublications {
  final List<ContentFields>? contentFields;
  final String? title;
  List<DocumetsPublications> documents;

  ItemsPublications({required this.documents, this.contentFields, this.title});

  ItemsPublications.fromJson(Map<String, dynamic> json)
      : contentFields = (json['contentFields'] as List?)
            ?.map((dynamic e) =>
                ContentFields.fromJson(e as Map<String, dynamic>))
            .toList(),
        title = json['title'] as String?,
        documents = [];

  Map<String, dynamic> toJson() => {
        'contentFields': contentFields?.map((e) => e.toJson()).toList(),
        'title': title,
        'documents': documents
      };
}

class DocumetsPublications {
  final String? fileextention;
  final String? url;

  DocumetsPublications({
    this.fileextention,
    this.url,
  });

  DocumetsPublications.fromJson(Map<String, dynamic> json)
      : fileextention = json['fileextention'] as String?,
        url = json['url'] as String?;

  get length => null;

  Map<String, dynamic> toJson() => {'fileextention': fileextention, 'url': url};
}

class ContentFields {
  final ContentFieldValue? contentFieldValue;
  final String? dataType;
  final String? label;
  final String? name;
  final List<dynamic>? nestedContentFields;
  final bool? repeatable;

  ContentFields({
    this.contentFieldValue,
    this.dataType,
    this.label,
    this.name,
    this.nestedContentFields,
    this.repeatable,
  });

  ContentFields.fromJson(Map<String, dynamic> json)
      : contentFieldValue = (json['contentFieldValue'] != null)
            ? ContentFieldValue.fromJson(
                Map<String, dynamic>.from(json['contentFieldValue'] as Map),
              )
            : null,
        dataType = json['dataType'] as String?,
        label = json['label'] as String?,
        name = json['name'] as String?,
        nestedContentFields = json['nestedContentFields'] as List?,
        repeatable = json['repeatable'] as bool?;

  Map<String, dynamic> toJson() => {
        'contentFieldValue': contentFieldValue?.toJson(),
        'dataType': dataType,
        'label': label,
        'name': name,
        'nestedContentFields': nestedContentFields,
        'repeatable': repeatable
      };
}

class ContentFieldValue {
  final Document? document;

  ContentFieldValue({
    this.document,
  });

  ContentFieldValue.fromJson(Map<String, dynamic> json)
      : document = (json['document'] as Map<String, dynamic>?) != null
            ? Document.fromJson(json['document'] as Map<String, dynamic>)
            : null;

  Map<String, dynamic> toJson() => {'document': document?.toJson()};
}

class Document {
  final String? contentType;
  final String? contentUrl;
  final String? description;
  final String? encodingFormat;
  final String? fileExtension;
  final int? id;
  final int? sizeInBytes;
  final String? title;

  Document({
    this.contentType,
    this.contentUrl,
    this.description,
    this.encodingFormat,
    this.fileExtension,
    this.id,
    this.sizeInBytes,
    this.title,
  });

  Document.fromJson(Map<String, dynamic> json)
      : contentType = json['contentType'] as String?,
        contentUrl = json['contentUrl'] as String?,
        description = json['description'] as String?,
        encodingFormat = json['encodingFormat'] as String?,
        fileExtension = json['fileExtension'] as String?,
        id = json['id'] as int?,
        sizeInBytes = json['sizeInBytes'] as int?,
        title = json['title'] as String?;

  Map<String, dynamic> toJson() => {
        'contentType': contentType,
        'contentUrl': contentUrl,
        'description': description,
        'encodingFormat': encodingFormat,
        'fileExtension': fileExtension,
        'id': id,
        'sizeInBytes': sizeInBytes,
        'title': title
      };
}
