// import 'package:json_annotation/json_annotation.dart';
// part 'product_response.g.dart';
//
// @JsonSerializable()
// class ProductResponse {
//   ProductResponse({
//     required this.id,
//     required this.name,
//     required this.lightIcon,
//     required this.darkIcon,
//     required this.nodeCount,
//     required this.isSelected,
//     required this.domains,
//   });
//
//   factory ProductResponse.fromJson(Map<String, dynamic> json) =>
//       _$ProductResponseFromJson(json);
//   String? id;
//   String? name;
//   @JsonKey(name: 'light_icon')
//   String? lightIcon;
//   @JsonKey(name: 'dark_icon')
//   String? darkIcon;
//   @JsonKey(name: 'node_count')
//   int? nodeCount;
//   bool? isSelected;
//   List<DomainsResponse>? domains;
//
//   Map<String, dynamic> toJson() => _$ProductResponseToJson(this);
// }
//
// @JsonSerializable()
// class DomainsResponse {
//   DomainsResponse({
//     required this.id,
//     required this.name,
//     required this.darkIcon,
//     required this.lightIcon,
//     required this.items,
//   });
//
//   factory DomainsResponse.fromJson(Map<String, dynamic> json) =>
//       _$DomainsResponseFromJson(json);
//   String? id;
//   String? name;
//   @JsonKey(name: 'dark_icon')
//   String? darkIcon;
//   @JsonKey(name: 'light_icon')
//   String? lightIcon;
//   List<DomainItemResponse>? items;
//
//   Map<String, dynamic> toJson() => _$DomainsResponseToJson(this);
// }
//
// @JsonSerializable()
// class DomainItemResponse {
//   DomainItemResponse({
//     required this.id,
//     required this.type,
//     required this.contentType,
//     required this.title,
//     required this.subTitle,
//     required this.domain,
//     required this.category,
//   });
//
//   factory DomainItemResponse.fromJson(Map<String, dynamic> json) =>
//       _$DomainItemResponseFromJson(json);
//   String? id;
//   String? type;
//   @JsonKey(name: 'content_type')
//   String? contentType;
//   String? title;
//   String? subTitle;
//   String? domain;
//   String? category;
//
//   Map<String, dynamic> toJson() => _$DomainItemResponseToJson(this);
// }
