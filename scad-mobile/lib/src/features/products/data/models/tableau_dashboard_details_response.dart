class TableauDashboardDetailsResponse {
  String? uuid;
  String? domainId;
  String? domainName;
  String? domainNameAr;
  String? productType;
  String? name;
  String? nameAr;
  String? url;
  String? urlAr;
  String? embedCode;
  String? embedCodeAr;
  String? embedCodeDark;
  String? embedCodeDarkAr;

  TableauDashboardDetailsResponse({
    this.uuid,
    this.domainId,
    this.domainName,
    this.domainNameAr,
    this.productType,
    this.name,
    this.nameAr,
    this.url,
    this.urlAr,
    this.embedCode,
    this.embedCodeAr,
    this.embedCodeDark,
    this.embedCodeDarkAr,
  });

  TableauDashboardDetailsResponse.fromJson(Map<String, dynamic> json) {
    uuid = json['uuid'] as String?;
    domainId = json['domain_id'] as String?;
    domainName = json['domain_name'] as String?;
    domainNameAr = json['domain_name_ar'] as String?;
    productType = json['product_type'] as String?;
    name = json['name'] as String?;
    nameAr = json['name_ar'] as String?;
    url = json['url'] as String?;
    urlAr = json['url_ar'] as String?;
    embedCode = json['embed_code'] as String?;
    embedCodeAr = json['embed_code_ar'] as String?;
    embedCodeDark = json['embed_code_dark'] as String?;
    embedCodeDarkAr = json['embed_code_dark_ar'] as String?;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['uuid'] = uuid;
    json['domain_id'] = domainId;
    json['domain_name'] = domainName;
    json['domain_name_ar'] = domainNameAr;
    json['product_type'] = productType;
    json['name'] = name;
    json['name_ar'] = nameAr;
    json['url'] = url;
    json['url_ar'] = urlAr;
    json['embed_code'] = embedCode;
    json['embed_code_ar'] = embedCodeAr;
    json['embed_code_dark'] = embedCodeDark;
    json['embed_code_dark_ar'] = embedCodeDarkAr;
    return json;
  }
}
