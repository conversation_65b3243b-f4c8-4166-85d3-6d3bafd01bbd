import 'package:scad_mobile/src/config/app_config/api_config.dart';
import 'package:scad_mobile/src/config/app_config/secret.dart';

class ProductsEndPoints extends ApiConfig {
  static String ifpPath = ApiConfig.ifpApiPath;
  static String appPath = ApiConfig.appApiPath;
  static String scadPath = ApiConfig.scadApiPath;

  static String getDashboards = '$appPath/user/product/list/';
  static String tableauDashboardDetails = '$appPath/user/product/{{uuid}}/';

  static String getAnalyticalApps = '$ifpPath/content-type/analytical-apps/v2';

  static String publication =
      '$scadPath/o/headless-delivery/v1.0/content-structures/108409/structured-contents';
  static String webReport =
      '$scadPath/o/headless-delivery/v1.0/content-structures/1625810/structured-contents?fields=title,contentFields';
  static String getForecast =
      '$ifpPath/content-type/statistics-insights/forecasts/list/';
  static String getReports = '$ifpPath/content-type/reports/';
  static String auth =
      '$scadPath/o/oauth2/token?client_id=${Secret.scadDigitalClientId}&client_secret=${Secret.scadDigitalClientSecret}&grant_type=client_credentials';
}
