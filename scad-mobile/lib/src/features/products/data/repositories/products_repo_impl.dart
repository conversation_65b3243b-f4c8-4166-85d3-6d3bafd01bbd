import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/models/response_models/api_response.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/features/home/<USER>/data_sources/api_end_points.dart';
import 'package:scad_mobile/src/features/home/<USER>/models/indicator_details/indicator_details.dart';
import 'package:scad_mobile/src/features/products/data/data_sources/products_end_points.dart';
import 'package:scad_mobile/src/features/products/data/models/authntification_response.dart';
import 'package:scad_mobile/src/features/products/data/models/forecast_response.dart';
import 'package:scad_mobile/src/features/products/data/models/product_dashboard.dart';
import 'package:scad_mobile/src/features/products/data/models/reports_response.dart';
import 'package:scad_mobile/src/features/products/data/models/response/product_response1.dart';
import 'package:scad_mobile/src/features/products/data/models/tableau_dashboard_details_response.dart';
import 'package:scad_mobile/src/features/products/data/models/web_report_response.dart';
import 'package:scad_mobile/src/features/products/domain/repositories/products_repository_imports.dart';
import 'package:scad_mobile/src/features/search/data/models/search_publications_response.dart';
import 'package:scad_mobile/src/services/http_service_impl.dart';
import 'package:scad_mobile/src/services/http_services.dart';
import 'package:scad_mobile/src/utils/extentions/string_extentions.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';
import 'package:scad_mobile/demo/demo_api_responses.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/demo/demo_api_responses.dart';

class ProductsRepositoryImpl implements ProductsRepository {
  ProductsRepositoryImpl() {
    _httpService = HttpServiceRequests();
  }

  late HttpService _httpService;

  @override
  Future<RepoResponse<List<ProductDashboardResponseItem>>>
      getDashboards() async {
    try {
      if(isDemoMode){
        List<ProductDashboardResponseItem> list = [];
        list = tbDashboards
            .map((e) => ProductDashboardResponseItem.fromJson(
            e as Map<String, dynamic>))
            .toList();

        return RepoResponse<List<ProductDashboardResponseItem>>.success(
          response: list,
        );
      }
      final String endpoint = ProductsEndPoints.getDashboards;
      final ApiResponse response = await _httpService.get(
        endpoint,
      );

      if (response.isSuccess) {
        List<ProductDashboardResponseItem> list = [];
        list = (response.response['data'] as List<dynamic>)
            .map((e) => ProductDashboardResponseItem.fromJson(
                e as Map<String, dynamic>))
            .toList();

        return RepoResponse<List<ProductDashboardResponseItem>>.success(
          response: list,
        );
      } else {
        return RepoResponse<List<ProductDashboardResponseItem>>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<List<ProductDashboardResponseItem>>.error(
        errorMessage:  LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<List<ProductResponse>>> productData() async {
    try {
      if(isDemoMode){
        List<ProductResponse> list = [];
        list = demoProductsResponse
            .map((e) => ProductResponse.fromJson(e as Map<String, dynamic>))
            .toList();
        return RepoResponse<List<ProductResponse>>.success(
          response: list,
        );
      }

      final String endpoint = ProductsEndPoints.getAnalyticalApps;
      final ApiResponse response = await _httpService.get(
        endpoint,
        server: ApiServer.ifp,
      );

      // final ApiResponse response =
      //     ApiResponse.success({'data': analyticalProductsList});
      if (response.isSuccess) {
        List<ProductResponse> list = [];
        list = (response.response['data'] as List<dynamic>)
            .map((e) => ProductResponse.fromJson(e as Map<String, dynamic>))
            .toList();

        /// TODO:- static data please remove it after use
        // ..addAll(
        //   [
        //     ProductResponse(
        //       id: '111',
        //       name: 'Publications',
        //       lightIcon: 'assets/temp_image/publications.svg',
        //       darkIcon: 'assets/temp_image/publications.svg',
        //       nodeCount: 0,
        //       isSelected: false,
        //       domains: [],
        //     ),
        //     ProductResponse(
        //       id: '111',
        //       name: 'Web Reports',
        //       lightIcon: 'assets/temp_image/web-reports.svg',
        //       darkIcon: 'assets/temp_image/web-reports.svg',
        //       nodeCount: 0,
        //       isSelected: false,
        //       domains: [],
        //     ),
        //   ],
        // );

        return RepoResponse<List<ProductResponse>>.success(
          response: list,
        );
      } else {
        return RepoResponse<List<ProductResponse>>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<List<ProductResponse>>.error(
        errorMessage:  LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<Map<String, IndicatorOverview>>> indicatorDetails(
    RequestParamsMap requestParams,
    Map<String, dynamic> payload,
  ) async {
    try {
      final String endpoint =
          HomeEndPoints.indicatorDetails.setUrlParams(requestParams);
      final response = await _httpService.postJson(
        endpoint,
        server: ApiServer.ifp,
        jsonPayloadMap: payload,
      );
      // final ApiResponse response = ApiResponse.success(indicatorDetailsResponseMap);

      if (response.isSuccess) {
        final Map<String, IndicatorOverview> map = {};
        response.response.entries.map((e) {
          map[e.key] =
              IndicatorOverview.fromJson(e.value as Map<String, dynamic>);
        });

        return RepoResponse<Map<String, IndicatorOverview>>.success(
          response: map,
        );
      } else {
        return RepoResponse<Map<String, IndicatorOverview>>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<Map<String, IndicatorOverview>>.error(
        errorMessage:  LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<TableauDashboardDetailsResponse>> tableauDashboardDetails(
    RequestParamsMap requestParams,
  ) async {
    try {
      final String endpoint =
          ProductsEndPoints.tableauDashboardDetails.setUrlParams(requestParams);

      final response = await _httpService.get(endpoint);

      if (response.isSuccess) {
        return RepoResponse<TableauDashboardDetailsResponse>.success(
          response: TableauDashboardDetailsResponse.fromJson(response.response),
        );
      } else {
        return RepoResponse<TableauDashboardDetailsResponse>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<TableauDashboardDetailsResponse>.error(
        errorMessage:  LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<PublicationsResult>> publications(
      RequestParamsMap requestParams, String token) async {
    try {
      if(isDemoMode){
        return RepoResponse<PublicationsResult>.success(
          response: PublicationsResult.fromJson({}),
        );
      }
      final String endpoint = ProductsEndPoints.publication +
          '?fields=contentFields,title&' +
          requestParams.entries
              .map((e) => '${e.key}=${e.value}')
              .toList()
              .join('&');

      final response = await _httpService.get(endpoint, token: token);

      if (response.isSuccess) {
        return RepoResponse<PublicationsResult>.success(
          response: PublicationsResult.fromJson(response.response),
        );
      } else {
        return RepoResponse<PublicationsResult>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<PublicationsResult>.error(
        errorMessage:  LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<Authentification>> authentification() async {
    try {
      if (isDemoMode) {
        return RepoResponse<Authentification>.success(
          response: Authentification.fromJson({
            "access_token": "abcd",
            "token_type": "Bearer",
            "expires_in": 604800,
          }),
        );
      }

      final String endpoint = ProductsEndPoints.auth;

      final response =
          await _httpService.postJson(endpoint, encodedHeader: true);
      if (response.isSuccess) {
        return RepoResponse<Authentification>.success(
          response: Authentification.fromJson(response.response),
        );
      } else {
        return RepoResponse<Authentification>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<Authentification>.error(
        errorMessage:  LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<Webreports>> webReport(
      RequestParamsMap requestParams, String token) async {
    try {
      final String endpoint = ProductsEndPoints.webReport;

      final response = await _httpService.get(endpoint, token: token);

      if (response.isSuccess) {
        return RepoResponse<Webreports>.success(
          response: Webreports.fromJson(response.response),
        );
      } else {
        return RepoResponse<Webreports>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<Webreports>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<List<ForecastResponse>>> getForecasts() async {
    try {
      if(isDemoMode){
        return RepoResponse<List<ForecastResponse>>.success(
          response: [],
        );
      }

      final String endpoint = ProductsEndPoints.getForecast;
      final ApiResponse response = await _httpService.get(
        endpoint,
        server: ApiServer.ifp,
      );

      if (response.isSuccess) {
        List<ForecastResponse> list = [];
        list = (response.response['data'] as List<dynamic>)
            .map((e) => ForecastResponse.fromJson(e as Map<String, dynamic>))
            .toList();

        return RepoResponse<List<ForecastResponse>>.success(
          response: list,
        );
      } else {
        return RepoResponse<List<ForecastResponse>>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<List<ForecastResponse>>.error(
        errorMessage:  LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<List<ReportResponse>>> getReports() async {
    try {
      if(isDemoMode){
        return RepoResponse<List<ReportResponse>>.success(
          response: [],
        );
      }
      final String endpoint = ProductsEndPoints.getReports;
      final ApiResponse response = await _httpService.get(
        endpoint,
        server: ApiServer.ifp,
      );

      if (response.isSuccess) {
        List<ReportResponse> list = [];
        list = (response.response['data'] as List<dynamic>)
            .map((e) => ReportResponse.fromJson(e as Map<String, dynamic>))
            .toList();

        return RepoResponse<List<ReportResponse>>.success(
          response: list,
        );
      } else {
        return RepoResponse<List<ReportResponse>>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<List<ReportResponse>>.error(
        errorMessage:  LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }
}
