import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:scad_mobile/route_manager/route_imports.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/config/dependancy_injection/injection_container.dart';
import 'package:scad_mobile/src/features/authentication/data/models/login_model/response/login_jwt_model.dart';
import 'package:scad_mobile/src/features/notification/presentation/bloc/notification_bloc.dart';
import 'package:scad_mobile/src/features/settings/presentation/bloc/setting_bloc.dart';
import 'package:scad_mobile/src/utils/app_utils/app_log.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_auth.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_persistent.dart';

class FirebaseConfig {
  static final _firebaseMessaging = FirebaseMessaging.instance;
  static final localNotifications = FlutterLocalNotificationsPlugin();

  static final _firebaseAnalytics = FirebaseAnalytics.instance;

  static Future<void> initNotifications() async {
    try {
      await _firebaseMessaging.requestPermission();
      final String? fCMToken = await _firebaseMessaging.getToken();
      await HiveUtilsPersistent.setDeviceToken(fCMToken ?? '');
      await initPushNotifications();
    } catch (e, s) {
      FirebaseCrashlytics.instance.recordError(e, s, fatal: true);
      Completer<dynamic>().completeError(e, s);
    }
  }

  static Future<void> initPushNotifications() async {
    try {
      await FirebaseMessaging.instance
          .setForegroundNotificationPresentationOptions(
        alert: true,
        badge: true,
        sound: true,
      );

      await localNotifications.initialize(
        const InitializationSettings(
          android: AndroidInitializationSettings('@mipmap/ic_launcher'),
          iOS: DarwinInitializationSettings(),
        ),
        onDidReceiveNotificationResponse: (value) async {
          try {
            if (value.payload == null) return;
            AppLog.info('value :- ${jsonDecode(value.payload.toString())}');
            final data = jsonDecode(value.payload.toString())['data'];

            onTapNotification(
              nodeId: data['node_id'].toString(),
              contentType: data['content_type'].toString(),
              uuid: data['uuid'].toString(),
            );
          } catch (e, s) {
            FirebaseCrashlytics.instance.recordError(e, s, fatal: true);
            Completer<dynamic>().completeError(e, s);
          }
        },
      );

      const generalNotificationDetails = NotificationDetails(
        android: AndroidNotificationDetails(
          '1',
          'Default Notifications',
          channelDescription: 'Default Notifications',
        ),
        iOS: DarwinNotificationDetails(),
      );

      await FirebaseMessaging.instance.getInitialMessage().then((message) {
        try {
          /// When app is in killed mode
          if (message != null) {
            final RemoteNotification? notification = message.notification;
            final AndroidNotification? android = message.notification?.android;
            if (notification != null && android != null) {
              onTapNotification(
                nodeId: message.data['node_id'].toString(),
                contentType: message.data['content_type'].toString(),
                uuid: message.data['uuid'].toString(),
              );
              // servicelocator<AppRouter>().push(
              //   DetailsPageRoute(
              //     fromNotification: true,
              //     id: message.data['node_id'].toString(),
              //     contentType: message.data['content_type'].toString(),
              //   ),
              // );
              // final notificationBloc = NotificationBloc();
              // notificationBloc.add(
              //   ReadNotificationEvent(
              //     uuid: message.data['uuid'].toString(),
              //     isRead: true,
              //     index: 0,
              //     toShowToast: false,
              //   ),
              // );
            }
          }
        } catch (e, s) {
          FirebaseCrashlytics.instance.recordError(e, s, fatal: true);
          Completer<dynamic>().completeError(e, s);
        }
      });

      FirebaseMessaging.onMessageOpenedApp.listen(handleMessage);
      FirebaseMessaging.onBackgroundMessage(handleBackgroundMessage);
      FirebaseMessaging.onMessage.listen((message) {
        try {
          final notification = message.notification;

          if (notification == null) return;

          if (notification.android != null) {
            localNotifications.show(
              notification.hashCode,
              notification.title,
              notification.body,
              generalNotificationDetails,
              payload: jsonEncode({
                'data': message.data,
                'title': notification.title,
                'body': notification.body,
              }),
            );
          }

          servicelocator<AppRouter>()
              .navigatorKey
              .currentContext!
              .read<SettingBloc>()
              .add(const DefaultSettingLoadEvent());
        } catch (e, s) {
          FirebaseCrashlytics.instance.recordError(e, s, fatal: true);
          Completer<dynamic>().completeError(e, s);
        }
      });
    } catch (e, s) {
      FirebaseCrashlytics.instance.recordError(e, s, fatal: true);
      Completer<dynamic>().completeError(e, s);
    }
  }

  static Future<void> handleMessage(RemoteMessage? message) async {
    try {
      if (message == null) return;
      AppLog.info('Message :- ${message.data}');
      AppLog.info('Message body:- ${message.notification?.body}');
      onTapNotification(
        nodeId: message.data['node_id'].toString(),
        contentType: message.data['content_type'].toString(),
        uuid: message.data['uuid'].toString(),
      );
      // await servicelocator<AppRouter>().push(
      //   DetailsPageRoute(
      //     fromNotification: true,
      //     id: message.data['node_id'].toString(),
      //     contentType: message.data['content_type'].toString(),
      //   ),
      // );
      // final notificationBloc = NotificationBloc();
      // notificationBloc.add(
      //   ReadNotificationEvent(
      //     uuid: message.data['uuid'].toString(),
      //     isRead: true,
      //     index: 0,
      //     toShowToast: false,
      //   ),
      // );
    } catch (e, s) {
      FirebaseCrashlytics.instance.recordError(e, s, fatal: true);
      Completer<dynamic>().completeError(e, s);
    }
  }

  static Future<void> handleBackgroundMessage(RemoteMessage message) async {
    try {
      final notification = message.notification;
      if (notification == null) return;
      AppLog.info('Title PN: ${message.notification?.title}');
      AppLog.info('Body PN: ${message.notification?.body}');
      AppLog.info('Payload PN: ${message.data}');
      AppLog.info('notification android PN: ${message.notification?.android}');
      // await servicelocator<AppRouter>().push(
      //   DetailsPageRoute(
      //     fromNotification: true,
      //     id: message.data['node_id'].toString(),
      //     contentType: message.data['content_type'].toString(),
      //   ),
      // );
      // final notificationBloc = NotificationBloc();
      // notificationBloc.add(
      //   ReadNotificationEvent(
      //     uuid: message.data['uuid'].toString(),
      //     isRead: true,
      //     index: 0,
      //     toShowToast: false,
      //   ),
      // );
      onTapNotification(
        nodeId: message.data['node_id'].toString(),
        contentType: message.data['content_type'].toString(),
        uuid: message.data['uuid'].toString(),
      );
    } catch (e, s) {
      FirebaseCrashlytics.instance.recordError(e, s, fatal: true);
      Completer<dynamic>().completeError(e, s);
    }
  }

  static void onTapNotification({
    String? nodeId,
    String? contentType,
    String? uuid,
  }) {
    try {
      final notificationBloc = NotificationBloc();
      servicelocator<AppRouter>()
          .removeWhere((route) => route.name == DetailsPageRoute.name);
      servicelocator<AppRouter>().push(
        DetailsPageRoute(
          fromNotification: true,
          id: nodeId,
          contentType: contentType,
        ),
      );
      notificationBloc.add(
        ReadNotificationEvent(
          uuid: uuid!,
          isRead: true,
          index: 0,
          toShowToast: false,
        ),
      );
    } catch (e, s) {
      FirebaseCrashlytics.instance.recordError(e, s, fatal: true);
      Completer<dynamic>().completeError(e, s);
    }
  }

  /// firebase analytics
  static Future<void> setScreenToAnalytics(String? screenName) async {
    try {
      final Map<String, dynamic> params = {};
      LoginJWTModel data = LoginJWTModel();

      if (HiveUtilsAuth.getUserId().isNotEmpty) {
        data = HiveUtilsAuth.getJWTDetails();
        params['user_name'] = _convertEmail(data.preferredUsername ?? '');
      }

      await _firebaseAnalytics.logScreenView(
        screenName: screenName,
        screenClass: screenName,
        parameters: params,
      );
    } catch(e,s){
      Completer<dynamic>().completeError(e,s);
    }
  }

  /// firebase analytics user id setting
  static Future<void> setUserIdToAnalytics(String userId) async {
    await _firebaseAnalytics.setUserId(id: _convertEmail(userId));
    await _firebaseAnalytics.setUserProperty(
      name: 'user_name',
      value: _convertEmail(userId),
    );
  }

  static String _convertEmail(String mail) {
    /// Define special characters used for obfuscation
    final List<String> specialCharacters = [
      '!',
      '`',
      '#',
      r'$',
      '%',
      '^',
      '&',
      '*',
      '(',
      ')',
      '+',
    ];

    /// Replace '@' with '_' and '.' with '-'
    final String obfuscatedText =
        mail.replaceAll('@', '_').replaceAll('.', '-');

    /// Insert random special characters around each character in the
    /// email and at the end and return the obfuscated email
    return '${_randomChoice(specialCharacters)}${obfuscatedText.split('').map((char) => '$char${_randomChoice(specialCharacters)}').join()}${_randomChoice(specialCharacters)}';
  }

  static String _randomChoice(List<String> list) {
    /// Generate a random index within the range of the list
    final int randomIndex = Random().nextInt(list.length);

    /// Return a list of random elements if count is greater than 1
    return List.generate(3, (_) => list[randomIndex]).join();
  }
}
