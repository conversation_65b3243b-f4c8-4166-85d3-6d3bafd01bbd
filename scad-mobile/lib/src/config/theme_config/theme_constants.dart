import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';

class AppThemeData {
  static ThemeData darkTheme = ThemeData.dark().copyWith(
    // textTheme: Typography().englishLike.apply(
    //       fontFamily: GoogleFonts.outfit().fontFamily,
    //     ),
    brightness: Brightness.dark,
    // splashColor: AppColors.greyShade13,
    progressIndicatorTheme: ProgressIndicatorThemeData(color: AppColors.blue),
    scaffoldBackgroundColor: AppColors.scaffoldBackgroundDark,
    textTheme: Typography().white.apply(
      fontFamily: GoogleFonts.outfit().fontFamily,
    ),
    // textTheme: TextTheme(
    //   titleMedium: TextStyle(
    //     color: AppColors.grey,
    //     fontSize: 14,
    //     fontWeight: FontWeight.w300,
    //   ),
    // ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        elevation: 0,
        foregroundColor: AppColors.white,
        backgroundColor: AppColors.blueLightOld,
        textStyle: AppTextStyles.s16w5cWhite,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        padding: const EdgeInsets.all(12),
        shadowColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    ),

    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: AppColors.blueShade22,
        backgroundColor: AppColors.white,
        textStyle: AppTextStyles.s16w5cBlueLight,
        shape: RoundedRectangleBorder(
          side: BorderSide(
              color: AppColors.blueShade22, width: 1),
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: ElevatedButton.styleFrom(
        elevation: 0,
        foregroundColor: AppColors.white,
        backgroundColor: Colors.transparent,
        textStyle: AppTextStyles.s16w5cBlueLight,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        padding: const EdgeInsets.all(12),
        shadowColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
          side: BorderSide(
            color: AppColors.white,
          ),
        ),
      ),
    ),
    textSelectionTheme: TextSelectionThemeData(
      cursorColor: AppColors.blueLightOld,
      selectionHandleColor: AppColors.blueLightOld,
      selectionColor: AppColors.blueShade7,
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      isDense: true,
      fillColor: AppColors.white,
      contentPadding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      hintStyle: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w400,
        color: AppColors.greyShade1,
      ),
      focusedBorder: const OutlineInputBorder(
        borderSide: BorderSide(
          color: Colors.transparent,
        ),
      ),
      border: const OutlineInputBorder(
        borderSide: BorderSide(
          color: Colors.transparent,
        ),
      ),
      enabledBorder: const OutlineInputBorder(
        borderSide: BorderSide(
          color: Colors.transparent,
        ),
      ),
    ),
  );

  static ThemeData lightTheme = ThemeData(
    fontFamily: GoogleFonts.outfit().fontFamily,
    brightness: Brightness.light,
    scaffoldBackgroundColor: AppColors.scaffoldBackgroundLight,
    // splashColor: AppColors.lightBlueContainer,
    progressIndicatorTheme: ProgressIndicatorThemeData(color: AppColors.blue),
    textTheme: TextTheme(
      titleMedium: TextStyle(
        color: AppColors.grey,
        fontSize: 14,
        fontWeight: FontWeight.w300,
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        elevation: 0,
        foregroundColor: AppColors.white,
        backgroundColor: AppColors.blueLight,
        textStyle: AppTextStyles.s16w5cWhite,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        padding: const EdgeInsets.all(12),
        shadowColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: ElevatedButton.styleFrom(
        elevation: 0,
        foregroundColor: AppColors.blueShade22,
        backgroundColor: AppColors.white,
        textStyle: AppTextStyles.s16w5cBlueLight,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        padding: const EdgeInsets.all(12),
        shadowColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          side: BorderSide( color:AppColors.blueShade22 , width: 1),
          borderRadius: BorderRadius.circular(10),
        ), 
      ),
    ),
    textSelectionTheme: TextSelectionThemeData(
      cursorColor: AppColors.blueLight,
      selectionHandleColor: AppColors.blueLight,
      selectionColor: AppColors.blueShade7,
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      isDense: true,
      fillColor: AppColors.white,
      contentPadding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      hintStyle: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w400,
        color: AppColors.greyShade1,
      ),
      focusedBorder: const OutlineInputBorder(
        borderSide: BorderSide(
          color: Colors.transparent,
        ),
      ),
      border: const OutlineInputBorder(
        borderSide: BorderSide(
          color: Colors.transparent,
        ),
      ),
      enabledBorder: const OutlineInputBorder(
        borderSide: BorderSide(
          color: Colors.transparent,
        ),
      ),
    ),

    // textTheme: const TextTheme(
    //   displayLarge: TextStyle(
    //       fontSize: 16, color: Colors.black, fontWeight: FontWeight.w400),
    // headline2: TextStyle(fontSize: 16, fontWeight: FontWeight.w400),
    // headline3: TextStyle(fontSize: 14, fontWeight: FontWeight.w400),
    // titleLarge: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
    // titleMedium: TextStyle(fontSize: 15, fontWeight: FontWeight.w500),
    // titleSmall: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
    // bodyMedium: TextStyle(
    //     fontSize: 13, fontWeight: FontWeight.w400), //DEFAULT TEXT SIZE
    // labelLarge: TextStyle(fontSize: 13, fontWeight: FontWeight.w400),
    // ),
    // primarySwatch: primaryColor,
    // appBarTheme: AppBarTheme(
    //   elevation: 0,
    //   titleSpacing: 0,
    //   titleTextStyle: const TextStyle(
    //       fontSize: 18, fontWeight: FontWeight.w400, color: Colors.black),
    //   iconTheme: IconThemeData(
    //     color: primaryColor.shade700,
    //   ),
    //   backgroundColor: ThemeColors.lWhiteffffffdBlack000000,
    //   actionsIconTheme: IconThemeData(color: primaryColor),
    // ),
    // elevatedButtonTheme: ElevatedButtonThemeData(
    //   style: ElevatedButton.styleFrom(
    //     // backgroundColor: AppColors.blue115CAA,
    //     // padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
    //     // minimumSize: const Size(460.0, 35.0),
    //     textStyle: AppTextStyle.style14w4_1,
    //     shape: RoundedRectangleBorder(
    //       borderRadius: BorderRadius.circular(10),
    //     ),
    //   ),
    // ),
    // cardColor: const Color(0xffFFFFFF),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: AppColors.blueShade22,
        backgroundColor: AppColors.white,
        textStyle: AppTextStyles.s16w5cBlueLight,
        shape: RoundedRectangleBorder(
          side: BorderSide(
              color: AppColors.blueShade22, width: 1),
          borderRadius: BorderRadius.circular(10),
        ),
        ),
      ),
    // ),
    // textButtonTheme: TextButtonThemeData(
    //   style: TextButton.styleFrom(
    //     textStyle: AppTextStyle.style14w4_1,
    //     shape: RoundedRectangleBorder(
    //       borderRadius: BorderRadius.circular(10),
    //     ),
    //   ),
    // ),
  );
}
