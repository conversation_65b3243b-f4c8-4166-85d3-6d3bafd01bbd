//Define global blocs as Multibloc provider list

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/presentation/bloc/indicator_card_bloc.dart';
import 'package:scad_mobile/src/config/dependancy_injection/injection_container.dart';
import 'package:scad_mobile/src/features/authentication/presentation/bloc/authentication_bloc.dart';
import 'package:scad_mobile/src/features/authentication/presentation/bloc/forgot_password/forgot_password_bloc.dart';
import 'package:scad_mobile/src/features/authentication/presentation/bloc/reset_password/reset_password_bloc.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/bloc/chat_with_sme_bloc.dart';
import 'package:scad_mobile/src/features/details/presentation/bloc/details_bloc.dart';
import 'package:scad_mobile/src/features/domains/presentation/bloc/domains_bloc.dart';
import 'package:scad_mobile/src/features/drawer_items/glossary/presentation/bloc/glossary_bloc.dart';
import 'package:scad_mobile/src/features/home/<USER>/bloc/home_bloc/home_bloc.dart';
import 'package:scad_mobile/src/features/drawer_items/about_this_app/presentation/bloc/about_app_bloc.dart';
import 'package:scad_mobile/src/features/drawer_items/feedback/presentation/bloc/feedback_bloc.dart';
import 'package:scad_mobile/src/features/drawer_items/terms_and_conditions/presentation/bloc/t_and_c_bloc.dart';
import 'package:scad_mobile/src/features/my_apps/presentation/bloc/myapps_bloc.dart';
import 'package:scad_mobile/src/features/notification/presentation/bloc/notification_bloc.dart';
import 'package:scad_mobile/src/features/onboarding/presentation/bloc/interest_domain_bloc.dart';
import 'package:scad_mobile/src/features/products/presentation/bloc/products_bloc.dart';
import 'package:scad_mobile/src/features/search/presentation/bloc/search_bloc.dart';
import 'package:scad_mobile/src/features/settings/presentation/bloc/setting_bloc.dart';
import 'package:scad_mobile/src/features/spatial_analytics/presentation/bloc/spatial_analytics_bloc.dart';

final providers = [
  BlocProvider<AuthenticationBloc>(
    create: (context) => servicelocator<AuthenticationBloc>(),
  ),
  BlocProvider<SpatialAnalyticsBloc>(
    create: (context) => servicelocator<SpatialAnalyticsBloc>(),
  ),
  BlocProvider<SearchBloc>(
    create: (context) => servicelocator<SearchBloc>(),
  ),
  BlocProvider<AboutAppBloc>(
    create: (context) => servicelocator<AboutAppBloc>(),
  ),
  BlocProvider<TAndCBloc>(
    create: (context) =>
        servicelocator<TAndCBloc>()..add(const TAndCLoadingEvent()),
  ),
  BlocProvider<FeedbackBloc>(
    create: (context) =>
        servicelocator<FeedbackBloc>()..add(const CombinedFeedbackLoadEvent()),
  ),
  BlocProvider<HomeBloc>(
    create: (context) => servicelocator<HomeBloc>(),
  ),
  BlocProvider<IndicatorCardBloc>(
    create: (context) => servicelocator<IndicatorCardBloc>(),
  ),
  BlocProvider<SettingBloc>(
    create: (context) => servicelocator<SettingBloc>()
      ..add(const ProfilePicSelectionEvent())
      ..add(const NameEditButtonTapEvent())
      ..add(const DefaultSettingLoadEvent()),
  ),

  BlocProvider<DomainsBloc>(
    create: (context) =>
        servicelocator<DomainsBloc>(), //..add(const DomainsInitEvent()),
  ),

  BlocProvider<InterestDomainBloc>(
    create: (context) => servicelocator<InterestDomainBloc>(),
  ),

  BlocProvider<ChatWithSmeBloc>(
      create: (context) => servicelocator<ChatWithSmeBloc>()),
  BlocProvider<GlossaryBloc>(
    create: (context) => servicelocator<GlossaryBloc>(),
  ),
  BlocProvider<DetailsBloc>(
    create: (context) => servicelocator<DetailsBloc>()
      ..add(const ToggleTermsAndConditionCheckEvent()),
  ),
  BlocProvider<ResetPasswordBloc>(
    create: (context) => servicelocator<ResetPasswordBloc>(),
  ),
  BlocProvider<ProductsBloc>(
    create: (context) =>
        servicelocator<ProductsBloc>(), //..add(const ProductsLoadEvent()),
  ),
  BlocProvider<MyAppsBloc>(
    create: (context) => servicelocator<MyAppsBloc>(),
  ),
  BlocProvider<ForgotPasswordBloc>(
    create: (context) => servicelocator<ForgotPasswordBloc>(),
  ),
  BlocProvider<NotificationBloc>(
    create: (context) => servicelocator<NotificationBloc>(),
  ),
];
