import 'dart:async';
import 'dart:io';

import 'package:hive_flutter/hive_flutter.dart';
import 'package:path_provider/path_provider.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_keys.dart';

class HiveConfig {
  static Future<void> initConfig() async {
    try {
    final Directory directory = await getApplicationSupportDirectory();

    Hive.init(directory.path);

    await Hive.openBox<dynamic>(HiveKeys.boxSettings);
    await Hive.openBox<dynamic>(HiveKeys.boxApiCache);
    await Hive.openBox<dynamic>(HiveKeys.boxAuth);
    await Hive.openBox<dynamic>(HiveKeys.boxPersistent);
    await Hive.openBox<dynamic>(HiveKeys.keyAppNotifications);
    await Hive.openBox<dynamic>(HiveKeys.keyMailNotifications);
    // await Hive.openBox<bool>(HiveKeys.isTermsAndConditionsAccepted);
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
    }
  }
}
