name: scad_mobile
description: Bayaan Gov

publish_to: 'none'

version: 1.0.1+7

environment:
  sdk: '>=3.1.5 <4.0.0'

dependencies:
  cupertino_icons: ^1.0.2
  flutter:
    sdk: flutter

  root_jailbreak_sniffer:
    path: ./libraries/root_jailbreak_sniffer-1.0.6
  uae_pass_flutter:
    path: ./libraries/uae_pass_plug_in-0.0.3

  dio: ^5.3.4
  get_it: ^7.6.4
  equatable: ^2.0.5
  auto_route: ^7.8.4
  local_auth: ^2.1.0
  jwt_decoder: ^2.0.1
  file_picker: ^6.1.1
  flutter_svg: ^2.0.9
  flutter_bloc: ^8.1.3
#  fluttertoast: ^8.2.4
  google_fonts: ^6.1.0
  hive_flutter: ^1.1.0
  flutter_xlider: ^3.5.0
  path_provider: ^2.1.1
  json_reflectable: ^1.0.1
  json_annotation: ^4.8.1
  easy_localization: ^3.0.3
  firebase_core: ^2.31.0
  firebase_messaging: ^14.9.2
  firebase_dynamic_links: ^5.5.3
  firebase_crashlytics: ^3.5.5
  firebase_analytics: ^10.10.5
  cached_network_image: ^3.3.0
  flutter_local_notifications: ^16.2.0
  shimmer: ^3.0.0
  flutter_rating_bar: ^4.0.1
#  flutter_widget_from_html: ^0.14.9 # ios error: audio_session package
  flutter_widget_from_html_core: ^0.14.11
  pinput: ^3.0.1
  dotted_border: ^2.1.0
  expandable: ^5.0.1
  dropdown_button2: ^2.3.9
  sliding_up_panel2: ^3.3.0+1
  timeline_tile: ^2.0.0
  flutter_chat_bubble: ^2.0.2
  overlay_support: ^2.1.0
  carousel_slider: ^4.2.1
  smooth_page_indicator: ^1.1.0
  flutter_inset_box_shadow: ^1.0.8
  image_picker: ^1.0.5
  scrollable_positioned_list: ^0.3.8
  syncfusion_flutter_sliders: ^25.1.37
  syncfusion_flutter_xlsio: ^25.1.37
  syncfusion_flutter_charts: ^25.1.37
  syncfusion_flutter_pdf: ^25.1.37
  syncfusion_flutter_core: ^25.1.37
  syncfusion_flutter_treemap: ^25.1.37
  syncfusion_flutter_datagrid: ^25.1.37
  image: ^4.1.3
  screenshot: ^2.1.0
  url_launcher: ^6.2.2
  package_info_plus: ^8.0.0
  cart_stepper: ^4.3.0
  webview_flutter: ^4.4.4
  intl: ^0.18.1
  lottie: ^3.0.0
  audioplayers: ^5.2.1
  permission_handler: ^11.3.0
  device_info_plus: ^9.1.2
  flutter_typeahead: ^5.2.0
  go_router: ^13.2.0
  flutter_sliding_up_panel: ^2.1.1
  open_filex: ^4.4.0
  flutter_udid: ^3.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.4.6
  flutter_lints: ^2.0.0
  hive_generator: ^2.0.1
  json_serializable: ^6.7.1
  very_good_analysis: ^5.1.0
  auto_route_generator: ^7.3.2

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/images/products/
    - assets/images/animation/
    - assets/translations/
    - assets/images/temp/
    - assets/images/png/
    - assets/images/icons/
    - assets/temp_image/
    - assets/audio/
    - assets/images/animation/dark_theme/

